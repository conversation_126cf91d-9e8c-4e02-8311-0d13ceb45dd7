<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical" >

    <SurfaceView
        android:id="@+id/capture_preview"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <RelativeLayout
        android:id="@+id/capture_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" >

        <ImageView
            android:id="@+id/capture_mask_top"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_alignParentTop="true"
            android:background="@drawable/shadow" />

        <RelativeLayout
            android:id="@+id/capture_crop_view"
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:layout_centerHorizontal="true"
            android:layout_below="@id/capture_mask_top"
            android:background="@drawable/qr_code_bg" >

            <ImageView
                android:id="@+id/capture_scan_line"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_marginBottom="5dp"
                android:layout_marginTop="5dp"
                android:visibility="visible"
                android:src="@drawable/scan_line" />
        </RelativeLayout>

        <ImageView
            android:id="@+id/capture_mask_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_below="@id/capture_crop_view"
            android:background="@drawable/shadow" />

        <ImageView
            android:id="@+id/capture_mask_left"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_above="@id/capture_mask_bottom"
            android:layout_alignParentLeft="true"
            android:layout_below="@id/capture_mask_top"
            android:layout_toLeftOf="@id/capture_crop_view"
            android:background="@drawable/shadow" />

        <ImageView
            android:id="@+id/capture_mask_right"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_above="@id/capture_mask_bottom"
            android:layout_alignParentRight="true"
            android:layout_below="@id/capture_mask_top"
            android:layout_toRightOf="@id/capture_crop_view"
            android:background="@drawable/shadow" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="50dp"
        android:layout_gravity="top"
        android:background="#99000000">

        <ImageButton
            android:id="@+id/capture_imageview_back"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:layout_centerVertical="true"
            android:background="@drawable/selector_capture_back"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="#ffffffff"
            android:textSize="20sp"
            android:text="扫一扫"/>

    </RelativeLayout>


</RelativeLayout>