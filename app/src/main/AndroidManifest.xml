<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.kisoft.yuejianli">

    <permission
        android:name="com.kisoft.yuejianli.permission.JPUSH_MESSAGE"
        android:protectionLevel="signature" />
    <permission
        android:name="${applicationId}.push.permission.MESSAGE"
        android:protectionLevel="signature" />
    <permission
        android:name="${applicationId}.permission.C2D_MESSAGE"
        android:protectionLevel="signature" />
    <permission
        android:name="${applicationId}.permission.MIPUSH_RECEIVE"
        android:protectionLevel="signature" />

    <uses-feature android:name="android.hardware.camera2" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<!--    <uses-permission android:name="android.permission.RECORD_AUDIO" />-->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" /> <!-- 极光推送 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- Required -->
    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Required  一些系统要求的权限，如访问网络等 -->
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="com.kisoft.yuejianli.permission.JPUSH_MESSAGE" />
    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- <uses-permission android:name="android.permission.WRITE_SETTINGS" /> &lt;!&ndash; Optional for location &ndash;&gt; -->
    <uses-permission android:name="android.permission.VIBRATE" /> <!-- 用于开启 debug 版本的应用在6.0 系统上 层叠窗口权限 -->
    <!-- <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" /> -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- Optional. Required for location feature -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- jpush分享 -->
    <!-- Required -->
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.BAIDU_LOCATION_SERVICE" />
    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Optional. Required for location feature -->
    <!-- <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" /> -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- <uses-permission android:name="android.permission.WRITE_SETTINGS" /> -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- 捕获屏幕所需权限，Q后新增权限(多人音视频屏幕分享使用) -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- 魅族推送配置 start -->
    <!-- 兼容 flyme5.0 以下版本，魅族内部集成 pushSDK 必填，不然无法收到消息 -->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.GET_TASKS" /> <!-- 兼容 flyme3.0 配置权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="com.meizu.flyme.push.permission.RECEIVE" />
    <uses-permission android:name="${applicationId}.push.permission.MESSAGE" /> <!-- 魅族推送配置 end -->
    <!-- Oppo推送配置（如果应用无透传权限，则不用配置） start -->
    <uses-permission android:name="com.meizu.c2dm.permission.RECEIVE" />
    <uses-permission android:name="${applicationId}.permission.C2D_MESSAGE" /> <!-- Oppo推送配置 end -->
    <!-- Mi推送配置 start -->
    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <uses-permission android:name="${applicationId}.permission.MIPUSH_RECEIVE" /> <!-- 华为角标 -->
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
    <uses-permission android:name="com.hihonor.android.launcher.permission.CHANGE_BADGE" />

    <application
        android:name=".YueApplacation"
        android:allowBackup="true"
        android:icon="@mipmap/ic_app_sc"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        android:usesNonSdkApi="true">
        <activity android:name=".scgl.ScglPublicApplyFormViewListActivity"></activity>
        <activity android:name=".scgl.ScglProjectInfoActivity"></activity>
        <activity android:name=".scgl.ScglSupervisionMenuListActivity"></activity>
        <activity android:name=".scgl.ScglSupervisionControlActivity"></activity>
        <activity android:name=".scgl.ScglPublicViewListActivity"></activity>
        <activity android:name=".scgl.ScglPublicViewDetailActivity"></activity>
        <activity android:name=".scgl.ScglPublicViewDetailWebActivity"></activity>
        <activity
            android:name=".scgl.EnterScglActivity"
            android:screenOrientation="portrait" />
        <activity android:name=".hdmh.HdmhProjectCheckActivity"></activity>
        <activity android:name=".hdmh.HdmhProjectSpecialCheckActivity"></activity>
        <activity android:name=".views.ScanCodeActivity" />
        <activity
            android:name="com.king.zxing.CaptureActivity"
            android:screenOrientation="portrait"
            android:theme="@style/CaptureTheme" />
        <activity android:name=".views.HelmetAlarmMapActivity" />
        <activity android:name=".views.WebNewActivity" />
        <activity android:name=".views.HelmetManagementVedioActivity" />
        <activity android:name=".views.HelmetManagementImageActivity" />
        <activity android:name=".views.HelmetManagementListActivity" />
        <activity android:name=".views.WebViewAndFileListActivity" />
        <activity android:name=".hdmh.HdmhProjectCheckListActivity" />
        <activity android:name=".hdmh.HdmhPublicApplyFormViewListActivity" />
        <activity android:name=".hdmh.HdmhProjectInfoActivity" />
        <activity android:name=".hdmh.HdmhPublicViewDetailFile2Activity" />
        <activity android:name=".hdmh.HdmhPublicViewDetailActivity" />
        <activity android:name=".hdmh.HdmhPublicViewListActivity" />
        <activity android:name=".hdmh.HdmhSupervisionControlActivity" />
        <activity android:name=".hdmh.HdmhSupervisionMenuListActivity" />
        <activity android:name=".views.ProjectSceneListActivity" />
        <activity android:name=".views.AnnNoticeDaiYueActivity" />
        <activity android:name=".views.ShouWenListActivity" />
        <activity android:name=".views.ZhuanfaChayueListActivity" />
        <activity android:name=".adpter.LcZhuanFaChaYueActivity" />
        <activity android:name=".views.YApprovalLogsActivity" />
        <activity android:name=".views.autoform.PublicAutoFormListActivity" />
        <activity android:name=".views.autoform.PublicAutoFormApplyActivity" />
        <activity
            android:name=".views.WeeklyReportListActivity"
            android:exported="false" />
        <activity
            android:name=".views.CertificateListActivity"
            android:exported="false" />
        <activity
            android:name=".views.WorkorderDetailActivity"
            android:exported="false" />
        <activity
            android:name=".views.WorkorderViewStartedActivity"
            android:exported="false" />
        <activity
            android:name=".views.WorkorderActivity"
            android:exported="false" />
        <activity
            android:name=".views.ExpertMainActivity"
            android:exported="false" /> <!-- <activity -->
        <!-- android:name=".viewproject.groupIds.CheckoutRecordItemSelActivity" -->
        <!-- android:exported="false" /> -->
        <activity
            android:name=".views.CheckoutRecordItemSelActivity"
            android:exported="false" />
        <activity
            android:name=".views.PunchCardMapNewActivity"
            android:exported="false" />
        <activity
            android:name=".views.CerNoticeListDetailActivity"
            android:exported="false" />
        <activity
            android:name=".views.CerNoticeListActivity"
            android:exported="false" />
        <activity
            android:name=".views.ExamMaterialsAddNoteActivity"
            android:exported="false" />
        <activity
            android:name=".views.ExamMaterialsNewActivity"
            android:exported="false" />
        <activity
            android:name=".views.CarListActivity"
            android:exported="false" />
        <activity
            android:name=".views.TestPictureAddActivity"
            android:exported="false" />
        <activity
            android:name=".views.ReadRecordListActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SubcontractListActivity"
            android:exported="false" />
        <activity
            android:name=".views.CourseDetailsViewController"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait" />
        <activity
            android:name=".im.IMCompanyContractListActivity"
            android:exported="false" />
        <activity
            android:name=".views.CompanyAddressListActivity"
            android:exported="false" />
        <activity
            android:name=".views.ShouWenDBListActivity"
            android:exported="false" />
        <activity
            android:name=".views.KeyProcessListActivity"
            android:exported="false" />
        <activity
            android:name=".views.ProcessAcceptRecordActivity"
            android:exported="false" />
        <activity
            android:name=".views.AddProcessAcceptActivity"
            android:exported="false" />
        <activity
            android:name=".views.ProcessAcceptActivity"
            android:exported="false" />
        <activity
            android:name=".views.XmkbRyjcActivity"
            android:exported="false" />
        <activity
            android:name=".views.SupDynamicQuestionListActivity"
            android:exported="false" />
        <activity android:name=".views.ProjectMonthReportListActivity" />
        <activity
            android:name=".views.ProjectDailyListActivity"
            android:exported="false" />
        <activity
            android:name=".views.CompanyPhoneDirectoryActivity"
            android:exported="false" />
        <activity android:name=".views.WbsListActivity" />
        <activity android:name=".views.CheckOutRecordActivity" />
        <activity android:name=".ui.UnitChildFXOtherListActivity" />
        <activity android:name=".ui.UnitChildFXCJListActivity" />
        <activity android:name=".ui.UnitChildFXListActivity" />
        <activity android:name=".ui.UnitChildListActivity" />
        <activity android:name=".ui.UnitListActivity" />
        <activity android:name=".ui.QuestionListActivity" />
        <activity android:name=".views.WorkContactSheetAddActivity" />
        <activity android:name=".views.ExamLearnProgressActivity" />
        <activity
            android:name=".views.VideoCenterPlayActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait" />
        <activity android:name=".views.WebActivity" />
        <activity android:name=".views.ShareTestActivity" />
        <activity android:name=".views.PermissionControlActivity" />
        <activity android:name=".im.IMContractListActivity" />
        <activity android:name="com.hyphenate.easeui.ui.EaseShowNormalFileActivity" />
        <activity android:name="com.hyphenate.easeui.ui.EaseShowBigImageActivity" />
        <activity android:name="com.hyphenate.easeui.ui.EaseShowVideoActivity" />
        <activity android:name="com.hyphenate.easeui.ui.EaseShowLocalVideoActivity" />
        <activity android:name=".im.chat.activity.EaseBaiduMapActivity" />
        <activity android:name=".im.chat.activity.ImageGridActivity" />
        <activity android:name=".im.chat.activity.ChatActivity" />
        <activity android:name=".im.chat.activity.SingleChatSetActivity" />
        <activity android:name=".im.ConversationListActivity" />
        <activity android:name=".views.RecordTestActivity" />
        <activity android:name=".views.ProjectCheckRecordListActivity" />
        <activity android:name=".views.ProjectCheckReviewActivity" />
        <activity android:name=".views.ProjectCheckTrackActivity" />
        <activity android:name=".views.ProjectCheckQuestionAddActivity" />
        <activity android:name=".views.ProjectCheckActivity" />
        <activity android:name=".views.ProjectCheckRecordActivity" />
        <activity android:name=".views.ApplyConPayPlanActivity" />
        <activity android:name=".views.PeEvaluateDetailActivity" />
        <activity android:name=".views.MailDetailActivity" />
        <activity android:name=".views.MailAddActivity" />
        <activity android:name=".views.MailListActivity" />
        <activity android:name=".views.SpotCheckActivity" />
        <activity android:name=".views.ResourceCertificateDetailActivity" />
        <activity android:name=".views.ResourceEmpProfessDetailActivity" />
        <activity android:name=".views.ResourcePerformanceDetailActivity" />
        <activity android:name=".views.ResourceContractDetailActivity" />
        <activity android:name=".views.ResourceManagementListActivity" />
        <activity android:name=".views.ResourceManagementActivity" />
        <activity
            android:name=".views.ExamQuestionDetailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.FaceDetectExpActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.OCRTestActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ExamClassifyTestActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ExamMaterialsListActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ExamErrorRecordActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/NoAnimTheme" />
        <activity
            android:name=".views.ExamOnlineActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ExamCollectActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ExamTestActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ExamPracticeActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ExamResultActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ExamRecordActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ItemSelectActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ExamIngActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/NoAnimTheme" />
        <activity
            android:name=".views.ExamMainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.WitnessSamplesInspectActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.DangerInspectionActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectRecordListActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ItemSelectMorePageActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.WorkAccountActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.watermark.ArrivalFragment"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.InstructionsListActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.InstructionsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SupDynamicFragment"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.OrganizationActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.MainActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.MaterialQualityCheckActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TaskQualityCheckActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProcessQualityCheckActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectProgressActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SupervisionLogAvcivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.OnSideCheckActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ShelteredCheckActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.PunchCardActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.PunchCardMapActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.PhoneDirectoryActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.NotesActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.MeetingsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.MettingInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.AttendanceRecordsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.MaterialAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectQualityActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectSafeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ApproveActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TodoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ApproveHandleActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.QualityAccidentActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SceneSafeInspectActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectSafeApproveHandleActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SupervisionNoticeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SuperNoticeListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SuperNoticeAnswerActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.QualityReformSuperNoticeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.MaterialAddListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.QualityCheckListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SupervisionLogListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.QualityInspectionListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.OnsideListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.QualityInvisibilityListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.KnowledgeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.PdfViewActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.LoginActivity"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".views.SplashActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.EnterActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectChoseActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectControlActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.HumanResourcesActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.FunctionAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.UserActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.BusinessManagementActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.CooperateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ContractTypeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ContractListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ContractInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.WorkSubmitActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.InvestmentActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.InvestmentListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.InvestmentInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.AddInvestmentListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SceneActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectInfoControlActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectProcessAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectProcessSelectActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectPrepareInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectPrepareInfoNewActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectEnvPLanListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectModelCommonDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectModelCommonDetailWebActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectOrgCoordinateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.EndServiceActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.UserInfoEditActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.WorkContactSheetDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.PassWordEditActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ContructPayCertificateDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ContructSafePayCertificateDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.EndServiceVisitPlanDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.UnexceptedEventDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.CustomerVisitDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.CustomerPleasedSurveyDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.CustomerManagementActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.CompanyTrainActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TrainInvitationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TrainActivityDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TrainSignInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectFirstMeetingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectSupPLanListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectSupPlanActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectSupBlameListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectSupBlameActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TenderPlanActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TenderPlanDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectProgressDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.MaterialAddSelectActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SuplogQualityInspectionSelectActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectQualityControlActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.MonthlySupActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TenderDepositActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TenderDepositDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TenderOpenInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TenderOpenInfoDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TenderItemAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SelectMarketInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.CompanyOrgInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TenderPlanAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TenderDepositAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TenderDepositReturnAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.OfficialSealApplyListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.OfficialSealApplyInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectNoticeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.NoticeDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.AddNoticeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProgressAllListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.AddProgressActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.GxListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SafeInspectListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.EnvironmentalInspectionActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.photo.PhotoShowBigActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.photo.PhotoAlbumActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.photo.PhotoListActivity"
            android:screenOrientation="portrait" /> <!-- 浏览大图 -->
        <activity
            android:name=".views.PendingActivity"
            android:screenOrientation="portrait" /> <!-- 查看自定义相册 -->
        <activity
            android:name=".views.ReplayActivity"
            android:screenOrientation="portrait" /> <!-- 相册图片列表 -->
        <activity
            android:name=".views.TaskDetailframeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.DocumentUpcomActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.DocumentDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.AnnNoticeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.AnnNoticeDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProcessListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.InitiateProcessActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ApplyFYBXMXActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ApplyActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TaskActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ContractInfoListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ContractChangeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ZxListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ConstructionUnitListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.QualitySecurityListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SecurityInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.QualityInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ArchivesTransferActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ArrivalActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.MeetingAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ArchivesOutdataActivity"
            android:screenOrientation="portrait" /> <!-- 外部资料移交和竣工档案移交合并List界面 -->
        <activity
            android:name=".views.ProgrammeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProgrammeAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TakePictureMapActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.KnowledgeListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectKanBanActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectCheckAndAcceptActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ProjectPreAcceptActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.PreAcceptAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.OutdataTransferActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ArchivesTransferAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.CompleteCheckActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.CompleteCheckAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.QualityReportActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.QualityReportAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TakePictureActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TakePictureAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.KnowledgeDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SceneDynFragment"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.MessageCenterActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.AbstractConferenceActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.AbstractConferenceDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.QuestionsManageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.QuestionDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ControllerListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.SuplogListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.TenderUnitListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ApproveOnsiteListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.ComSourseListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".views.DangersListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".face.FaceDetectActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.jpush.android.ui.PopWinActivity"
            android:exported="false"
            android:theme="@style/MyDialogStyle" />
        <activity
            android:name="cn.jpush.android.ui.PushActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:theme="@android:style/Theme.NoTitleBar">
            <intent-filter>
                <action android:name="cn.jpush.android.ui.PushActivity" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="com.kisoft.yuejianli" />
            </intent-filter>
        </activity>
        <activity
            android:name="cn.jpush.android.service.JNotifyActivity"
            android:exported="true"
            android:taskAffinity="jpush.custom"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.JNotifyActivity" />

                <category android:name="com.kisoft.yuejianli" />
            </intent-filter>
        </activity>
        <activity
            android:name="cn.jiguang.share.android.ui.JiguangShellActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:windowSoftInputMode="stateHidden|adjustResize">

            <!-- Optional QQ分享回调 -->
            <!-- scheme为“tencent”前缀再加上QQ开发者应用的appID；例如appID为123456，则scheme＝“tencent123456” -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="tencent1109201211" />
            </intent-filter>
        </activity>
        <activity
            android:name=".wxapi.WXEntryActivity"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- 极光推送 -->
        <!-- Rich push 核心功能 since 2.0.6 -->
        <!-- Required SDK核心功能 -->
        <activity
            android:name=".orc.camera.CameraActivity"
            android:configChanges="screenSize|orientation"
            android:theme="@android:style/Theme.Holo.NoActionBar.Fullscreen" /> <!-- Required SDK 核心功能 -->
        <!-- 可配置android:process参数将PushService放在其他进程中 -->
        <activity android:name=".im.group.activity.ChatRoomAdminAuthorityActivity" /> <!-- since 3.0.9 Required SDK 核心功能 -->
        <activity android:name=".im.group.activity.ChatRoomDetailActivity" /> <!-- since 1.8.0 option 可选项。用于同一设备中不同应用的JPush服务相互拉起的功能。 -->
        <!-- 若不启用该功能可删除该组件，将不拉起其他应用也不能被其他应用拉起 -->
        <activity android:name=".im.group.activity.GroupAdminAuthorityActivity" /> <!-- since 3.1.0 Required SDK 核心功能 -->
        <activity android:name=".im.group.activity.GroupDetailActivity" /> <!-- Required SDK核心功能 -->
        <activity android:name=".im.group.activity.GroupSimpleDetailActivity" /> <!-- Since JCore2.0.0 Required SDK核心功能 -->
        <!-- 可配置android:process参数将Service放在其他进程中；android:enabled属性不能是false -->
        <!-- 这个是自定义Service，要继承极光JCommonService，可以在更多手机平台上使得推送通道保持的更稳定 -->
        <activity android:name=".im.group.activity.GroupSharedFilesActivity" /> <!-- Required SDK核心功能 -->
        <activity android:name=".im.group.activity.GroupPickContactsActivity" /> <!-- User defined.  For test only  用户自定义的广播接收器 -->
        <!-- User defined.  For test only  用户自定义的广播接收器 -->
        <activity android:name=".im.group.activity.GroupManageIndexActivity" /> <!-- User defined.  For test only  用户自定义接收消息器,3.0.7开始支持,目前新tag/alias接口设置结果会在该广播接收器对应的方法中回调 -->
        <activity android:name=".im.group.activity.GroupTransferActivity" /> <!-- User defined. 用户自定义 Receiver 接收被拉起回调 -->
        <!-- 自定义 Receiver 组件，继承cn.jpush.android.service.WakedResultReceiver类,复写onWake(int wakeType)或 onWake(Context context, int wakeType)方法以监听被拉起 -->
        <activity android:name=".im.group.activity.NewChatRoomActivity" /> <!-- Required SDK核心功能 since 3.3.0 -->
        <activity android:name=".im.group.activity.NewGroupActivity" /> <!-- User defined.  For test only  用户自定义接收消息器,3.0.7开始支持,目前新tag/alias接口设置结果会在该广播接收器对应的方法中回调 -->
        <!-- Required  . Enable it you can get statistics data with channel -->
        <activity android:name=".im.contact.activity.ContactDetailActivity" />
        <activity android:name=".im.chat.activity.PickAtUserActivity" /> <!-- </>值来自开发者平台取得的AppKey -->
        <!-- jpush分享 -->
        <!-- Required SDK核心功能 -->
        <activity android:name=".im.search.SearchSingleChatActivity" /> <!-- Optional 微信分享回调,wxapi必须在包名路径下，否则回调不成功 -->
        <activity android:name=".im.chat.activity.ChatHistoryActivity" />
        <activity android:name=".im.chat.activity.ForwardMessageActivity" />
        <activity android:name=".im.search.SearchConversationActivity" />
        <activity android:name=".im.message.SystemMsgsActivity" />
        <activity android:name=".im.group.activity.GroupMemberTypeActivity" />
        <activity android:name=".im.search.SearchGroupChatActivity" />
        <activity android:name=".im.common.conference.ConferenceInviteActivity" />
        <activity android:name=".im.contact.activity.GroupContactManageActivity" />
        <activity android:name=".im.search.SearchFriendsActivity" />
        <activity android:name=".im.search.SearchGroupActivity" />
        <activity android:name=".im.search.SearchPublicGroupActivity" />
        <activity android:name=".im.group.activity.GroupPrePickActivity" />
        <activity android:name=".views.UserPermissionActivity" />
        <activity
            android:name="com.vivo.push.sdk.LinkProxyClientActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name=".views.OpenClickActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="com.kisoft.yuejianli.views.OpenClickActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity android:name=".plugin.JChatProCallbackActivity" />
        <activity android:name=".im.group.activity.GroupMemberAuthorityActivity" />

        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="9a8d1bc6d9e2469f356eb73ce1309947" />
        <meta-data
            android:name="JPUSH_CHANNEL"
            android:value="developer-default" />
        <meta-data
            android:name="JPUSH_APPKEY"
            android:value="0c8c3761a5bcbcd93d34c5fe" />
        <meta-data
            android:name="EASEMOB_APPKEY"
            android:value="1132210305048609#demo" /> <!-- <meta-data -->
        <!-- android:name="com.vivo.push.api_key" -->
        <!-- android:value="${VIVO_PUSH_APPKEY}" /> -->
        <!-- <meta-data -->
        <!-- android:name="com.vivo.push.app_id" -->
        <!-- android:value="${VIVO_PUSH_APPID}" /> -->
        <meta-data
            android:name="OPPO_APPKEY"
            android:value="OP-您的应用对应的OPPO的APPKEY" />
        <meta-data
            android:name="OPPO_APPID"
            android:value="OP-您的应用对应的OPPO的APPID" />
        <meta-data
            android:name="OPPO_APPSECRET"
            android:value="OP-您的应用对应的OPPO的APPSECRET" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">

            <!-- 提供共享路径 -->
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
        <provider
            android:name="cn.jpush.android.service.DataProvider"
            android:authorities="com.kisoft.yuejianli.DataProvider"
            android:exported="false" />
        <provider
            android:name="cn.jpush.android.service.DownloadProvider"
            android:authorities="com.kisoft.yuejianli.DownloadProvider"
            android:exported="true" />

        <receiver
            android:name="cn.jpush.android.service.PushReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter android:priority="1000">
                <action android:name="cn.jpush.android.intent.NOTIFICATION_RECEIVED_PROXY" /> <!-- Required  显示通知栏 -->
                <category android:name="com.kisoft.yuejianli" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.USER_PRESENT" />
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
            <!-- Optional -->
            <intent-filter>
                <action android:name="android.intent.action.PACKAGE_ADDED" />
                <action android:name="android.intent.action.PACKAGE_REMOVED" />

                <data android:scheme="package" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="cn.jpush.android.service.AlarmReceiver"
            android:exported="false" /> <!-- 设置环信应用的AppKey -->
        <receiver
            android:name=".jpush.MyReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.REGISTRATION" /> <!-- Required  用户注册SDK的intent -->
                <action android:name="cn.jpush.android.intent.MESSAGE_RECEIVED" /> <!-- Required  用户接收SDK消息的intent -->
                <action android:name="cn.jpush.android.intent.NOTIFICATION_RECEIVED" /> <!-- Required  用户接收SDK通知栏信息的intent -->
                <action android:name="cn.jpush.android.intent.NOTIFICATION_OPENED" /> <!-- Required  用户打开自定义通知栏的intent -->
                <action android:name="cn.jpush.android.intent.CONNECTION" /> <!-- 接收网络变化 连接/断开 since 1.6.3 -->
                <category android:name="com.kisoft.yuejianli" />
            </intent-filter>
        </receiver> <!-- <meta-data android:name="EASEMOB_APPKEY"  android:value="1132210305048609#yuejianli" /> -->
        <!-- 声明SDK所需的service SDK核心功能 -->
        <receiver
            android:name=".jpush.MyJPushMessageReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>

                <!-- Required 用户注册 SDK 的 intent -->
                <action android:name="cn.jpush.android.intent.REGISTRATION" />
                <!-- Required 用户接收 SDK 消息的 intent -->
                <action android:name="cn.jpush.android.intent.MESSAGE_RECEIVED" />
                <!-- Required 用户接收 SDK 通知栏信息的 intent -->
                <action android:name="cn.jpush.android.intent.NOTIFICATION_RECEIVED" />
                <!-- Required 用户打开自定义通知栏的 intent -->
                <action android:name="cn.jpush.android.intent.NOTIFICATION_OPENED" />
                <!-- 接收网络变化 连接/断开 since 1.6.3 -->
                <action android:name="cn.jpush.android.intent.CONNECTION" />

                <category android:name="com.kisoft.yuejianli" />
            </intent-filter>
        </receiver>
        <receiver android:name=".jpush.MyWakedResultReceiver">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.WakedReceiver" />

                <category android:name="${applicationId}" />
            </intent-filter>
        </receiver> <!-- 声明SDK所需的receiver -->
        <receiver android:name="com.hyphenate.chat.EMMonitorReceiver">
            <intent-filter>
                <action android:name="android.intent.action.PACKAGE_REMOVED" />

                <data android:scheme="package" />
            </intent-filter>
            <!-- 可选filter -->
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.USER_PRESENT" />
            </intent-filter>
        </receiver> <!-- 华为 HMS Config -->
        <receiver android:name="cn.jpush.android.service.PluginVivoMessageReceiver">
            <intent-filter>

                <!-- 接收 push 消息 -->
                <action android:name="com.vivo.pushclient.action.RECEIVE" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="com.huawei.hms.client.appid"
            android:value="104217911" />
        <meta-data
            android:name="com.huawei.hms.client.cpid"
            android:value="2850086000480192304" /> <!-- huawei push end -->
        <!-- VIVO推送配置 start -->
        <receiver android:name="com.hyphenate.push.platform.vivo.EMVivoMsgReceiver">
            <intent-filter>

                <!-- 接收 push 消息 -->
                <action android:name="com.vivo.pushclient.action.RECEIVE" />
            </intent-filter>
        </receiver>
        <receiver android:name="com.hyphenate.push.platform.meizu.EMMzMsgReceiver">
            <intent-filter>

                <!-- 接收 push 消息 -->
                <action android:name="com.meizu.flyme.push.intent.MESSAGE" />
                <!-- 接收 register 消息 -->
                <action android:name="com.meizu.flyme.push.intent.REGISTER.FEEDBACK" />
                <!-- 接收 unregister 消息 -->
                <action android:name="com.meizu.flyme.push.intent.UNREGISTER.FEEDBACK" />
                <!-- 兼容低版本 Flyme3 推送服务配置 -->
                <action android:name="com.meizu.c2dm.intent.REGISTRATION" />
                <action android:name="com.meizu.c2dm.intent.RECEIVE" />

                <category android:name="${applicationId}" />
            </intent-filter>
        </receiver>
        <receiver android:name="cn.jpush.android.service.PluginMeizuPlatformsReceiver">
            <intent-filter>

                <!-- 接收 push 消息 -->
                <action android:name="com.meizu.flyme.push.intent.MESSAGE" />
                <!-- 接收 register 消息 -->
                <action android:name="com.meizu.flyme.push.intent.REGISTER.FEEDBACK" />
                <!-- 接收 unregister 消息 -->
                <action android:name="com.meizu.flyme.push.intent.UNREGISTER.FEEDBACK" />
                <!-- 兼容低版本 Flyme3 推送服务配置 -->
                <action android:name="com.meizu.c2dm.intent.REGISTRATION" />
                <action android:name="com.meizu.c2dm.intent.RECEIVE" />

                <category android:name="${applicationId}" />
            </intent-filter>
        </receiver> <!-- 推送配置项 -->
        <receiver
            android:name="com.xiaomi.push.service.receivers.NetworkStatusReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.xiaomi.push.service.receivers.PingReceiver"
            android:exported="false"
            android:process=":pushservice">
            <intent-filter>
                <action android:name="com.xiaomi.push.PING_TIMER" />
            </intent-filter>
        </receiver>
        <receiver android:name=".im.common.receiver.MiMsgReceiver">
            <intent-filter>
                <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.MESSAGE_ARRIVED" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.ERROR" />
            </intent-filter>
        </receiver> <!-- VIVO推送配置 end -->
        <!-- MEIZU推送配置 start -->
        <receiver
            android:name="cn.jpush.android.service.PluginXiaomiPlatformsReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.MESSAGE_ARRIVED" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.ERROR" />
            </intent-filter>
        </receiver>

        <service
            android:name="cn.jpush.android.service.PluginHuaweiPlatformsService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name=".service.RecordingService"
            android:enabled="true"
            android:exported="true" />
        <service android:name="com.amap.api.location.APSService" /> <!-- MEIZU推送配置 end -->
        <!-- Oppo推送配置 start -->
        <service
            android:name="cn.jpush.android.service.PushService"
            android:enabled="true"
            android:exported="false"
            android:process=":mult">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.REGISTER" />
                <action android:name="cn.jpush.android.intent.REPORT" />
                <action android:name="cn.jpush.android.intent.PushService" />
                <action android:name="cn.jpush.android.intent.PUSH_TIME" />
            </intent-filter>
        </service> <!-- 兼容Q以下版本 -->
        <service
            android:name="cn.jpush.android.service.DaemonService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.DaemonService" />

                <category android:name="com.kisoft.yuejianli" />
            </intent-filter>
        </service> <!-- 兼容Q版本 -->
        <service
            android:name=".service.PushService"
            android:enabled="true"
            android:exported="false"
            android:process=":pushcore">
            <intent-filter>
                <action android:name="cn.jiguang.user.service.action" />
            </intent-filter>
        </service>
        <service
            android:name="com.hyphenate.chat.EMChatService"
            android:exported="true" />
        <service
            android:name="com.hyphenate.chat.EMJobService"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" /> <!-- since JPushv3.6.8 ，oppov2.1.0 oppo 核心功能 -->
        <service
            android:name=".im.common.service.HMSPushService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
            </intent-filter>
        </service> <!-- Oppo推送配置 end -->
        <!-- Mi推送配置 start -->
        <service
            android:name="com.vivo.push.sdk.service.CommandClientService"
            android:exported="true" />
        <service
            android:name="com.meizu.cloud.pushsdk.NotificationService"
            android:exported="true" /> <!-- 注：此service必须在3.0.1版本以后（包括3.0.1版本）加入 -->
        <service
            android:name="com.heytap.msp.push.service.CompatibleDataMessageCallbackService"
            android:permission="com.coloros.mcs.permission.SEND_MCS_MESSAGE">
            <intent-filter>
                <action android:name="com.coloros.mcs.action.RECEIVE_MCS_MESSAGE" />
            </intent-filter>
        </service>
        <service
            android:name="com.heytap.msp.push.service.DataMessageCallbackService"
            android:permission="com.heytap.mcs.permission.SEND_PUSH_MESSAGE">
            <intent-filter>
                <action android:name="com.heytap.mcs.action.RECEIVE_MCS_MESSAGE" />
                <action android:name="com.heytap.msp.push.RECEIVE_MCS_MESSAGE" />
            </intent-filter>
        </service> <!-- 注：此service必须在2.2.5版本以后（包括2.2.5版本）加入 -->
        <service
            android:name="cn.jpush.android.service.PluginOppoPushService"
            android:permission="com.coloros.mcs.permission.SEND_MCS_MESSAGE">
            <intent-filter>
                <action android:name="com.coloros.mcs.action.RECEIVE_MCS_MESSAGE" />
            </intent-filter>
        </service>
        <service
            android:name="com.xiaomi.push.service.XMPushService"
            android:enabled="true"
            android:process=":pushservice" />
        <service
            android:name="com.xiaomi.push.service.XMJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":pushservice" />
        <service
            android:name="com.xiaomi.mipush.sdk.PushMessageHandler"
            android:enabled="true"
            android:exported="true" />
        <service
            android:name="com.xiaomi.mipush.sdk.MessageHandleService"
            android:enabled="true" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />
    </application>

</manifest>