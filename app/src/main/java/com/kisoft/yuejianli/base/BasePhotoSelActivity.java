package com.kisoft.yuejianli.base;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.PhotoUploadResulte;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.TakePictureInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.entity.event.ImageCompressEvent;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.ui.ImageDialog;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.PermissionsChecker;
import com.kisoft.yuejianli.utils.PhotoUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.views.watermark.ImageUtil;
import com.kisoft.yuejianli.views.watermark.PhotoCaptureActivity;
import com.kisoft.yuejianli.views.watermark.PhotoListener;
import com.kisoft.yuejianli.views.watermark.WaterMask;
import com.kisoft.yuejianli.views.watermark.WaterMaskHelper;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.common.util.LogUtil;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Locale;

import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;
import pub.devrel.easypermissions.AfterPermissionGranted;
import pub.devrel.easypermissions.EasyPermissions;

public abstract class BasePhotoSelActivity<Model extends BaseModel, Presenter extends BasePresenter> extends BaseActivity implements PhotoListener, WaterMask.WaterMaskListener{

    private static final int REQUEST_CODE_PERMISSION_CHOOSE_PHOTO = 1;
    private static final int REQUEST_CODE_PERMISSION_TAKE_PHOTO = 2;

    private static final int PRC_PHOTO_PICKER = 1;


    private TakePictureInfo takePictureInfo=new TakePictureInfo();

    private boolean isAdd = false;

    private PermissionsChecker permissionsChecker;
    protected ArrayList<String> images = new ArrayList<>();
    private BGAPhotoHelper mPhotoHelper;
    static final String[] PERMISSIONS = new String[]{
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA
    };
    private static final int REQUEST_CODE_CHOOSE_PHOTO = 1;
    private static final int REQUEST_CODE_TAKE_PHOTO = 2;
    private static final int REQUEST_CODE_CROP = 3;
    private ImageDialog imageDialog;

    private int maskLocation=4;
    private ArrayList<String> uris;
    WaterMaskHelper waterMaskHelper = new WaterMaskHelper(this);
    private boolean isTakePhoto;
    AMapLocation locationdata = null;
    String address="";

    @Override
    public int getLayoutId() {
        return 0;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initWater();
        initData();
        initView();
    }

    //初始化水印
    private void initWater() {
        //初始化水印工具
        waterMaskHelper = new WaterMaskHelper(this, this, this);
        maskLocation = WaterMask.DefWaterMaskParam.Location.center;
    }

    @Override                       //添加水印：
    public WaterMask.WaterMaskParam onDraw() {

        //拍照后调用，设置水印的基本参数
        WaterMask.WaterMaskParam param = new WaterMask.WaterMaskParam();
        //创建当前时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        Date date = new Date();
        String format = dateFormat.format(date);
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        if (userInfo != null) {
            param.txt.add("记录人：" + userInfo.getName());
        }
        ProjectInfo project = SettingManager.getInstance().getProject();
        if (project != null) {
            param.txt.add("项目：" + project.getProjectName());
        }
        if (isTakePhoto) {
            param.txt.add(format + "  " + address);
        } else {
            param.txt.add("日期：" + format);
        }
        param.location = WaterMask.DefWaterMaskParam.Location.left_bottom;
        param.itemCount = 60;
        return param;
    }

    @Override
    public void onChoose(ArrayList<String> photos) {
        uris = photos;
        //Glide.with(CompleteCheckAddActivity.this).load(photos.get(0)).placeholder(R.mipmap.ic_launcher).centerCrop().error(R.mipmap.ic_launcher).crossFade().into(ivSfenclosure);
    }

    private void initView() {

        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);
        imageDialog = new ImageDialog();
    }

    private void initData() {
        permissionsChecker = new PermissionsChecker(this);
        // 初始添加现场照片
//        images.add("");
    }

    @AfterPermissionGranted(PRC_PHOTO_PICKER)
    protected void choicePhotoWrapper() {
        if (permissionsChecker.lacksPermissions(PERMISSIONS)) {
            getPermissions(Constant.REQUEST_CODE_TAKE_POHO);
            return;
        }
        // todo 空照片 ，添加
        String str[] = new String[]{"系统相机", "手机相册"};
        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
        ab.setItems(str, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                switch (which) {
                    case 0://相机

                        isTakePhoto = true;
                        takePhoto();
                        break;
                    case 1://相册
                        isTakePhoto = false;
                        getPhoto();
                        break;
                }
            }
        });
        ab.show();
    }

    @AfterPermissionGranted(REQUEST_CODE_PERMISSION_CHOOSE_PHOTO)
    public void choosePhoto() {
        String[] perms = {Manifest.permission.WRITE_EXTERNAL_STORAGE};
        if (EasyPermissions.hasPermissions(this, perms)) {
            startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), REQUEST_CODE_CHOOSE_PHOTO);
        } else {
            EasyPermissions.requestPermissions(this, "请开起存储空间权限，以正常使用 悦监理", REQUEST_CODE_PERMISSION_CHOOSE_PHOTO, perms);
        }
    }

    @AfterPermissionGranted(REQUEST_CODE_PERMISSION_TAKE_PHOTO)
    public void takePhoto1() {
        String[] perms = {Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.CAMERA};
        if (EasyPermissions.hasPermissions(this, perms)) {
            try {
                startActivityForResult(mPhotoHelper.getTakePhotoIntent(), REQUEST_CODE_TAKE_PHOTO);
            } catch (Exception e) {
                BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
            }
        } else {
            EasyPermissions.requestPermissions(this, "请开起存储空间和相机权限，以正常使用 悦监理", REQUEST_CODE_PERMISSION_TAKE_PHOTO, perms);
        }
    }


    BGAPhotoHelper1 mPhotoHelper1=new BGAPhotoHelper1();
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode==REQUEST_CODE_CHOOSE_PHOTO){
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        }
        if(requestCode==REQUEST_CODE_TAKE_PHOTO){
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800, 800), REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        }

        if(requestCode==REQUEST_CODE_CROP){
            if (resultCode == Activity.RESULT_OK) {
                // getCropIntent  获取裁剪完图片的路径
                String photoPath = mPhotoHelper1.getCropFilePath();
                File file = new File(mPhotoHelper1.getCropFilePath());
                double fileSize = FileUtil.getFileOrFilesSize(photoPath, 3);
                Log.i("fileSize", fileSize + "");
                if (fileSize > 1) {
                    ImageUtil.compressImageByLuban(photoPath);
                } else {
                    uploadCorpPhoto(file);
                }
            } else {
                mPhotoHelper.deleteCameraFile();
                mPhotoHelper.deleteCropFile();
            }
        }

        if(requestCode==500){
            if (resultCode==501){
                takePictureInfo.setLongLat(data.getStringExtra("myLatLng"));
            }
        }

    }
    //加水印上传图片
    private void uploadCorpPhoto(File file) {
        //应该在这里把绘制完的水印图片路径传过去
        ArrayList<String> strings = new ArrayList<>();
        strings.add(file.toString());
        if (waterMaskHelper.getPhotoListener() != null) {
            //选择照片的uri，默认为下标1的元素
            waterMaskHelper.getPhotoListener().onChoose(strings);
        }
        if (waterMaskHelper.getWaterMarkListener() != null) {
            LogUtil.e("getWaterMarkListener");
            WaterMask.WaterMaskParam maskParam = waterMaskHelper.getWaterMarkListener().onDraw();
            waterMaskHelper.getWaterMarkListener().onDraw();
            Bitmap bitmap = ImageUtil.getBitmap(String.valueOf((file)));
            WaterMask.draw(mContext, bitmap, String.valueOf((file)), maskParam);
            mContext.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(file)));
        }

        // 图片上传
        try {
            PhotoUtil.uploadPhoto1(file.getPath());
        } catch (Exception e) {
            e.printStackTrace();
        }
        LogUtil.e("uploadPhotoImage " + file.getPath());
    }


    //新图片压缩
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void messageEvent(ImageCompressEvent event) {
        if (event == null) return;
        if (event.getType() == ImageCompressEvent.TYPE_START) {
            showProgress();
        } else if (event.getType() == ImageCompressEvent.TYPE_END) {
            dismissProgress();
            uploadCorpPhoto(event.getFile());
        } else if (event.getType() == ImageCompressEvent.TYPE_ERROR) {
            showToast("图片压缩失败");
            dismissProgress();
        }
    }

    //拍照功能代码
    /**
     * 6.0之上权限
     */
    private void getPermissions(int code) {
        ActivityCompat.requestPermissions(this, PERMISSIONS, code);
    }

    /**
     * 拍照
     */
    protected void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), REQUEST_CODE_TAKE_PHOTO);
            PhotoCaptureActivity.setWaterListener(waterMaskHelper.getWaterMarkListener());
            PhotoCaptureActivity.setPhotoListener(waterMaskHelper.getPhotoListener());
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    /**
     * 相册获得照片
     */
    protected void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), REQUEST_CODE_CHOOSE_PHOTO);
    }
    /**
     * 通过url，展示照片
     *
     * @param url
     */
    protected void showImage(String url) {
        if (imageDialog != null) {
            imageDialog.showImageDialog(getFragmentManager(), url);
        }
    }
    private void initImage(String url) {
        Log.i("000", "______图片地址：" + url);
        images.add(0, url);
        onChangeImages(images);
    }

    protected void onChangeImages(ArrayList<String> images){

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(PhotoUploadResulte resulte) {
        dismissProgress();
        showToast(resulte.getMessge());
        if (resulte.isIsok()) {
            // todo 图片上传成功
            String imageUri = SettingManager.getInstance().getBaseUrl() + resulte.getImageUrl();
            initImage(imageUri);
        } else {
            initImage(resulte.getImageUrl());
        }
    }

//    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this);
//    }
//
//    @Override
//    public void onPermissionsGranted(int requestCode, List<String> perms) {
//    }
//
//    @Override
//    public void onPermissionsDenied(int requestCode, List<String> perms) {
//        if (requestCode == PRC_PHOTO_PICKER) {
//            Toast.makeText(this, "您拒绝了「图片选择」所需要的相关权限!", Toast.LENGTH_SHORT).show();
//        }
//    }


    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == Constant.REQUEST_CODE_TAKE_POHO) {
            takePhoto();
        }
    }
    private AMapLocationClient locationClient = null;
    private AMapLocationClientOption locationOption = null;
    private void initLocation(){
        //初始化client
        locationClient = new AMapLocationClient(this.getApplicationContext());
        locationOption = getDefaultOption();
        //设置定位参数
        locationClient.setLocationOption(locationOption);
        // 设置定位监听
        locationClient.setLocationListener(locationListener);

    }
    /**
     * 默认的定位参数
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private AMapLocationClientOption getDefaultOption() {
        AMapLocationClientOption mOption = new AMapLocationClientOption();
        mOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);//可选，设置定位模式，可选的模式有高精度、仅设备、仅网络。默认为高精度模式
        mOption.setGpsFirst(false);//可选，设置是否gps优先，只在高精度模式下有效。默认关闭
        mOption.setHttpTimeOut(30000);//可选，设置网络请求超时时间。默认为30秒。在仅设备模式下无效
        mOption.setInterval(10000);//可选，设置定位间隔。默认为2秒
        mOption.setNeedAddress(true);//可选，设置是否返回逆地理地址信息。默认是true
        mOption.setOnceLocation(false);//可选，设置是否单次定位。默认是false
        mOption.setOnceLocationLatest(false);//可选，设置是否等待wifi刷新，默认为false.如果设置为true,会自动变为单次定位，持续定位时不要使用
        AMapLocationClientOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTP);//可选， 设置网络请求的协议。可选HTTP或者HTTPS。默认为HTTP
        mOption.setSensorEnable(false);//可选，设置是否使用传感器。默认是false
        mOption.setWifiScan(true); //可选，设置是否开启wifi扫描。默认为true，如果设置为false会同时停止主动刷新，停止以后完全依赖于系统刷新，定位位置可能存在误差
        mOption.setLocationCacheEnable(true); //可选，设置是否使用缓存定位，默认为true
        mOption.setGeoLanguage(AMapLocationClientOption.GeoLanguage.DEFAULT);//可选，设置逆地理信息的语言，默认值为默认语言（根据所在地区选择语言）
        return mOption;
    }
    /**
     * 定位监听
     */
    AMapLocationListener locationListener = new AMapLocationListener() {
        @Override
        public void onLocationChanged(AMapLocation location) {
            if (null != location) {
                locationdata = location;
                //errCode等于0代表定位成功，其他的为定位失败，具体的可以参照官网定位错误码说明
                if (location.getErrorCode() == 0) {
                    address=location.getAddress();
                    stopLocation();
                    destroyLocation();
                }
            } else {
                showToast("定位失败");
            }
        }
    };

    /**
     * 开始定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void startLocation() {
        // 设置定位参数
        locationClient.setLocationOption(locationOption);
        // 启动定位
        locationClient.startLocation();
    }

    /**
     * 停止定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void stopLocation() {
        // 停止定位
        locationClient.stopLocation();
    }

    /**
     * 销毁定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void destroyLocation() {
        if (null != locationClient) {
            /**
             * 如果AMapLocationClient是在当前Activity实例化的，
             * 在Activity的onDestroy中一定要执行AMapLocationClient的onDestroy
             */
            locationClient.onDestroy();
            locationClient = null;
            locationOption = null;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        //初始化定位
        initLocation();
        //启动定位
        startLocation();
    }

    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {

    }
}
