package com.kisoft.yuejianli.adpter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.ConInfo;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

import java.util.ArrayList;
import java.util.List;

/**
 * 劳动合同选择
 */
public class ConInfoAdapter extends BaseQuickAdapter<ConInfo, YBaseViewHolder> {

    public ConInfoAdapter() {
        super(R.layout.item_con_info);
    }

    @Override
    protected void convert(YBaseViewHolder helper, ConInfo item) {
        helper.setSelected(R.id.itemView,item.isSelect()).setVisible(R.id.ivSelected,item.isSelect())
                .setText(R.id.tvName,item.getConName())
                .setText(R.id.tvNo,"合同编号："+item.getConNo()).setText(R.id.tvType,"类型："+item.getConClass())
                .setText(R.id.tvTotalPrice,"总投资："+item.getPjTotalInv()).setText(R.id.tvCompactPrice,"合同金额："+item.getConAmount())
                .setText(R.id.tvChargeDirName,"项目总监："+item.getChargeDirName()).setText(R.id.tvChargeEngName,"项目负责人："+item.getChargeEngName())
                .setText(R.id.tvCompactType,"合同类型："+ item.getConType()).setText(R.id.tvBidDirName,"备案总监："+ item.getBidDirName());
    }

    //获取选择的数据
    public List<ConInfo> getSelectData(){
        List<ConInfo> filingCertificates=new ArrayList<>();
        for (ConInfo mDatum : mData) {
            if(mDatum.isSelect()){
                filingCertificates.add(mDatum);
            }
        }
        return filingCertificates;
    }
}
