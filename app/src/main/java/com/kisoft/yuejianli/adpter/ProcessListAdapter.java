package com.kisoft.yuejianli.adpter;

import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.views.InitiateProcessFragment;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description: 流程审批列表 适配器
 * Author     : bhd119
 * QQ         : 602394773
 */
public class ProcessListAdapter extends BaseQuickAdapter<ProcessListBean, ProcessListAdapter.ViewHolder> {

    public ProcessListAdapter() {
        super(R.layout.item_process);
    }

    @Override
    protected void convert(ProcessListAdapter.ViewHolder viewHolder, ProcessListBean item) {
        if (item != null) {
            viewHolder.ivProgressTitle.setText(StringUtil.isEmpty(item.getName()) ? "" : item.getName());
            viewHolder.ivProgressStatus.setText(StringUtil.isEmpty(item.getFlowStateName()) ? "" : item.getFlowStateName());
            viewHolder.ivProgressBeginDate.setText(item.getDraftDateStr() == null ? "" : item.getDraftDateStr());
//            try {
//                viewHolder.ivProgressBeginDate.setText(item.getDraftDate() == null ? "" : DateUtil.longToString(Long.parseLong(item.getDraftDate().getTime()), DateUtil.YMD));
//            } catch (ParseException e) {
//                e.printStackTrace();
//            }
            viewHolder.ivProgressUsername.setText(StringUtil.isEmpty(item.getDraftMan()) ? "" : item.getDraftMan());
            String type = "";
            switch (item.getBusinessType()) {
                case "t_org_empleave":
                    type = "员工请假";
                    break;
                case "t_org_emploan":
                    type = "借款申请";
                    break;
                case "t_org_empsupple":
                    type = "补签申请";
                    break;
                case "t_org_empreim":
                    type = "费用报销";
                    break;
                case "t_org_empdimission":
                    type = "离职申请";
                    break;
                case "t_con_payment":
                    type = "开票申请";
                    break;
                case "t_cvs_sealuseapply":
                    type = "用印申请";
                    break;
                case "t_inv_boundaccpt":
                    type = "保证金申请";
                    break;
                case "t_inv_docborrowapp":
                    type = "证件借用";
                    break;
                case "t_supervision_appeal":
                    type = "现场信息申诉";
                    break;
                case InitiateProcessFragment.CON_APPLY_TYPE:
                    type="合同申请";
                    break;
                case InitiateProcessFragment.BUSINESS_TRAVEL_TYPE:
                    type="出差申请";
                    break;
                case InitiateProcessFragment.OFFICIAL_APPLY_TYPE:
                    type="转正申请";
                    break;
                case InitiateProcessFragment.CON_SETTL_TYPE:
                    type="子合同结算";
                    break;
            }
            viewHolder.ivProgressDate.setText(type);
        }
    }

    public class ViewHolder extends BaseViewHolder {
        @BindView(R.id.iv_progress_title)
        TextView ivProgressTitle;
        @BindView(R.id.iv_progress_status)
        TextView ivProgressStatus;
        @BindView(R.id.iv_progress_begin_date)
        TextView ivProgressBeginDate;
        @BindView(R.id.iv_progress_username)
        TextView ivProgressUsername;
        @BindView(R.id.iv_progress_date)
        TextView ivProgressDate;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
