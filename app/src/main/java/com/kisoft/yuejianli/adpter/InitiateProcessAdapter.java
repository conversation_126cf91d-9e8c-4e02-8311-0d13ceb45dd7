package com.kisoft.yuejianli.adpter;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kisoft.yuejianli.adpter.common.CommonBaseAdapter;
import com.kisoft.yuejianli.adpter.common.CommonViewHolder;
import com.kisoft.yuejianli.entity.ProcessInfo;
import com.kisoft.yuejianli.views.ApplyActivity;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.views.InitiateProcessFragment;

/**
 * Description: 发起流程适配器
 * Author     : yanlu
 * Date       : 2019/1/16 17:20
 */

public class InitiateProcessAdapter extends CommonBaseAdapter<ProcessInfo> {

    public InitiateProcessAdapter(Context mContext, List<ProcessInfo> mDatas) {
        super(mContext, mDatas, R.layout.item_init_process);
    }

    @Override
    public void convert(CommonViewHolder holder, final int position, final ProcessInfo processInfo) {
        ViewHolder vh = new ViewHolder(holder.getConvertView());
        vh.iv.setImageResource(processInfo.getImgSrc());
        vh.tv.setText(processInfo.getTitle());
        vh.llItemMain.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(mContext, ApplyActivity.class);
                String businessType = "";
                switch (position) {
                    case 0:
                        businessType = "t_org_empleave";
                        break;
                    case 1:
                        businessType = "t_org_emploan";
                        break;
                    case 2:
                        businessType = "t_org_empsupple";
                        break;
                    case 3:
                        businessType = "t_org_empreim";
                        break;
                    case 4:
                        businessType = "t_org_empdimission";
                        break;
                    case 5:
                        businessType = "t_con_payment";
                        break;
                    case 6:
                        businessType = "t_cvs_sealuseapply";
                        break;
                    case 7:
                        businessType = "t_inv_boundaccpt";
                        break;
                    case 8:
                        businessType = "t_inv_docborrowapp";
                        break;

                        //现场信息申诉(日志、月报)
                    case 9:
                        businessType = "t_supervision_appeal";
                        break;
                    case 10:
                        businessType = InitiateProcessFragment.BUSINESS_TRAVEL_TYPE;
                        break;
                }
                intent.putExtra("businessType", businessType);
                mContext.startActivity(intent);
            }
        });
    }

    static class ViewHolder {
        @BindView(R.id.iv)
        ImageView iv;
        @BindView(R.id.tv)
        TextView tv;
        @BindView(R.id.ll_item_main)
        LinearLayout llItemMain;

        ViewHolder(View view) {
            ButterKnife.bind(this, view);
        }
    }
}
