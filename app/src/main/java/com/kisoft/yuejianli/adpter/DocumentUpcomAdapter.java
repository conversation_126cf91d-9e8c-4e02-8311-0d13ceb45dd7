package com.kisoft.yuejianli.adpter;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kisoft.yuejianli.adpter.common.CommonRecyclerAdapter;
import com.kisoft.yuejianli.adpter.common.CommonViewHolder;
import com.kisoft.yuejianli.entity.ArcWorkDTO;
import com.kisoft.yuejianli.entity.ArchivesTempletDto;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.io.Serializable;
import java.text.ParseException;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import com.kisoft.yuejianli.R;

/**
 * Description: 待办公文列表适配器
 * Author     : yanlu
 * Date       : 2018/12/29 16:36
 */
public class DocumentUpcomAdapter extends CommonRecyclerAdapter<Serializable> {

    private String type;

    /**
     * 子类构造方法中调用
     *
     * @param mContext
     * @param mDatas
     */
    public DocumentUpcomAdapter(Context mContext, List<Serializable> mDatas, String type) {
        super(mContext, mDatas, R.layout.item_document_upcom);
        this.type = type;
    }

    @Override
    public void convert(CommonViewHolder holder, final int position, final Serializable dto) {
        ViewHolder vh = new ViewHolder(holder.getConvertView());
        vh.tvNo.setText(position + 1 + "");
        if (type.equals("0")) {  //公文起草
            vh.tvNo.setVisibility(View.VISIBLE);
            ArchivesTempletDto.ArchivesTempletDtoBean bean = (ArchivesTempletDto.ArchivesTempletDtoBean) dto;
            vh.tvDocumentName.setText(StringUtil.isEmpty(bean.getTemName()) ? "" : bean.getTemName());
            vh.tvDocumentName.setGravity(Gravity.CENTER);
            vh.tvUpcomStatueLabel.setVisibility(View.GONE);
            vh.tvUpcomStatue.setVisibility(View.GONE);
            vh.tvDocumentUser.setVisibility(View.GONE);
            vh.tvDocumentCompany.setVisibility(View.GONE);
            vh.tvDocumentTime.setVisibility(View.GONE);
        } else if (type.equals("4")){
            ProcessListBean arcWorkDTO = (ProcessListBean) dto;
            vh.tvDocumentName.setText("文件名称:" + (StringUtil.isEmpty(arcWorkDTO.getName()) ? "" : arcWorkDTO.getName()));
            vh.tvDocumentUser.setText("起草人:" + arcWorkDTO.getDraftMan());
            //vh.tvDocumentCompany.setText(dto.);
            try {
                vh.tvDocumentTime.setText("起草时间:" + (arcWorkDTO.getDraftDate() != null ? DateUtil.longToString(Long.parseLong(arcWorkDTO.getDraftDate().getTime()), DateUtil.YMD_HMS) : ""));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            vh.tvUpcomStatue.setText(StringUtil.isEmpty(arcWorkDTO.getFlowStateName()) ? "" : arcWorkDTO.getFlowStateName());


        } else {  //公文起草{
            ArcWorkDTO arcWorkDTO = (ArcWorkDTO) dto;
            vh.tvDocumentName.setText("文件名称:" + (StringUtil.isEmpty(arcWorkDTO.getName()) ? "" : arcWorkDTO.getName()));
            vh.tvDocumentUser.setText("起草人:" + arcWorkDTO.getDraftMan());
            //vh.tvDocumentCompany.setText(dto.);
            try {
                vh.tvDocumentTime.setText("起草时间:" + (arcWorkDTO.getDraftDate() != null ? DateUtil.longToString(Long.parseLong(arcWorkDTO.getDraftDate().getTime()), DateUtil.YMD_HMS) : ""));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            vh.tvUpcomStatue.setText(StringUtil.isEmpty(arcWorkDTO.getFlowStateName()) ? "" : arcWorkDTO.getFlowStateName());
        }

        vh.llMain.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (null != onItemClickListener) {
                    onItemClickListener.onClick(dto, position);
                }
            }
        });
    }

    static class ViewHolder {
        @BindView(R.id.tv_no)
        TextView tvNo;
        @BindView(R.id.tv_document_name)
        TextView tvDocumentName;
        @BindView(R.id.tv_upcom_statue_label)
        TextView tvUpcomStatueLabel;
        @BindView(R.id.tv_upcom_statue)
        TextView tvUpcomStatue;
        @BindView(R.id.tv_document_user)
        TextView tvDocumentUser;
        @BindView(R.id.tv_document_company)
        TextView tvDocumentCompany;
        @BindView(R.id.tv_document_time)
        TextView tvDocumentTime;
        @BindView(R.id.ll_main)
        View llMain;

        ViewHolder(View view) {
            ButterKnife.bind(this, view);
        }
    }

    public static abstract class OnItemClickListener {
        public void onClick(Serializable o, int position) {
        }
    }

    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }
}
