package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.entity.ProjectSafeInspection;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/5/23.
 */

public class ProjectSafeProblemAdapter extends BaseQuickAdapter<ProjectSafeInspection,BaseViewHolder> {

    private int unCompleteColor = ContextCompat.getColor(YueApplacation.mContext, R.color.sign_not);
    private int completeColor = ContextCompat.getColor(YueApplacation.mContext, R.color.sign_ok);
    public ProjectSafeProblemAdapter(int layoutResId, @Nullable List<ProjectSafeInspection> data) {
        super(layoutResId, data);
    }


    @Override
    protected void convert(BaseViewHolder helper, ProjectSafeInspection item) {

        TextView tvTitle = helper.itemView.findViewById(R.id.tv_title);
        TextView tvStatus = helper.itemView.findViewById(R.id.tv_status);
        TextView tvReportName = helper.itemView.findViewById(R.id.tv_report_name);
        TextView tvReportTime = helper.itemView.findViewById(R.id.tv_report_time);
        tvTitle.setText(item.getTaskName() == null ? "" : item.getTaskName());
        tvReportName.setText(item.getInspector());
        tvReportTime.setText(DateUtil.getFormatDate(item.getInspectTimeStr(), DateUtil.YMD));
        if(StringUtil.isEqual(ProjectSafeInspection.PDS_QUALITY_INSPECT_YYZ_DESC, item.getInspectStatus())){
            tvStatus.setText("已整改");
            tvStatus.setTextColor(completeColor);
        }else {
            tvStatus.setText("待整改");
            tvStatus.setTextColor(unCompleteColor);
        }

    }
}
