package com.kisoft.yuejianli.adpter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.ButtonBean;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

/**
 * 考试首页按钮
 */
public class ExamButtonAdapter extends BaseQuickAdapter<ButtonBean, YBaseViewHolder> {
    public ExamButtonAdapter() {
        super(R.layout.item_exam_button);
    }

    @Override
    protected void convert(YBaseViewHolder helper, ButtonBean item) {
        helper.setImageResource(R.id.ivPhoto,item.getIcon()).setText(R.id.tvName,item.getText());
    }
}
