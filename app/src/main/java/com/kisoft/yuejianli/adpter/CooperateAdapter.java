package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;

import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.entity.CooperateItem;

import java.util.List;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.FunctionPermission;
import com.kisoft.yuejianli.manager.SettingManager;
import com.scwang.smartrefresh.layout.footer.FalsifyFooter;

import org.spongycastle.pqc.math.ntru.polynomial.TernaryPolynomial;

/**
 * Created by tudou on 2018/6/8.
 */

public class CooperateAdapter extends BaseQuickAdapter<CooperateItem, BaseViewHolder> {

    private OnClickCooperateListener listener;

    public CooperateAdapter(int layoutResId, @Nullable List<CooperateItem> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, final CooperateItem item) {
        TextView tvCooperateName = helper.itemView.findViewById(R.id.tv_work_name);
        View sendView = helper.itemView.findViewById(R.id.rl_send_work);
        View todoView = helper.itemView.findViewById(R.id.rl_todo_work);
        TextView tvSendNum = helper.itemView.findViewById(R.id.tv_send_num);
        TextView tvTodoNum = helper.itemView.findViewById(R.id.tv_todo_num);
        TextView tvSendMame = helper.itemView.findViewById(R.id.tv_send_name);
        TextView tvTodoName = helper.itemView.findViewById(R.id.tv_todo_name);
        TextView tvThreeName = helper.itemView.findViewById(R.id.tv_wait_name);
        TextView tvThreeNum = helper.itemView.findViewById(R.id.tv_wait_num);
        View threeView = helper.itemView.findViewById(R.id.rl_wait_work);
        View threebtView = helper.itemView.findViewById(R.id.ll_wait_name);

        tvCooperateName.setText(item.getCooperateName());
        tvSendNum.setText(item.getSendNum());
        tvSendMame.setText(item.getSendName());
        tvTodoNum.setText(item.getTodoNum());
        tvTodoName.setText(item.getTodoName());

        if (item.getThreeName().equals("收文登记")) {
            FunctionPermission permissionFunction = SettingManager.getInstance().getPermissionFunction();
            if (permissionFunction != null && permissionFunction.isHasOffice()) {
                sendView.setEnabled(true);
                todoView.setEnabled(true);
            }else {
                tvSendNum.setText("-");
                tvSendMame.setText("-");
                tvTodoNum.setText("-");
                tvTodoName.setText("-");
                sendView.setEnabled(false);
                todoView.setEnabled(false);
            }
        }else {
            sendView.setEnabled(true);
            todoView.setEnabled(true);
        }
        if (item.getThreeName().equals("收文登记") || item.getThreeName().equals("待我查阅")) {
            threeView.setVisibility(View.VISIBLE);
            threebtView.setVisibility(View.VISIBLE);
            tvThreeName.setText(item.getThreeName());
            tvThreeNum.setText(item.getThreeNum());
        } else {
            threeView.setVisibility(View.GONE);
            threebtView.setVisibility(View.GONE);
        }
//        if (item.getThreeName().equals("收文登记")){
//            threeView.setVisibility(View.VISIBLE);
//            threebtView.setVisibility(View.VISIBLE);
//            tvThreeName.setText(item.getThreeName());
//            tvThreeNum.setText(item.getThreeNum());
//        }else{
//            threeView.setVisibility(View.GONE);
//            threebtView.setVisibility(View.GONE);
//        }
        sendView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (listener != null) {
                    listener.onClickSendView(item);
                }
            }
        });

        todoView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                listener.onClickTodoView(item);
            }
        });

        threeView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                listener.onClickThreeView(item);
            }
        });

    }


    public interface OnClickCooperateListener {

        void onClickSendView(CooperateItem cooperateItem);

        void onClickTodoView(CooperateItem cooperateItem);

        void onClickThreeView(CooperateItem cooperateItem);

    }

    public void setOnClickCooperateListener(OnClickCooperateListener listener) {
        this.listener = listener;
    }
}
