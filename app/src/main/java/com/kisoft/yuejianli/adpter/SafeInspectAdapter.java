package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.ProjectSafeInspection;
import com.kisoft.yuejianli.entity.SideReport;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.List;

/**
 * Created by tudou on 2018/5/30.
 */

public class SafeInspectAdapter extends BaseQuickAdapter<ProjectSafeInspection, BaseViewHolder> {

    public SafeInspectAdapter(int layoutResId, @Nullable List<ProjectSafeInspection> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, ProjectSafeInspection item) {
        ImageView ivType = helper.itemView.findViewById(R.id.iv_type);
        TextView tvName = helper.itemView.findViewById(R.id.tv_material_name);
        TextView tvStatus = helper.itemView.findViewById(R.id.tv_material_rank);

        TextView tvPersonkey = helper.itemView.findViewById(R.id.tv_person_key);
        TextView tvPerson = helper.itemView.findViewById(R.id.tv_inspector);
        TextView tvTimeKey = helper.itemView.findViewById(R.id.tv_time_key);
        TextView tvTime = helper.itemView.findViewById(R.id.tv_inspector_time);

        ivType.setImageResource(R.drawable.ic_sup_log);

        tvName.setText("现场安全检查");
        tvStatus.setText("");
        tvPersonkey.setText("巡视人：");
        tvPerson.setText(item.getCreatorName());
        tvTimeKey.setText("日期：");
        tvTime.setText(DateUtil.getYmdByTime(item.getCreateTimeStr()));
    }
}
