package com.kisoft.yuejianli.adpter;

import androidx.annotation.NonNull;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.ProjectMonthReportListBean;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

public class ProjectMonthReportListAdapter extends BaseQuickAdapter<ProjectMonthReportListBean,YBaseViewHolder> {
    public ProjectMonthReportListAdapter() {
        super(R.layout.item_material_add);
    }


    @Override
    protected void convert(@NonNull YBaseViewHolder helper, ProjectMonthReportListBean item) {
        helper.setImageResource(R.id.iv_type,R.drawable.ic_sup_log).setText(R.id.tv_material_name,"监理月报")
                .setText(R.id.tv_material_rank,"").setText(R.id.tv_person_key,"起草人：")
                .setText(R.id.tv_inspector,item.getCreateName()).setText(R.id.tv_time_key,"日期：")
                .setText(R.id.tv_inspector_time, item.getMonthlyReportDate());
    }
}
