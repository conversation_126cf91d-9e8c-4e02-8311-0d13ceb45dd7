package com.kisoft.yuejianli.adpter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.EmpCompactList;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

/**
 * 劳动合同选择
 */
public class EmpCompactSelectedAdapter extends BaseQuickAdapter<EmpCompactList.EmpCompact, YBaseViewHolder> {

    public EmpCompactSelectedAdapter() {
        super(R.layout.item_data_selected);
    }

    @Override
    protected void convert(YBaseViewHolder helper, EmpCompactList.EmpCompact item) {
        helper.setText(R.id.tvName,item.getName()).addOnClickListener(R.id.ivDelete);
    }

}
