package com.kisoft.yuejianli.adpter;

import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseMultiItemQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.entity.MultiItemEntity;

import java.util.ArrayList;
import java.util.List;
import com.kisoft.yuejianli.entity.ComFile;
import com.kisoft.yuejianli.entity.ComFileType;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/5.
 */

public class ComBookAdapter extends BaseMultiItemQuickAdapter<MultiItemEntity, BaseViewHolder> {

    public static final int COM_TYPE = 0;
    public static final int COM_FILE = 1;

    private OnClickComBookListener listener;

    private List<Integer> expendList = new ArrayList<>();

    /**
     * Same as QuickAdapter#QuickAdapter(Context,int) but with
     * some initialization data.
     *
     * @param data A new list is created out of this one to avoid mutable list
     */
    public ComBookAdapter(List<MultiItemEntity> data) {
        super(data);
        addItemType(COM_TYPE, R.layout.item_com_file_type);
        addItemType(COM_FILE, R.layout.item_com_file);
    }

    @Override
    protected void convert(BaseViewHolder helper, final MultiItemEntity item) {
        final int position = helper.getAdapterPosition();
        switch (helper.getItemViewType()){
            case COM_TYPE:
                ComFileType type = (ComFileType) item;
                TextView tvTypeName = helper.itemView.findViewById(R.id.tv_type_name);
                tvTypeName.setText(type.getTypeName());
                helper.itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {

                        if(clickType(position)){
                            //  如果展开项包含
                            expand(position);
                        }else {
                            //  不包含
                            collapse(position);
                        }

                    }
                });

                break;
            case COM_FILE:
                final ComFile file  = (ComFile) item;
                TextView tvBookName = helper.itemView.findViewById(R.id.tv_book_name);
                tvBookName.setText(file.getFileName());
                helper.itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        if(listener != null){
                            listener.onClickComBook(file);
                        }
                    }
                });
        }
    }

    private boolean clickType(int position){
        boolean isExpend = false;
        Integer pos = new Integer(position);
        if(this.expendList.contains(pos)){   //如果包含，删除
            this.expendList.remove(pos);
        }else {
            this.expendList.add(pos);
        }

        if(this.expendList.contains(pos)){
            isExpend = true;
        }else {
            isExpend = false;
        }
        notifyDataSetChanged();
        return  isExpend;
    }



    public interface OnClickComBookListener{
       void onClickComBook(ComFile file);
    }


    public void setOnClickComBookListener(OnClickComBookListener listener){
        this.listener = listener;
    }
}
