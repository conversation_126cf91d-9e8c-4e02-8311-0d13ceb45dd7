package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.entity.MaterialType;

import java.util.List;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;

/**
 * Created by tudou on 2018/3/14.
 */

public class MaterialTypeAdapter extends BaseQuickAdapter<MaterialType, BaseViewHolder> {

    public MaterialTypeAdapter(int layoutResId, @Nullable List<MaterialType> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, MaterialType item) {
        final int position = helper.getAdapterPosition();
        View line = helper.itemView.findViewById(R.id.line);
        TextView tvMaterialName = helper.itemView.findViewById(R.id.tv_material);
        if (position == 0) {
            line.setVisibility(View.INVISIBLE);
        } else {
            line.setVisibility(View.VISIBLE);
        }
        tvMaterialName.setText(item.getName());
        tvMaterialName.setTextColor(ContextCompat.getColor(YueApplacation.mContext, R.color.text_bg));

    }
}
