package com.kisoft.yuejianli.adpter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

import java.util.List;

/**
 * String类型显示一行
 */
public class StringItemLineAdapter extends BaseQuickAdapter<String, YBaseViewHolder> {
    public StringItemLineAdapter(List<String> stringList) {
        super(R.layout.item_string_item_line,stringList);
    }

    @Override
    protected void convert(YBaseViewHolder helper, String item) {
        helper.setText(R.id.tvContent,item);
    }
}
