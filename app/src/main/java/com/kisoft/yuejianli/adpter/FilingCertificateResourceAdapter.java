package com.kisoft.yuejianli.adpter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.EmpProfess;
import com.kisoft.yuejianli.entity.FilingCertificate;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

/**
 * 公司证件资源管理
 */
public class FilingCertificateResourceAdapter extends BaseQuickAdapter<FilingCertificate, YBaseViewHolder> {

    public FilingCertificateResourceAdapter() {
        super(R.layout.item_emp_profess_resource);
    }

    @Override
    protected void convert(YBaseViewHolder helper, FilingCertificate item) {
        helper.setText(R.id.tvName,item.getFcName())
                .setText(R.id.tvType,"类别："+item.getFcStyle())
                .setText(R.id.tvNo,"证书编号："+item.getFcNumber()).setText(R.id.tvAwardName,"颁发机构："+item.getAwardAgenName())
                .setText(R.id.tvTime,"有效期："+ item.getIssueDateStr()+" 至 "+item.getValidDateStr())
                .setText(R.id.tvState,"失效".equals(item.getCredentials())?"已失效":null);
    }

}
