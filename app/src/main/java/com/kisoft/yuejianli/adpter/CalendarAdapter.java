package com.kisoft.yuejianli.adpter;

import android.content.Context;
import android.graphics.Color;
import android.widget.TextView;

import com.kisoft.yuejianli.adpter.common.CommonBaseAdapter;
import com.kisoft.yuejianli.adpter.common.CommonViewHolder;
import com.kisoft.yuejianli.entity.CalendarData;
import com.kisoft.yuejianli.entity.PunchCardInfo;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.R;

/**
 * Author     : yanlu
 * Date       : 2018/12/17 10:36
 */

public class CalendarAdapter extends CommonBaseAdapter {

    public List<PunchCardInfo> info = new ArrayList<>();

    /**选择的位置**/
    private int selectPosition=-1;

    /**是否选中本月第一天**/
    private boolean isCheckedFirstDay=true;

    public void setCheckedFirstDay(boolean checkedFirstDay) {
        isCheckedFirstDay = checkedFirstDay;
    }

    public void setSelectPosition(int selectPosition) {
        this.selectPosition = selectPosition;
        notifyDataSetChanged();
    }

    public CalendarAdapter(Context mContext, List mDatas) {
        //子类适配中，必须实现父类的构造方法
        super(mContext, mDatas, R.layout.item_calendar);
    }

    @Override
    public void convert(CommonViewHolder holder, int position, Object o) {
        CalendarData calendarData= (CalendarData) o;

        TextView tv_day=holder.getView(R.id.tv_item_calendar_day);
        tv_day.setText(calendarData.getDay());


        //选中
        if(selectPosition==position){
            //淡蓝色背景，蓝色边框，字体颜色-白色
            tv_day.setBackgroundResource(R.drawable.shape_circle_calendar_checked_bg);
            tv_day.setTextColor(Color.WHITE);
        }else{
            //为今天
            if(calendarData.isToday()){
                //蓝色背景，字体颜色-白色
                tv_day.setBackgroundResource(R.drawable.shape_circle_calendar_today_bg);
                tv_day.setTextColor(Color.WHITE);
            }else{
                tv_day.setBackgroundColor(Color.TRANSPARENT);
                tv_day.setTextColor(Color.BLACK);
                for (int i = 0; i < info.size(); i++) {
                    PunchCardInfo data = info.get(i);
                    if (data.getYear().equals(calendarData.getYear()) &&
                            data.getMonth().equals(calendarData.getMonth()) &&
                            data.getDay().equals(calendarData.getDay())) {
                        //蓝色框，字体颜色-黑色
                        tv_day.setBackgroundResource(R.drawable.shape_circle_calendar_bg);
                        tv_day.setTextColor(Color.BLACK);
                    }
                }
            }

        }
    }

}
