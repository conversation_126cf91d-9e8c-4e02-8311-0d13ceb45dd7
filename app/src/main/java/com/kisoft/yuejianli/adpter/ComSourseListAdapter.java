package com.kisoft.yuejianli.adpter;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.interf.GlobalListener;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ComSourseListBean;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description: 适配器
 * Author     : bhd119
 * QQ         : 602394773
 */
public class ComSourseListAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    List<ComSourseListBean> data;
    Context mContext;
    GlobalListener<ComSourseListBean> globalListener;

    public ComSourseListAdapter(@Nullable Context context, @Nullable List<ComSourseListBean> data, GlobalListener<ComSourseListBean> globalListener) {
        mContext = context;
        this.data = data;
        this.globalListener = globalListener;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            default:
            case Constant.VIEWTYPE_COM_FOLDER:
                return new ViewHolder1(View.inflate(mContext, R.layout.item_com_resourse_folder, null));
            case Constant.VIEWTYPE_COM_FILE:
                return new ViewHolder2(View.inflate(mContext, R.layout.item_com_resourse_file, null));
        }
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, final int position) {
        final ComSourseListBean item = data.get(position);
        switch (holder.getItemViewType()) {
            default:
            case Constant.VIEWTYPE_COM_FOLDER:
                bindViewHolder1((ViewHolder1) holder, data.get(position));
                break;
            case Constant.VIEWTYPE_COM_FILE:
                bindViewHolder2((ViewHolder2) holder, data.get(position));
                break;

        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (null != globalListener)
                    globalListener.onRootViewClick(view, position, item);
            }
        });
    }

    private void bindViewHolder1(ViewHolder1 holder, ComSourseListBean item) {
        holder.tv_name.setText(item.getName()+"("+item.getFileCounts()+")");
    }

    private void bindViewHolder2(ViewHolder2 holder, ComSourseListBean item) {
        holder.tv_name.setText(item.getName());
        holder.tv_describe.setText(item.getType() + " " + item.getFsize() + " " + item.getCreator() + " " + item.getDatetime());
    }

    @Override
    public int getItemViewType(int position) {
        if ("文件夹".equals(data.get(position).getType())) {
            return Constant.VIEWTYPE_COM_FOLDER;
        } else {
            return Constant.VIEWTYPE_COM_FILE;
        }
    }

    @Override
    public int getItemCount() {
        if (data == null || data.isEmpty())
            return 0;
        return data.size();
    }

    public class ViewHolder1 extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_name)
        TextView tv_name;

        ViewHolder1(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }

    public class ViewHolder2 extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_name)
        TextView tv_name;
        @BindView(R.id.tv_describe)
        TextView tv_describe;

        ViewHolder2(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
