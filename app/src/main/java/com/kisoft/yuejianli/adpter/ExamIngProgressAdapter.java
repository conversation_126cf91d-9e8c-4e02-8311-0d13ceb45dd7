package com.kisoft.yuejianli.adpter;

import androidx.core.content.ContextCompat;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.ExamQuestionInfo;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

/**
 * 查看答题进度
 */
public class ExamIngProgressAdapter extends BaseQuickAdapter<ExamQuestionInfo, YBaseViewHolder> {

    private int currentPos;

    public ExamIngProgressAdapter() {
        super(R.layout.item_exam_ing_progress);
    }

    @Override
    protected void convert(YBaseViewHolder helper, ExamQuestionInfo item) {
        helper.setVisible(R.id.tvCurrentPage,currentPos==helper.getAdapterPosition()).setText(R.id.tvPage,String.valueOf(helper.getAdapterPosition()+1));
        if(ExamQuestionInfo.ANSWER_PROGRESS_NONE.equals(item.getIsRight())){
            helper.setBackgroundRes(R.id.tvPage,R.drawable.bg_circle_grey_empty).setTextColor(R.id.tvPage, ContextCompat.getColor(mContext,R.color.ic_text_normal));
        }else if(ExamQuestionInfo.ANSWER_PROGRESS_COMPLETE.equals(item.getIsRight())){
            helper.setBackgroundRes(R.id.tvPage,R.drawable.bg_circle_blue_empty).setTextColor(R.id.tvPage, ContextCompat.getColor(mContext,R.color.colorPrimary));
        }else if(ExamQuestionInfo.ANSWER_PROGRESS_ERROR.equals(item.getIsRight())){
            helper.setBackgroundRes(R.id.tvPage,R.drawable.bg_circle_answer_error).setTextColor(R.id.tvPage, ContextCompat.getColor(mContext,R.color.red_color));
        }
    }

    public void setCurrentPos(int currentPos) {
        this.currentPos = currentPos;
        notifyDataSetChanged();
    }
}
