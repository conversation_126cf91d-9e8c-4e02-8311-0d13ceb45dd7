package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.entity.TrainInvitation;

import java.util.List;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/19.
 */

public class TrainPlanAdapter extends BaseQuickAdapter<TrainInvitation,BaseViewHolder> {

    public TrainPlanAdapter(int layoutResId, @Nullable List<TrainInvitation> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, TrainInvitation item) {
        TextView tvTitle  = helper.itemView.findViewById(R.id.tv_train_plan_title);
        TextView tvOrg = helper.itemView.findViewById(R.id.tv_train_unit);
        TextView tvEndTime = helper.itemView.findViewById(R.id.tv_end_time);

        tvTitle.setText(item.getTitle());
        tvOrg.setText(item.getDepartmentName());
        tvEndTime.setText(item.getInviDateStr());
    }
}
