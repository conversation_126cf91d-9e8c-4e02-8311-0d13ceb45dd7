package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.entity.ProjectInfo;

import java.util.List;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/3/16.
 */

public class MyProjectAdapter extends BaseQuickAdapter<ProjectInfo, BaseViewHolder> {


    public MyProjectAdapter(int layoutResId, @Nullable List<ProjectInfo> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, ProjectInfo item) {
        TextView tvProjectName = helper.itemView.findViewById(R.id.tv_my_project);
        tvProjectName.setText(item.getProjectName());
    }
}
