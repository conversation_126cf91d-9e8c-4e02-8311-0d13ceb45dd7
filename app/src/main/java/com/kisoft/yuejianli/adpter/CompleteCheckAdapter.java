package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.CompleteCheckInfo;
import java.util.List;

public class CompleteCheckAdapter extends BaseQuickAdapter<CompleteCheckInfo, BaseViewHolder> {
    public CompleteCheckAdapter(int layoutResId, @Nullable List<CompleteCheckInfo> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, CompleteCheckInfo item) {
        TextView tvAcceptanceId = helper.itemView.findViewById(R.id.tv_acceptance_id);
        TextView tvCheckupTime = helper.itemView.findViewById(R.id.tv_checkup_time);
        TextView tvPwiName = helper.itemView.findViewById(R.id.tv_pwi_name);
        TextView tvSuperVisor = helper.itemView.findViewById(R.id.tv_super_visor);
        TextView tvAuditState = helper.itemView.findViewById(R.id.tv_audit_state);
        View ll_acceptname = helper.itemView.findViewById(R.id.ll_accept_name);
        ll_acceptname.setVisibility(View.VISIBLE);
        tvAcceptanceId.setText("建设单位:"+item.getBuidUnit());
        tvCheckupTime.setText("验收日期:"+item.getWorkDate());
        tvPwiName.setText("验收人:"+item.getCreateName());
        tvSuperVisor.setText("合格状态:"+item.getQualifiedState());

    }

}
