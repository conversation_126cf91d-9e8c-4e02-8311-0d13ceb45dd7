package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.InfoIssueDto;
import com.kisoft.yuejianli.utils.ImageLoadUtil;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import com.kisoft.yuejianli.R;

/**
 * Description: 公告通知适配器
 * Author     : yanlu
 * Date       : 2019/1/14 16:36
 */

public class AnnNoticeAdapter extends BaseQuickAdapter<InfoIssueDto.InfoIssueBean, AnnNoticeAdapter.ViewHolder> {

    private int type = 0;

    public void setType(int type) {
        this.type = type;
    }

    public AnnNoticeAdapter(@Nullable List data) {
        super(R.layout.item_ann_notice, data);
    }


    @Override
    protected void convert(ViewHolder vh, InfoIssueDto.InfoIssueBean item) {
        if (item != null) {
            if (type == 0) {
                vh.llFirstType.setVisibility(View.VISIBLE);
                vh.llOtherType.setVisibility(View.GONE);
                vh.ivAnnNoticeContent.setText(item.getTitle());
                vh.ivAnnNoticeUsername.setText(item.getFcreatorName());
                vh.ivAnnNoticeDate.setText(item.getFcreatedate());
                vh.tvType.setText(item.getName());
            } else {
                vh.llFirstType.setVisibility(View.GONE);
                vh.llOtherType.setVisibility(View.VISIBLE);
                ImageLoadUtil.loading(mContext, item.getFpath(), vh.ivAnnNoticeImg);
                vh.ivAnnNoticeTitle.setText(item.getTitle());
                vh.ivAnnNoticeDate2.setText(item.getFcreatedate());
            }
        }
    }

    public class ViewHolder extends BaseViewHolder{
        @BindView(R.id.ll_first_type)
        LinearLayout llFirstType;
        @BindView(R.id.ll_other_type)
        LinearLayout llOtherType;
        @BindView(R.id.iv_ann_notice_img)
        ImageView ivAnnNoticeImg;
        @BindView(R.id.iv_ann_notice_title)
        TextView  ivAnnNoticeTitle;
        @BindView(R.id.iv_ann_notice_content)
        TextView  ivAnnNoticeContent;
        @BindView(R.id.iv_ann_notice_username)
        TextView  ivAnnNoticeUsername;
        @BindView(R.id.iv_ann_notice_date)
        TextView  ivAnnNoticeDate;
        @BindView(R.id.iv_ann_notice_date2)
        TextView  ivAnnNoticeDate2;
        @BindView(R.id.tv_type)
        TextView  tvType;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
