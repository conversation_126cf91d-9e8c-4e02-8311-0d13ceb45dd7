package com.kisoft.yuejianli.adpter;

import android.view.View;
import android.widget.TextView;
import androidx.annotation.NonNull;
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.views.WbsListActivity;
import com.kisoft.yuejianli.views.autoform.PublicAutoFormListDataInfo;

import java.util.ArrayList;
import java.util.List;

public class PublicAutoFormListAdapter extends BaseMultiItemQuickAdapter<MultiItemEntity, BaseViewHolder> {
    private List<Integer> expendList = new ArrayList<>();
    private OnClickListItemListener listener;

    public PublicAutoFormListAdapter(List<MultiItemEntity> data) {
        super(data);
        addItemType(WbsListActivity.SECTION_VIEW, R.layout.item_wbs_sectionview);
        addItemType(WbsListActivity.ITEM_VIEW, R.layout.item_exam_quest_bank_types);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, MultiItemEntity item) {
        int position = helper.getAdapterPosition();
        switch (helper.getItemViewType()) {
            case WbsListActivity.SECTION_VIEW:

                PublicAutoFormListDataInfo info = (PublicAutoFormListDataInfo) item;
                TextView tvSection = helper.itemView.findViewById(R.id.tv_wbs_section);
                tvSection.setText(info.getName());
                //tvSection.setText();
                break;

            case WbsListActivity.ITEM_VIEW:
                PublicAutoFormListDataInfo.PublicAutoFormListDataBean bean = (PublicAutoFormListDataInfo.PublicAutoFormListDataBean) item;
                TextView tvTaskTitle = helper.itemView.findViewById(R.id.tvName);
                tvTaskTitle.setText(bean.getFormName());
                helper.itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        if (listener != null) {
                            listener.onClickItemListener(bean);
                        }
                    }
                });

                break;
        }
    }

    public interface OnClickListItemListener {
        void onClickItemListener(PublicAutoFormListDataInfo.PublicAutoFormListDataBean info);
    }

    public void setOnClickListItemListener(OnClickListItemListener listener) {
        this.listener = listener;
    }
}

