package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.entity.UnexpectedEvent;

import java.util.List;

import com.kisoft.yuejianli.R;

/**
 * Created by tudo<PERSON> on 2018/6/16.
 */

public class UnexpectedEventAdapter extends BaseQuickAdapter<UnexpectedEvent, BaseViewHolder> {

    public UnexpectedEventAdapter(int layoutResId, @Nullable List<UnexpectedEvent> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, UnexpectedEvent item) {
        TextView tvTime = helper.itemView.findViewById(R.id.tv_unexcepted_time);
        ImageView ivStatus = helper.itemView.findViewById(R.id.iv_status);
        if(item.getStatus() == 0){
            ivStatus.setImageResource(R.drawable.ic_un_complate);
        }else if(item.getStatus() == 1){
            ivStatus.setImageResource(R.drawable.ic_complate);
        }
        tvTime.setText(item.getUnexpectedTime());
    }
}
