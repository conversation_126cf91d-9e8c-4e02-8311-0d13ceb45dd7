package com.kisoft.yuejianli.adpter;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.AppPermission;

import java.util.List;
import java.util.Map;

public class InitiateProcessPermissionMoreTypeAdapter extends RecyclerView.Adapter<InitiateProcessPermissionMoreTypeAdapter.InnerHolder>  {
    //定义2个常量标识,因为2种类型
    public static final int TYPE_FULL_IMAGE = 0;
    public static final int TYPE_RIGHT_IMAGE = 1;
    private List<Map> mData;
    private OnItemClickListener mOnItemClickListener;
    public InitiateProcessPermissionMoreTypeAdapter() {
    }

    @NonNull
    @Override
    public InnerHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View view = View.inflate(viewGroup.getContext(), R.layout.item_init_process_button, null);
        return new InnerHolder(view);
    }


    @Override
    public void onBindViewHolder(@NonNull InnerHolder holder, int position) {
        holder.setData(mData.get(position), position);
    }

    @Override
    public int getItemCount() {
        if (mData != null) {
            return mData.size();
        }
        return 0;
    }


    public void setData(List<Map> list) {
        this.mData = list;
        notifyDataSetChanged();
    }

    public interface OnItemClickListener {
        void onItemClick(Map map);
    }
    public void setOnItemClickListener(OnItemClickListener listener) {
        this.mOnItemClickListener = listener;
    }

    public class InnerHolder extends RecyclerView.ViewHolder {
        TextView sectionTitle;
        private Map tmap;
        public InnerHolder(@NonNull View itemView) {
            super(itemView);
            sectionTitle = itemView.findViewById(R.id.tvName);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mOnItemClickListener != null) {
                        mOnItemClickListener.onItemClick(tmap);
                    }
                }
            });
        }

        public void setData(Map map, int position) {
            this.tmap = map;
            sectionTitle.setText(map.get("menuName").toString());
        }
    }
}