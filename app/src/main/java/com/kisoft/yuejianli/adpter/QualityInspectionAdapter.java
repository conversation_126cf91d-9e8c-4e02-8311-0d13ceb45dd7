package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import com.kisoft.yuejianli.R;

import com.kisoft.yuejianli.entity.QualityInspection;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/5/25.
 */

public class QualityInspectionAdapter extends BaseQuickAdapter<QualityInspection,BaseViewHolder> {


    public QualityInspectionAdapter(int layoutResId, @Nullable List<QualityInspection> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, QualityInspection item) {
        ImageView ivType = helper.itemView.findViewById(R.id.iv_type);
        TextView tvName = helper.itemView.findViewById(R.id.tv_material_name);
        TextView tvStatus = helper.itemView.findViewById(R.id.tv_material_rank);

        TextView tvPersonkey = helper.itemView.findViewById(R.id.tv_person_key);
        TextView tvPerson = helper.itemView.findViewById(R.id.tv_inspector);
        TextView tvTimeKey = helper.itemView.findViewById(R.id.tv_time_key);
        TextView tvTime = helper.itemView.findViewById(R.id.tv_inspector_time);

        ivType.setImageResource(R.drawable.ic_sup_log);
        tvName.setText("现场巡视");
        tvStatus.setText("");
        tvPersonkey.setText("巡视人：");
        tvTimeKey.setText("日期：");
        tvPerson.setText(StringUtil.isEmpty(item.getCreateName()) ? "" : item.getCreateName());
        tvTime.setText(DateUtil.getYmdByTime(item.getCreateTime()));
    }
}
