package com.kisoft.yuejianli.adpter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.ConInfo;
import com.kisoft.yuejianli.entity.ConSettlinfo;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

import java.util.ArrayList;
import java.util.List;

/**
 * 子合同选择
 */
public class ConSettlInfoAdapter extends BaseQuickAdapter<ConSettlinfo, YBaseViewHolder> {

    public ConSettlInfoAdapter() {
        super(R.layout.item_con_info);
    }

    @Override
    protected void convert(YBaseViewHolder helper, ConSettlinfo item) {
        helper.setText(R.id.tvName,item.getSubConName())
                .setText(R.id.tvNo,"合同编号："+item.getSubConNo()).setText(R.id.tvType,"类型："+item.getConType())
                .setText(R.id.tvTotalPrice,"合同金额："+item.getConAmount()).setText(R.id.tvCompactPrice,"")
                .setText(R.id.tvChargeDirName,"所属主合同："+item.getConName()).setText(R.id.tvChargeEngName,"")
                .setText(R.id.tvCompactType,"合同类型："+ item.getConType()).setText(R.id.tvBidDirName,"");
    }

}
