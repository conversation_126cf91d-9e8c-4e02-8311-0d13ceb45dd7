package com.kisoft.yuejianli.adpter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.EmpProfess;
import com.kisoft.yuejianli.entity.EmpProfessList;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

import java.util.ArrayList;
import java.util.List;

/**
 * 个人证件选择
 */
public class EmpProfessAdapter extends BaseQuickAdapter<EmpProfess, YBaseViewHolder> {

    public EmpProfessAdapter() {
        super(R.layout.item_emp_profess);
    }

    @Override
    protected void convert(YBaseViewHolder helper, EmpProfess item) {
        helper.setSelected(R.id.itemView,item.isSelect()).setVisible(R.id.ivSelected,item.isSelect())
                .setVisible(R.id.ivInvalid,item.isInvalid()).setText(R.id.tvName,item.getEname())
                .setText(R.id.tvOwner,"持证人："+item.getName()).setText(R.id.tvType,"证书类型："+item.getProtype())
                .setText(R.id.tvNo,"证书编号："+item.getProfessnum()).setText(R.id.tvAwardName,"颁发机构："+item.getCorp())
                .setText(R.id.tvTime,"有效期："+ item.getAppdate()+" 至 "+item.getExpdate());
    }

    //获取选择的数据
    public List<EmpProfess> getSelectData(){
        List<EmpProfess> filingCertificates=new ArrayList<>();
        for (EmpProfess mDatum : mData) {
            if(mDatum.isSelect()){
                filingCertificates.add(mDatum);
            }
        }
        return filingCertificates;
    }
}
