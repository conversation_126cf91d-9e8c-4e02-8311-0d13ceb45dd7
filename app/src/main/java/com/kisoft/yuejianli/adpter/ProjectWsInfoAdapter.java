package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.entity.ProjectWsInfo;

import java.util.List;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/4/13.
 */

public class ProjectWsInfoAdapter extends BaseQuickAdapter<ProjectWsInfo,BaseViewHolder> {

    public ProjectWsInfoAdapter(int layoutResId, @Nullable List<ProjectWsInfo> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, ProjectWsInfo item) {
        final int position = helper.getAdapterPosition();
        View line = helper.itemView.findViewById(R.id.line);
        TextView tvWsName = helper.itemView.findViewById(R.id.tv_material);
        if(position == 0){
            line.setVisibility(View.INVISIBLE);
        }else {
            line.setVisibility(View.VISIBLE);
        }
        tvWsName.setText(item.getPwiName());
    }
}
