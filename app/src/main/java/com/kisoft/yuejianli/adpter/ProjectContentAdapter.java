package com.kisoft.yuejianli.adpter;

import android.graphics.drawable.Drawable;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.entity.ProjectContent;
import com.kisoft.yuejianli.utils.StringUtil;

import org.xutils.common.util.LogUtil;

import java.util.List;

/**
 * Created by tudou on 2018/3/12.
 */

public class ProjectContentAdapter extends BaseQuickAdapter<ProjectContent, BaseViewHolder> {

    public ProjectContentAdapter(int layoutResId, @Nullable List<ProjectContent> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, ProjectContent item) {
        TextView tvContent = helper.itemView.findViewById(R.id.tv_content);
        Drawable contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_project_introduction);

        tvContent.setText(item.getName());
        switch (item.getId()) {
            /** 工程监理模块 */
            case ProjectContent.PROJECT_INFO:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_project_introduction);
                break;
            case ProjectContent.PROJECT_QUALITY:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_project_quality);
                break;
            case ProjectContent.PROJECT_SAFETY:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_project_safety);
                break;
            case ProjectContent.PROJECT_PROGRESS:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_project_progress);
                break;
            case ProjectContent.PROJECT_CONTRACT:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_project_contrat);
                break;
            case ProjectContent.PROJECT_INVESTMENT:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_project_investment);
                break;
            case ProjectContent.PROJECT_INFO_CONTROL:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_project_library);
                break;
            case ProjectContent.PROJECT_END_SERVICE:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_project_tree);
                break;
            case ProjectContent.PROJECT_NOTICE_SERVICE:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_notice);
                break;
            case ProjectContent.PROJECT_SCENE:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_project_record);
                break;
            case ProjectContent.PROJECT_PREPARE_INFO:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_prapare_info);
                break;
            case ProjectContent.PROJECT_ORGANIZATION_COORDINATE:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_org_coordinate);
                break;
            /*case ProjectContent.PROJECT_ABSTRACT_CONFERENCE_SERVICE:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_org_coordinate);
                break;*/
            case ProjectContent.PROJECT_QUESTION_MANAGE_SERVICE:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_org_coordinate);
                break;
            case ProjectContent.PROJECT_KNOWLEDGE_SERVICE:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_org_coordinate);
                break;

            //竣工验收
            case ProjectContent.PROJECT_CHECK_AND_ACCEPT:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_org_coordinate);
                break;
            /** 人力资源模块 */
            case ProjectContent.PROJECT_ATTENDANCE_SGIN_IN:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_human_sign);
                break;

            case ProjectContent.PROJECT_ATTENDANCE_RECORD:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_human_sign_record);
                break;

            case ProjectContent.PROJECT_HUMAN_ASSESSMENT:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_human_assessment);
                break;

 /*           case ProjectContent.PROJECT_COMPANY_TRAINING:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_human_train);
                break;*/

            case ProjectContent.PROJECT_LIBRARY:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_human_knowledge);
                break;


            /** 经营管理模块  */
            case ProjectContent.PROJECT_TENDER_PLAN:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_tender_plan);
                break;

            case ProjectContent.PROJECT_TENDER_MONEY:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_tender_money);
                break;

            case ProjectContent.PROJECT_TENDER_FILE:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_tender_file);
                break;

            case ProjectContent.PROJECT_TENDER_START:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_tender_start);
                break;

            case ProjectContent.PROJECT_CUSTOMER_MANAGEMENT:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_customer_manager);
                break;

            case ProjectContent.PROJECT_RESOURCE_MANAGEMENT:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_resource_management);
                break;

            /** 竣工验收模块  */
            case ProjectContent.PROJECT_ACCEPT_PRE:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_02);
                break;

            case ProjectContent.PROJECT_ACCEPT_CHECK:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_03);
                break;

            case ProjectContent.PROJECT_ACCEPT_REPORT:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_04);
                break;

            case ProjectContent.PROJECT_ACCEPT_ARCHIVES:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_05);
                break;

            case ProjectContent.PROJECT_ACCEPT_MEANS:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_07);
                break;

            /** 资源管理模块  */
            case ProjectContent.PROJECT_RESOURCE_CONTRACT:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_resource_management_contract);
                break;

            case ProjectContent.PROJECT_RESOURCE_SUP:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_resource_management_sup);
                break;

            case ProjectContent.PROJECT_RESOURCE_PERSON:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_resource_management_preson);
                break;

            case ProjectContent.PROJECT_RESOURCE_EMP:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_resource_management_emp);
                break;

            case ProjectContent.PROJECT_RESOURCE_CERTIFICATE:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_resource_management_certificate);
                break;

                //项目看板
            case ProjectContent.PROJECT_KANBAN:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_org_coordinate);
                break;
                //监理指令
            /*case ProjectContent.PROJECT_INSTRUCTION:
                contentIc=ContextCompat.getDrawable(YueApplacation.mContext,R.drawable.ic_org_coordinate);
                break;*/
        }
        contentIc.setBounds(0, 0, contentIc.getMinimumWidth(), contentIc.getMinimumHeight());
        tvContent.setCompoundDrawables(null, contentIc, null, null);
    }

}
