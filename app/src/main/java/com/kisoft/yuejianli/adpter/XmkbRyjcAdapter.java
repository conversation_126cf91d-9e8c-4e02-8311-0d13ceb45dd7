package com.kisoft.yuejianli.adpter;
import android.widget.TextView;
import androidx.annotation.Nullable;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.RYJCBean;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.List;

public class XmkbRyjcAdapter extends BaseQuickAdapter<RYJCBean.UserNameListDataBean, BaseViewHolder> {
    public XmkbRyjcAdapter(int layoutResId, @Nullable List<RYJCBean.UserNameListDataBean> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, RYJCBean.UserNameListDataBean item) {
        TextView tvName = helper.itemView.findViewById(R.id.tv_name);
        TextView tvText = helper.itemView.findViewById(R.id.tv_text);
        if (StringUtil.isEmpty(item.getUserName())) {
            tvName.setText("");
        } else {
            tvName.setText(item.getUserName());
        }
        if (StringUtil.isEmpty(item.getPostName())) {
            tvText.setText("");
        } else {
            tvText.setText(item.getPostName());
        }
    }
}

/*
public class XmkbRyjcAdapter extends RecyclerView.Adapter<XmkbRyjcAdapter.InnerHolder>{

    private List<RYJCBean.UserNameListDataBean> mData;
    public XmkbRyjcAdapter(List<RYJCBean.UserNameListDataBean> data) {
        this.mData = data;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public InnerHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = View.inflate(parent.getContext(), R.layout.item_arrival, null);
        return new InnerHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull InnerHolder holder, int position) {
        holder.setData(mData.get(position));
    }

    @Override
    public int getItemCount() {
        if (mData != null) {
            return mData.size();
        }
        return 0;
    }

    public void setNewData(List<RYJCBean.UserNameListDataBean> ryjcList) {
        this.mData = ryjcList;
        notifyDataSetChanged();
    }

    public class InnerHolder extends RecyclerView.ViewHolder{
        TextView tvName;
        TextView tvPosition;
        public InnerHolder(@NonNull View itemView) {
            super(itemView);
            tvName=itemView.findViewById(R.id.tv_name);
            tvPosition=itemView.findViewById(R.id.tv_text);
        }

        public void setData(RYJCBean.UserNameListDataBean bean){
            tvName.setText(bean.getUserName());
            tvPosition.setText(bean.getPostName());
        }
    }
}
*/
