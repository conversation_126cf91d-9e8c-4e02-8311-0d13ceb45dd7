package com.kisoft.yuejianli.adpter;

import android.content.Context;
import android.widget.TextView;

import com.kisoft.yuejianli.adpter.common.CommonBaseAdapter;
import com.kisoft.yuejianli.adpter.common.CommonViewHolder;

import java.util.List;

import com.kisoft.yuejianli.R;

/**
 * 日历适配器
 */

public class SelectDateAdapter extends CommonBaseAdapter {


    public SelectDateAdapter(Context mContext, List mDatas) {
        //子类适配中，必须实现父类的构造方法
        super(mContext, mDatas, R.layout.item_select_date);
    }

    @Override
    public void convert(CommonViewHolder holder, int position, Object o) {
        TextView tv_day=holder.getView(R.id.tv_item_select_date);
        String date= (String) o;
        tv_day.setText(date);
    }
}
