package com.kisoft.yuejianli.adpter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.common.CommonRecyclerAdapter;
import com.kisoft.yuejianli.adpter.common.CommonViewHolder;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.UnitListBean;

import java.io.Serializable;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class UnitChildFXOtherListAdapter extends CommonRecyclerAdapter<Serializable> {

    private Integer type;

    /**
     * 子类构造方法中调用
     *
     * @param mContext
     * @param mDatas
     */
    public UnitChildFXOtherListAdapter(Context mContext, List<Serializable> mDatas, Integer type) {
        super(mContext, mDatas, R.layout.item_dangers_list);
        this.type = type;
    }

    @Override
    public void convert(CommonViewHolder holder, final int position, final Serializable dto) {
        ViewHolder vh = new ViewHolder(holder.getConvertView());

        if (type == Constant.REQUEST_CODE_UNIT_YINBI){
            UnitListBean.ListDataBean.ChildListDataBean.FXChildListDataBean.ChildMapDataBean.YDataBean bean = (UnitListBean.ListDataBean.ChildListDataBean.FXChildListDataBean.ChildMapDataBean.YDataBean) dto;
            vh.tvName.setText(bean.getConcealment());
        }
        if (type == Constant.REQUEST_CODE_UNIT_PANGZHAN){
            UnitListBean.ListDataBean.ChildListDataBean.FXChildListDataBean.ChildMapDataBean.PDataBean bean = (UnitListBean.ListDataBean.ChildListDataBean.FXChildListDataBean.ChildMapDataBean.PDataBean) dto;
            vh.tvName.setText(bean.getAsideItem());
        }
        if (type == Constant.REQUEST_CODE_UNIT_WEIDA){
            UnitListBean.ListDataBean.ChildListDataBean.FXChildListDataBean.ChildMapDataBean.WDataBean bean = (UnitListBean.ListDataBean.ChildListDataBean.FXChildListDataBean.ChildMapDataBean.WDataBean) dto;
            vh.tvName.setText(bean.getDanagerProPosition());
        }
//        if (type == Constant.REQUEST_CODE_UNIT_YINBI){
//            UnitListBean.ListDataBean.ChildListDataBean.FXChildListDataBean.ChildMapDataBean.YDataBean bean = (UnitListBean.ListDataBean.ChildListDataBean.FXChildListDataBean.ChildMapDataBean.YDataBean) dto;
//            vh.tvName.setText(bean.getConcealment());
//        }
        vh.llMain.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (null != onItemClickListener) {
                    onItemClickListener.onClick(dto, position);
                }
            }
        });
    }

    static class ViewHolder {
        @BindView(R.id.tv_name)
        TextView tvName;
        @BindView(R.id.iv_enter)
        ImageView ivEnter;
        @BindView(R.id.tv_select)
        TextView tvSelect;
        @BindView(R.id.ll_main)
        View llMain;

        ViewHolder(View view) {
            ButterKnife.bind(this, view);
            tvName.setVisibility(View.VISIBLE);
            ivEnter.setVisibility(View.GONE);
            tvSelect.setVisibility(View.GONE);
        }
    }

    public static abstract class OnItemClickListener {
        public void onClick(Serializable o, int position) {
        }
    }

    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }
}