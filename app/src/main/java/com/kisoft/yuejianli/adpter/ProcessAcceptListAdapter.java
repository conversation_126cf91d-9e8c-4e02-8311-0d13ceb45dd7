package com.kisoft.yuejianli.adpter;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.ProcessAcceptListBean;

import java.util.List;

public class ProcessAcceptListAdapter extends BaseQuickAdapter<ProcessAcceptListBean, BaseViewHolder> {
    public ProcessAcceptListAdapter(int layoutResId, @Nullable List<ProcessAcceptListBean> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, ProcessAcceptListBean item) {
        helper.setText(R.id.tvName,item.getUnitName()).setText(R.id.tvReceiver,"验收日期："+item.getResultDate())
                .setText(R.id.tvTime,"创建人："+item.getCreateName());
    }
}
