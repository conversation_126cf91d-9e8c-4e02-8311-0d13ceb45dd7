package com.kisoft.yuejianli.adpter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.interf.GlobalListener;
import com.kisoft.yuejianli.entity.UnitListBean;

import java.util.List;

public class UnitChildListAdapter extends BaseQuickAdapter<UnitListBean.ListDataBean.ChildListDataBean, BaseViewHolder> {
    GlobalListener<UnitListBean.ListDataBean.ChildListDataBean> globalListener;

    public UnitChildListAdapter(int layoutResId, @Nullable List<UnitListBean.ListDataBean.ChildListDataBean> data,GlobalListener<UnitListBean.ListDataBean.ChildListDataBean> globalListener) {
        super(layoutResId, data);
        this.globalListener = globalListener;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, UnitListBean.ListDataBean.ChildListDataBean item) {
        TextView tv_title = helper.getView(R.id.tv_name);
        tv_title.setText(item.getName());
        ImageView iv_enter = helper.getView(R.id.iv_enter);
        iv_enter.setVisibility(View.GONE);
        final TextView tv_select = helper.getView(R.id.tv_select);
        tv_select.setVisibility(View.GONE);
        helper.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (globalListener != null) {
                    globalListener.onRootViewClick(view, helper.getAdapterPosition(), item);
                }
            }
        });
    }
}
