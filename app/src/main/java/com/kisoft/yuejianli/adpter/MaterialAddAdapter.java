package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.MaterialInspect;

/**
 * Created by tudou on 2018/5/25.
 */

public class MaterialAddAdapter extends BaseQuickAdapter<MaterialInspect,BaseViewHolder> {


    public MaterialAddAdapter(int layoutResId, @Nullable List<MaterialInspect> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, MaterialInspect item) {
        ImageView ivType = helper.itemView.findViewById(R.id.iv_type);
        TextView tvMaterialName = helper.itemView.findViewById(R.id.tv_material_name);
        TextView tvMaterialRank = helper.itemView.findViewById(R.id.tv_material_rank);
        TextView tvInspector = helper.itemView.findViewById(R.id.tv_inspector);
        TextView tvInspectTime = helper.itemView.findViewById(R.id.tv_inspector_time);


        tvMaterialRank.setText(item.getSerialName());
        // todo 处理检查人
        tvInspector.setText(item.getCreateName());
        tvInspectTime.setText(item.getEnterTimeStr());


    }
}
