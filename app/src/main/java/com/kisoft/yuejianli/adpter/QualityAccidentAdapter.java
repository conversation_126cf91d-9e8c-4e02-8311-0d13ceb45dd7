package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.widget.ImageView;
import android.widget.TextView;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.entity.QualityAccident;

import java.util.List;
import com.kisoft.yuejianli.R;

import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/4/25.
 */

public class QualityAccidentAdapter extends BaseQuickAdapter<QualityAccident,BaseViewHolder> {

    public QualityAccidentAdapter(int layoutResId, @Nullable List<QualityAccident> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, QualityAccident item) {

        ImageView ivLeavel = helper.itemView.findViewById(R.id.iv_leavel);
        TextView tvAccidentTitle = helper.itemView.findViewById(R.id.tv_accident_title);
        TextView tvTime = helper.itemView.findViewById(R.id.tv_time);
        TextView tvAccidentReport = helper.itemView.findViewById(R.id.tv_accident_reporter);
        TextView tvAccidentCharge = helper.itemView.findViewById(R.id.tv_accident_charge);

        if(StringUtil.isEqual(item.getAccidentLevel(),QualityAccident.ACCIDENT_LEVEL1)){
            ivLeavel.setImageResource(R.drawable.ic_accident_leavel_1);
        }else if(StringUtil.isEqual(item.getAccidentLevel(), QualityAccident.ACCIDENT_LEVEL2)){
            ivLeavel.setImageResource(R.drawable.ic_accident_leavel_2);
        }else if(StringUtil.isEqual(item.getAccidentLevel(),QualityAccident.ACCIDENT_LEVEL3)){
            ivLeavel.setImageResource(R.drawable.ic_accident_leavel_3);
        }

        tvAccidentTitle.setText(item.getTitle());
        tvTime.setText(item.getCorrectTimeStr());
//        tvAccidentReport.setText(item.getCreator());
//        tvAccidentCharge.setText(item.getSummarizier());

    }
}
