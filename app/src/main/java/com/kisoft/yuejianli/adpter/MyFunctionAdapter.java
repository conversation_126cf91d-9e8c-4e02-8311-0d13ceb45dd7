package com.kisoft.yuejianli.adpter;

import android.graphics.drawable.Drawable;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.entity.ProjectContent;
import com.kisoft.yuejianli.views.ProjectFragment;

/**
 * Created by tudou on 2018/6/8.
 */

public class MyFunctionAdapter extends BaseQuickAdapter<ProjectContent, BaseViewHolder> {

    public MyFunctionAdapter(int layoutResId, @Nullable List<ProjectContent> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, ProjectContent item) {
        TextView tvContent = helper.itemView.findViewById(R.id.tv_content);
        Drawable contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_function);

        tvContent.setText(item.getName());
        switch (item.getId()) {
            case ProjectFragment.PROJECT_QUALITY:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_function);
                break;
            case ProjectFragment.PROJECT_SAFETY:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_function);
                break;
            case ProjectFragment.PROJECT_PROGRESS:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_function);
                break;
            case ProjectFragment.PROJECT_CONTRACT:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_function);
                break;
            case ProjectFragment.PROJECT_INVESTMENT:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_function);
                break;
            case ProjectFragment.PROJECT_LIBRARY:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_function);
                break;
            case ProjectFragment.PROJECT_TREE:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_function);
                break;
            case ProjectFragment.PROJECT_RECORD:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_function);
                break;
        }
        contentIc.setBounds(0, 0, contentIc.getMinimumWidth(), contentIc.getMinimumHeight());
        tvContent.setCompoundDrawables(null, contentIc, null, null);
    }
}
