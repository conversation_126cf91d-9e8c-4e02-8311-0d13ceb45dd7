package com.kisoft.yuejianli.adpter;

import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseMultiItemQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.facebook.drawee.view.SimpleDraweeView;
import com.kisoft.yuejianli.entity.Communication;
import com.kisoft.yuejianli.views.PhoneDirectoryActivity;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.R;

import com.kisoft.yuejianli.entity.Department;

/**
 * Created by tudou on 2018/3/13.
 */

public class PhoneAdapter extends BaseMultiItemQuickAdapter<MultiItemEntity, BaseViewHolder> {


    private List<Integer> expendList = new ArrayList<>();
    private OnClickCommuniationListener listener;

    /**
     * Same as <PERSON>Adapter#QuickAdapter(Context,int) but with
     * some initialization data.
     *
     * @param data A new list is created out of this one to avoid mutable list
     */
    public PhoneAdapter(List<MultiItemEntity> data) {
        super(data);
        addItemType(PhoneDirectoryActivity.PHONE_DEPARTMENT, R.layout.item_phone_department);
        addItemType(PhoneDirectoryActivity.PHONE_USER, R.layout.item_phone_user);



    }

    @Override
    protected void convert(BaseViewHolder helper, final MultiItemEntity item) {
        final int position = helper.getAdapterPosition();
        switch (helper.getItemViewType()) {
            case PhoneDirectoryActivity.PHONE_DEPARTMENT:
                Log.i("000", "----------------------0");
                final Department department = (Department) item;
                final ImageView ivShow = (ImageView) helper.itemView.findViewById(R.id.iv_show);
                TextView tvDepartment = (TextView) helper.itemView.findViewById(R.id.work_name);
                TextView tvSize = (TextView) helper.itemView.findViewById(R.id.worker_size);
                tvDepartment.setText(department.getName());
                tvSize.setText(department.getSize()+"人");
                 helper.itemView.setOnClickListener(new View.OnClickListener() {
                     @Override
                     public void onClick(View view) {
                        if(clickDepartment(position)){
                            //  如果展开项包含
                            expand(position);
                            ivShow.setImageResource(R.drawable.ic_up);
                        }else {
                            //  不包含
                            collapse(position);
                            ivShow.setImageResource(R.drawable.ic_down);
                        }
                     }
                 });
                Log.i("000", "------------00000----------1");
                break;

            case PhoneDirectoryActivity.PHONE_USER:

                Log.i("000", "----------------------1");
                final Communication communication = (Communication) item;
                TextView tvUserName = (TextView) helper.itemView.findViewById(R.id.tv_user_name);
                TextView tvUserWork = (TextView) helper.itemView.findViewById(R.id.tv_user_work);
                SimpleDraweeView avatar = helper.itemView.findViewById(R.id.user_avatar);
                tvUserName.setText(communication.getUserName());
                tvUserWork.setText(communication.getConstUnit());
                helper.itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        if(listener != null){
                            listener.onClickCommuniation(communication);
                        }
                    }
                });
                Log.i("000", "-------------11111---------1");
                break;
            default:
                break;
        }
    }


    private boolean clickDepartment(int position){
        boolean isExpend = false;
        Integer pos = new Integer(position);
        if(this.expendList.contains(pos)){   //如果包含，删除
            this.expendList.remove(pos);
        }else {
            this.expendList.add(pos);
        }

        if(this.expendList.contains(pos)){
            isExpend = true;
        }else {
            isExpend = false;
        }
        notifyDataSetChanged();
        return  isExpend;
    }

    public interface OnClickCommuniationListener{
        void onClickCommuniation(Communication communication);
    }

    public void setClickCommuniationListener(OnClickCommuniationListener listener){
        this.listener = listener;
    }
}
