package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.Html;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.common.SimpleTextWatcher;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ExamQuestionInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;
import com.kisoft.yuejianli.views.EnterActivity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 考试详情
 */
public class ExamDetailAdapter extends BaseQuickAdapter<ExamQuestionInfo, YBaseViewHolder> {

    private List<ExamQuestionInfo.AnswerListBean> judgeAnswerList = new ArrayList<>();
    private boolean showAnalysisAll=false;

    public ExamDetailAdapter() {
        super(R.layout.item_exam_detail);
        judgeAnswerList.add(new ExamQuestionInfo.AnswerListBean("错"));
        judgeAnswerList.add(new ExamQuestionInfo.AnswerListBean("对"));
    }

    @Override
    protected void convert(YBaseViewHolder helper, final ExamQuestionInfo item) {
        RecyclerView recycleView=helper.getView(R.id.recycleView);
        EditText etContent = helper.getView(R.id.etContent);
        if(ExamQuestionInfo.Q_TYPE_GAP_FILLING.equals(item.getQustType())||ExamQuestionInfo.Q_TYPE_SHORT_ANSWER.equals(item.getQustType())){
            recycleView.setVisibility(View.GONE);
            etContent.clearFocus();
            if(etContent.getTag() instanceof TextWatcher){
                etContent.removeTextChangedListener((TextWatcher) etContent.getTag());
            }
            etContent.setText(StringUtil.isEmpty(item.getTaskAnswer())?"":item.getTaskAnswer());
            //文本改变监听
            final TextWatcher getPosWatcher=new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable editable) {
                    if (TextUtils.isEmpty(editable)) {
                        item.setTaskAnswer(null);
                        item.setHasAnswer(false);
                        item.setIsRight(ExamQuestionInfo.ANSWER_PROGRESS_NONE);
                    } else {
                        item.setTaskAnswer(String.valueOf(editable));
                        item.setHasAnswer(true);
                        item.setIsRight(ExamQuestionInfo.ANSWER_PROGRESS_COMPLETE);
                    }

                }
            };
            etContent.addTextChangedListener(getPosWatcher);
            etContent.setTag(getPosWatcher);
            etContent.setVisibility(View.VISIBLE);
            if(showAnalysisAll||item.isShowAnalysis()){
                etContent.setEnabled(false);
            }else {
                etContent.setEnabled(true);
            }
        }else {
            etContent.setVisibility(View.GONE);
            recycleView.setLayoutManager(new LinearLayoutManager(mContext,LinearLayoutManager.VERTICAL,false));
            if(ExamQuestionInfo.Q_TYPE_JUDGE.equals(item.getQustType())&&item.getAnswerList().size()==0){
                item.setAnswerList(judgeAnswerList);
            }
            final ExamQuestionAdapter questionAdapter=new ExamQuestionAdapter(item.getAnswerList(),item.getAnswer(),item.getTaskAnswer());
            recycleView.setAdapter(questionAdapter);
            recycleView.setNestedScrollingEnabled(false);
            questionAdapter.setOnItemClickListener(new OnItemClickListener() {
                @Override
                public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                    if(showAnalysisAll||item.isShowAnalysis()){
                        return;
                    }
                    Object object=adapter.getData().get(position);
                    if(object instanceof ExamQuestionInfo.AnswerListBean){
                        ExamQuestionInfo.AnswerListBean answer= (ExamQuestionInfo.AnswerListBean) object;
                        if(ExamQuestionInfo.Q_TYPE_MORE_SELECT.equals(item.getQustType())){
                            answer.setSelect(!answer.isSelect());
                            questionAdapter.notifyItemChanged(position);
                        }else {
                            questionAdapter.clearSelect();
                            answer.setSelect(!answer.isSelect());
                            questionAdapter.notifyDataSetChanged();
                        }
                        item.setHasAnswer(true);
                        item.setIsRight(ExamQuestionInfo.ANSWER_PROGRESS_COMPLETE);
                        if(ExamQuestionInfo.Q_TYPE_MORE_SELECT.equals(item.getQustType())){
                            item.setTaskAnswer(questionAdapter.getSelectAnswer()+"@");
                        }else {
                            item.setTaskAnswer(questionAdapter.getSelectAnswer());
                        }
                    }
                }
            });
            questionAdapter.setShowAnalysisAll(showAnalysisAll||item.isShowAnalysis());
            recycleView.setVisibility(View.VISIBLE);
        }
        helper.setText(R.id.tvTitle, Html.fromHtml((helper.getAdapterPosition()+1)
                +"."+Constant.HTML_REMIND_LEFT+"【"+ StringUtil.getQuestionTypeName(item.getQustType()) +"】"
                +Constant.HTML_REMIND_RIGHT+item.getQustName())).addOnClickListener(R.id.tvConfirm)
                .setText(R.id.tvAnalysis,item.getAnswerAnalsis());
        StringBuilder answer=new StringBuilder();
        if(!StringUtil.isEmpty(item.getAnswer())){
            String[] split = item.getAnswer().split("@");
            for (String s : split) {
                answer.append(s).append(" ");
            }
        }
        helper.setText(R.id.tvRightAnswer,"正确答案："+StringUtil.trimString(answer.toString()," "));
        if(showAnalysisAll||item.isShowAnalysis()){
            helper.setVisible(R.id.llRightAnswer,true).setVisible(R.id.tvConfirm,false);
        }else {
            helper.setVisible(R.id.llRightAnswer,false).setVisible(R.id.tvConfirm,true);
        }
    }

    public boolean isShowAnalysisAll() {
        return showAnalysisAll;
    }

    public void setShowAnalysisAll(boolean showAnalysisAll){
        this.showAnalysisAll=showAnalysisAll;
        notifyDataSetChanged();
        if(showAnalysisAll){
            for (ExamQuestionInfo mDatum : mData) {
                if(StringUtil.isEmpty(mDatum.getTaskAnswer())){
                    mDatum.setIsRight(ExamQuestionInfo.ANSWER_PROGRESS_NONE);
                }else if(ExamQuestionInfo.Q_TYPE_SHORT_ANSWER.equals(mDatum.getQustType())){
                    mDatum.setIsRight(ExamQuestionInfo.ANSWER_PROGRESS_COMPLETE);
                }else if(!mDatum.getTaskAnswer().equals(mDatum.getAnswer())){
                    mDatum.setIsRight(ExamQuestionInfo.ANSWER_PROGRESS_ERROR);
                }
            }
        }
    }

    //设置题目是否答错
    public void setAnswerState(ExamQuestionInfo mDatum){
        if(StringUtil.isEmpty(mDatum.getTaskAnswer())){
            mDatum.setIsRight(ExamQuestionInfo.ANSWER_PROGRESS_NONE);
        }else if(ExamQuestionInfo.Q_TYPE_SHORT_ANSWER.equals(mDatum.getQustType())){
            mDatum.setIsRight(ExamQuestionInfo.ANSWER_PROGRESS_COMPLETE);
        }else if(!mDatum.getTaskAnswer().trim().equals(mDatum.getAnswer().trim())){
            mDatum.setIsRight(ExamQuestionInfo.ANSWER_PROGRESS_ERROR);

            examWrongbookAction(mDatum);
        }
    }

    // 统计错题
    public void examWrongbookAction(ExamQuestionInfo mDatum){
     UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("eqId", mDatum.getEqbId());
        parameters.put("qustName", mDatum.getQustName());
        parameters.put("diffDegree", mDatum.getDiffDegree());
        parameters.put("qustType", mDatum.getQustType());
        parameters.put("answer", mDatum.getAnswer());
        parameters.put("points", mDatum.getPoints());
        parameters.put("answerAnalsis", mDatum.getAnswerAnalsis());
        parameters.put("eqbId", mDatum.getEqbId());
        parameters.put("eqbName", mDatum.getEqbName());
        parameters.put("userId", userInfo.getId());
        parameters.put("userName",userInfo.getName());
        parameters.put("epId","");
        parameters.put("etId","");
//        [param setObject:self.model.eqId forKey:@"eqId"];
//        [param setObject:self.model.qustName forKey:@"qustName"];
//        [param setObject:self.model.diffDegree forKey:@"diffDegree"];
//        [param setObject:self.model.qustType forKey:@"qustType"];
//        [param setObject:self.model.answer forKey:@"answer"];
//        [param setObject:self.model.points forKey:@"points"];
//        [param setObject:self.model.answerAnalsis forKey:@"answerAnalsis"];
//        [param setObject:self.model.eqbId forKey:@"eqbId"];
//        [param setObject:self.model.eqbName forKey:@"eqbName"];
//        [param setObject:m_model.userId forKey:@"userId"];
//        [param setObject:m_model.userName forKey:@"userName"];
//        [param setObject:@"" forKey:@"epId"];
//        [param setObject:@"" forKey:@"etId"];



        Api.getGbkApiserver().examWrongbookAction("save", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {

                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }




    //获取未答题数目
    public int getNoAnswerNum(){
        int i=0;
        for (ExamQuestionInfo mDatum : mData) {
            if(StringUtil.isEmpty(mDatum.getTaskAnswer())){
                i++;
            }
        }
        return i;
    }

    public static class ExamQuestionAdapter extends BaseQuickAdapter<ExamQuestionInfo.AnswerListBean,YBaseViewHolder>{

        private List<String> answerLetters,taskAnswerLetters;
        private boolean showAnalysisAll=false;

        public ExamQuestionAdapter(@Nullable List<ExamQuestionInfo.AnswerListBean> data, String answer,String taskAnswer) {
            super(R.layout.item_exam_detail_question, data);
            if(StringUtil.isEmpty(answer)){
                answer="";
            }
            if(StringUtil.isEmpty(taskAnswer)){
                taskAnswer="";
            }
            answerLetters= Arrays.asList(answer.split("@"));
            taskAnswerLetters= Arrays.asList(taskAnswer.split("@"));
        }

        @Override
        protected void convert(YBaseViewHolder helper, ExamQuestionInfo.AnswerListBean item) {
            helper.setSelected(R.id.tvSelect,item.isSelect()).setText(R.id.tvContent,item.getAnswerContent())
                    .setText(R.id.tvSelect,item.getAnswerOption());
            if(showAnalysisAll){
                if(taskAnswerLetters.contains(item.getAnswerOption())){
                    if(answerLetters.contains(item.getAnswerOption())){
                        helper.setImageResource(R.id.ivErrorSelect,R.drawable.icon_right_count);
                    }else {
                        helper.setImageResource(R.id.ivErrorSelect,R.drawable.icon_wrong_count);
                    }
                    helper.setVisible(R.id.tvSelect,false).setVisible(R.id.ivErrorSelect,true);
                }else {
                    helper.setVisible(R.id.tvSelect,true).setVisible(R.id.ivErrorSelect,false);
                }
            }else {
                helper.setVisible(R.id.tvSelect,true).setVisible(R.id.ivErrorSelect,false);
            }
//            LogUtil.e("ExamQuestionAdapter "+answerLetters+" showAnalysisAll="+showAnalysisAll);
        }

        public void setShowAnalysisAll(boolean showAnalysisAll){
            this.showAnalysisAll=showAnalysisAll;
            notifyDataSetChanged();
        }

        public String getSelectAnswer(){
            StringBuilder stringBuilder=new StringBuilder();
            for (int i = 0; i < mData.size(); i++) {
                ExamQuestionInfo.AnswerListBean mDatum=mData.get(i);
                if(mDatum.isSelect()){
                    stringBuilder.append(mDatum.getAnswerOption()).append("@");
                }
            }
            return StringUtil.trimString(stringBuilder.toString(),"@");
        }

        private String getLetter(int pos){
            String letter="";
            switch (pos){
                case 0:
                    letter="A";
                    break;
                case 1:
                    letter="B";
                    break;
                case 2:
                    letter="C";
                    break;
                case 3:
                    letter="D";
                    break;
                case 4:
                    letter="E";
                    break;
                case 5:
                    letter="F";
                    break;
                case 6:
                    letter="G";
                    break;
                case 7:
                    letter="H";
                    break;
            }
            return letter;
        }

        void clearSelect() {
            for (ExamQuestionInfo.AnswerListBean mDatum : mData) {
                mDatum.setSelect(false);
            }
        }
    }

}
