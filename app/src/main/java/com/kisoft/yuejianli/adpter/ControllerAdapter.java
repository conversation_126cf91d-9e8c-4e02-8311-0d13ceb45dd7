package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.QualityInspection;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.List;

/**
 * Created by tudou on 2018/5/25.
 */

public class ControllerAdapter extends BaseQuickAdapter<QualityInspection, BaseViewHolder> {

    private int pageType; // 1质量    2安全

    public void setPageType(int pageType) {
        this.pageType = pageType;
        notifyDataSetChanged();
    }

    public ControllerAdapter(int layoutResId, @Nullable List<QualityInspection> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, QualityInspection item) {
        TextView tvPerson = helper.itemView.findViewById(R.id.tv_inspector);
        TextView tv_state = helper.itemView.findViewById(R.id.tv_state);
        TextView tv_status = helper.itemView.findViewById(R.id.tv_status);
        TextView tvTime = helper.itemView.findViewById(R.id.tv_inspector_time);

        switch (pageType) {
            case 1:
                switch (item.getQiStatus()) {
                    case "1":
                        tv_state.setText("质量情况:预警");
                        break;
                    case "2":
                        tv_state.setText("质量情况:异常");
                        break;
                    default:
                        tv_state.setText("质量情况:正常");
                        break;
                }
                break;
            case 2:
                switch (item.getSafeStatus()) {
                    case "1":
                        tv_state.setText("安全情况:预警");
                        break;
                    case "2":
                        tv_state.setText("安全情况:异常");
                        break;
                    default:
                        tv_state.setText("安全情况:正常");
                        break;
                }
                break;
        }
        tv_status.setText(item.isStatus() ? "已上报" : "未上报");

        tvPerson.setText(StringUtil.isEmpty(item.getCreateName()) ? "" : item.getCreateName());
        tvTime.setText(DateUtil.getYmdByTime(item.getCreateTime()));
    }
}
