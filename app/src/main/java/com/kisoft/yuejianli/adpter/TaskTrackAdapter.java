package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.model.TaskRecordDto;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.Date;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import com.kisoft.yuejianli.R;

public class TaskTrackAdapter extends BaseQuickAdapter<TaskRecordDto, TaskTrackAdapter.ViewHolder> {

    public TaskTrackAdapter(@Nullable List<TaskRecordDto> mDatas) {
        super(R.layout.item_task_track, mDatas);
    }

    @Override
    protected void convert(ViewHolder holder, TaskRecordDto dto) {
        if (dto != null) {
            holder.tvUsername.setText(StringUtil.isEmpty(dto.getUsername()) ? "跟踪人：" : "跟踪人：" + dto.getUsername());

            long time = dto.getCreatedate().getTime();
            holder.tvCreateDate.setText("发表日期：" + DateUtil.YMD.format(new Date(time)));
            holder.tvContent.setText(StringUtil.isEmpty(dto.getReport()) ? "工作内容：" : "工作内容：" + dto.getReport());
            holder.tvReportType.setText(StringUtil.isEmpty(dto.getReporttype()) ? "任务关系：" : "任务关系：" + dto.getReporttype());
            holder.tvTtpercent.setText("完成百分比：" + dto.getTtpercent() + "%");
            holder.tvProgress.setText(StringUtil.isEmpty(dto.getProgress()) ? "完成状态：" : "完成状态：" + dto.getProgress());
        }
    }

    class ViewHolder extends BaseViewHolder {
        @BindView(R.id.tv_username)
        TextView tvUsername;
        @BindView(R.id.tv_create_date)
        TextView tvCreateDate;
        @BindView(R.id.tv_content)
        TextView tvContent;
        @BindView(R.id.tv_report_type)
        TextView tvReportType;
        @BindView(R.id.tv_ttpercent)
        TextView tvTtpercent;
        @BindView(R.id.tv_progress)
        TextView tvProgress;

        public ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
