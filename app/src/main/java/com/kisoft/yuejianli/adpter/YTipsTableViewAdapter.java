package com.kisoft.yuejianli.adpter;

import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.JDData;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class YTipsTableViewAdapter extends BaseQuickAdapter<JDData.JDBean, YTipsTableViewAdapter.ViewHolder> {

    public YTipsTableViewAdapter(@Nullable List<JDData.JDBean> data) {
        super(R.layout.item_enclosure_list, data);
    }

    @Override
    protected void convert(@NonNull YTipsTableViewAdapter.ViewHolder helper, JDData.JDBean item) {
        if (item != null) {
            helper.tvFileName.setText(item.getName());
        }
    }

    public class ViewHolder extends BaseViewHolder {
        @BindView(R.id.tv_file_name)
        TextView tvFileName;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
