package com.kisoft.yuejianli.adpter;

import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.JDData;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class YTipsTableViewTHJDAdapter extends BaseQuickAdapter<JDData, YTipsTableViewTHJDAdapter.ViewHolder> {

    public YTipsTableViewTHJDAdapter(@Nullable List<JDData> data) {
        super(R.layout.item_enclosure_list, data);
    }

    @Override
    protected void convert(@NonNull YTipsTableViewTHJDAdapter.ViewHolder helper, JDData item) {
        if (item != null) {
            helper.tvFileName.setText(item.getName());
        }
    }

    public class ViewHolder extends BaseViewHolder {
        @BindView(R.id.tv_file_name)
        TextView tvFileName;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
