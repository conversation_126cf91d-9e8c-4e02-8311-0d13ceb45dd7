package com.kisoft.yuejianli.adpter;

import android.graphics.drawable.Drawable;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.entity.ContorlContent;
import com.kisoft.yuejianli.views.ControlFragment;

import java.util.List;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/3/12.
 */
public class ContorlContentAdapter extends BaseQuickAdapter<ContorlContent, BaseViewHolder> {

    public ContorlContentAdapter(int layoutResId, @Nullable List<ContorlContent> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, ContorlContent item) {
        TextView tvContent = helper.itemView.findViewById(R.id.tv_content);
        Drawable contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_06);

        tvContent.setText(item.getName());
        switch (item.getId()) {
            case ControlFragment.CONTORL_1:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_01);
                break;
            case ControlFragment.CONTORL_2:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_02);
                break;
            case ControlFragment.CONTORL_3:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_03);
                break;
            case ControlFragment.CONTORL_4:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_04);
                break;
            case ControlFragment.CONTORL_5:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_05);
                break;
            case ControlFragment.CONTORL_6:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_06);
                break;
            case ControlFragment.CONTORL_7:
            case ControlFragment.CONTORL_9:
            case ControlFragment.CONTORL_10:
            case ControlFragment.CONTORL_11:
            case ControlFragment.CONTORL_12:
            case ControlFragment.CONTORL_13:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_07);
                break;
            case ControlFragment.CONTORL_8:
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_08);
                break;
        }
        contentIc.setBounds(0, 0, contentIc.getMinimumWidth(), contentIc.getMinimumHeight());
        tvContent.setCompoundDrawables(null, contentIc, null, null);
    }

}
