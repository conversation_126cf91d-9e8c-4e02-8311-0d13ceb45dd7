package com.kisoft.yuejianli.adpter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.interf.GlobalListener;
import com.kisoft.yuejianli.entity.ApplyType;

import java.util.List;

/**
 * Created by tudou on 2018/6/7.
 */
public class DangersListAdapter extends BaseQuickAdapter<ApplyType, BaseViewHolder> {

    GlobalListener<ApplyType> globalListener;
    int selectPos = -1;
    TextView tvSelect;

    public DangersListAdapter(int layoutResId, @Nullable List<ApplyType> data, GlobalListener<ApplyType> globalListener) {
        super(layoutResId, data);
        this.globalListener = globalListener;
    }

    @Override
    protected void convert(final BaseViewHolder helper, final ApplyType item) {
        helper.setText(R.id.tv_name, item.getItemname());
        ImageView iv_enter = helper.getView(R.id.iv_enter);
        final TextView tv_select = helper.getView(R.id.tv_select);
        iv_enter.setVisibility("0".equals(item.getIsHave()) ? View.VISIBLE : View.GONE);

        helper.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (globalListener != null) {
                    if (!"0".equals(item.getIsHave())) {
                        if (selectPos != -1) {
                            tvSelect.setVisibility(View.GONE);
                        }
                        if (selectPos == helper.getAdapterPosition()) {
                            selectPos = -1;
                            tvSelect = null;
                        }else {
                            selectPos = helper.getAdapterPosition();
                            tvSelect = tv_select;
                            tvSelect.setVisibility(View.VISIBLE);
                        }
                    }
                    globalListener.onRootViewClick(v, helper.getAdapterPosition(), item);
                }
            }
        });
    }
}
