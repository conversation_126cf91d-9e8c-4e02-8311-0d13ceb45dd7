package com.kisoft.yuejianli.adpter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.FilingCertificate;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

/**
 * 公司证件选择
 */
public class FilingCertificateSelectedAdapter extends BaseQuickAdapter<FilingCertificate, YBaseViewHolder> {


    public FilingCertificateSelectedAdapter() {
        super(R.layout.item_data_selected);
    }

    @Override
    protected void convert(YBaseViewHolder helper, FilingCertificate item) {
        helper.setText(R.id.tvName,item.getFcName()).addOnClickListener(R.id.ivDelete);
    }

}
