package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.GatteEntity;
import com.kisoft.yuejianli.entity.GatteEntity;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.text.ParseException;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Created by tudou on 2018/4/4.
 */

public class GatteAdapter extends BaseQuickAdapter<GatteEntity, BaseViewHolder> {

    public GatteAdapter(@Nullable List<GatteEntity> data) {
        super(R.layout.item_gatte_content, data);
    }

    @Override
    protected void convert(BaseViewHolder holder, GatteEntity item) {

    }

}
