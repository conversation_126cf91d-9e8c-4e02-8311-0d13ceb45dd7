package com.kisoft.yuejianli.adpter.common;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import java.util.List;

/**
 * Created by liangqs on 2017/12/26.
 */
public abstract class CommonRecyclerAdapter<T> extends RecyclerView.Adapter<CommonViewHolder>{
    //为了让子类访问，于是将属性设置为protected
    //上下文对象
    protected Context mContext;

    //数据源
    protected List<T> mDatas;

    //布局ID
    private int layoutId;

    /**
     * 子类构造方法中调用
     * @param mContext
     * @param mDatas
     * @param layoutId
     */
    public CommonRecyclerAdapter(Context mContext, List<T> mDatas, int layoutId) {
        this.mContext = mContext;
        this.mDatas = mDatas;
        this.layoutId = layoutId;
    }


    @Override
    public CommonViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new CommonViewHolder(LayoutInflater.from(mContext).inflate(layoutId, parent, false));
    }

    @Override
    public void onBindViewHolder(CommonViewHolder holder, int position) {
        convert(holder,position,mDatas.get(position));
    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    //将convert方法公布出去
    public abstract void convert(CommonViewHolder holder, int position, T t);

}
