package com.kisoft.yuejianli.adpter;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.widget.EditText;

import androidx.annotation.NonNull;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.common.SimpleTextWatcher;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

import java.util.Map;

public class CheckOutRecordOneAdapter extends BaseQuickAdapter<Map, YBaseViewHolder> {

    private boolean isOnlyShow=true;

    public CheckOutRecordOneAdapter(boolean isOnlyShow) {
        super(R.layout.item_checkout_record_one);
        this.isOnlyShow=isOnlyShow;
    }

    @Override
    protected void convert(@NonNull YBaseViewHolder helper, Map map) {
        EditText kfztdwCheck = helper.getView(R.id.kfztdwCheck);
        EditText jldwCheck = helper.getView(R.id.jldwCheck);
        if(isOnlyShow){
            kfztdwCheck.setFocusable(false);
            jldwCheck.setFocusable(false);

//            etGetPos.setOnTouchListener(new View.OnTouchListener() {
//                @Override
//                public boolean onTouch(View view, MotionEvent motionEvent) {
//                    //触摸的是EditText而且当前EditText能够滚动则将事件交给EditText处理。否则将事件交由其父类处理
//                    if (view.getId() == R.id.etGetPos) {
//                        view.getParent().requestDisallowInterceptTouchEvent(true);
//                        if (motionEvent.getAction() == MotionEvent.ACTION_OUTSIDE) {
//                            view.getParent().requestDisallowInterceptTouchEvent(false);
//                        }
//                    }
//                    return false;
//                }
//            });
        }else {

            helper.addOnClickListener(R.id.snText);

            //清除焦点
            kfztdwCheck.clearFocus();
            jldwCheck.clearFocus();

            //先清除之前的文本改变监听
            if(kfztdwCheck.getTag() instanceof TextWatcher){
                kfztdwCheck.removeTextChangedListener((TextWatcher) kfztdwCheck.getTag());
            }
            if(jldwCheck.getTag() instanceof TextWatcher){
                jldwCheck.removeTextChangedListener((TextWatcher) jldwCheck.getTag());
            }
        }

        //设置数据
        jldwCheck.setText(StringUtil.isEmpty(map.get("snText").toString())?"":map.get("snText").toString());
        jldwCheck.setText(StringUtil.isEmpty(map.get("checksAndCheckmethod").toString())?"":map.get("checksAndCheckmethod").toString());
        kfztdwCheck.setText(StringUtil.isEmpty(map.get("kfztdwCheck").toString())?"":map.get("kfztdwCheck").toString());
        jldwCheck.setText(StringUtil.isEmpty(map.get("jldwCheck").toString())?"":map.get("jldwCheck").toString());

        helper.setText(R.id.snText,map.get("snText").toString())
                .setText(R.id.checksAndCheckmethod,map.get("checksAndCheckmethod").toString());

        if(!isOnlyShow){

            //文本改变监听
            final TextWatcher getKfztdwCheck=new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable editable) {
                    if (TextUtils.isEmpty(editable)) {
//                        item.setSampleAction(null);
                        map.put("kfztdwCheck","");
                    } else {
//                        item.setSampleAction(String.valueOf(editable));
                        map.put("kfztdwCheck",String.valueOf(editable));
                    }
                }
            };

            final TextWatcher getJldwCheck=new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable editable) {
                    if (TextUtils.isEmpty(editable)) {
//                        item.setSampleNum(null);
                        map.put("jldwCheck","");
                    } else {
//                        item.setSampleNum(String.valueOf(editable));
                        map.put("jldwCheck",String.valueOf(editable));
                    }
                }
            };

            //监听设置到不同的EditText上
            kfztdwCheck.addTextChangedListener(getKfztdwCheck);
            kfztdwCheck.setTag(getKfztdwCheck);

            jldwCheck.addTextChangedListener(getJldwCheck);
            jldwCheck.setTag(getJldwCheck);

        }
    }
}
