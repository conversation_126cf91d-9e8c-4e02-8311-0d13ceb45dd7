package com.kisoft.yuejianli.adpter;

import androidx.annotation.NonNull;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.WitnessSamplesInspectInfo;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

import java.util.Map;

public class CheckoutRecordAdapter extends BaseQuickAdapter<Map,YBaseViewHolder> {


    public CheckoutRecordAdapter() {
        super(R.layout.item_material_add);
    }
//
//    @Override
//    protected void convert(YBaseViewHolder helper, WitnessSamplesInspectInfo item) {
//        helper.setImageResource(R.id.iv_type,R.drawable.ic_sup_log).setText(R.id.tv_material_name,"见证取样送检")
//                .setText(R.id.tv_material_rank,"").setText(R.id.tv_person_key,"送检人：")
//                .setText(R.id.tv_inspector,item.getCreateName()).setText(R.id.tv_time_key,"日期：")
//                .setText(R.id.tv_inspector_time, item.getCreateTime());
//    }

    @Override
    protected void convert(@NonNull YBaseViewHolder helper, Map map) {
        helper.setImageResource(R.id.iv_type,R.drawable.ic_sup_log).setText(R.id.tv_material_name,"检验批质量验收记录")
                .setText(R.id.tv_material_rank,"").setText(R.id.tv_person_key,"提交人：")
                .setText(R.id.tv_inspector,map.get("createName").toString()).setText(R.id.tv_time_key,"日期：")
                .setText(R.id.tv_inspector_time, map.get("createTime").toString());
    }
}
