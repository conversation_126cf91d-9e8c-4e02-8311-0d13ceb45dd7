package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import com.kisoft.yuejianli.R;

import com.kisoft.yuejianli.entity.SupervisionLog;
import com.kisoft.yuejianli.utils.DateUtil;

/**
 * Created by tudou on 2018/5/25.
 */

public class SupervisionLogAdapter extends BaseQuickAdapter<SupervisionLog,BaseViewHolder> {


    public SupervisionLogAdapter(int layoutResId, @Nullable List<SupervisionLog> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, SupervisionLog item) {

        ImageView ivType = helper.itemView.findViewById(R.id.iv_type);
        TextView tvSuplogName = helper.itemView.findViewById(R.id.tv_material_name);
        TextView tv_warn_weather = helper.itemView.findViewById(R.id.tv_warn_weather);
        TextView tvSuplogStatus = helper.itemView.findViewById(R.id.tv_material_rank);

        TextView tvPersonkey = helper.itemView.findViewById(R.id.tv_person_key);
        TextView tvPerson = helper.itemView.findViewById(R.id.tv_inspector);
        TextView tvTimeKey = helper.itemView.findViewById(R.id.tv_time_key);
        TextView tvTime = helper.itemView.findViewById(R.id.tv_inspector_time);

        tv_warn_weather.setVisibility(View.VISIBLE);
        tv_warn_weather.setText("天气预警："+item.getWeatherWarnLevel());
        ivType.setImageResource(R.drawable.ic_sup_log);
        tvSuplogName.setText("监理日志");
        tvSuplogStatus.setText("");
        tvPersonkey.setText("监理员：");
        tvTimeKey.setText("日期：");
        tvPerson.setText(item.getCreateName());
        tvTime.setText(DateUtil.getYmdByTime(item.getCreateTime()));



    }
}
