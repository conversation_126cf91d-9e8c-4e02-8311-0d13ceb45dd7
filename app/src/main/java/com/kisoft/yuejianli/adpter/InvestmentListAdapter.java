package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.entity.ContractInfo;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/7.
 */
public class InvestmentListAdapter extends BaseQuickAdapter<ContractInfo, InvestmentListAdapter.ViewHolder> {

    public InvestmentListAdapter(int layoutResId, @Nullable List<ContractInfo> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(ViewHolder viewHolder, ContractInfo item) {
        if (item != null) {
            viewHolder.tvNo.setText(item.getContractUnit());

            // 累计付款
            Float totalPay = StringUtil.isEmpty(item.getConPaySumPrices()) ? 0f : Float.parseFloat(item.getConPaySumPrices());

            // 合同总价 == 合同价+累计变更
            Float contractTotalPrice = (StringUtil.isEmpty(item.getContractPrice()) ? 0f : Float.parseFloat(item.getContractPrice())) +
                    (StringUtil.isEmpty(item.getChangeSumPrices()) ? 0f : Float.parseFloat(item.getChangeSumPrices()));

            // 百分比 = 累计付款 / 合同总价
            float percentage = contractTotalPrice == 0 ? 0 : (totalPay / contractTotalPrice) * 100;
            viewHolder.tvPercentage.setText((percentage == 0 ? "0" : String.format("%.2f", percentage)) + "%");
            viewHolder.tvContractPrice.setText("合同价(元):" + item.getContractPrice());
            viewHolder.tvType.setText("合同类型:" + item.getContractGenre());
            viewHolder.tvChangeTotalPrice.setText("累计变更价(元):" + item.getChangeSumPrices());
            viewHolder.tvPayTotal.setText("累计付款(元):" + item.getConPaySumPrices());
        }
    }

    public class ViewHolder extends BaseViewHolder {
        @BindView(R.id.tv_no)
        TextView tvNo;
        @BindView(R.id.tv_percentage)
        TextView tvPercentage;
        @BindView(R.id.tv_contract_price)
        TextView tvContractPrice;
        @BindView(R.id.tv_type)
        TextView tvType;
        @BindView(R.id.tv_change_total_price)
        TextView tvChangeTotalPrice;
        @BindView(R.id.tv_pay_total)
        TextView tvPayTotal;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
