package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.entity.ProjectInfo;

import java.util.List;

import butterknife.BindView;
import butterknife.BindViews;
import butterknife.ButterKnife;
import com.kisoft.yuejianli.R;

/**
 * Created by tudo<PERSON> on 2018/3/13.
 */
public class ProjectListAdapter extends BaseQuickAdapter<ProjectInfo, ProjectListAdapter.ViewHolder> {

    public ProjectListAdapter(int layoutResId, @Nullable List<ProjectInfo> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(ProjectListAdapter.ViewHolder viewHolder, ProjectInfo item) {
        if (item != null) {
            viewHolder.tvProjectName.setText(item.getProjectName());
            viewHolder.tvManager.setText("总　　监："+item.getPjDirName());
            viewHolder.tvManager1.setText("主管领导："+item.getChargeDirName());
            viewHolder.tvEngineerManager.setText("主管工程师："+item.getChargeEngName());
            String state = "";
            switch (item.getState()) {
                case 0:
                    state = "准备";
                    break;
                case 1:
                    state = "前期";
                    break;
                case 2:
                    state = "中期";
                    break;
                case 3:
                    state = "后期";
                    break;
                case 4:
                    state = "收尾";
                    break;
            }
            viewHolder.tvStatus.setText("工程状态："+state);
        }
    }

    public class ViewHolder extends BaseViewHolder {
        @BindView(R.id.tv_project_name)
        TextView tvProjectName;
        @BindView(R.id.tv_manager)
        TextView tvManager;
        @BindView(R.id.tv_manager1)
        TextView tvManager1;
        @BindView(R.id.tv_engineer_manager)
        TextView tvEngineerManager;
        @BindView(R.id.tv_status)
        TextView tvStatus;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
