package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.entity.VisitPlan;

import java.util.List;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/16.
 */

public class VisitPlanAdapter extends BaseQuickAdapter<VisitPlan, BaseViewHolder> {

    public VisitPlanAdapter(int layoutResId, @Nullable List<VisitPlan> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, VisitPlan item) {
        TextView tvVisitUnit = helper.itemView.findViewById(R.id.tv_visit_unit);
        TextView tvFormulater  = helper.itemView.findViewById(R.id.tv_formulater);
        ImageView ivStatus = helper.itemView.findViewById(R.id.iv_status);
        if(item.getStatus() == 0){
            ivStatus.setImageResource(R.drawable.ic_un_complate);
        }else if(item.getStatus() == 1){
            ivStatus.setImageResource(R.drawable.ic_complate);
        }

        tvVisitUnit.setText(item.getVisitUnit());
        tvFormulater.setText(item.getVisiteRname());
    }
}
