package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.entity.ProjectProcessProgress;
import com.kisoft.yuejianli.entity.ProjectProgress;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.List;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/3/15.
 */

public class ProjectProgressAdapter extends BaseQuickAdapter<ProjectProgress, BaseViewHolder> {

    public ProjectProgressAdapter(int layoutResId, @Nullable List<ProjectProgress> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, ProjectProgress item) {
        TextView tvPartName = helper.itemView.findViewById(R.id.tv_process_name);
        ImageView ivStatus = helper.itemView.findViewById(R.id.iv_status);
        TextView tvStatus = helper.itemView.findViewById(R.id.tv_status);
        tvPartName.setText(item.getContent());
        if(StringUtil.isEqual(ProjectProgress.COMP_STATUS_2 , item.getCompStatus())){
            switch (item.getScheStatus()){
                case ProjectProgress.SCHE_STATUS_0:
                    ivStatus.setImageResource(R.drawable.ic_progress_behind);
                    tvStatus.setText(ProjectProgress.SCHE_STATUS_0_NAME);
                    break;
                case ProjectProgress.SCHE_STATUS_1:
                    ivStatus.setImageResource(R.drawable.ic_progress_complete);
                    tvStatus.setText(ProjectProgress.SCHE_STATUS_1_NAME);
                    break;
                case ProjectProgress.SCHE_STATUS_2:
                    ivStatus.setImageResource(R.drawable.ic_progress_complete);
                    tvStatus.setText(ProjectProgress.SCHE_STATUS_2_NAME);
                    break;
                    default:
                        break;
            }
        }else {
            ivStatus.setImageResource(R.drawable.ic_progress_no);
            tvStatus.setText(ProjectProgress.COMP_STATUS_0_NAME);
        }

    }
}
