package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.entity.MarketInfo;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/7/23.
 */
public class MarketInfoAdapter extends BaseQuickAdapter<MarketInfo, MarketInfoAdapter.ViewHolder> {

    public MarketInfoAdapter(int layoutResId, @Nullable List<MarketInfo> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(ViewHolder viewHolder, MarketInfo item) {
        if (item != null) {
            viewHolder.tvProjectName.setText(item.getProjectName());
            viewHolder.tvBuildCompany.setText(item.getDevlopOrg());
            viewHolder.tvManager.setText(item.getChargeEngName());
            viewHolder.tvDate.setText(item.getCreateTime());
            viewHolder.tvType.setText(item.getIsSourceName());
        }
    }

    public class ViewHolder extends BaseViewHolder {
        @BindView(R.id.tv_project_name)
        TextView tvProjectName;
        @BindView(R.id.tv_build_company)
        TextView tvBuildCompany;
        @BindView(R.id.tv_manager)
        TextView tvManager;
        @BindView(R.id.tv_type)
        TextView tvType;
        @BindView(R.id.tv_date)
        TextView tvDate;

        public ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
