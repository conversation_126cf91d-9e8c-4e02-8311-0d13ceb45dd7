package com.kisoft.yuejianli.adpter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.PersonPerformance;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

/**
 * 个人业绩
 */
public class PerformanceResourceAdapter extends BaseQuickAdapter<PersonPerformance, YBaseViewHolder> {

    public PerformanceResourceAdapter() {
        super(R.layout.item_performance_resource);
    }

    @Override
    protected void convert(YBaseViewHolder helper, PersonPerformance item) {
        helper.setText(R.id.tvName,item.getUserName()).setText(R.id.tvStation,"岗位："+item.getUserStation())
                .setText(R.id.tvSex,"性别："+item.getSex()).setText(R.id.tvDeptName,"部门："+item.getDeptName());
    }
}
