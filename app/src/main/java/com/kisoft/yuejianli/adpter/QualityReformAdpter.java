package com.kisoft.yuejianli.adpter;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.entity.QualityRefromSuperNotice;
import com.kisoft.yuejianli.entity.SuperNoticeAnswer;
import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/5/21.
 */

public class QualityReformAdpter extends BaseQuickAdapter<QualityRefromSuperNotice ,BaseViewHolder> {

    int reformColor = ContextCompat.getColor(YueApplacation.mContext, R.color.sign_ok);
    int unReformColor = ContextCompat.getColor(YueApplacation.mContext, R.color.sign_not);

    public QualityReformAdpter(int layoutResId, @Nullable List<QualityRefromSuperNotice> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, QualityRefromSuperNotice item) {
        TextView tvRefromName = helper.itemView.findViewById(R.id.tv_reform_name);
        TextView tvCompleteTime = helper.itemView.findViewById(R.id.tv_complete_time);
        TextView tvStatus = helper.itemView.findViewById(R.id.tv_reform_status);

        tvRefromName.setText("工程质量整改通知单");
        tvCompleteTime.setText(item.getRectificationTime());
        if(StringUtil.isEqual(SuperNoticeAnswer.ANSWER_TYPE_QUALITY_REFORM,item.getReadStatus())){    // 已回复
            tvStatus.setText("已回复");
            tvStatus.setTextColor(reformColor);
        }else {
            tvStatus.setText("未回复");
            tvStatus.setTextColor(unReformColor);
        }
    }
}
