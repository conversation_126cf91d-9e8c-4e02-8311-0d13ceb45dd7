package com.kisoft.yuejianli.adpter;

import android.content.SharedPreferences;
import androidx.annotation.Nullable;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.ProgrammeInfoList;
import com.kisoft.yuejianli.manager.SettingManager;
import java.util.List;

public class ProgrammeAdapter extends BaseQuickAdapter<ProgrammeInfoList.DataBean, BaseViewHolder> {

    public ProgrammeAdapter(int layoutResId, @Nullable List<ProgrammeInfoList.DataBean> data) {
        super(layoutResId, data);
    }
    private static SharedPreferences preferences;
    private static SharedPreferences.Editor editor;
    @Override
    protected void convert(BaseViewHolder helper, ProgrammeInfoList.DataBean item) {
        helper.addOnClickListener(R.id.tv_item_update);
        LinearLayout llTime = helper.itemView.findViewById(R.id.ll_item_time);
        TextView tvItemTime=helper.itemView.findViewById(R.id.tv_item_time);
        TextView tvTitle = helper.itemView.findViewById(R.id.tv_title);
        TextView tvUpdate = helper.itemView.findViewById(R.id.tv_item_update);
        if(item.getUsername().equals("1")){
            tvItemTime.setText(item.getHh24()+":00");
            llTime.setVisibility(View.VISIBLE);
        }else{
            llTime.setVisibility(View.GONE);
        }
        if(SettingManager.getInstance().getUserInfo().getId().equals(item.getCreator())){
            tvUpdate.setText("修改");
            tvTitle.setText(item.getTopic());tvTitle.setText(item.getTopic());
        }else{
            tvUpdate.setText("");
            tvTitle.setText(item.getTopic()+"("+item.getCreatorName()+")");
            tvUpdate.setVisibility(View.GONE);
        }
    }

}
