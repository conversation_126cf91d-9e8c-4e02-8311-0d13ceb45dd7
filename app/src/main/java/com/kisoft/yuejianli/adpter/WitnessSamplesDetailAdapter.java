package com.kisoft.yuejianli.adpter;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.MotionEvent;
import android.view.View;
import android.widget.EditText;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.common.SimpleTextWatcher;
import com.kisoft.yuejianli.entity.WitnessSamplesInspectInfo;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

/**
 * 见证取样详情
 */
public class WitnessSamplesDetailAdapter extends BaseQuickAdapter<WitnessSamplesInspectInfo.WitnessSamplesDetail, YBaseViewHolder> {

    private boolean isOnlyShow=true;

    public WitnessSamplesDetailAdapter(boolean isOnlyShow) {
        super(R.layout.item_witness_samples_detail);
        this.isOnlyShow=isOnlyShow;
    }

    @Override
    protected void convert(YBaseViewHolder helper, final WitnessSamplesInspectInfo.WitnessSamplesDetail item) {
        EditText etGetPos = helper.getView(R.id.etGetPos);
        EditText etGetNum = helper.getView(R.id.etGetNum);
        EditText etSendNum = helper.getView(R.id.etSendNum);
        EditText etReportNo = helper.getView(R.id.etReportNo);

        if(isOnlyShow){
            etGetPos.setFocusable(false);
            etGetNum.setFocusable(false);
            etSendNum.setFocusable(false);
            etReportNo.setFocusable(false);

            etGetPos.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View view, MotionEvent motionEvent) {
                    //触摸的是EditText而且当前EditText能够滚动则将事件交给EditText处理。否则将事件交由其父类处理
                    if (view.getId() == R.id.etGetPos) {
                        view.getParent().requestDisallowInterceptTouchEvent(true);
                        if (motionEvent.getAction() == MotionEvent.ACTION_OUTSIDE) {
                            view.getParent().requestDisallowInterceptTouchEvent(false);
                        }
                    }
                    return false;
                }
            });
        }else {
            helper.addOnClickListener(R.id.tvGetTime).addOnClickListener(R.id.tvGetMan)
                    .addOnClickListener(R.id.tvWitnessMan).addOnClickListener(R.id.tvSendTime).addOnClickListener(R.id.tvReportTime);
            //清除焦点
            etGetPos.clearFocus();
            etGetNum.clearFocus();
            etSendNum.clearFocus();
            etReportNo.clearFocus();
            //先清除之前的文本改变监听
            if(etGetPos.getTag() instanceof TextWatcher){
                etGetPos.removeTextChangedListener((TextWatcher) etGetPos.getTag());
            }
            if(etGetNum.getTag() instanceof TextWatcher){
                etGetNum.removeTextChangedListener((TextWatcher) etGetNum.getTag());
            }
            if(etSendNum.getTag() instanceof TextWatcher){
                etSendNum.removeTextChangedListener((TextWatcher) etSendNum.getTag());
            }
            if(etReportNo.getTag() instanceof TextWatcher){
                etReportNo.removeTextChangedListener((TextWatcher) etReportNo.getTag());
            }
        }
        //设置数据
        etGetPos.setText(StringUtil.isEmpty(item.getSampleAction())?"":item.getSampleAction());
        etGetNum.setText(StringUtil.isEmpty(item.getSampleNum())?"":item.getSampleNum());
        etSendNum.setText(StringUtil.isEmpty(item.getSubmissionNum())?"":item.getSubmissionNum());
        etReportNo.setText(StringUtil.isEmpty(item.getReportNumber())?"":item.getReportNumber());
        helper.setText(R.id.tvGetTime,item.getSampleDate()).setText(R.id.tvSendTime,item.getSubmissionDate())
                .setText(R.id.tvReportTime,item.getReportDate())
                .setText(R.id.tvGetMan,item.getSampleName()).setText(R.id.tvWitnessMan,item.getEyewitness());
        if(!isOnlyShow){
            //文本改变监听
            final TextWatcher getPosWatcher=new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable editable) {
                    if (TextUtils.isEmpty(editable)) {
                        item.setSampleAction(null);
                    } else {
                        item.setSampleAction(String.valueOf(editable));
                    }
                }
            };
            final TextWatcher getNumWatcher=new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable editable) {
                    if (TextUtils.isEmpty(editable)) {
                        item.setSampleNum(null);
                    } else {
                        item.setSampleNum(String.valueOf(editable));
                    }
                }
            };
            final TextWatcher sendNumWatcher=new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable editable) {
                    if (TextUtils.isEmpty(editable)) {
                        item.setSubmissionNum(null);
                    } else {
                        item.setSubmissionNum(String.valueOf(editable));
                    }
                }
            };
            final TextWatcher reportNoWatcher=new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable editable) {
                    if (TextUtils.isEmpty(editable)) {
                        item.setReportNumber(null);
                    } else {
                        item.setReportNumber(String.valueOf(editable));
                    }
                }
            };
            //监听设置到不同的EditText上
            etGetPos.addTextChangedListener(getPosWatcher);
            etGetPos.setTag(getPosWatcher);
            etGetNum.addTextChangedListener(getNumWatcher);
            etGetNum.setTag(getNumWatcher);
            etSendNum.addTextChangedListener(sendNumWatcher);
            etSendNum.setTag(sendNumWatcher);
            etReportNo.addTextChangedListener(reportNoWatcher);
            etReportNo.setTag(reportNoWatcher);
        }
    }
}