package com.kisoft.yuejianli.ui.scgl;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kisoft.yuejianli.R;

import java.util.ArrayList;

import cn.bingoogolapple.photopicker.widget.BGASortableNinePhotoLayout;

public class YImageViewCell extends FrameLayout implements BGASortableNinePhotoLayout.Delegate {

    private BGASortableNinePhotoLayout mPhotosSnpl;
    private ArrayList<String> images = new ArrayList<>();
    private AddClickListener mAddClickListener;
    private PreviewClickListener mPreviewClickListener;

    private boolean isApply;

    public boolean isApply() {
        return isApply;
    }

    public void setApply(boolean apply) {
        isApply = apply;
        mPhotosSnpl.setEditable(isApply);
        mPhotosSnpl.setPlusEnable(isApply);
    }

    public ArrayList<String> getImages() {
        return images;
    }

    public void setImages(ArrayList<String> images) {
        this.images = images;
        mPhotosSnpl.setData(images);
    }

    public YImageViewCell(@NonNull Context context) {
        this(context, null);
    }

    public YImageViewCell(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public YImageViewCell(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        // 获取自定义相关属性
        initAttrs(context, attrs);
        // 初始化控件
        initView();
        // 设置监听
        initListener();
    }

    private void initAttrs(Context context, AttributeSet attrs) {
//        TypedArray a = context.obtainStyledAttributes(attrs,R.styleable.YLabelCell);
//        mTitle = a.getString(R.styleable.YLabelCell_title);
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.rv_item_image, this, true);
        mPhotosSnpl = findViewById(R.id.snpl_moment_add_photos);
        // 设置拖拽排序控件的代理
        mPhotosSnpl.setDelegate(this);
    }

    private void initListener() {

    }

    private void choicePhotoWrapper() {
        if (mAddClickListener != null) {
            mAddClickListener.addPicture();
        }
    }


    @Override
    public void onClickAddNinePhotoItem(BGASortableNinePhotoLayout sortableNinePhotoLayout, View view, int position,
                                        ArrayList<String> models) {
        choicePhotoWrapper();
    }

    @Override
    public void onClickDeleteNinePhotoItem(BGASortableNinePhotoLayout sortableNinePhotoLayout, View view,
                                           int position, String model, ArrayList<String> models) {
        mPhotosSnpl.removeItem(position);
    }

    @Override
    public void onClickNinePhotoItem(BGASortableNinePhotoLayout sortableNinePhotoLayout, View view, int position,
                                     String model, ArrayList<String> models) {
        // 查看大图
        if (mPreviewClickListener != null) {
            mPreviewClickListener.PreviewClickPicture(model);
        }
    }

    @Override
    public void onNinePhotoItemExchanged(BGASortableNinePhotoLayout sortableNinePhotoLayout, int fromPosition,
                                         int toPosition, ArrayList<String> models) {
    }

    public interface AddClickListener {
        void addPicture();
    }

    public void setAddClickListener(AddClickListener addClickListener) {
        this.mAddClickListener = addClickListener;
    }

    public interface PreviewClickListener {
        void PreviewClickPicture(String url);
    }

    public void setPreviewClickListener(PreviewClickListener clickListener) {
        this.mPreviewClickListener = clickListener;
    }
}