package com.kisoft.yuejianli.ui;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.UnitListAdapter;
import com.kisoft.yuejianli.adpter.interf.GlobalListener;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.UnitListContract;
import com.kisoft.yuejianli.entity.UnitListBean;
import com.kisoft.yuejianli.model.UnitListModel;
import com.kisoft.yuejianli.presenter.UnitListPresenter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class UnitListActivity extends BaseActivity<UnitListModel, UnitListPresenter> implements UnitListContract.UnitListViewContract {
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    public Integer mType;
    public Integer mEndType;

//    public enum unitType {
//        unitTypeProject,    // 单位工程1
//        unitTypeDistributed,// 分部工程2
//        unitTypeSubdivision,// 分项工程3
//        unitTypeHidden,  //隐蔽性4
//        unitTypeSide,   // 旁站4
//        unitTypeDanager,// 危大4
//        unitTypeCheck,// 抽检4
//    }

    private List<UnitListBean.ListDataBean> mDatas = new ArrayList<>();
    private UnitListModel mModel;
    private UnitListPresenter mPresnter;
    private UnitListAdapter mAdapter;

    public Integer getType() {
        return mType;
    }

    public void setType(Integer type) {
        mType = type;
    }

    public Integer getEndType() {
        return mEndType;
    }

    public void setEndType(Integer endType) {
        mEndType = endType;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mType = getIntent().getIntExtra("mType", 1);// 获取跳转类型
        mEndType = getIntent().getIntExtra("mEndType", 1);// 获取跳转类型
        mModel = new UnitListModel(this);
        mPresnter = new UnitListPresenter(this, mModel);

        initMVP(mModel, mPresnter);
        initView();
    }

    private void initView() {

        tvTitle.setText("单位工程");
        tvSubmit.setText("确定");
        tvSubmit.setVisibility(View.GONE);
        ivAction.setVisibility(View.GONE);

        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            manager.setSmoothScrollbarEnabled(false);
            rvContent.setLayoutManager(manager);
            rvContent.setNestedScrollingEnabled(false);
        }

        mAdapter = new UnitListAdapter(R.layout.item_dangers_list, mEndType, mDatas,
                new GlobalListener<UnitListBean.ListDataBean>() {
                    @Override
                    public void onViewClick(int id, int position, UnitListBean.ListDataBean model) {

                    }
                    @Override
                    public void onRootViewClick(View view, int position, UnitListBean.ListDataBean model) {
                        if (model != null) {
                            // 单位工程
                            if (mType == 1) {
                                if (mEndType == mType) {
                                    // 直接返回
                                    Intent intent = new Intent();
                                    intent.putExtra("data", model.getName());
                                    intent.putExtra("ids", model.getId());
                                    intent.putExtra("childData", (Serializable) model.getChildList());
                                    Log.d("TAG", "onRootViewClick: " + model.toString());
                                    setResult(Activity.RESULT_OK, intent);
                                    finish();
                                }
                                if (mEndType > mType) {
                                    // 跳转下一级 分部
                                    Intent intent = new Intent(mContext, UnitChildListActivity.class);
                                    intent.putExtra("mType", 2);//分部工程2
                                    intent.putExtra("mEndType", mEndType);
                                    intent.putExtra("data", (Serializable) model.getChildList());
                                    startActivityForResult(intent, Constant.REQUEST_CODE_UNIT_FENBU);
                                }
                            }
                        }
                    }
                });

        rvContent.setAdapter(mAdapter);
        mPresnter.getProjectUnitList();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_unit_list;
    }

    @Override
    public void listBack(UnitListBean types) {
        this.mDatas.clear();
        if (types != null) {
            this.mDatas.addAll(types.getList());
        }
        mAdapter.notifyDataSetChanged();
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case Constant.REQUEST_CODE_UNIT_FENBU:
                if (resultCode == Activity.RESULT_OK) {
                    setResult(Activity.RESULT_OK, data);
                    finish();
                }
                break;
        }
    }
}