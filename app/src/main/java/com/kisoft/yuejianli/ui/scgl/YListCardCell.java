package com.kisoft.yuejianli.ui.scgl;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.kisoft.yuejianli.R;

public class YListCardCell extends FrameLayout {

    String mTitle;
    private TextView mTvTitle;
    private RecyclerView mRvCard;
//    private YLabelCell mLabel1;
//    private YLabelCell mLabel2;
//    private YLabelCell mLabel3;


    public String getTitle() {
        return mTitle;
    }

    public void setTitle(String title) {
        mTitle = title;
    }


    public TextView getTvTitle() {
        return mTvTitle;
    }

    public void setTvTitle(TextView tvTitle) {
        mTvTitle = tvTitle;
    }

    public YListCardCell(@NonNull Context context) {
        this(context,null);
    }

    public YListCardCell(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }

    public YListCardCell(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        // 获取自定义相关属性
        initAttrs(context, attrs);
        // 初始化控件
        initView();
        // 设置监听
        initListener();
    }

    private void initAttrs(Context context, AttributeSet attrs) {
//        TypedArray a = context.obtainStyledAttributes(attrs,R.styleable.YLabelCell);
//        mTitle = a.getString(R.styleable.YLabelCell_title);
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.cell_card, this, true);
        mTvTitle = this.findViewById(R.id.tv_view_title);
        mRvCard = this.findViewById(R.id.rv_card);
//        mLabel1 = this.findViewById(R.id.label1);
//        mLabel2 = this.findViewById(R.id.label2);
//        mLabel3 = this.findViewById(R.id.label3);
    }

    private void initListener() {

    }
}
