package com.kisoft.yuejianli.ui;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.entity.Material;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.adpter.MaterialAdapter;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/3/14.
 */

public class MaterialPopWindow extends PopupWindow  {

    private Context mContext;
    private RecyclerView rvContent;
    private MaterialAdapter mAdapter;
    private List<Material> materials = new ArrayList<>();
    private View rootView;

    private OnClickMaterialListener listener;

    public MaterialPopWindow(Context context, int width){
        super(context);
        mContext = context;
        init(width);
    }




    private void init(int wid){
        rootView = LayoutInflater.from(mContext).inflate(R.layout.spiner_window, null);
        setContentView(rootView);
        setWidth(wid);
        setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        setFocusable(true);
        ColorDrawable dw = new ColorDrawable(0x00);
        setBackgroundDrawable(dw);
        rvContent = rootView.findViewById(R.id.rv_material);
        if(rvContent.getLayoutManager() == null){
            LinearLayoutManager manager = new LinearLayoutManager(mContext);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new MaterialAdapter(R.layout.item_material_selector , materials);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if(listener != null){
                    listener.onClickMaterial(materials.get(position));
                }
            }
        });
        rvContent.setAdapter(mAdapter);
    }

    public void refreshData(List<Material> materials){
        this.materials.clear();
        Material material = new Material();
        this.materials.add(material);
        this.materials.addAll(materials);
        mAdapter.notifyDataSetChanged();
    }

    public View location;

    public void showAsDropDown(View anchor){
        location = anchor;
        super.showAsDropDown(anchor);
    }


    public interface OnClickMaterialListener{
        void onClickMaterial(Material material);
    }

    public void setOnClickMaterialListener(OnClickMaterialListener listener){
        this.listener = listener;
    }


}
