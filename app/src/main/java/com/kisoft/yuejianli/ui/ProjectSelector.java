package com.kisoft.yuejianli.ui;

import android.app.Dialog;
import android.app.DialogFragment;
import android.app.FragmentManager;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.adpter.ProjectListAdapter;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/3/13.
 */

public class ProjectSelector extends DialogFragment implements View.OnClickListener {


    private OnClickProjectListener listener;
    private Context context;
    private ProjectInfo defaultProject;
    private View rootView;
    private ImageView ivBack;
    private TextView tvTitle;
    private RecyclerView rvProjects;
    private ProjectListAdapter mAdapter;
    private List<ProjectInfo> projectInfos = new ArrayList<>();


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        getDialog().setCanceledOnTouchOutside(true);
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        getDialog().getWindow().setBackgroundDrawableResource(android.R.color.white);
        getDialog().getWindow().setWindowAnimations(R.style.dialog_up_down);
        WindowManager.LayoutParams lp = getDialog().getWindow().getAttributes();
        lp.gravity = Gravity.CENTER;
        getDialog().getWindow().setAttributes(lp);
        rootView = inflater.inflate(R.layout.dialog_project_select, null);
        initData();
        initView();
        return rootView;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        setStyle(DialogFragment.STYLE_NO_TITLE, android.R.style.Theme_Holo_Dialog_MinWidth);
        super.onActivityCreated(savedInstanceState);
    }

    @Override
    public void onStart() {
        Dialog dialog = getDialog();
        if(dialog != null){
            DisplayMetrics dm = new DisplayMetrics();
            getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
            dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        }
        super.onStart();

    }

    private void initView() {
        ivBack = (ImageView) rootView.findViewById(R.id.iv_back);
        ivBack.setOnClickListener(this);
        tvTitle = (TextView) rootView.findViewById(R.id.tv_title);
        tvTitle.setText("切换项目");
        rvProjects = (RecyclerView) rootView.findViewById(R.id.rv_project_list);
        if (rvProjects.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(context);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvProjects.setLayoutManager(manager);
        }
        mAdapter = new ProjectListAdapter(R.layout.item_project_list, projectInfos);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                defaultProject = projectInfos.get(position);
                if (listener != null) {
                    if(!StringUtil.isEqual(defaultProject.getProjectId(),
                            SettingManager.getInstance().getProject().getProjectId())){
                        listener.onClickProject(defaultProject);
                    }
                    dismiss();
                }
            }
        });
        rvProjects.setAdapter(mAdapter);
    }


    public void showProjectSeletor(Context context, FragmentManager manager,List<ProjectInfo> projectInfos){
        this.context =context;
        if(projectInfos != null  && projectInfos.size()>0){
            this.projectInfos.clear();
            this.projectInfos.addAll(projectInfos);
        }

        show(manager, "project_selector");
    }

    private void initData() {
        // todo 项目集合数据
        if(projectInfos.size() == 0){

        }

    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                dismiss();
                break;
            default:
                break;
        }
    }

    public interface OnClickProjectListener {
        void onClickProject(ProjectInfo info);
    }

    public void setOnClickProjectListener(OnClickProjectListener listener) {
        this.listener = listener;
    }
}
