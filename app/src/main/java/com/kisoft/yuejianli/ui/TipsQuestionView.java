package com.kisoft.yuejianli.ui;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.Base;
import com.kisoft.yuejianli.entity.AttachmentData;
import com.kisoft.yuejianli.ui.dragview.FloatLayer;
import com.kisoft.yuejianli.utils.ScreenUtils;
import com.kisoft.yuejianli.views.WebActivity;

import java.util.HashMap;


public class TipsQuestionView extends FrameLayout {

    CardView floatCardView = null;
    FloatLayer floatLayer = null;
    private String docContent;//文档文字
    private Boolean status;

    private TextView mTitle;
    private RelativeLayout mSelectView;
    private EditText mQuestEdit;
    private TextView mDocBtn;
    private ImageView mArrowImg;
    private YTipsDetailWebView mWeb;

    private AttachmentData attachmentData;

    public AttachmentData getAttachmentData() {
        return attachmentData;
    }

    public void setAttachmentData(AttachmentData attachmentData) {
        this.attachmentData = attachmentData;
        mWeb.setAttachmentData(attachmentData);
    }

    public String getDocContent() {
        return docContent;
    }

    public void setDocContent(String docContent) {
        this.docContent = docContent;
    }

    public TextView getTitle() {
        return mTitle;
    }

    public void setTitle(TextView title) {
        mTitle = title;
    }

    public EditText getQuestEdit() {
        return mQuestEdit;
    }

    public void setQuestEdit(EditText questEdit) {
        mQuestEdit = questEdit;
    }

    public RelativeLayout getSelectView() {
        return mSelectView;
    }

    public void setSelectView(RelativeLayout selectView) {
        mSelectView = selectView;
    }

    public TextView getDocBtn() {
        return mDocBtn;
    }

    public void setDocBtn(TextView docBtn) {
        mDocBtn = docBtn;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
        this.mQuestEdit.setEnabled(status);
        this.mSelectView.setEnabled(status);
        if (status) {
            // 启用
            this.mQuestEdit.setHint("请选择问题");
            mDocBtn.setVisibility(VISIBLE);
            mArrowImg.setVisibility(VISIBLE);
        } else {
            // 禁用
            this.mQuestEdit.setHint("");
            mDocBtn.setVisibility(GONE);
            mArrowImg.setVisibility(GONE);
        }
    }

    public TipsQuestionView(@NonNull Context context) {
        this(context, null);
    }

    public TipsQuestionView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TipsQuestionView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        // 初始化控件
        initView();
        // 设置监听
        initListener();
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.tips_question_view, this, true);
        mSelectView = this.findViewById(R.id.select_quest_rl);
        mQuestEdit = this.findViewById(R.id.quest_detail_et);
        mDocBtn = this.findViewById(R.id.tv_select_btn);
        mTitle = this.findViewById(R.id.tv_tips_title);
        mArrowImg = this.findViewById(R.id.iv_select_contruction_unit);


        floatCardView = new CardView(getContext());
        //View textView = (View) LayoutInflater.from(getContext()).inflate(R.layout.tips_detail_view, floatCardView,
        // false);
        YTextViewCell cell = new YTextViewCell(getContext());
        mWeb = new YTipsDetailWebView(getContext());
        mWeb.getImgBtn().setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (floatLayer != null){
                    floatLayer.dismiss();
                }
            }
        });
//        View vv = inflate(getContext(), R.layout.tips_detail_webview, floatCardView);

//                ImageView floatIconView = new ImageView(getContext());
//                floatIconView.setImageResource(R.mipmap.bga_pp_ic_camera);
//                floatIconView.setScaleType(ImageView.ScaleType.FIT_CENTER);
//                floatIconView.setBackgroundResource(R.color.colorPrimary);

        floatCardView.addView(mWeb);
        floatCardView.setCardBackgroundColor(Color.WHITE);
        floatCardView.setRadius(5);
        int width = ScreenUtils.getScreenWidth(getContext());
        int height = (int) (width * 1.2);
        floatCardView.setLayoutParams(new ViewGroup.LayoutParams(width, height));

    }

    private void initListener() {
        mDocBtn.setOnClickListener(new OnClickListener() {
            @SuppressLint("ResourceAsColor")
            @Override
            public void onClick(View view) {
                /*
                BottomSheetDialog dialog = new BottomSheetDialog(getContext());
                dialog.setContentView(R.layout.tips_detail_view);
                TextView mTipsTv = dialog.findViewById(R.id.tv_tips_help);
                mTipsTv.setText("暂无帮助文档");
                    if (StringUtil.isEmpty(docContent)) {ke
                        mTipsTv.setText("暂无帮助文档");
                    } else {
                        mTipsTv.setText(docContent);
                    }
                dialog.show();
                    */
                mWeb.setfBody(docContent);
                if (floatLayer == null) {
//                    web.setfBody(this.docContent);
                    showTipsWebView();
                } else {
                    floatLayer.show();
                }

//                final DraggableAlertDialog dialog = new DraggableAlertDialog();
//                TextView textView = new TextView(getContext());
//                textView.setText("我的内容");
//                textView.setPadding(50 ,50, 50, 50);
//                textView.setBackgroundColor(R.color.colorAccent);
//                dialog.setView(textView);
//                dialog.show();

//                TextView textView = (TextView) findViewById(R.id.tv_main);
            }
        });
    }

    private void showTipsWebView() {

        floatLayer = new FloatLayer(getContext());
        floatLayer.floatView(floatCardView);
        floatLayer.snapEdge(FloatLayer.Edge.NONE);
        floatLayer.outside(false);
        floatLayer.defPercentX(1);
        floatLayer.defPercentY(1F);
        floatLayer.defAlpha(0F);
        floatLayer.defScale(0F);
        floatLayer.normalAlpha(1f);
        floatLayer.normalScale(1);
        floatLayer.lowProfileDelay(3000);
        floatLayer.lowProfileAlpha(1F);
        floatLayer.lowProfileScale(1F);
        floatLayer.lowProfileIndent(0.5F);
        floatLayer.paddingLeft(0);
        floatLayer.paddingTop(0);
        floatLayer.paddingRight(0);
        floatLayer.paddingBottom(0);
        floatLayer.marginLeft(0);
        floatLayer.marginTop(0);
        floatLayer.marginRight(0);
        floatLayer.marginBottom(0);
//                floatLayer.onFloatClick(new Layer.OnClickListener() {
//                    @Override
//                    public void onClick(@NonNull Layer layer, @NonNull View v) {
//                        //AnyLayer.toast().message("点击了悬浮按钮").gravity(Gravity.CENTER).show();
//                        floatLayer.dismiss();
//                    }
//                });
        floatLayer.show();
    }
}
