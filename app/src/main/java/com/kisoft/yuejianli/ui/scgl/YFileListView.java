package com.kisoft.yuejianli.ui.scgl;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.EnclosureListAdapter;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;

public class YFileListView extends FrameLayout {

    String mTitle;
    private Context mContext;
    private RecyclerView rvContent;
    private TextView mAddFileBtn;
    private TextView mTvTitle;
    private boolean isApply;

    private EnclosureListAdapter mAdapter;
    private ArrayList<EnclosureListDto> mList;
    private OpenFileContract mOpenFileContract = null;
    private FileOperateContract mFileContract = null;

    public boolean isApply() {
        return isApply;
    }

    public void setApply(boolean apply) {
        isApply = apply;
        initListener();
    }

    public TextView getAddFileBtn() {
        return mAddFileBtn;
    }

    public void setAddFileBtn(TextView addFileBtn) {
        mAddFileBtn = addFileBtn;
    }

    public RecyclerView getRvContent() {
        return rvContent;
    }

    public void setRvContent(RecyclerView rvContent) {
        this.rvContent = rvContent;
    }

    public ArrayList<EnclosureListDto> getList() {
        return mList;
    }

    public void setList(ArrayList<EnclosureListDto> list) {
        mList = list;
        mAdapter.setNewData(mList);
        mAdapter.notifyDataSetChanged();
    }

    public YFileListView(@NonNull Context context) {
        this(context, null);
    }

    public YFileListView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public YFileListView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        // 获取自定义相关属性
        initAttrs(context, attrs);
        // 初始化控件
        initView();
    }
    private void initAttrs(Context context, AttributeSet attrs) {
        TypedArray a = context.obtainStyledAttributes(attrs,R.styleable.YFileListView);
        mTitle = a.getString(R.styleable.YFileListView_title);
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.file_list_view, this, true);
        rvContent = this.findViewById(R.id.rv_file_list);
        mAddFileBtn = this.findViewById(R.id.tv_add_item);
        mTvTitle = this.findViewById(R.id.title);
        if (!StringUtil.isEmpty(mTitle)){
            mTvTitle.setText(mTitle);
        }
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            layoutManager.setSmoothScrollbarEnabled(false);
            rvContent.setLayoutManager(layoutManager);
            rvContent.setNestedScrollingEnabled(false);
        }
        mAdapter = new EnclosureListAdapter(mList);
        rvContent.setAdapter(mAdapter);
    }

    private void initListener() {
        if (isApply) {
            mAddFileBtn.setVisibility(VISIBLE);
            // 删除附件
            mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                    // 删除附件
                    showFileOperate(position);
                }
            });
        } else {
            mAddFileBtn.setVisibility(GONE);
            mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                    // 打开附件
                    String webUrl = mList.get(position).getViewUrl();
                    if (webUrl != null && !webUrl.isEmpty()) {
                        if (mOpenFileContract != null) {
                            mOpenFileContract.openFile(mList.get(position));
                        }
                    }else {
                        Toast.makeText(mContext, "预览地址为空", Toast.LENGTH_SHORT).show();
                        if (mOpenFileContract != null) {
                            mOpenFileContract.openFile(mList.get(position));
                        }
                    }
                }
            });
        }
    }

    private void showFileOperate(int position) {

        // 空照片 ，添加
        String[] str = new String[]{"删除", "取消"};
        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
        ab.setTitle("确认删除附件吗？");
        ab.setNegativeButton("取消", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
//                Toast.makeText(mContext, "你点击了取消按钮~", Toast.LENGTH_SHORT).show();
            }
        });
        ab.setPositiveButton("确定", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
//                Toast.makeText(mContext, "你点击了确定按钮~", Toast.LENGTH_SHORT).show();
                if (mFileContract != null) {
//                    mList.remove(position);
                    mFileContract.openFile(position);
                }
            }
        });

/*
        ab.setItems(str, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                switch (which) {
                    case 0://删除
                        if (mFileContract != null) {
                            mList.remove(position);
                            mFileContract.openFile(position);
                        }
                        break;
                    case 1://取消

                        break;
                }
            }
        });
        */
        ab.show();
    }

    public void setOpenFileContract(OpenFileContract openFileContract) {
        this.mOpenFileContract = openFileContract;
    }

    public interface OpenFileContract {
        void openFile(EnclosureListDto dto);
    }

    public void setFileOperateContract(FileOperateContract fileContract) {
        this.mFileContract = fileContract;
    }

    public interface FileOperateContract {
        void openFile(int index);
    }
}
