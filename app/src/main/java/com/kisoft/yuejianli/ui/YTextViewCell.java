package com.kisoft.yuejianli.ui;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kisoft.yuejianli.R;

public class YTextViewCell extends FrameLayout {
    String mTitle;
    private TextView mTvTitle;
    private EditText mEtContent;
//    private RelativeLayout mSelectView;

    public TextView getTvTitle() {
        return mTvTitle;
    }

    public void setTvTitle(TextView tvTitle) {
        mTvTitle = tvTitle;
    }

    public EditText getEtContent() {
        return mEtContent;
    }

    public void setEtContent(EditText etContent) {
        mEtContent = etContent;
    }

//    public RelativeLayout getSelectView() {
//        return mSelectView;
//    }
//
//    public void setSelectView(RelativeLayout selectView) {
//        mSelectView = selectView;
//    }

    public YTextViewCell(@NonNull Context context) {
        this(context,null);
    }

    public YTextViewCell(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }

    public YTextViewCell(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        // 获取自定义相关属性
        initAttrs(context, attrs);
        // 初始化控件
        initView();
        // 设置监听
        initListener();
    }
    private void initAttrs(Context context, AttributeSet attrs) {
        TypedArray a = context.obtainStyledAttributes(attrs,R.styleable.YLabelCell);
        mTitle = a.getString(R.styleable.YLabelCell_title);
    }
    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.cell_textview, this, true);
        mTvTitle = this.findViewById(R.id.tv_view_title);
        mEtContent = this.findViewById(R.id.et_view_content);
//        mSelectView = this.findViewById(R.id.rl_content);
        mTvTitle.setText(mTitle);
    }

    private void initListener() {
    }
}
