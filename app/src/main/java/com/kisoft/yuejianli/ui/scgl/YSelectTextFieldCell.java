package com.kisoft.yuejianli.ui.scgl;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kisoft.yuejianli.R;

public class YSelectTextFieldCell extends FrameLayout {

    String mTitle;
    String mContent;
    private TextView mTvTitle;
    private TextView mTvContent;

    public String getTitle() {
        return mTitle;
    }

    public void setTitle(String title) {
        mTitle = title;
    }

    public String getContent() {
        return mContent;
    }

    public void setContent(String content) {
        mContent = content;
        mTvContent.setText(mContent);
    }

    public TextView getTvTitle() {
        return mTvTitle;
    }

    public void setTvTitle(TextView tvTitle) {
        mTvTitle = tvTitle;
    }

    public TextView getTvContent() {
        return mTvContent;
    }

    public void setTvContent(TextView tvContent) {
        mTvContent = tvContent;
    }

    public YSelectTextFieldCell(@NonNull Context context) {
        this(context,null);
    }

    public YSelectTextFieldCell(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }

    public YSelectTextFieldCell(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        // 获取自定义相关属性
        initAttrs(context, attrs);
        // 初始化控件
        initView();
        // 设置监听
        initListener();
    }

    private void initAttrs(Context context, AttributeSet attrs) {
        TypedArray a = context.obtainStyledAttributes(attrs,R.styleable.YLabelCell);
        mTitle = a.getString(R.styleable.YLabelCell_title);
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.cell_select_label, this, true);
        mTvTitle = this.findViewById(R.id.tv_label_title);
        mTvContent = this.findViewById(R.id.tv_label_content);

        mTvTitle.setText(mTitle);
    }

    private void initListener() {

    }
}
