package com.kisoft.yuejianli.ui;

import android.app.Dialog;
import android.app.DialogFragment;
import android.app.FragmentManager;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.kisoft.yuejianli.adpter.SearchProcessAdapter;
import com.kisoft.yuejianli.base.Base;
import com.kisoft.yuejianli.entity.ProjectProcess;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/11.
 */

public class SearchDialog extends DialogFragment implements View.OnClickListener, TextWatcher {

    private OnClickEnsureListener listener;

    private Context context;

    /** 时间选择控件  */
    private TextView tvNotTime;
    private ImageView tvLeft;
    private ImageView tvRight;
    private TextView tvMonth;

    /** 人名选择  */
    private EditText etPersonName;

    /** 分类选择  */
    private TextView tvNoType;
    private RecyclerView rvType;

    private View rootView;
    private TextView tvCancle;
    private TextView tvEnsure;

    private List<ProjectProcess> processes = new ArrayList<>();
    private SearchProcessAdapter mAdapter;


    /** 筛选后的数据  */
    private boolean isNoType = true;
    private boolean isNoTime = true;
    private boolean isNotPerson = true;
    private String month = "";
    private String personName = "";
    private List<ProjectProcess> checkProcesses = new ArrayList<>();

    private SearchInfo searchInfo = new SearchInfo();


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        getDialog().setCanceledOnTouchOutside(true);
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        getDialog().getWindow().setBackgroundDrawableResource(android.R.color.transparent);
//        getDialog().getWindow().setWindowAnimations(R.style.dialog_up_down);
        WindowManager.LayoutParams lp = getDialog().getWindow().getAttributes();
        lp.gravity = Gravity.CENTER;
        getDialog().getWindow().setAttributes(lp);
        rootView = inflater.inflate(R.layout.dialog_search, null);
        initView();
        return rootView;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        setStyle(DialogFragment.STYLE_NO_TITLE, android.R.style.Theme_Holo_Dialog_MinWidth);
        super.onActivityCreated(savedInstanceState);
    }

    @Override
    public void onStart() {
        Dialog dialog = getDialog();
        if(dialog != null){
            DisplayMetrics dm = new DisplayMetrics();
            getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
            dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        }
        super.onStart();

    }

    private void initView() {
        tvNotTime = rootView.findViewById(R.id.tv_time_not);
        tvNotTime.setOnClickListener(this);
        tvLeft = rootView.findViewById(R.id.iv_left);
        tvLeft.setOnClickListener(this);
        tvRight = rootView.findViewById(R.id.iv_right);
        tvRight.setOnClickListener(this);
        tvMonth = rootView.findViewById(R.id.tv_month);
        month = DateUtil.getMonthDate();
        tvMonth.setText(month);

        etPersonName = rootView.findViewById(R.id.et_query_content);
        etPersonName.addTextChangedListener(this);

        tvNoType = rootView.findViewById(R.id.tv_type_not);
        tvNoType.setOnClickListener(this);
        rvType = rootView.findViewById(R.id.rv_type_search);
        if(rvType.getLayoutManager() == null){
            GridLayoutManager manager = new GridLayoutManager(context, 2);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvType.setLayoutManager(manager);
        }
        mAdapter = new SearchProcessAdapter(R.layout.item_search_process, processes);
        rvType.setAdapter(mAdapter);

        tvCancle = rootView.findViewById(R.id.tv_cancle);
        tvCancle.setOnClickListener(this);
        tvEnsure = rootView.findViewById(R.id.tv_ensure);
        tvEnsure.setOnClickListener(this);
        initCheckStatus();
    }


    public void showSearchDialog(Context context, List<ProjectProcess> processes, FragmentManager manager){
        this.context = context;
        this.processes.clear();
        this.processes.addAll(processes);
        show(manager, "communication");
    }


    @Override
    public void onClick(View view) {

        switch (view.getId()) {
            case R.id.tv_time_not:
                isNoTime = !isNoTime;
                month = "";
                initCheckStatus();
                break;
            case R.id.iv_left:
                getLastMonth();
                initCheckStatus();
                break;

            case R.id.iv_right:
                getAfterMonth();
                initCheckStatus();
                break;

            case R.id.tv_type_not:
                isNoType = !isNoType;
                this.checkProcesses .clear();
                initCheckStatus();
                break;

            case R.id.tv_cancle:
                isNoTime = true;
                isNotPerson = true;
                isNoType = true;
                month = "";
                personName = "";
                checkProcesses.clear();
                initCheckStatus();
                break;

            case R.id.tv_ensure:    // 确定筛选
                initCheckData();
            if(listener != null){
                listener.initSearchInfo(searchInfo);
            }

                break;
            default:
                break;
        }

    }



    private void initCheckData(){
        this.checkProcesses.clear();
        for(int i=0; i<mAdapter.getCheckItems().size(); i++){
            this.checkProcesses.add(processes.get(mAdapter.getCheckItems().get(i)));
        }
        searchInfo.isNotCareTime = this.isNoTime;
        searchInfo.setMonth(this.month);
        searchInfo.isNotCareName = this.isNotPerson;
        searchInfo.setName(this.personName);
        searchInfo.isNotCareType = this.isNoType;
        searchInfo.processes.clear();
        searchInfo.processes.addAll(this.checkProcesses);
        dismiss();
    }


    private void initCheckStatus(){
        if(isNoTime){
            tvNotTime.setBackgroundResource(R.drawable.button_fill_normal);
        }else {
            tvNotTime.setBackgroundResource(R.drawable.button_fill_pressed);
        }
        tvMonth.setText(month);

        if(isNoType){
            tvNoType.setBackgroundResource(R.drawable.button_fill_normal);
            mAdapter.clearnCheckItems();
        }else {
            tvNoType.setBackgroundResource(R.drawable.button_fill_pressed);

        }
    }

    private void getLastMonth(){
        String time = this.month;
        month = DateUtil.getLsatMonth(time);
        tvMonth.setText(month);
        isNoTime = false;
    }

    private void getAfterMonth(){
        String time = this.month;
        month = DateUtil.getAfterMonth(time);
        tvMonth.setText(month);
        isNoTime = false;
    }

    @Override
    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void afterTextChanged(Editable editable) {
        if(editable != null){
            personName = editable.toString().trim();
        }
    }

    public interface OnClickEnsureListener {

        void initSearchInfo( SearchInfo searchInfo);
    }

    public void setOnClickEnsureListener(OnClickEnsureListener listener) {
        this.listener = listener;
    }

    public class SearchInfo extends Base{
        public boolean isNotCareTime = true;
        public String month = "";
        public boolean isNotCareType = true;
        public List<ProjectProcess> processes;
        public boolean isNotCareName = true;
        public String name = "";

        public SearchInfo() {
        }

        public boolean isNotCareTime() {
            return isNotCareTime;
        }

        public void setNotCareTime(boolean notCareTime) {
            isNotCareTime = notCareTime;
        }

        public String getMonth() {
            return month;
        }

        public void setMonth(String month) {
            this.month = month;
        }

        public boolean isNotCareType() {
            return isNotCareType;
        }

        public void setNotCareType(boolean notCareType) {
            isNotCareType = notCareType;
        }

        public List<ProjectProcess> getProcesses() {
            return processes;
        }

        public void setProcesses(List<ProjectProcess> processes) {
            this.processes = processes;
        }

        public boolean isNotCareName() {
            return isNotCareName;
        }

        public void setNotCareName(boolean notCareName) {
            isNotCareName = notCareName;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
