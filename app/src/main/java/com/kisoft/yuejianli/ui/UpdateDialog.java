package com.kisoft.yuejianli.ui;

import android.app.Dialog;
import android.app.DialogFragment;
import android.app.FragmentManager;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/7/27.
 */

public class UpdateDialog extends DialogFragment implements View.OnClickListener{

    private View mRootView;
    private TextView tvTitle;
    private TextView tvContent;
    private TextView tvYes;
    private TextView tvCancle;
    private OnDialogClickListener listener;
    private String title = "";
    private String content = "";

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        getDialog().setCanceledOnTouchOutside(false);
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        getDialog().getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        WindowManager.LayoutParams lp = getDialog().getWindow().getAttributes();
        lp.gravity = Gravity.CENTER;
        getDialog().getWindow().setAttributes(lp);
        mRootView = initView(inflater);
        return mRootView;
    }

    private View initView(LayoutInflater inflater){
        mRootView = inflater.inflate(R.layout.dialog_update, null);
        tvTitle = mRootView.findViewById(R.id.tv_dialog_title);
        tvContent = mRootView.findViewById(R.id.tv_content);
        if (!StringUtil.isEmpty(title)){
            tvTitle.setText(title);
        }
        if (!StringUtil.isEmpty(content)){
            tvContent.setText(content);
        }
        tvYes = mRootView.findViewById(R.id.tv_yes);
        tvCancle = mRootView.findViewById(R.id.tv_cancel);
        tvYes.setOnClickListener(this);
        tvCancle.setOnClickListener(this);
        return mRootView;
    }

    @Override
    public void onStart() {
        Dialog dialog = getDialog();
        if (dialog != null) {
            DisplayMetrics dm = new DisplayMetrics();
            getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
            dialog.getWindow().setLayout(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        super.onStart();
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_TITLE, android.R.style.Theme_Holo_Light_Dialog_MinWidth);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()){
            case R.id.tv_yes:
                if (listener!= null){
                    listener.clickResult(true);
                }
                dismiss();
                break;

            case R.id.tv_cancel:
                if (listener != null){
                    listener.clickResult(false);
                }
                dismiss();

                break;
                default:
                    dismiss();
                    break;
        }
    }

    public void showUpdateDialog(String title, String content, FragmentManager manager){
        this.title = title;
        this.content = content;
        show(manager, "update");
    }

    public interface OnDialogClickListener{
        void clickResult(boolean isDo);
    }

    public void setOnClickDialogListener(OnDialogClickListener listener){
        this.listener = listener;
    }
}
