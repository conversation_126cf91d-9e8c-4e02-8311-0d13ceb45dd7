package com.kisoft.yuejianli.ui;

import android.app.Activity;
import android.graphics.drawable.ColorDrawable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.PopupWindow;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.entity.ConCostTicketShow;
import com.kisoft.yuejianli.entity.ConPayment;
import com.kisoft.yuejianli.utils.ScreenUtils;
import com.kisoft.yuejianli.utils.recyclerview.HorizontalDividerItemDecoration;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;

/**
 * 子合同关联  成本票 关联收款
 */

public class SelectConSettlRelevanceWindow extends PopupWindow {

    public static int TYPE_CON_COST_TICKET=3;
    public static int TYPE_CON_PAYMENT=5;
    private int type;
    @BindView(R.id.mRecyclerView)
    RecyclerView mRecyclerView;
    private Activity mContext;

    private OnClickButtonListener listener;
    private Unbinder unbinder;
    private SelectCostTicketAdapter selectCostTicketAdapter;
    private SelectPaymentAdapter selectPaymentAdapter;

    public SelectConSettlRelevanceWindow(Activity context, int type) {
        super(context);
        mContext = context;
        this.type=type;
        init();
    }


    private void init() {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.popup_select_item, null);
        setContentView(rootView);
        unbinder = ButterKnife.bind(this, rootView);
        setWidth(ScreenUtils.getScreenWidth(mContext));
//        setWidth((int) (ScreenUtils.getScreenWidth(mContext)*2.0/3));
        setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        setFocusable(true);
        ColorDrawable dw = new ColorDrawable(0x600000);
        setBackgroundDrawable(dw);
        //产生背景变暗效果
        WindowManager.LayoutParams lp = mContext.getWindow().getAttributes();
        lp.alpha = 0.6f;
        mContext.getWindow().setAttributes(lp);

        mRecyclerView.setLayoutManager(new LinearLayoutManager(mContext,LinearLayoutManager.VERTICAL,false));
        mRecyclerView.addItemDecoration(new HorizontalDividerItemDecoration.Builder(mContext)
                .drawable(R.color.line_space)
                .margin(ScreenUtils.dip2px(mContext, 10), ScreenUtils.dip2px(mContext, 10))
                .size(ScreenUtils.dip2px(mContext, 1))
                .build());
        if(type==TYPE_CON_COST_TICKET){
            selectCostTicketAdapter = new SelectCostTicketAdapter();
            mRecyclerView.setAdapter(selectCostTicketAdapter);
            selectCostTicketAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                    Object object=adapter.getData().get(position);
                    if(listener!=null){
                        listener.onClickConfirm(object,position);
                        dismiss();
                    }
                }
            });
        }else if(type==TYPE_CON_PAYMENT){
            selectPaymentAdapter = new SelectPaymentAdapter();
            mRecyclerView.setAdapter(selectPaymentAdapter);
            selectPaymentAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                    Object object=adapter.getData().get(position);
                    if(listener!=null){
                        listener.onClickConfirm(object,position);
                        dismiss();
                    }
                }
            });
        }
    }

    public void setCostTicketData(List<ConCostTicketShow> itemList) {
        if(selectCostTicketAdapter==null)return;
        selectCostTicketAdapter.setNewData(itemList);
    }

    public void setConPayment(List<ConPayment> itemList) {
        if(selectPaymentAdapter==null)return;
        selectPaymentAdapter.setNewData(itemList);
    }

    @Override
    public void dismiss() {
        WindowManager.LayoutParams lp = mContext.getWindow().getAttributes();
        lp.alpha = 1f;
        mContext.getWindow().setAttributes(lp);
        if (listener != null) {
            listener.dismiss();
        }
        unbinder.unbind();
        super.dismiss();
    }

    public View location;

    public void showAsDropDown(View anchor) {
        location = anchor;
        super.showAsDropDown(anchor);
    }

    public interface OnClickButtonListener {

        void onClickConfirm(Object object, int pos);

        void dismiss();
    }

    public void setOnClickButtonListener(OnClickButtonListener listener) {
        this.listener = listener;
    }


    private static class SelectCostTicketAdapter extends BaseQuickAdapter<ConCostTicketShow, YBaseViewHolder>{


        SelectCostTicketAdapter() {
            super(R.layout.item_select_item);
        }

        @Override
        protected void convert(YBaseViewHolder helper, ConCostTicketShow item) {
            helper.setText(R.id.tvContent,"开票金额："+item.getPayAmount()+"    "+item.getPayDate());
        }

    }

    private static class SelectPaymentAdapter extends BaseQuickAdapter<ConPayment, YBaseViewHolder>{


        SelectPaymentAdapter() {
            super(R.layout.item_select_item);
        }

        @Override
        protected void convert(YBaseViewHolder helper, ConPayment item) {
            helper.setText(R.id.tvContent,"到账金额："+item.getCpmAmount()+"    "+item.getCreatetime());
        }

    }

}
