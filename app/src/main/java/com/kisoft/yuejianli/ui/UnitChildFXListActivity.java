package com.kisoft.yuejianli.ui;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.UnitChildFXListAdapter;
import com.kisoft.yuejianli.adpter.interf.GlobalListener;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.UnitListBean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

// 分项
public class UnitChildFXListActivity extends BaseActivity {
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    public Integer mType;
    public Integer mEndType;

    private List<UnitListBean.ListDataBean.ChildListDataBean.FXChildListDataBean> mFXDatas = new ArrayList<>(); // 分部

    private UnitChildFXListAdapter mFXAdapter;

    public Integer getType() {
        return mType;
    }

    public void setType(Integer type) {
        mType = type;
    }

    public Integer getEndType() {
        return mEndType;
    }

    public void setEndType(Integer endType) {
        mEndType = endType;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mType = getIntent().getIntExtra("mType", 1);// 获取跳转类型
        mEndType = getIntent().getIntExtra("mEndType", 1);// 获取跳转类型
        mFXDatas =
                (List<UnitListBean.ListDataBean.ChildListDataBean.FXChildListDataBean>) getIntent().getSerializableExtra("data");
        initView();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_unit_list;
    }

    private void initView() {

        tvTitle.setText("分项工程");
        tvSubmit.setText("确定");
        tvSubmit.setVisibility(View.GONE);
        ivAction.setVisibility(View.GONE);
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            manager.setSmoothScrollbarEnabled(false);
            rvContent.setLayoutManager(manager);
            rvContent.setNestedScrollingEnabled(false);
        }

        mFXAdapter = new UnitChildFXListAdapter(R.layout.item_dangers_list, mFXDatas,
                new GlobalListener<UnitListBean.ListDataBean.ChildListDataBean.FXChildListDataBean>() {
            @Override
            public void onViewClick(int id, int position,
                                    UnitListBean.ListDataBean.ChildListDataBean.FXChildListDataBean model) {

            }

            @Override
            public void onRootViewClick(View view, int position,
                                        UnitListBean.ListDataBean.ChildListDataBean.FXChildListDataBean model) {
                if (model != null) {
                    // 分项工程
                    if (mType == 3) {
                        if (mEndType == mType) {
                            // 直接返回
                            Intent intent = new Intent();
                            intent.putExtra("data", model.getName());
                            intent.putExtra("data1", model.getRamark());
                            intent.putExtra("ids", model.getId());
                            intent.putExtra("businessType", model.getBusinessType());
                            Log.d("TAG", "onRootViewClick: " + model.toString());
                            setResult(Activity.RESULT_OK, intent);
                            finish();
                        }
                        if (mEndType > mType) {
                            // 跳转下一级  危大、隐蔽、旁站、抽检
                            // 抽检
                            if (mEndType == 4) {
                                // 跳转下一级  抽检
                                Intent intent = new Intent(mContext, UnitChildFXCJListActivity.class);
                                intent.putExtra("mType", 4);
                                intent.putExtra("mEndType", mEndType);
                                intent.putExtra("data", (Serializable) model.getChildMap().getC());
                                startActivityForResult(intent, Constant.REQUEST_CODE_UNIT_CHOUJIAN);
                            }
                            if (mEndType == 5) {
                                // 跳转下一级  隐蔽
                                Intent intent = new Intent(mContext, UnitChildFXOtherListActivity.class);
                                intent.putExtra("mType", 4);
                                intent.putExtra("mEndType", mEndType);
                                intent.putExtra("data", (Serializable) model.getChildMap().getY());
                                startActivityForResult(intent, Constant.REQUEST_CODE_UNIT_YINBI);
                            }
                            if (mEndType == 6) {
                                // 跳转下一级  旁站
                                Intent intent = new Intent(mContext, UnitChildFXOtherListActivity.class);
                                intent.putExtra("mType", 4);
                                intent.putExtra("mEndType", mEndType);
                                intent.putExtra("data", (Serializable) model.getChildMap().getP());
                                startActivityForResult(intent, Constant.REQUEST_CODE_UNIT_PANGZHAN);
                            }
                            if (mEndType == 7) {
                                // 跳转下一级  危大
                                Intent intent = new Intent(mContext, UnitChildFXOtherListActivity.class);
                                intent.putExtra("mType", 4);
                                intent.putExtra("mEndType", mEndType);
                                intent.putExtra("data", (Serializable) model.getChildMap().getW());
                                startActivityForResult(intent, Constant.REQUEST_CODE_UNIT_WEIDA);
                            }

                        }
                    }
                }
            }
        });
        rvContent.setAdapter(mFXAdapter);
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case Constant.REQUEST_CODE_UNIT_CHOUJIAN:
            case Constant.REQUEST_CODE_UNIT_PANGZHAN:
            case Constant.REQUEST_CODE_UNIT_WEIDA:
            case Constant.REQUEST_CODE_UNIT_YINBI:
                if (resultCode == Activity.RESULT_OK) {
                    setResult(Activity.RESULT_OK, data);
                    finish();
                }
                break;
        }
    }
}