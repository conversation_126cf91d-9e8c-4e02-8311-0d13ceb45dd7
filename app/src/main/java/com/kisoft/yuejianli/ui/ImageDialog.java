package com.kisoft.yuejianli.ui;

import android.app.Dialog;
import android.app.DialogFragment;
import android.app.FragmentManager;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;

import com.facebook.drawee.view.SimpleDraweeView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.ui.zoomable.ZoomableDraweeView;
import com.kisoft.yuejianli.utils.ImageLoadUtil;

import uk.co.senab.photoview.PhotoView;
import uk.co.senab.photoview.PhotoViewAttacher;

/**
 * Created by tudou on 2018/4/10.
 */

public class ImageDialog extends DialogFragment implements View.OnClickListener {


    private View mRootView;
    private View bg;
    private SimpleDraweeView imageView;
    private String imageUrl = "";


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        getDialog().setCanceledOnTouchOutside(true);
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        getDialog().getWindow().setBackgroundDrawableResource(android.R.color.transparent);

        WindowManager.LayoutParams lp = getDialog().getWindow().getAttributes();
        lp.gravity = Gravity.CENTER;
        getDialog().getWindow().setAttributes(lp);
        mRootView = inflater.inflate(R.layout.dialog_image, null);
        initView();
        return mRootView;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        setStyle(DialogFragment.STYLE_NO_TITLE, android.R.style.Theme_Holo_Dialog_MinWidth);
        super.onActivityCreated(savedInstanceState);
    }


    @Override
    public void onStart() {
        Dialog dialog = getDialog();
        if(dialog != null){
            DisplayMetrics dm = new DisplayMetrics();
            getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
            dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        }
        super.onStart();
    }

    private void initView() {
        bg = mRootView.findViewById(R.id.tv_bg);
        bg.setOnClickListener(this);
        ZoomableDraweeView ivPhoto = mRootView.findViewById(R.id.ivPhoto);
        imageView = mRootView.findViewById(R.id.iv_image);
        imageView.setImageURI(Uri.parse(imageUrl));
//        ImageLoadUtil.loading(getActivity(),imageUrl,R.drawable.ic_add_picture,ivPhoto);
//        PhotoView photoView = mRootView.findViewById(R.id.photoView);
//        ImageLoadUtil.loading(getActivity(),imageUrl,R.drawable.ic_add_picture,photoView);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tv_bg:
                dismiss();
                break;
            default:

                break;
        }
    }


    public void showImageDialog(FragmentManager manager, String imageUrl){
        this.imageUrl = imageUrl;
        show(manager, "image");
    }
}
