package com.kisoft.yuejianli.scgl;


import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.res.AssetManager;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.hdmh.HdmhMenuInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.ui.badgeview.YBadgeImageView;
import com.kisoft.yuejianli.ui.scgl.YLabelCell1;
import com.kisoft.yuejianli.ui.scgl.YLabelCell2;
import com.kisoft.yuejianli.utils.GsonUtil;
import com.kisoft.yuejianli.utils.PhoneUtil;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.views.ArrivalActivity;
import com.kisoft.yuejianli.views.ProjectChoseActivity;
import com.kisoft.yuejianli.views.PunchCardActivity;
import com.kisoft.yuejianli.views.WbsListActivity;

import org.jetbrains.annotations.NotNull;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 权限控制显示菜单
 */
public class ScglSupervisionControlActivity extends BaseActivity {

    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.tool_bar)
    Toolbar toolBar;

    @BindView(R.id.workPanel)
    LinearLayout workPanel;
    @BindView(R.id.projectPanel)
    LinearLayout projectPanel;

    @BindView(R.id.project_name)
    YLabelCell2 project_name;
    @BindView(R.id.construction_unit)
    YLabelCell1 construction_unit;
    @BindView(R.id.project_address)
    YLabelCell1 project_address;
    @BindView(R.id.contract_duration)
    YLabelCell1 contract_duration;

    // 公司管理
    @BindView(R.id.attendance)
    YBadgeImageView attendance;
    @BindView(R.id.wbs)
    YBadgeImageView wbs;
    @BindView(R.id.fangan)
    YBadgeImageView fangan;


    @BindView(R.id.mRecyclerView)
    RecyclerView mRecyclerView;
    boolean isShowProjectView, isShowWorkView = false;

    @BindView(R.id.mWorkRecyclerView)
    RecyclerView mWorkRecyclerView;
    private String mType;

    public static void launch(Activity activity) {
        Intent intent = new Intent(activity, ScglSupervisionControlActivity.class);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initProjectInfoView();
        initWorkRecyclerView();
        initProjectRecyclerView();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_permission_scgl_control;
    }

    private void initProjectInfoView() {
        tvTitle.setPadding(0, 0, PhoneUtil.dpTopx(this, 50), 0);
        if (SettingManager.getInstance().getProject() != null) {
            tvTitle.setText(SettingManager.getInstance().getProject().getProjectName());
        }
        ivAction.setVisibility(View.VISIBLE);
        ivAction.setImageResource(R.drawable.ic_project_list);

        ProjectInfo projectInfo = SettingManager.getInstance().getProject();
        project_name.getTvTitle().setText(projectInfo.getProjectName());
        construction_unit.getTvContent().setText(projectInfo.getOwnerName());
        project_address.getTvContent().setText(projectInfo.getPjAddress());
        contract_duration.getTvContent().setText(projectInfo.getConPeriod());
    }

    //到岗情况
    @OnClick({R.id.ll_num, R.id.attendance, R.id.wbs})
    public void onViewClicked(View view) {
        if (view.getId() == R.id.ll_num) {
            //到岗情况
            Intent intent1 = new Intent(mContext, ArrivalActivity.class);
            Bundle bundle = new Bundle();
            bundle.putString("headCount", "3");
            bundle.putString("leaveCount", "3");
            bundle.putString("errCount", "3");
            bundle.putString("workCount", "3");
            intent1.putExtras(bundle);
            startActivity(intent1);
        } else if (view.getId() == R.id.attendance) {
            // 打卡
            Intent intent = new Intent();
            intent.setClass(this, PunchCardActivity.class);
            startActivity(intent);
        } else if (view.getId() == R.id.wbs) {
            // wbs
            Intent intent = new Intent(mContext, WbsListActivity.class);
            startActivity(intent);
        }
    }

    // 公司管理
    private void initCompanyManagementView() {

    }

    @SuppressLint("ClickableViewAccessibility")
    private void initWorkRecyclerView() {

        String jsonData = getJson(this, "scglwork.json");
        //        解决 泛型<T>不能强转为List
        List<Map<String, Object>> maps = GsonUtil.GsonToListMaps(jsonData);
        List<ScglMenuInfo> ScglMenuInfos = GsonUtil.jsonToList(jsonData, ScglMenuInfo.class);

        if (mWorkRecyclerView.getLayoutManager() == null) {
            GridLayoutManager layoutManager = new GridLayoutManager(this, 3) {
                @Override
                public boolean canScrollVertically() {
                    return false;
                }
            };
//            GridLayoutManager layoutManager = new GridLayoutManager(this, 3);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            mWorkRecyclerView.setLayoutManager(layoutManager);
        }

        MenuInfoAdapter permissionTypeAdapter = new MenuInfoAdapter(R.layout.item_project_content,
                ScglMenuInfos);
        mWorkRecyclerView.setAdapter(permissionTypeAdapter);
        permissionTypeAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                Object object = adapter.getData().get(position);
                if (object instanceof ScglMenuInfo) {
                    ScglMenuInfo item = (ScglMenuInfo) object;
                    if (!StringUtil.isEmpty(item.getClassName())) {
                        launchActivity(ScglSupervisionControlActivity.this,item.getClassName(),item);
                    } else
                    if (item.isApply()) {
                        // 申请表单
                        ScglPublicApplyFormViewListActivity.launch(ScglSupervisionControlActivity.this,item);
                    } else {
                        // 普通表单
                        ScglPublicViewListActivity.launch(ScglSupervisionControlActivity.this, item);
                    }
                }
            }
        });
        workPanel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isShowWorkView) {
                    mWorkRecyclerView.setVisibility(View.VISIBLE);
                } else {
                    mWorkRecyclerView.setVisibility(View.GONE);
                }
                isShowWorkView = !isShowWorkView;
            }
        });
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initProjectRecyclerView() {

        String jsonData = getJson(this, "scglproject.json");
        //        解决 泛型<T>不能强转为List
        List<Map<String, Object>> maps = GsonUtil.GsonToListMaps(jsonData);
        List<ScglMenuInfo> ScglMenuInfos = GsonUtil.jsonToList(jsonData, ScglMenuInfo.class);

        if (mRecyclerView.getLayoutManager() == null) {
            GridLayoutManager layoutManager = new GridLayoutManager(this, 3) {
                @Override
                public boolean canScrollVertically() {
                    return false;
                }
            };
//            GridLayoutManager layoutManager = new GridLayoutManager(this, 3);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            mRecyclerView.setLayoutManager(layoutManager);
        }

        MenuInfoAdapter permissionTypeAdapter = new MenuInfoAdapter(R.layout.item_project_content,
                ScglMenuInfos);
        mRecyclerView.setAdapter(permissionTypeAdapter);
//        permissionTypeAdapter.setOnItemClickListener(this);
        permissionTypeAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                Object object = adapter.getData().get(position);
                if (object instanceof ScglMenuInfo) {
                    ScglMenuInfo info = (ScglMenuInfo) object;
                    List<ScglMenuInfo> childList = info.getChildList();

                    if (!StringUtil.isEmpty(info.getClassName())) {
                        launchActivity(ScglSupervisionControlActivity.this,info.getClassName(),info);
                    } else {

                        ScglSupervisionMenuListActivity.launch(ScglSupervisionControlActivity.this, childList,
                                info.getTitle());
                    }
                }
            }
        });

        projectPanel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isShowProjectView) {
                    mRecyclerView.setVisibility(View.VISIBLE);
                } else {
                    mRecyclerView.setVisibility(View.GONE);
                }
                isShowProjectView = !isShowProjectView;
            }
        });
    }

    private void launchActivity(Activity activity, String name, ScglMenuInfo item){
        try {
            Intent intent = new Intent(Intent.ACTION_MAIN);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.putExtra("ScglMenuInfo", item);
            ComponentName cn =new ComponentName(activity.getPackageName(),name);
            intent.setComponent(cn);
            activity.startActivity(intent);
        }catch (Exception e){
            e.printStackTrace();
        }
    }


    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @OnClick(R.id.iv_action)
    public void rightClick() {
        Intent intent = new Intent();
        intent.setClass(this, ProjectChoseActivity.class);
        startActivityForResult(intent, Constant.REQUEST_CODE_CHANGE_PEOJECT);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Constant.REQUEST_CODE_CHANGE_PEOJECT) {
            tvTitle.setText(SettingManager.getInstance().getProject().getProjectName());
        }
    }

    private String getJson(Context context, String fileName) {
        StringBuffer stringBuffer = new StringBuffer();
        try {
            AssetManager assetsManager = context.getAssets();
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(assetsManager.open(fileName)));
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuffer.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return stringBuffer.toString();
    }


    public class MenuInfoAdapter extends BaseQuickAdapter<ScglMenuInfo, BaseViewHolder> {
        public MenuInfoAdapter(int layoutResId,
                               @Nullable @org.jetbrains.annotations.Nullable List<ScglMenuInfo> data) {
            super(layoutResId, data);
        }

        @Override
        protected void convert(@NonNull @NotNull BaseViewHolder helper, ScglMenuInfo item) {
            String image = item.getImage();
            if (StringUtil.isEmpty(image)) {
                image = "ic_customer_manager";
            }

            // 获取资源ID
            int resId = StringUtil.getResource(image, StringUtil.RES_TYPE_DRAWABLE);

            // 检查资源ID是否有效
            if (resId <= 0) {
                // 如果无效，使用默认图标
                resId = R.drawable.ic_customer_manager;
            }

            TextView tvContent = helper.itemView.findViewById(R.id.tv_content);
            Drawable contentIc = ContextCompat.getDrawable(YueApplacation.mContext, resId);

            if (contentIc != null) {
                contentIc.setBounds(0, 0, contentIc.getMinimumWidth(), contentIc.getMinimumHeight());
                tvContent.setCompoundDrawables(null, contentIc, null, null);
            }

            tvContent.setText(item.getTitle());
        }
    }
}
