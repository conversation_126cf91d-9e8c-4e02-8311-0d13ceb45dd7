package com.kisoft.yuejianli.scgl;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.interf.GlobalListener;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ComPanyOrgInfo;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.FunctionPermission;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.entity.event.BeanEvent;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.ui.YSubmitCell;
import com.kisoft.yuejianli.ui.scgl.YFileListItemView;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.GsonUtil;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.views.CompanyOrgInfoActivity;
import com.kisoft.yuejianli.views.WebActivity;
import com.kisoft.yuejianli.views.watermark.ImageUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/22.
 */
public class ScglPublicViewDetailActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;

    @BindView(R.id.rv_content)
    RecyclerView mRecyclerView;

    @BindView(R.id.y_file_list)
    YFileListItemView mFileView;

    @BindView(R.id.submit_cell)
    YSubmitCell submitCell;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    private String bType = "";
    private String bId = "";
    private String formKey = "";
    boolean noFile;
    Map<String, String> systemItemMap;// 获取
    Map<String, Object> mDataMap = new HashMap<>();
    boolean isAdd = false;
    private ScglPublicViewDetailAdapter mAdapter;

    private List<Map<String, Object>> mData = new ArrayList<>();
    private String orgNameKey;
    private String orgIdKey;

    private String personKey;
    private String personIdKey;

    private boolean mDateFormat = false;
    private Map<String, Object> mPageData;
    boolean modify; // 是否可以修改

    @Override
    public int getLayoutId() {
        return R.layout.activity_scgl_public_view_detail;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this);
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().unregister(this);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getIntent() != null) {
            bType = getIntent().getStringExtra("btype");
            isAdd = getIntent().getBooleanExtra("isAdd", false);
            modify = getIntent().getBooleanExtra("modify", false);
            if (!isAdd) {
                bId = getIntent().getStringExtra("bId");
                mDataMap = (Map<String, Object>) getIntent().getSerializableExtra(
                        "ScglPublicViewDetailData");
            }
        }
        mPageData = ScglPageConfigData.getPageData(bType,"");
        mData = (List<Map<String, Object>>) mPageData.get("data");
        initData();
        initView();
        if (noFile) {
        } else {
            fileMethod();
        }
    }

    private void initView() {
        tvTitle.setText(mPageData.get("pageTitle").toString());

        if (mRecyclerView.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            mRecyclerView.setLayoutManager(manager);
        }
        boolean isShow = false;
        if (isAdd || modify) {
            isShow = true;
        } else {
            isShow = false;
        }

        mAdapter = new ScglPublicViewDetailAdapter(mData, this, isShow, mDataMap, new GlobalListener() {
            @Override
            public void onViewClick(int id, int position, Object model) {
                Log.i("TAG", "onViewClick: " + "id==" + id + "position ==" + position + "model ==" + model);
            }

            @Override
            public void onRootViewClick(View view, int position, Object model) {
                mDataMap = (Map) model;
                Log.i("TAG", "onViewClick: " + "position ==" + position + "model ==" + model);
            }
        });
        mRecyclerView.setAdapter(mAdapter);
        mAdapter.setSelectTextViewListener(new ScglPublicViewDetailAdapter.SelectTextViewListener() {
            @Override
            public void skipType(String type, String key, String extra) {
                if (type.equals("Depart")) {
                    selectDepartment(key, extra);
                } else if (type.equals("Person")) {
                    selectPerson(key, extra);
                }
            }
        });

        submitCell.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                uploadMulFile();
            }
        });
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        if (isAdd) {
            for (Map map : mData) {
                String key = map.get("key").toString();
                mDataMap.put(key, "");
            }
            // 申请页面
            userInfo = SettingManager.getInstance().getUserInfo();
            projectInfo = SettingManager.getInstance().getProject();

            mDataMap.put("createTime", DateUtil.dateToString(new Date(), DateUtil.YMD_HMS));
            mDataMap.put("projectName", projectInfo.getProjectName());
            mDataMap.put("projectId", projectInfo.getProjectId());
            mDataMap.put("createId", userInfo.getId());
            mDataMap.put("createName", userInfo.getName());
            mDataMap.put("deptName", userInfo.getFatherName());
            mDataMap.put("deptId", userInfo.getFather());
            mDataMap.put("companyId", userInfo.getCompanyId());
            mDataMap.put("companyName", userInfo.getCompanyName());
//            if (systemItemMap != null) {
//                String spkey = systemItemMap.get("spkey");
//                int index = Integer.parseInt(systemItemMap.get("index"));
//                if (SettingManager.getInstance().getFormParamType(spkey) != null && SettingManager.getInstance()
//                .getFormParamType(spkey).size() > 0) {
//                    List<ApplyType> infos = SettingManager.getInstance().getFormParamType(spkey);
//                    mData.get(index).put("SystemItemModel", infos);
//                }
//            }
        } else {
            // 查看详情

            getFileList(bId);
            mDataMap.put("modifyId", userInfo.getId());
            mDataMap.put("modifyName", userInfo.getName());
            mDataMap.put("modifyTime", DateUtil.dateToString(new Date(), DateUtil.YMD_HMS));
            formKey = bId;
            submitCell.setVisibility(View.GONE);
            if (modify) {
                submitCell.setVisibility(View.VISIBLE);
            } else {
                submitCell.setVisibility(View.GONE);
                tvSubmit.setVisibility(View.GONE);
            }

//            if (!StringUtil.isEmpty(bId) && !modify) {
//                if (!StringUtil.isEmpty(mPageData.get("updateUrl") + "")) {
//                    tvSubmit.setText("修改");
//                    FunctionPermission permissionFunction = SettingManager.getInstance().getPermissionFunction();
//                    if (permissionFunction != null && permissionFunction.isHasUpdateFunction()) {
//                        tvSubmit.setVisibility(View.VISIBLE);
//                    }
//                    tvSubmit.setOnClickListener(new View.OnClickListener() {
//                        @Override
//                        public void onClick(View v) {
//                            // 修改
//                            finish();
//                            getIntent().putExtra("modify", true);
//                            startActivity(getIntent());
//                        }
//                    });
//                }
//            }
        }
    }

    private void selectDepartment(String key, String extra) {
        this.orgNameKey = key;
        this.orgIdKey = extra;

        Intent intent = new Intent(this, CompanyOrgInfoActivity.class);
        intent.putExtra(CompanyOrgInfoActivity.KEY_SELECT_DEPARTMENT, true);
        startActivity(intent);
    }


    private void selectPerson(String key, String extra) {
        this.personKey = key;
        this.personIdKey = extra;

        Intent intent = new Intent();
        intent.putExtra("isSingle", false);
        intent.setClass(this, CompanyOrgInfoActivity.class);
        startActivityForResult(intent, Constant.REQEST_CODE_SELECT_COMPANY_ORG_INFO);
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void selectOrg(BeanEvent beanEvent) {
        if (beanEvent != null && BeanEvent.TYPE_COMPANY_ORG.equals(beanEvent.getDataType())) {
            ComPanyOrgInfo comPanyOrgInfo = (ComPanyOrgInfo) beanEvent.getData();
            Log.i("Subscribe", "selectOrg: " + comPanyOrgInfo);
            mDataMap.put(orgNameKey, comPanyOrgInfo.getName());
            mDataMap.put(orgIdKey, comPanyOrgInfo.getId());
            mAdapter.notifyDataSetChanged();
        } else if (beanEvent != null && BeanEvent.TYPE_COMPANY_PERSON.equals(beanEvent.getDataType())) {
            List<ComPanyOrgInfo> comPanyOrgInfo = (List<ComPanyOrgInfo>) beanEvent.getData();
            Log.i("TAG", "selectOrg: " + comPanyOrgInfo);
        }
    }


    //提交
    private void sendData(Map param) {
        showProgress();
//        pramaras.put("data", StringUtil.objectToJson(param));
        Map<String, Object> pramaras = new HashMap<>();
        String url = "";
        try {
            pramaras.put("data", URLEncoder.encode(StringUtil.objectToJson(param), "UTF-8"));
            if (isAdd) {
                url = SettingManager.getInstance().getBaseUrl() + mPageData.get("saveUrl").toString();
            } else {
                url = SettingManager.getInstance().getBaseUrl() + mPageData.get("updateUrl").toString();
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        OkHttpRequestManager.getInstance().post(url, pramaras, new IRequestCallback() {
            @Override
            public void onSuccess(String response) {
                Log.i("TAG", "onSuccess: " + response);
//                Map<String, Object> map = GsonUtil.GsonToMaps(response);
//                Map<String, Object> map1 = (Map<String, Object>) map.get("data");
//                mList = (List<Map<String, Object>>) map1.get("data");
                try {
                    Map<String, Object> map = GsonUtil.GsonToMaps(response);
                    int code = (int) Double.parseDouble(map.get("code").toString());
                    if (code == 200) {
                        finish();
                    } else {
                        showToast(map.get("message").toString());
                    }
                }catch (Exception e) {
                    e.printStackTrace();
                }
                dismissProgress();
            }

            @Override
            public void onFailure(Throwable throwable) {
                throwable.printStackTrace();
                dismissProgress();
            }
        });

    }

    //获取详情
    private void getData() {

    }


    private ArrayList<EnclosureListDto> mFileList = new ArrayList<>();
    private boolean isTakePhoto;
    private BGAPhotoHelper mPhotoHelper;

    private void fileMethod() {
        mFileView.setApply(isAdd);
        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);
//        // 获取主键
        getPrimarykey();

        // 点击添加附件
        mFileView.getAddFileBtn().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 最多添加9个附件
                if (mFileList.size() >= 9) {
                    return;
                }
                showFileSelect();
            }
        });
        // 删除附件
        mFileView.setFileOperateContract(new YFileListItemView.FileOperateContract() {
            @Override
            public void openFile(int index) {
                mFileList.remove(index);
                mFileView.setList(mFileList);
            }
        });
        // 打开附件
        mFileView.setOpenFileContract(new YFileListItemView.OpenFileContract() {
            @Override
            public void openFile(EnclosureListDto dto) {
                WebActivity.launch(ScglPublicViewDetailActivity.this, dto);
            }
        });
    }

    private void showFileSelect() {
        // 空照片 ，添加
        String[] str = new String[]{"系统相机", "手机相册", "手机文件"};
        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
        ab.setCancelable(true);
        ab.setItems(str, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                switch (which) {
                    case 0://相机
                        isTakePhoto = true;
                        takePhoto();
                        break;
                    case 1://相册
                        isTakePhoto = false;
                        getPhoto();
                        break;
                    case 2://手机文件
                        getFile();
                        break;
                }
            }
        });
        ab.show();
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), Constant.REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), Constant.REQUEST_CODE_TAKE_PHOTO);
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    /**
     * 打开文件选择器
     */
    private void getFile() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("*/*");// 任意类型文件
        this.startActivityForResult(intent, Constant.REQUEST_CODE_FILE_SELECT);
    }

    BGAPhotoHelper1 mPhotoHelper1 = new BGAPhotoHelper1();

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQUEST_CODE_CHOOSE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_TAKE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800, 800),
                            Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_CROP) {
            if (resultCode == Activity.RESULT_OK) {
                // getCropIntent  获取裁剪完图片的路径
                String photoPath = mPhotoHelper1.getCropFilePath();
//                showToast(photoPath);
                File file = new File(mPhotoHelper1.getCropFilePath());
                double fileSize = FileUtil.getFileOrFilesSize(photoPath, 3);
                Log.i("fileSize", fileSize + "");

                //showToast(photoPath);
                if (fileSize > 1) {
                    ImageUtil.compressImageByLuban(photoPath);
                    String s = ImageUtil.compressImage(photoPath);
                    mFileList.add(new EnclosureListDto(file.getName(), s));
                } else {
                    mFileList.add(new EnclosureListDto(file.getName(), photoPath));
                }
                mFileView.setList(mFileList);
            } else {
                mPhotoHelper.deleteCameraFile();
                mPhotoHelper.deleteCropFile();
            }
        } else if (requestCode == Constant.REQUEST_CODE_FILE_SELECT) {
            String path = "";
            if (data == null) {
                // 用户未选择任何文件，直接返回
                return;
            }
            path = FileUtil.getRealPath(this, data.getData());
            File file = new File(path);
            mFileList.add(new EnclosureListDto(file.getName(), path));
            mFileView.setList(mFileList);
//            showToast(path);
        } else if (requestCode == Constant.REQEST_CODE_SELECT_COMPANY_ORG_INFO) {
            if (data != null) {
                List<String> ids = new ArrayList<>();
                List<String> names = new ArrayList<>();
                ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                if (names != null && names.size() > 0) {
                    String idStr = TextUtils.join(",", ids);
                    String nameStr = TextUtils.join(",", names);
                    mDataMap.put(personKey, nameStr);
                    mDataMap.put(personIdKey, idStr);
                    mAdapter.notifyDataSetChanged();
                }
            }
        }
    }

    private void uploadMulFile() {
        if (mFileList.isEmpty()) {
            sendData(mDataMap);
        } else {
            if (formKey.isEmpty()) {
                getPrimarykey();
                showToast("未获取到主键");
                return;
            }
            mDataMap.put("id", formKey);
            showProgress();
            String s = DateUtil.dateToString(new Date(), DateUtil.YM1);
            HashMap<String, String> paras = new HashMap<>();
            paras.put("filePath", mPageData.get("filePath").toString() + s);
            paras.put("businessId", formKey);
            paras.put("businessType", bType);
            Log.i("TAG", "uploadMulFile: " + paras.toString());
            OkHttpRequestManager.getInstance().uploadMulFile(mFileList, paras, new IRequestCallback() {
                @Override
                public void onSuccess(String response) {
                    Log.i("upload", "onSuccess: ");
                    dismissProgress();
                    sendData(mDataMap);
                }

                @Override
                public void onFailure(Throwable throwable) {
                    Log.i("upload", "onFailure: ");
                    dismissProgress();
                }
            });
        }
    }

    // 获取附件列表
    private void getFileList(String bt) {
        HashMap<String, Object> paras = new HashMap<>();
        paras.put("businessId", bt);
        paras.put("businessType", bType);
        Api.getGbkApiserver().getEnclosureList(Constant.HTTP_GET_ENCLOSURE_LIST, paras).enqueue(new Callback<NetworkResponse<List<EnclosureListDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<EnclosureListDto>>> call,
                                   Response<NetworkResponse<List<EnclosureListDto>>> response) {
                if (response.body().getData() != null) {
                    mFileList.clear();
                    mFileList.addAll(response.body().getData());
                    mFileView.setList(mFileList);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<EnclosureListDto>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }


    // 获取UUID
    protected void getPrimarykey() {
        String busId = "";
        Api.getGbkApiserver().testApi("getUUID", new HashMap<>()).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    formKey = response.body().getData().toString();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }


    private void getSystemItemWithTypeCode(String spkey, Integer index) {

        /*if (SettingManager.getInstance().getFormParamType(spkey) != null && SettingManager.getInstance()
        .getFormParamType(spkey).size() > 0) {
            List<ApplyType> infos = SettingManager.getInstance().getFormParamType(spkey);
            mData.get(index).put("SystemItemModel", infos);
            mAdapter.setData(mData);
        }*/
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("typeCode", code);
//        Api.getGbkApiserver().getLeaveType(Constant.HTTP_GET_LEAVE_TYPE, parameters).enqueue(new
//        Callback<NetworkResponse<List<ApplyType>>>() {
//            @Override
//            public void onResponse(Call<NetworkResponse<List<ApplyType>>> call,
//                                   Response<NetworkResponse<List<ApplyType>>> response) {
//                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
//                    List<ApplyType> data = response.body().getData();
//                    mData.get(index).put("SystemItemModel", data);
//                    mAdapter.setData(mData);
////                    }
//                }
//            }
//
//            @Override
//            public void onFailure(Call<NetworkResponse<List<ApplyType>>> call, Throwable t) {
//
//            }
//        });
    }


}
