package com.kisoft.yuejianli.scgl;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse2;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.AppPermission;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.ui.scgl.YLabelCell;
import com.kisoft.yuejianli.ui.scgl.YLabelCell1;
import com.kisoft.yuejianli.utils.GsonUtil;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.views.EnvironmentalInspectionActivity;
import com.kisoft.yuejianli.views.ProjectModelCommonDetailActivity;
import com.kisoft.yuejianli.views.ProjectModelCommonDetailData;
import com.kisoft.yuejianli.views.ProjectModelCommonDetailWebActivity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

public class ScglPublicViewListActivity extends BaseActivity implements BaseRefreshListener {

    //    @BindView(R.id.iv_back)
//    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    //    @BindView(R.id.tool_bar)
//    Toolbar toolBar;
    @BindView(R.id.mRecyclerView)
    RecyclerView mRecyclerView;
    @BindView(R.id.prLayout)
    PullToRefreshLayout prLayout;

//    private boolean mXmkn = false;
//    private String selectType;

    private View empotyView;
    private ProjectSceneListAdapter mAdapter;
    private List<Map<String, Object>> mList = new ArrayList<>();
    ScglMenuInfo mPermission;
    private List<Map<String, Object>> mListConfigData = new ArrayList<>();

    private int page = 1;
    private int pageSize = 10;
    private int total = 0;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    private String mListUrl;

    public static void launch(Activity activity, ScglMenuInfo item) {
        Intent intent = new Intent(activity, ScglPublicViewListActivity.class);
        intent.putExtra("ScglPublicViewListData", item);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getIntent() != null) {
            mPermission = (ScglMenuInfo) getIntent().getSerializableExtra("ScglPublicViewListData");
        }
        initData();
        initView();
    }

    private void initView() {
        tvSubmit.setText("新增");
        tvSubmit.setVisibility(View.VISIBLE);
        // 周报
        if (StringUtil.isEqual(mPermission.getBusinessType(),"T_PROD_WEEKLY_REPORT")){
            tvSubmit.setVisibility(View.GONE);
        }
        // 项目季度、年终考评
        if (StringUtil.isEqual(mPermission.getBusinessType(),"T_PROJ_ASSESSMENT_SCORE")){
            tvSubmit.setVisibility(View.GONE);
        }

        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        if (mRecyclerView.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            mRecyclerView.setLayoutManager(manager);
        }
        mAdapter = new ProjectSceneListAdapter(mList,mListConfigData);
        mAdapter.setEmptyView(empotyView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                goDetail(mList.get(position), false);
            }
        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                mRecyclerView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        page++;
                        if (mList.size() >= total) {
                            mAdapter.loadMoreEnd();
                        } else {
                            getData();
                        }
                    }
                }, 1000);
            }
        }, mRecyclerView);
        mRecyclerView.setAdapter(mAdapter);
        prLayout.setRefreshListener(this);
        refresh();
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        mListUrl =
                ScglPageConfigData.getPageData(mPermission.getBusinessType(),mPermission.getTitle()).get("listUrl").toString();
        tvTitle.setText(mPermission.getTitle());

        // 设置列表展示字段
        List<Map<String, Object>> pageData = (List<Map<String, Object>>) ScglPageConfigData.getPageData(mPermission.getBusinessType(),mPermission.getTitle()).get("data");
        // 获取pageData中map中showList为true的map
        if (pageData != null && pageData.size() > 0){
            for (Map<String, Object> map : pageData) {
                if (map.containsKey("showList") && (boolean) map.get("showList")) {
                    mListConfigData.add(map);
                }
            }
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_project_scene_list;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @OnClick(R.id.tv_submit)
    public void add() {
        goDetail(null, true);
    }

    private void goDetail(Map<String, Object> map, boolean isAdd) {

        String primaryKey = ScglPageConfigData.getPageData(mPermission.getBusinessType(),mPermission.getTitle()).get("primaryKey").toString();


        if (StringUtil.isEqual(mPermission.getBusinessType(),"T_PROD_WEEKLY_REPORT")){
            // 周报
            Intent intent = new Intent();
            intent.setClass(this, ProjectModelCommonDetailWebActivity.class);
            String ids = map.get("id").toString();
            String urlStr = SettingManager.getInstance().getBaseUrl() + "/pds/ProdWeeklyReportAction.do?method=toProdWeeklyReportModify&pid=2&ids=" + ids +"&userId="+ userInfo.getId() +"&projectId="+ userInfo.getProjectId();

            intent.putExtra("url",urlStr);
            intent.putExtra("bid",ids);
            intent.putExtra("bType",mPermission.getBusinessType());
            startActivity(intent);
        } else if (StringUtil.isEqual(mPermission.getBusinessType(),"T_PROJ_ASSESSMENT_SCORE")){
            // 项目季度、年终考评
            Intent intent = new Intent();
            intent.setClass(this, ProjectModelCommonDetailWebActivity.class);
            String ids = map.get("id").toString();
            String urlStr = SettingManager.getInstance().getBaseUrl() + "/pds/ProjAssessmentScoreAction.do?method=toProjAssessmentScoreModify&pid=2&ids="+ ids +"&userId="+ userInfo.getId() +"&projectId="+ userInfo.getProjectId();
            intent.putExtra("url",urlStr);
            intent.putExtra("bid",ids);
            intent.putExtra("bType",mPermission.getBusinessType());
            startActivity(intent);
        }else if (StringUtil.isEqual(mPermission.getBusinessType(),"T_PROD_ENVIRONMENTAL_CHECK")){
            // 环保检查记录
            Intent intent = new Intent();
            intent.setClass(this, EnvironmentalInspectionActivity.class);
            intent.putExtra("isAdd", isAdd);
            intent.putExtra("btype", mPermission.getBusinessType());
            if (!isAdd) {
                intent.putExtra("EnvironmentalInspectionActivityData", (Serializable) map);
                intent.putExtra("bId", map.get("id").toString());
            }
            startActivity(intent);
        }else {
            Intent intent = new Intent();
            intent.setClass(this, ScglPublicViewDetailActivity.class);
            intent.putExtra("isAdd", isAdd);
            intent.putExtra("btype", mPermission.getBusinessType());
            if (!isAdd) {
                intent.putExtra("ScglPublicViewDetailData", (Serializable) map);
                intent.putExtra("bId", map.get(primaryKey).toString());
            }
            startActivity(intent);
        }
    }

    @Override
    public void refresh() {
        page = 1;
        getData();
    }

    @Override
    public void loadMore() {
        page++;
        getData();
    }

    private void getData() {
        String p = Integer.toString(page);
        String z = Integer.toString(pageSize);
        Map<String, Object> pram = new HashMap<>();
        pram.put("projectId", projectInfo.getProjectId());
        pram.put("page", p);
        pram.put("limit", z);
        String url = SettingManager.getInstance().getBaseUrl() + mListUrl;
        OkHttpRequestManager.getInstance().post(url, pram, new IRequestCallback() {
            @Override
            public void onSuccess(String response) {
                prLayout.finishRefresh();
                prLayout.finishLoadMore();
                Log.i("TAG", "onSuccess: " + response);
//                Map<String, Object> map = GsonUtil.GsonToMaps(response);
//                Map<String, Object> map1 = (Map<String, Object>) map.get("data");
//                mList = (List<Map<String, Object>>) map1.get("data");
                try {
                    NetworkResponse2 response2 = GsonUtil.GsonToBean(response, NetworkResponse2.class);
                    NetworkResponse2.NetworkResponse2Data data = response2.getData();
                    total = data.getTotal();
                    if (page == 1) {
                        mList = data.getData();
                    } else {
                        mList.addAll(data.getData());
                    }
                    mAdapter.setNewData(mList);
                }catch (Exception e){
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(Throwable throwable) {
                throwable.printStackTrace();
                prLayout.finishRefresh();
                prLayout.finishLoadMore();
            }
        });
    }

    // 信息管理
    public class ProjectSceneListAdapter extends BaseQuickAdapter<Map<String, Object>, BaseViewHolder> {
        private final List<Map<String, Object>> mConfigData;

        public ProjectSceneListAdapter(List<Map<String, Object>> data, List<Map<String, Object>> mListConfigData) {
            super(R.layout.cell_card, data);
            this.mConfigData = mListConfigData;
        }

        @Override
        protected void convert(BaseViewHolder helper, Map item) {
            TextView tvPlanTitle = helper.itemView.findViewById(R.id.tv_view_title);
            tvPlanTitle.setText(mPermission.getTitle());
            RecyclerView rv_card = helper.itemView.findViewById(R.id.rv_card);
            rv_card.setLayoutManager(new LinearLayoutManager(mContext,LinearLayoutManager.VERTICAL,false));
            try {
                List<Map<String, Object>> cardCellData = new ArrayList<>();
                for (Map<String, Object> configDatum : mConfigData) {
                    Map<String, Object> cardCell = new HashMap<>();

                    String title = configDatum.get("title").toString();
                    String content = item.get(configDatum.get("key").toString()).toString();
                    cardCell.put("title",title);
                    cardCell.put("content",content);
                    cardCellData.add(cardCell);
                }

                rv_card.setAdapter(new CardCellAdapter(cardCellData));
            }catch (Exception e){
                e.printStackTrace();
            }
        }



//        @Override
//        protected void convert(BaseViewHolder helper, Map item) {
//
//            TextView tvPlanTitle = helper.itemView.findViewById(R.id.tv_view_title);
//            YLabelCell1 label1 = helper.itemView.findViewById(R.id.label1);
//            YLabelCell1 label2 = helper.itemView.findViewById(R.id.label2);
//            try {
//                if(StringUtil.isEqual("T_PROD_WEEKLY_REPORT_SUMMARY",mPermission.getBusinessType())) {
//                    String year = item.get("year").toString() + "年";
//                    String month = item.get("month").toString();
//                    String week = item.get("week").toString();
//                    tvPlanTitle.setText(year + month + week);
//                }else{
//                    tvPlanTitle.setText(mPermission.getTitle());
//                }
//                label1.getTvTitle().setText("旁站人员");
//                label1.getTvContent().setText(item.get("createName").toString());
//
//                label2.getTvTitle().setText("旁站时间");
//                label2.getTvContent().setText(item.get("createTime").toString());
//            }catch (Exception e){
//                e.printStackTrace();
//            }
//        }
    }

    public class CardCellAdapter extends BaseQuickAdapter<Map<String, Object>, BaseViewHolder> {
        public CardCellAdapter(List<Map<String, Object>> data) {
            super(R.layout.cell_label_3, data);
        }

        @Override
        protected void convert(BaseViewHolder helper, Map item) {
            TextView tv_title = helper.itemView.findViewById(R.id.tv_label_title);
            TextView tv_content = helper.itemView.findViewById(R.id.tv_label_content);

            tv_title.setText(item.get("title").toString());
            tv_content.setText(item.get("content").toString());
        }
    }
}
