package com.kisoft.yuejianli.scgl;

import static com.kisoft.yuejianli.im.common.utils.ToastUtils.showToast;

import android.annotation.SuppressLint;
import android.app.DatePickerDialog;
import android.content.Context;
import android.text.Editable;
import android.text.InputType;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.bigkoo.pickerview.TimePickerView;
import com.bigkoo.pickerview.lib.WheelView;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.adpter.interf.GlobalListener;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.PhoneUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;

public class ScglPublicViewDetailAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    List<Map<String,Object>> mData;
    Context mContext;
    GlobalListener globalListener;
    Map mDataMap;
    boolean isApply;
    private TimePickerView tpv;

//    Handler handler;
    private ArrayAdapter<String> mArrayAdapter;
    private ArrayAdapter<ApplyType> mItemAdapter;
    private SelectTextViewListener mSelectTextViewListener;

    public ScglPublicViewDetailAdapter(List<Map<String,Object>> data, Context context, boolean isApply,
                                       Map<String, Object> dataMap,
                                       GlobalListener globalListener) {
        this.mData = data;
        mContext = context;
        this.isApply = isApply;
        this.mDataMap = dataMap;
        this.globalListener = globalListener;
//        this.handler = handler;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == Constant.VIEWTYPE_SUBMIT_CELL) {
            return new SubmitView(View.inflate(mContext, R.layout.cell_submit, null));
        }
        if (viewType == Constant.VIEWTYPE_LABEL_CELL) {
            return new LabelViewSingle(View.inflate(mContext, R.layout.cell_label, null));
        }
        if (viewType == Constant.VIEWTYPE_LABEL_CELL_2) {
            return new LabelViewSingle2(View.inflate(mContext, R.layout.cell_label_2, null));
        }
        if (viewType == Constant.VIEWTYPE_LABELVIEW_CELL) {
            return new LabelViewMult(View.inflate(mContext, R.layout.cell_label_mult, null));
        }
        if (viewType == Constant.VIEWTYPE_TEXTFIELD_CELL) {
            return new TextFieldView(View.inflate(mContext, R.layout.cell_textfield, null));
        }
        if (viewType == Constant.VIEWTYPE_TEXTVIEW_CELL) {
            return new TextFieldMulView(View.inflate(mContext, R.layout.cell_textview, null));
        }
        if (viewType == Constant.VIEWTYPE_SELECTFIELD_CELL) {
            return new SelectView(View.inflate(mContext, R.layout.cell_select, null));
        }
        if (viewType == Constant.VIEWTYPE_DATESEL_CELL) {
            return new DateSelectView(View.inflate(mContext, R.layout.cell_date, null));
        }

        if (viewType == Constant.VIEWTYPE_SELECTVIEW_CELL) {
            return new SelectTextView(View.inflate(mContext, R.layout.cell_select_textview, null));
        }
        return null;
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, @SuppressLint("RecyclerView") int position) {
        Map map = mData.get(position);
        if (map.get("viewType").equals(Constant.VIEWTYPE_SUBMIT_CELL)) {
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (globalListener != null) {
                        globalListener.onRootViewClick(view, position, mDataMap);
                    }
                }
            });
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_LABEL_CELL)) {
            bindLabelViewSingle((LabelViewSingle) holder, map);
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_LABEL_CELL_2)) {
            bindLabelViewSingle2((LabelViewSingle2) holder, map);
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_LABELVIEW_CELL)) {
            bindLabelViewMult((LabelViewMult)holder,map );
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_TEXTFIELD_CELL)) {
            bindTextFieldView((TextFieldView) holder, map, position);
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_TEXTVIEW_CELL)) {
            bindTextFieldMulView((TextFieldMulView) holder, map, position);
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_SELECTFIELD_CELL)) {
            bindSelectView((SelectView) holder, map, position);
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_DATESEL_CELL)) {
            bindDateSelectView((DateSelectView) holder, map, position);
        }

        if (map.get("viewType").equals(Constant.VIEWTYPE_SUBMIT_CELL)) {

        }


        if (map.get("viewType").equals(Constant.VIEWTYPE_SELECTVIEW_CELL)) {
            bindSelectTextView((SelectTextView) holder,map );
        }

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (globalListener != null) {
                    globalListener.onRootViewClick(view, position, mDataMap);
                }
            }
        });
    }

    @Override
    public int getItemViewType(int position) {
        Map map = mData.get(position);
        if (map.get("viewType").equals(Constant.VIEWTYPE_SUBMIT_CELL)) {
            return Constant.VIEWTYPE_SUBMIT_CELL;
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_LABEL_CELL)) {
            return Constant.VIEWTYPE_LABEL_CELL;
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_LABEL_CELL_2)) {
            return Constant.VIEWTYPE_LABEL_CELL_2;
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_TEXTFIELD_CELL)) {
            return Constant.VIEWTYPE_TEXTFIELD_CELL;
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_TEXTVIEW_CELL)) {
            return Constant.VIEWTYPE_TEXTVIEW_CELL;
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_SELECTFIELD_CELL)) {
            return Constant.VIEWTYPE_SELECTFIELD_CELL;
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_DATESEL_CELL)) {
            return Constant.VIEWTYPE_DATESEL_CELL;
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_LABELVIEW_CELL)) {
            return Constant.VIEWTYPE_LABELVIEW_CELL;
        }
        if (map.get("viewType").equals(Constant.VIEWTYPE_SELECTVIEW_CELL)) {
            return Constant.VIEWTYPE_SELECTVIEW_CELL;
        }
        return Constant.VIEWTYPE_LABEL_CELL;
    }

    @Override
    public int getItemCount() {
        if (mData == null || mData.isEmpty()) {
            return 0;
        }
        return mData.size();
    }

    public void setData(Map<String, Object> dataMap) {
        this.mDataMap = dataMap;
        Log.i("setData", "setData: " + this.mDataMap);
        notifyDataSetChanged();
    }

    // 提交cell
    public class SubmitView extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_sub)
        TextView tvSub;

        public SubmitView(@NonNull View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    // 单行展示cell
    public class LabelViewSingle extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_label_title)
        TextView tvLabelTitle;

        @BindView(R.id.tv_label_content)
        TextView tvLabelContent;

        public LabelViewSingle(@NonNull View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    private void bindLabelViewSingle(LabelViewSingle holder, Map map) {
        holder.tvLabelTitle.setText(map.get("title").toString());
        String key = map.get("key").toString();
        Object value = mDataMap.get(key);
        if (value != null){
            holder.tvLabelContent.setText(value.toString().trim());
        }
        Log.i("tag","key:" + key + "\n"+ mDataMap.get(key).toString());
//        if (key != null) {
//            if (mDataMap.get(key) != null) {
//            }
//        }
    }

    // 单行展示cell标题
    public class LabelViewSingle2 extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_label_title)
        TextView tvLabelTitle;

        public LabelViewSingle2(@NonNull View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    private void bindLabelViewSingle2(LabelViewSingle2 holder, Map map) {
        holder.tvLabelTitle.setText(map.get("title").toString());
        String key = map.get("key").toString();
        Object value = mDataMap.get(key);
        if (value != null){
            holder.tvLabelTitle.setText(value.toString().trim());
        }
//        Log.i("tag","key:" + key + "\n"+ mDataMap.get(key).toString());
//        if (key != null) {
//            if (mDataMap.get(key) != null) {
//            }
//        }
    }


    // 展示cell
    public class LabelViewMult extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_label_title)
        TextView tvLabelTitle;

        @BindView(R.id.tv_view_content_mult)
        TextView tvLabelContent;

        public LabelViewMult(@NonNull View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    private void bindLabelViewMult(LabelViewMult holder, Map map) {
        holder.tvLabelTitle.setText(map.get("title").toString());
        String key = map.get("key").toString();
        Object value = mDataMap.get(key);
        if (value != null){
            holder.tvLabelContent.setText(value.toString().trim());
        }
//        if (key != null) {
//            if (mDataMap.get(key) != null) {
//            }
//        }
    }





    // 单行输入cell
    public class TextFieldView extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_field_title)
        TextView fieldTitle;

        @BindView(R.id.et_field_content)
        EditText fieldContent;

        public TextFieldView(@NonNull View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
            fieldContent.setEnabled(isApply);
            if (!isApply) {
                fieldContent.setHint("");
            }
        }
    }

    private void bindTextFieldView(TextFieldView holder, Map map, int position) {
        String key = map.get("key").toString();

//        String capitalKey = "";
//        if (map.containsKey("capitalKey")) {
        String capitalKey = map.containsKey("capitalKey") ? map.get("capitalKey").toString() : "";
//        }

        // 展示数据
        holder.fieldTitle.setText(map.get("title").toString());

        //put("keyboardType","number");

        if (isApply) {
            if (!capitalKey.isEmpty()) {
                holder.fieldContent.setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL);
            } else if("number".equals(map.get("keyboardType"))){
                holder.fieldContent.setInputType(InputType.TYPE_CLASS_NUMBER);
            }else {
                holder.fieldContent.setInputType(InputType.TYPE_CLASS_TEXT);
            }
            if (holder.fieldContent.getTag() instanceof TextWatcher) {
                holder.fieldContent.removeTextChangedListener((TextWatcher) holder.fieldContent.getTag());
            }
            Object value = mDataMap.get(key);
            if (value != null){
                holder.fieldContent.setText(value.toString().trim());
            }
            final TextWatcher watcher = new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                }

                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                }

                @Override
                public void afterTextChanged(Editable editable) {
                    // 设置提交数据
                    //通过接口回调将数据传递到Activity中,修改list里的bean

                    // 判断文字长度，是否超过最大长度
                    if (editable.length() > 5) {
                        // 如果超过最大长度，截取前10个字符
                        editable.delete(5, editable.length());
                        holder.fieldContent.setText(editable);
                        holder.fieldContent.setSelection(editable.length()); // 设置光标位置
                        showToast("输入内容不能超过5个字");
                    }


                    mDataMap.put(key, holder.fieldContent.getText().toString().trim());
//                    if (!capitalKey.isEmpty()) {
//                        String num = mDataMap.get(key).toString();
//                        if (!StringUtil.isEmpty(num)) {
//                            double value = Double.valueOf(num.toString());
//                            String format = String.format("%.2f", value);
//                            mDataMap.put(capitalKey, StringUtil.getChinessMoney(format));
//                            Message msg = new Message();
//                            msg.what = 1;
//                            msg.arg1 = position - 1;
//                            handler.sendMessage(msg);
//                        }
//                    }
                }
            };
            //5.设置一个Tag
            holder.fieldContent.addTextChangedListener(watcher);
            holder.fieldContent.setTag(watcher);
        } else {
            Object value = mDataMap.get(key);
            if (value != null){
                holder.fieldContent.setText(value.toString().trim());
            }
        }
    }

    // 多行输入cell
    public class TextFieldMulView extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_view_title)
        TextView textViewTitle;

        @BindView(R.id.et_view_content)
        EditText textViewContent;

        public TextFieldMulView(@NonNull View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
            textViewContent.setEnabled(isApply);
            if (!isApply) {
                textViewContent.setHint("");
            }
        }
    }

    private void bindTextFieldMulView(TextFieldMulView holder, Map map, int position) {
        String key = map.get("key").toString();
        // 展示数据
        holder.textViewTitle.setText(map.get("title").toString());


        if (isApply) {
            if (holder.textViewContent.getTag() instanceof TextWatcher) {
                holder.textViewContent.removeTextChangedListener((TextWatcher) holder.textViewContent.getTag());
            }
            Object value = mDataMap.get(key);
            if (value != null){
                holder.textViewContent.setText(value.toString().trim());
            }

            final TextWatcher watcher = new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                }

                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                }

                @Override
                public void afterTextChanged(Editable editable) {

                    // 判断文字长度，是否超过最大长度
                    if (editable.length() > 5) {
                        // 如果超过最大长度，截取前10个字符
                        editable.delete(5, editable.length());
                        holder.textViewContent.setText(editable);
                        holder.textViewContent.setSelection(editable.length()); // 设置光标位置
                        showToast("输入内容不能超过5个字");
                    }

                    // 设置提交数据
                    mDataMap.put(key, holder.textViewContent.getText().toString().trim());
                }
            };
            //5.设置一个Tag
            holder.textViewContent.addTextChangedListener(watcher);
            holder.textViewContent.setTag(watcher);
        } else {
            Object value = mDataMap.get(key);
            if (value != null){
                holder.textViewContent.setText(value.toString().trim());
            }
        }
    }

    // 下拉选框cell
    public class SelectView extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_label_title)
        TextView tvTitle;

        @BindView(R.id.sp_select)
        Spinner spSelect;

        @BindView(R.id.tv_select_text)
        TextView tvShowText;

        public SelectView(@NonNull View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
            spSelect.setEnabled(isApply);
        }
    }

    private void bindSelectView(SelectView holder, Map map, int position) {
        holder.setIsRecyclable(false);

        holder.tvTitle.setText(map.get("title").toString());
        String key = map.get("key").toString();
        String showKey = "";
        if (map.containsKey("showKey")) {
            showKey = map.get("showKey").toString();
        }
        String extra = "";
        if (map.containsKey("extra")) {
            extra = map.get("extra").toString();
        }
        if (extra.equals("typeCode")) {
            if (isApply) {
                if (map.containsKey("SystemItemModel")) {
                    List<ApplyType> list = (List<ApplyType>) map.get("SystemItemModel");
                    mItemAdapter = new ArrayAdapter<>(mContext, android.R.layout.simple_list_item_1, list);
                    holder.spSelect.setAdapter(mItemAdapter);
                    mItemAdapter.notifyDataSetChanged();
                    holder.tvShowText.setVisibility(View.GONE);
                    holder.spSelect.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                        @Override
                        public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                            mDataMap.put(key, list.get(i).getItemguid());
                        }

                        @Override
                        public void onNothingSelected(AdapterView<?> adapterView) {

                        }
                    });
                }
            } else {
                holder.tvShowText.setVisibility(View.VISIBLE);
                holder.spSelect.setVisibility(View.GONE);
                if (mDataMap.get(showKey) != null) {
                    String str = mDataMap.get(showKey).toString();
                    holder.tvShowText.setText(str);
                }
            }
        } else if (extra.equals("notIndex")) {
            if (isApply) {
                List<String> list = (List<String>) map.get("data");
                if (list != null) {
                    mArrayAdapter = new ArrayAdapter<String>(mContext, android.R.layout.simple_list_item_1, list);
                    holder.spSelect.setAdapter(mArrayAdapter);
                    holder.tvShowText.setVisibility(View.GONE);
                    holder.spSelect.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                        @Override
                        public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                            //
                            mDataMap.put(key, list.get(i));
                        }

                        @Override
                        public void onNothingSelected(AdapterView<?> adapterView) {

                        }
                    });
                }

            } else {
                holder.tvShowText.setVisibility(View.VISIBLE);
                holder.spSelect.setVisibility(View.GONE);
                if (mDataMap.get(key) != null) {
                    String str = mDataMap.get(key).toString();
                    holder.tvShowText.setText(str);
                }
            }
        } else {
            List<String> list = (List<String>) map.get("data");
            if (list != null) {
                mArrayAdapter = new ArrayAdapter<String>(mContext, android.R.layout.simple_list_item_1, list);
                holder.spSelect.setAdapter(mArrayAdapter);
                holder.tvShowText.setVisibility(View.GONE);
                holder.spSelect.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                    @Override
                    public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                        //
                        mDataMap.put(key, list.get(i));
                    }

                    @Override
                    public void onNothingSelected(AdapterView<?> adapterView) {

                    }
                });
            }


//            mArrayAdapter = new ArrayAdapter<String>(mContext, android.R.layout.simple_list_item_1, list);
//            holder.spSelect.setAdapter(mArrayAdapter);
//            if (isApply) {
//                holder.spSelect.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
//                    @Override
//                    public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
//                        //
//                        mDataMap.put(key, String.valueOf(i));
//                    }
//                    @Override
//                    public void onNothingSelected(AdapterView<?> adapterView) {
//
//                    }
//                });
//            } else {
//            }

            if (mDataMap.get(key) != null) {
                String str = mDataMap.get(key).toString();
                if (StringUtil.isEmpty(str)) {
                    holder.spSelect.setSelection(0, true);
                } else {
                    holder.spSelect.setSelection(Integer.parseInt(str), true);
                }
            }
        }
    }


    // 选择文本cell
    public class SelectTextView extends RecyclerView.ViewHolder {
        @BindView(R.id.rl_content)
        RelativeLayout selView;
        @BindView(R.id.et_view_content)
        TextView tvContent;
        @BindView(R.id.tv_view_title)
        TextView tvTitle;
        public SelectTextView(@NonNull View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    private void bindSelectTextView(SelectTextView holder, Map map) {
        String key = map.get("key").toString();
        String extra = map.get("extra").toString();
        String type = map.get("type").toString();
        holder.tvTitle.setText(map.get("title").toString());
        Object value = mDataMap.get(key);
        if (value != null){
            holder.tvContent.setText(value.toString().trim());
        }
        holder.selView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mSelectTextViewListener != null){
                    mSelectTextViewListener.skipType(type,key,extra);
                }
            }
        });
//        if (key != null) {
//            if (mDataMap.get(key) != null) {
//            }
//        }
    }








    public class DateSelectView extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_date_title)
        TextView tvDateTitle;

        @BindView(R.id.tv_date_content)
        TextView tvDateContent;

        public DateSelectView(@NonNull View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    private void bindDateSelectView(DateSelectView holder, Map map, int position) {
        holder.setIsRecyclable(false);
        holder.tvDateTitle.setText(map.get("title").toString());
//        String timeType = "";
        Object timeType = map.get("timeType");//minute
        if (map.get("timeType") != null) {
        }
        String key = map.get("key").toString();
//        if (timeType.equals("minute")) {
//
//        }

        Object value = mDataMap.get(key);
        if (value != null){
            holder.tvDateContent.setText(value.toString().trim());
        }
        if (isApply) {
            holder.tvDateContent.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // 时间选择
                    if (timeType != null) {
                        initDatePick(key);
                        tpv.show(holder.tvDateContent);
                    } else {
                        showDatePickerDialog(holder.tvDateContent, key);
                    }
                }
            });
        }
    }


    /**
     * 展示日期选择对话框
     */
    private void showDatePickerDialog(TextView tv, String key) {
        Calendar c = Calendar.getInstance();
        new DatePickerDialog(mContext, new DatePickerDialog.OnDateSetListener() {
            @Override
            public void onDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                // TODO Auto-generated method stub
                int m = monthOfYear + 1;
                String month = m >= 10 ? m + "" : "0" + m;
                String day = dayOfMonth >= 10 ? dayOfMonth + "" : "0" + dayOfMonth;
                String dateStr = year + "-" + month + "-" + day;
                tv.setText(dateStr);
                mDataMap.put(key, dateStr);
                Log.i("mDataMap", "showDatePickerDialog: ---->" + mDataMap);
            }
        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)).show();
    }

    // 展示时间选择
    private void initDatePick(String key) {
        Calendar selectedDate = Calendar.getInstance();
        Calendar startDate = Calendar.getInstance();
        startDate.set(1950, 0, 1, 0, 0);
        Calendar endDate = Calendar.getInstance();
        endDate.set(2033, 11, 30, 23, 30);
        //时间选择器
        tpv = new TimePickerView.Builder(mContext, new TimePickerView.OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {//选中事件回调
                // 这里回调过来的v,就是show()方法里面所添加的 View 参数，如果show的时候没有添加参数，v则为null
                String dateStr = DateUtil.dateToString(date, DateUtil.YMD_HM);
                TextView tvDate = (TextView) v;
                tvDate.setText(dateStr);
                mDataMap.put(key, dateStr);
//                switch (timeType) {
//                    case TIME_TYPE_INVISIBLE:
//                        tvAltertime.setText(DateUtil.dateToString(date, DateUtil.YMD_HM));
//                        programmeInfo.setAlterTime(DateUtil.dateToString(date, DateUtil.YMD_HM));
//                        break;
//                }
            }
        })
                //年月日时分秒 的显示与否，不设置则默认全部显示
                .setType(new boolean[]{true, true, true, true, true, false})
                .setLabel("年", "月", "日", "时", "分", "")
                .isCenterLabel(false)
                .setDividerType(WheelView.DividerType.WRAP)
                .setDividerColor(ContextCompat.getColor(YueApplacation.mContext, R.color.colorAccent))
                .setContentSize(PhoneUtil.dpTopx(YueApplacation.mContext, 8))
                .setDate(selectedDate)
                .setRangDate(startDate, endDate)
                .setDecorView(null)
                .build();
    }


    public interface SelectTextViewListener {
        void skipType(String type,String key,String extra);
    }

    public void setSelectTextViewListener(SelectTextViewListener selectTextViewListener) {
        this.mSelectTextViewListener = selectTextViewListener;
    }
}



