package com.kisoft.yuejianli.scgl;

import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by tudou on 2018/6/22.
 */
public class ScglPageConfigData {

    private static List<String> getWeatherList(){
        List<String> list = new ArrayList() {{
            add("晴");
            add("多云");
            add("阴");
            add("雨");
            add("阵雨");
            add("雪");
        }};
        return list;
    }

   public static Map<String, Object> getPageData(String bType,String menuTitle){
       List<Map<String, Object>> data = null;
       String titleKey = "";
       String listUrl = "";
       String saveUrl = "";
       String updateUrl = "";
       String filePath = "";
       String pageTitle = "";
       String primaryKey = "id";
       boolean isApply = false;

       if (StringUtil.isEqual("t_filingSupPlan", bType)) {
           // 监理计划
           if (StringUtil.isEqual(menuTitle, "监理计划")) {
               listUrl = "/jpf/project/filingSupPlanAction.do?method=list_App&type=a";
           }else if (StringUtil.isEqual(menuTitle, "安全计划")) {
               listUrl = "/jpf/project/filingSupPlanAction.do?method=list_App&type=b";
           }
           pageTitle = menuTitle;
           filePath = "/pdm/project/";
           saveUrl = "";
           updateUrl = "";
           isApply = true;
           data = new ArrayList() {{
               add(new HashMap() {{ put("title", "项目名称:");put("key", "projectName");put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL); }});
               add(new HashMap() {{ put("title", "编号:");put("key", "spNumber");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
               add(new HashMap() {{ put("title", "总监:");put("key", "supDirectorName");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
               add(new HashMap() {{ put("title", "编制人:");put("key", "createName");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
               add(new HashMap() {{ put("title", "主题:");put("key", "spSubject");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
               add(new HashMap() {{ put("title", "内容:");put("key", "spContent");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
           }};
       }else if (StringUtil.isEqual("t_filingDetailRule", bType)) {
           // 监理细则
           pageTitle = "监理细则";
           filePath = "/pdm/project/";
           listUrl = "/jpf/project/filingDetailRuleAction.do?method=list_App";
           saveUrl = "";
           updateUrl = "";
           isApply = true;
           data = new ArrayList() {{
               add(new HashMap() {{ put("title", "项目名称:");put("key", "projectName");put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL); }});
               add(new HashMap() {{ put("title", "编号:");put("key", "drNumber");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
               add(new HashMap() {{ put("title", "专业:");put("key", "expertise");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
               add(new HashMap() {{ put("title", "总监:");put("key", "supDirectorName");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
               add(new HashMap() {{ put("title", "编制人:");put("key", "createName");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
               add(new HashMap() {{ put("title", "主题:");put("key", "drSubject");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
               add(new HashMap() {{ put("title", "内容:");put("key", "drContent");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
           }};
       }
       else if (StringUtil.isEqual("T_PDS_ENVIRONMENTAL_DETAIL",bType)){
            //环保细则
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "标题:");
                   put("key", "title");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "内容:");
                   put("key", "content");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PDS_ENVIRONMENTAL_PLAN",bType)){
           //环保计划
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "标题:");
                   put("key", "title");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "内容:");
                   put("key", "content");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PDS_SPECIAL_SUP_PLAN",bType)){
           //监理专项方案
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "标题:");
                   put("key", "title");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "类别:");
                   put("key", "type");
                   put("viewType", Constant.VIEWTYPE_LABEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "内容:");
                   put("key", "content");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PDS_DANGEROUS_PROJECT_PLAN",bType)){
           //危大工程方案
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "标题:");
                   put("key", "title");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "内容:");
                   put("key", "content");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_SUPERVISION_LOG",bType)){
           // 监理日志
           pageTitle = "监理日志";
           filePath = "/pdm/progress/";
           titleKey = "workTasks";
           primaryKey = "slId";
           listUrl = "pds/SupervisionLogAction.do?method=getSupervisionLogList_APP";
           saveUrl = "pds/SupervisionLogAction.do?method=saveSupervisionLog_APP";
           updateUrl = "pds/SupervisionLogAction.do?method=updateSupervisionLog_APP";

        //    List<String> list = new ArrayList() {{
        //        add("晴");
        //        add("多云");
        //        add("阴");
        //        add("雨");
        //        add("阵雨");
        //        add("雪");
        //    }};
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABEL_CELL_2);
               }});
               add(new HashMap() {{
                   put("title", "编号:");
                   put("key", "slNumber");
                   put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "记录人:");
                   put("key", "createName");
                   put("showList", true);
                   put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "日期:");
                   put("key", "createTime");
                   put("showList", true);
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "审核人:");
                   put("key", "checkUserName");
                   put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "天气情况:");
                   put("key", "weather");
                   put("data", getWeatherList());
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "主要施工情况:");
                   put("key", "position");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "监理主要工作:");
                   put("key", "detectionSituation");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "问题及处理情况:");
                   put("key", "problem");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PDS_SECURITY_LOG_SCGL",bType)){
           // 安全监理日志
           pageTitle = "安全监理日志";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "pds/PdsSecurityLogScglAction.do?method=getPdsSecurityLogScglList_APP";
           saveUrl = "pds/PdsSecurityLogScglAction.do?method=savePdsSecurityLogScgl_APP";
           updateUrl = "pds/PdsSecurityLogScglAction.do?method=updatePdsSecurityLogScgl_APP";

           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABEL_CELL_2);
               }});
               add(new HashMap() {{
                   put("title", "监理机构:");
                   put("key", "supervisoryAuthority");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "施工合同号:");
                   put("key", "constructionContractNo");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "记录人:");
                   put("key", "recorder");
                   put("showList", true);
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "记录日期:");
                   put("key", "recordDate");
                   put("showList", true);
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "审核人:");
                   put("key", "auditor");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "审核日期:");
                   put("key", "auditDate");
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "天气:");
                   put("key", "weather");
                   put("data", getWeatherList());
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "各合同段主要施工项目简述:");
                   put("key", "projectDescription");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "监理机构主要安全监理工作简述:");
                   put("key", "safetyWork");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "安全问题处理情况简述:");
                   put("key", "safetyProblem");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PDS_ENVIRONMENTAL_LOG",bType)){
           // 环保监理日志
           pageTitle = "环保监理日志";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "/pds/PdsEnvironmentalLogAction.do?method=getPdsEnvironmentalLogList_APP";
           saveUrl = "/pds/PdsEnvironmentalLogAction.do?method=savePdsEnvironmentalLog_APP";
           updateUrl = "/pds/PdsEnvironmentalLogAction.do?method=updatePdsEnvironmentalLog_APP";
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "编号:");
                   put("key", "serialNumber");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "监理机构:");
                   put("key", "supervisorOrganization");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "记录人:");
                   put("key", "recorder");
                   put("showList", true);
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "审核人:");
                   put("key", "auditor");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "日期:");
                   put("key", "dateTime");
                   put("showList", true);
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "天气:");
                   put("key", "weatherSituation");
                   put("data", getWeatherList());
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "主要施工情况:");
                   put("key", "constructionSituation");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "监理主要工作:");
                   put("key", "supervisionWork");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "问题及处理情况:");
                   put("key", "problem");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_SIDEREPORTING",bType)){
           // 旁站记录
           pageTitle = "旁站记录";
           filePath = "/pdm/icon/";
           titleKey = "workTasks";
           primaryKey = "SId";
           listUrl = "/pds/SidereportingAction.do?method=getSidereportingList_APP";
           saveUrl = "/pds/SidereportingAction.do?method=saveSidereporting_APP";
           updateUrl = "/pds/SidereportingAction.do?method=updateSidereporting_APP";
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "编号:");
                   put("key", "SNumber");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "施工单位:");
                   put("key", "company");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "合同段:");
                   put("key", "conSzj");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "旁站人员:");
                   put("key", "createName");
                   put("showList", true);
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "开始时间:");
                   put("key", "startTime");
                   put("showList", true);
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "旁站项目:");
                   put("key", "workingProcedure");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "施工过程简述:");
                   put("key", "constructionSituation");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "旁站工作情况:");
                   put("key", "supCondt");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "主要数据记录:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "发现的问题:");
                   put("key", "treatmentSituation");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "处理结果:");
                   put("key", "dispose");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PDS_OTHER_PLAN",bType)){
           // 其他资料
           pageTitle = "其他资料";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "/pds/PdsOtherPlanAction.do?method=getPdsOtherPlanList_APP";
           saveUrl = "/pds/PdsOtherPlanAction.do?method=savePdsOtherPlan_APP";
           updateUrl = "/pds/PdsOtherPlanAction.do?method=updatePdsOtherPlan_APP";
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "标题:");
                   put("key", "title");
                   put("showList", true);
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "内容:");
                   put("key", "content");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("t_quality_inspection",bType)){
           // 现场巡视记录
           pageTitle = "现场巡视记录";
           filePath = "/pdm/progress/";
           titleKey = "workTasks";
           primaryKey = "qiId";
           listUrl = "pds/QualityInspectionAction.do?method=getQualityInspectionList_APP";
           saveUrl = "pds/QualityInspectionAction.do?method=saveQualityInspection_APP";
           updateUrl = "pds/QualityInspectionAction.do?method=updateQualityInspection_APP";
           List<String> list1 = new ArrayList() {{
                add("完成");
                add("延误");
                add("提前完成");
                add("按计划完成");
            }};
            List<String> list2 = new ArrayList() {{
                add("正常");
                add("预警");
                add("异常");
            }};
            List<String> list3 = new ArrayList() {{
                add("正常");
                add("预警");
                add("异常");
            }};
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "编号:");
                   put("key", "qiNumber");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "施工单位:");
                   put("key", "constUnit");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "合同段:");
                   put("key", "conSzj");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "巡视人:");
                   put("key", "createName");
                   put("showList", true);
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "巡视时间:");
                   put("key", "createTime");
                   put("showList", true);
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "巡视的范围:");
                   put("key", "insPosition");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "主要施工情况:");
                   put("key", "constContent");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "质量安全环保等情况:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "发现的问题及处理意见:");
                   put("key", "opinion");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "进度情况:");
                   put("key", "proStatus");
                   put("data", list1);
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "质量情况:");
                   put("key", "qiStatus");
                   put("data", list2);
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "安全情况:");
                   put("key", "safeStatus");
                   put("data", list3);
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("t_supervision_instruct",bType)){
           // 监理指令
           pageTitle = "监理指令";
           filePath = "/pdm/icon/";
           titleKey = "workTasks";
           primaryKey = "siId";
           listUrl = "pds/SupervisionInstructAction.do?method=getSupervisionInstructList_APP";
           saveUrl = "pds/SupervisionInstructAction.do?method=saveSupervisionInstruct_APP";
           updateUrl = "pds/SupervisionInstructAction.do?method=updateSupervisionInstruct_APP";
           List<String> probTypeList = new ArrayList() {{
            add("人员问题");
            add("其他问题");
            add("安全问题");
            add("工艺问题");
            add("环保问题");
            add("程序问题");
            add("质量问题");
        }};
        List<String> probClassList = new ArrayList() {{
            add("巡检");
            add("旁站");
            add("质量");
            add("进度");
            add("安全");
            add("环保");
            add("其他");
        }};
           data = new ArrayList() {{
            add(new HashMap() {{
                put("title", "项目名称:");
                put("key", "projectName");
                put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "施工单位:");
                put("key", "company");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "监理合同号:");
                put("key", "conNo");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "监理指令编号:");
                put("key", "siNum");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "指令主题:");
                put("key", "structName");
                put("showList", true);
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "现场位置:");
                put("key", "siteLocal");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "存在问题:");
                put("key", "probRemark");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "处理措施:");
                put("key", "handleRemark");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "抄送:");
                put("key", "cc");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "备注:");
                put("key", "remark");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "问题类型:");
                put("key", "probType");
                put("data", probTypeList);
                put("extra", "notIndex");
                put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
            }});
            add(new HashMap() {{
                put("title", "问题类别:");
                put("key", "probClass");
                put("data", probClassList);
                put("extra", "notIndex");
                put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
            }});
        }};
       }else if (StringUtil.isEqual("t_supervision_instruct2",bType)){
           // 监理指令
           pageTitle = "监理通知单";
           filePath = "/pdm/icon/";
           titleKey = "workTasks";
           primaryKey = "siId";
           listUrl = "pds/SupervisionInstruct2Action.do?method=getSupervisionInstructList_APP";
           saveUrl = "pds/SupervisionInstruct2Action.do?method=saveSupervisionInstruct_APP";
           updateUrl = "pds/SupervisionInstruct2Action.do?method=updateSupervisionInstruct_APP";
           List<String> probTypeList = new ArrayList() {{
               add("人员问题");
               add("其他问题");
               add("安全问题");
               add("工艺问题");
               add("环保问题");
               add("程序问题");
               add("质量问题");
           }};
           List<String> probClassList = new ArrayList() {{
               add("巡检");
               add("旁站");
               add("质量");
               add("进度");
               add("安全");
               add("环保");
               add("其他");
           }};
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "施工单位:");
                   put("key", "company");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "监理合同号:");
                   put("key", "conNo");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "监理指令编号:");
                   put("key", "siNum");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "指令主题:");
                   put("key", "structName");
                   put("showList", true);
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "现场位置:");
                   put("key", "siteLocal");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "存在问题:");
                   put("key", "probRemark");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "处理措施:");
                   put("key", "handleRemark");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "抄送:");
                   put("key", "cc");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "问题类型:");
                   put("key", "probType");
                   put("data", probTypeList);
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "问题类别:");
                   put("key", "probClass");
                   put("data", probClassList);
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PDS_WEATHER_GLASS",bType)){
           // 晴雨表记录
           pageTitle = "晴雨表记录";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "pds/PdsWeatherGlassAction.do?method=getPdsWeatherGlassList_APP";
           saveUrl = "pds/PdsWeatherGlassAction.do?method=savePdsWeatherGlass_APP";
           updateUrl = "pds/PdsWeatherGlassAction.do?method=updatePdsWeatherGlass_APP";
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "日期:");
                   put("key", "weatherDate");
                   put("showList", true);
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "天气情况:");
                   put("key", "weather");
                   put("data", getWeatherList());
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "温度:");
                   put("key", "temperature");
                   put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PDS_STOP_WORK_ORDER",bType)){
        // 停工令
        pageTitle = "停工令";
        filePath = "/pds/icon/";
        titleKey = "workTasks";
        listUrl = "pds/PdsStopWorkOrderAction.do?method=getPdsStopWorkOrderList_APP";
        saveUrl = "pds/PdsStopWorkOrderAction.do?method=savePdsStopWorkOrder_APP";
        updateUrl = "pds/PdsStopWorkOrderAction.do?method=updatePdsStopWorkOrder_APP";
        data = new ArrayList() {{
            add(new HashMap() {{
                put("title", "项目名称:");
                put("key", "projectName");
                put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "施工单位:");
                put("key", "constructionUnit");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "监理单位:");
                put("key", "supervisionUnit");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "合同段:");
                put("key", "contractSegments");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "编号:");
                put("key", "numbering");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "停工依据、原因:");
                put("key", "cause");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "停工范围:");
                put("key", "downtimeScope");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "停工开始时间:");
                put("key", "shutdownDate");
                put("showList", true);
                put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
            }});
            add(new HashMap() {{
                put("title", "停工期间应做工作:");
                put("key", "job");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "总监办意见:");
                put("key", "superintendentOpinion");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "总监理工程师签字:");
                put("key", "majordomoName");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "总监理工程师签字时间:");
                put("key", "majordomoDate");
                put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
            }});
            add(new HashMap() {{
                put("title", "项目经理签字:");
                put("key", "projectManagerName");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "收件日期:");
                put("key", "receiptDate");
                put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
            }});
            add(new HashMap() {{
                put("title", "备注:");
                put("key", "remark");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
        }};
    }else if (StringUtil.isEqual("T_PDS_RESUMPTION_WORK_ORDER",bType)){
        // 复工令
        pageTitle = "复工令";
        filePath = "/pds/icon/";
        titleKey = "workTasks";
        listUrl = "pds/PdsResumptionWorkOrderAction.do?method=getPdsResumptionWorkOrderList_APP";
        saveUrl = "pds/PdsResumptionWorkOrderAction.do?method=savePdsResumptionWorkOrder_APP";
        updateUrl = "pds/PdsResumptionWorkOrderAction.do?method=updatePdsResumptionWorkOrder_APP";
        data = new ArrayList() {{
            add(new HashMap() {{
                put("title", "项目名称:");
                put("key", "projectName");
                put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "施工单位:");
                put("key", "constructionUnit");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "监理单位:");
                put("key", "supervisionUnit");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "合同段:");
                put("key", "contractSegments");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "编号:");
                put("key", "numbering");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "复工依据、原因:");
                put("key", "cause");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "复工范围:");
                put("key", "downtimeScope");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "复工日期:");
                put("key", "shutdownDate");
                put("showList", true);
                put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
            }});
            add(new HashMap() {{
                put("title", "复工时应做好如下工作:");
                put("key", "job");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "总监办意见:");
                put("key", "superintendentOpinion");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "总监理工程师签字:");
                put("key", "majordomoName");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "总监理工程师签字时间:");
                put("key", "majordomoDate");
                put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
            }});
            add(new HashMap() {{
                put("title", "项目经理签字:");
                put("key", "projectManagerName");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "收件日期:");
                put("key", "receiptDate");
                put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
            }});
            add(new HashMap() {{
                put("title", "备注:");
                put("key", "remark");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
        }};
    }else if (StringUtil.isEqual("T_PDS_START_WORK_ORDER",bType)){
        // 开工令
        pageTitle = "开工令";
        filePath = "/pds/icon/";
        titleKey = "workTasks";
        listUrl = "pds/PdsStartWorkOrderAction.do?method=getPdsStartWorkOrderList_APP";
        saveUrl = "pds/PdsStartWorkOrderAction.do?method=savePdsStartWorkOrder_APP";
        updateUrl = "pds/PdsStartWorkOrderAction.do?method=updatePdsStartWorkOrder_APP";
        data = new ArrayList() {{
            add(new HashMap() {{
                put("title", "项目名称:");
                put("key", "projectName");
                put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "承包单位:");
                put("key", "contractingUnit");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "监理单位:");
                put("key", "supervisionUnit");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "合同段:");
                put("key", "contractSegments");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "编号:");
                put("key", "numbering");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "起讫桩号:");
                put("key", "startEndStationNumbers");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "开工范围:");
                put("key", "scope");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "合同签订日期:");
                put("key", "signing");
                put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
            }});
            add(new HashMap() {{
                put("title", "要求开工日期:");
                put("key", "commencementDate");
                put("showList", true);
                put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
            }});
            add(new HashMap() {{
                put("title", "工期起算日期:");
                put("key", "startDate");
                put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
            }});
            add(new HashMap() {{
                put("title", "合同工期:");
                put("key", "duration");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "开工建议:");
                put("key", "superintendentOpinion");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "总监理工程师签字:");
                put("key", "majordomoName");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "总监理工程师签字时间:");
                put("key", "majordomoDate");
                put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
            }});
            add(new HashMap() {{
                put("title", "项目经理签字:");
                put("key", "projectManagerName");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
            add(new HashMap() {{
                put("title", "收件日期:");
                put("key", "receiptDate");
                put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
            }});
            add(new HashMap() {{
                put("title", "备注:");
                put("key", "remark");
                put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
            }});
        }};
    }else if (StringUtil.isEqual("T_PROD_PROMPT_NOTICE",bType)){
           // 生产经营工作提示单/整改单
           pageTitle = "工作提示单/整改单";
           filePath = "/pds/production/";
           titleKey = "workTasks";
           listUrl = "/pds/ProdPromptNoticeAction.do?method=getProdPromptNoticeList";
           saveUrl = "/pds/ProdPromptNoticeAction.do?method=saveProdPromptNotice_APP";
           updateUrl = "/pds/ProdPromptNoticeAction.do?method=updateProdPromptNotice_APP";
           List<String> list = new ArrayList() {{
               add("提示单");
               add("整改单");
           }};
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "资料名称:");
                   put("key", "dataName");
                   put("data", list);
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "编号:");
                   put("key", "serialNumber");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "内容:");
                   put("key", "content");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "生产部门:");
                   put("key", "productionDepartment");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "问题闭合情况，回复日期:");
                   put("key", "problemClosure");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "下发日期:");
                   put("key", "deliveryDate");
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "提示、通知人:");
                   put("key", "prompter");
                   put("extra", "prompterId");
                   put("type", "Person");
                   put("viewType", Constant.VIEWTYPE_SELECTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_OA_MANAGE", bType)) {
           // 自主经营项目审批
           pageTitle = "自主经营项目审批";
           filePath = "/pds/production/";
           titleKey = "workTasks";
           listUrl = "";
           saveUrl = "";
           updateUrl = "";
           isApply = true;
           data = new ArrayList() {{
               add(new HashMap() {{ put("title", "项目名称:");put("key", "projectName");put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL); }});
           }};
       }else if (StringUtil.isEqual("T_PROJ_CHECK",bType)){
           // 项目自查
           pageTitle = "项目自查";
           filePath = "/pds/subcompany/";
           titleKey = "workTasks";
           listUrl = "/pds/ProjCheckAction.do?method=getProjCheckAppList";
           saveUrl = "/pds/ProjCheckAction.do?method=saveProjCheck_APP";
           updateUrl = "/pds/ProjCheckAction.do?method=updateProjCheck_APP";

           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "自查内容:");
                   put("key", "checkContent");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "自查人:");
                   put("key", "checkPerson");
                   put("extra", "checkPersonId");
                   put("type", "Person");
                   put("viewType", Constant.VIEWTYPE_SELECTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "问题及处理措施:");
                   put("key", "problemAndSolve");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "自查日期:");
                   put("key", "checkDate");
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "整改完成日期:");
                   put("key", "solveDate");
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PROD_WEEKLY_REPORT",bType)){
           // 项目周报
           pageTitle = "项目周报";
           filePath = "";
           titleKey = "workTasks";
           listUrl = "/pds/ProdWeeklyReportAction.do?method=getProdWeeklyReportList";
           saveUrl = "";
           data = new ArrayList<>();
       }else if (StringUtil.isEqual("T_PROJ_ASSESSMENT_SCORE",bType)){
           // 项目季度、年终考评
           pageTitle = "项目季度、年终考评";
           filePath = "";
           titleKey = "workTasks";
           listUrl = "/pds/ProjAssessmentScoreAction.do?method=getProjAssessmentScoreList";
           saveUrl = "";
           data = new ArrayList<>();
       }else if (StringUtil.isEqual("T_PROD_ENVIRONMENTAL_CHECK",bType)){
           // 环保检查记录
           pageTitle = "环保检查记录";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "/pds/ProdEnvironmentalCheckAction.do?method=getProdEnvironmentalCheckList";
           saveUrl = "/pds/ProdEnvironmentalCheckAction.do?method=saveProdEnvironmentalCheck_APP";
           data = new ArrayList<>();
       }else if (StringUtil.isEqual("T_PROD_WEEKLY_REPORT_SUMMARY",bType)){
           // 周报汇总上传
           pageTitle = "周报汇总上传";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "/pds/ProdWeeklyReportSummaryAction.do?method=getProdWeeklyReportSummaryList";
           saveUrl = "/pds/ProdWeeklyReportSummaryAction.do?method=saveProdWeeklyReportSummary_APP";
           List<String> yearList = new ArrayList() {{
               add("2022");
               add("2023");
               add("2024");
               add("2025");
               add("2026");
               add("2027");
               add("2028");
           }};
           List<String> monthsList = new ArrayList() {{
               add("1月");
               add("2月");
               add("3月");
               add("4月");
               add("5月");
               add("6月");
               add("7月");
               add("8月");
               add("9月");
               add("10月");
               add("11月");
               add("12月");
           }};
           List<String> weeksList = new ArrayList() {{
               add("第一周");
               add("第二周");
               add("第三周");
               add("第四周");
               add("第五周");
           }};
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "填报年份:");
                   put("key", "year");
                   put("data", yearList);
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});

               add(new HashMap() {{
                   put("title", "填报月份:");
                   put("key", "month");
                   put("data", monthsList);
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "填报周份:");
                   put("key", "week");
                   put("data", weeksList);
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PROD_MONTHLY_REPORT",bType)){
           // 月报板块
           pageTitle = "月报板块";
           filePath = "/pds/production/";
           titleKey = "workTasks";
           listUrl = "/pds/ProdMonthlyReportAction.do?method=getProdMonthlyReportList";
           saveUrl = "/pds/ProdMonthlyReportAction.do?method=saveProdMonthlyReport_APP";
           List<String> yearList = new ArrayList() {{
               add("2022");
               add("2023");
               add("2024");
               add("2025");
               add("2026");
               add("2027");
               add("2028");
           }};
           List<String> monthsList = new ArrayList() {{
               add("1月");
               add("2月");
               add("3月");
               add("4月");
               add("5月");
               add("6月");
               add("7月");
               add("8月");
               add("9月");
               add("10月");
               add("11月");
               add("12月");
           }};
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "填报年份:");
                   put("key", "year");
                   put("data", yearList);
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});

               add(new HashMap() {{
                   put("title", "填报月份:");
                   put("key", "month");
                   put("data", monthsList);
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("testEngineeringApp",bType)){
           // 工程门户
           pageTitle = "工程门户";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "";
           saveUrl = "";
           data = new ArrayList<>();
       } else if (StringUtil.isEqual("testEngineeringApp",bType)){
           // 项目管理清单
           pageTitle = "项目管理清单";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "";
           saveUrl = "";
           data = new ArrayList<>();
       } else if (StringUtil.isEqual("testEngineeringApp",bType)){
           // 经营门户
           pageTitle = "经营门户";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "";
           saveUrl = "";
           data = new ArrayList<>();
       }

       Map<String, Object> map= new HashMap<>();
       map.put("pageTitle", pageTitle);
       map.put("listUrl", listUrl);
       map.put("saveUrl", saveUrl);
       map.put("primaryKey", primaryKey);
       map.put("updateUrl", updateUrl);
       map.put("filePath", filePath);
       map.put("isApply", isApply);
       map.put("data",data);
       return map;
   }
}
