package com.kisoft.yuejianli.im.chat.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.amap.api.location.CoordinateConverter;
import com.amap.api.location.DPoint;
import com.amap.api.maps.AMap;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.MapView;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.CameraPosition;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.maps.model.MyLocationStyle;
import com.hyphenate.easeui.ui.base.EaseBaseActivity;
import com.hyphenate.easeui.utils.EaseCommonUtils;
import com.hyphenate.easeui.widget.EaseTitleBar;
import com.kisoft.yuejianli.R;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 替换环信UI引用百度地图
 */
public class EaseBaiduMapActivity extends EaseBaseActivity implements EaseTitleBar.OnBackPressListener,
        EaseTitleBar.OnRightClickListener{
    @BindView(R.id.mapView)
    MapView mapView;
    @BindView(R.id.title_bar_map)
    EaseTitleBar titleBarMap;

    private AMap aMap;
    private AMapLocation lastLocation;
    protected double latitude;
    protected double longtitude;
    protected String address;
    private MyLocationStyle myLocationStyle;

    public static void actionStartForResult(Fragment fragment, int requestCode) {
        Intent intent = new Intent(fragment.getContext(), EaseBaiduMapActivity.class);
        fragment.startActivityForResult(intent, requestCode);
    }

    public static void actionStartForResult(Activity activity, int requestCode) {
        Intent intent = new Intent(activity, EaseBaiduMapActivity.class);
        activity.startActivityForResult(intent, requestCode);
    }

    public static void actionStart(Context context, double latitude, double longtitude, String address) {
        Intent intent = new Intent(context, EaseBaiduMapActivity.class);
        intent.putExtra("latitude", latitude);
        intent.putExtra("longtitude", longtitude);
        intent.putExtra("address", address);
        context.startActivity(intent);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //initialize SDK with context, should call this before setContentView
//        SDKInitializer.initialize(getApplicationContext());
        setContentView(R.layout.ease_activity_chatmap);
        ButterKnife.bind(this);
        setFitSystemForTheme(false, R.color.transparent, true);
        initIntent();
        initView();
        initListener();
        mapView.onCreate(savedInstanceState);
    }

    private void initIntent() {
        latitude = getIntent().getDoubleExtra("latitude", 0);
        longtitude = getIntent().getDoubleExtra("longtitude", 0);
        address = getIntent().getStringExtra("address");
    }

    private void initView() {
        titleBarMap.setRightTitleResource(R.string.button_send);
        double latitude = getIntent().getDoubleExtra("latitude", 0);
        if(latitude != 0) {
            titleBarMap.getRightLayout().setVisibility(View.GONE);
        }else {
            titleBarMap.getRightLayout().setVisibility(View.VISIBLE);
            titleBarMap.getRightLayout().setClickable(false);
        }
        ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) titleBarMap.getLayoutParams();
        params.topMargin = (int) EaseCommonUtils.dip2px(this, 24);
        titleBarMap.setBackgroundColor(ContextCompat.getColor(this, R.color.transparent));
        titleBarMap.getRightText().setTextColor(ContextCompat.getColor(this, R.color.white));
        titleBarMap.getRightText().setBackgroundResource(R.drawable.ease_title_bar_right_selector);
        int left = (int) EaseCommonUtils.dip2px(this, 10);
        int top = (int) EaseCommonUtils.dip2px(this, 5);
        titleBarMap.getRightText().setPadding(left, top, left, top);
        ViewGroup.LayoutParams layoutParams = titleBarMap.getRightLayout().getLayoutParams();
        if(layoutParams instanceof ViewGroup.MarginLayoutParams) {
            ((ViewGroup.MarginLayoutParams) layoutParams).setMargins(0, 0, left, 0);
        }

        aMap = mapView.getMap();
        myLocationStyle = new MyLocationStyle();//初始化定位蓝点样式类
        myLocationStyle.myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE);//定位一次，且将视角移动到地图中心点。
//        myLocationStyle.interval(10000); //设置连续定位模式下的定位间隔，只在连续定位模式下生效，单次定位模式下不会生效。单位为毫秒。
        aMap.setMyLocationStyle(myLocationStyle);//设置定位蓝点的Style
        aMap.getUiSettings().setMyLocationButtonEnabled(true);//设置默认定位按钮是否显示，非必需设置。
        aMap.getUiSettings().setLogoBottomMargin(-50);  //  隐藏logo
        aMap.setMyLocationEnabled(true);// 设置为true表示启动显示定位蓝点，false表示隐藏定位蓝点并不进行定位，默认是false
        //设置希望展示的地图缩放级别
        aMap.moveCamera(CameraUpdateFactory.zoomTo(15));
        aMap.getUiSettings().setCompassEnabled(true);

        initLocation();
        mapView.setLongClickable(true);
    }

    private void initListener() {
        titleBarMap.setOnBackPressListener(this);
        titleBarMap.setOnRightClickListener(this);
    }

    protected void showMap(double latitude, double longtitude) {
        try {
            DPoint lng = new DPoint(latitude, longtitude);
            CoordinateConverter converter = new CoordinateConverter(this);
            converter.coord(lng);
            converter.from(CoordinateConverter.CoordType.ALIYUN);
            DPoint convertLatLng = converter.convert();
            LatLng latLng = new LatLng(convertLatLng.getLatitude(), convertLatLng.getLongitude());
            MarkerOptions ooA = new MarkerOptions().position(latLng).icon(BitmapDescriptorFactory
                    .fromResource(R.drawable.ease_icon_marka))
                    .zIndex(4).draggable(true);
            aMap.addMarker(ooA);
            aMap.moveCamera(CameraUpdateFactory.newCameraPosition(new CameraPosition(latLng,17.0f,0,0)));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onBackPress(View view) {
        onBackPressed();
    }

    @Override
    public void onRightClick(View view) {
        sendLocation();
    }

    private void sendLocation() {
        Intent intent = getIntent();
        intent.putExtra("latitude", lastLocation.getLatitude());
        intent.putExtra("longitude", lastLocation.getLongitude());
        intent.putExtra("address", lastLocation.getAddress());
        this.setResult(RESULT_OK, intent);
        finish();
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        mapView.onSaveInstanceState(outState);
    }

    @Override
    protected void onResume() {
        mapView.onResume();
        startLocation();
        super.onResume();
    }

    @Override
    protected void onPause() {
        mapView.onPause();
        stopLocation();
        super.onPause();
        lastLocation = null;
    }

    @Override
    protected void onDestroy() {
        destroyLocation();
        mapView.onDestroy();
        super.onDestroy();
    }

    private AMapLocationClient locationClient = null;
    private AMapLocationClientOption locationOption = null;

    /**
     * 初始化定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void initLocation() {
        //初始化client
        locationClient = new AMapLocationClient(this.getApplicationContext());
        locationOption = getDefaultOption();
        //设置定位参数
        locationClient.setLocationOption(locationOption);
        // 设置定位监听
        locationClient.setLocationListener(locationListener);

    }

    /**
     * 默认的定位参数
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private AMapLocationClientOption getDefaultOption() {
        AMapLocationClientOption mOption = new AMapLocationClientOption();
        mOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);//可选，设置定位模式，可选的模式有高精度、仅设备、仅网络。默认为高精度模式
        mOption.setGpsFirst(false);//可选，设置是否gps优先，只在高精度模式下有效。默认关闭
        mOption.setHttpTimeOut(30000);//可选，设置网络请求超时时间。默认为30秒。在仅设备模式下无效
//        mOption.setInterval(10000);//可选，设置定位间隔。默认为2秒
        mOption.setNeedAddress(true);//可选，设置是否返回逆地理地址信息。默认是true
        mOption.setOnceLocation(false);//可选，设置是否单次定位。默认是false
        mOption.setOnceLocationLatest(false);//可选，设置是否等待wifi刷新，默认为false.如果设置为true,会自动变为单次定位，持续定位时不要使用
        AMapLocationClientOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTP);//可选， 设置网络请求的协议。可选HTTP或者HTTPS。默认为HTTP
        mOption.setSensorEnable(false);//可选，设置是否使用传感器。默认是false
        mOption.setWifiScan(true); //可选，设置是否开启wifi扫描。默认为true，如果设置为false会同时停止主动刷新，停止以后完全依赖于系统刷新，定位位置可能存在误差
        mOption.setLocationCacheEnable(true); //可选，设置是否使用缓存定位，默认为true
        mOption.setGeoLanguage(AMapLocationClientOption.GeoLanguage.DEFAULT);//可选，设置逆地理信息的语言，默认值为默认语言（根据所在地区选择语言）
        return mOption;
    }


    /**
     * 定位监听
     */
    AMapLocationListener locationListener = new AMapLocationListener() {
        @Override
        public void onLocationChanged(AMapLocation location) {
            if (null != location) {
                //errCode等于0代表定位成功，其他的为定位失败，具体的可以参照官网定位错误码说明
                if (location.getErrorCode() == 0) {
                    if (lastLocation != null) {
                        if (lastLocation.getLatitude() == location.getLatitude() && lastLocation.getLongitude() == location.getLongitude()) {
                            Log.d("map", "same location, skip refresh");
                            // mMapView.refresh(); //need this refresh?
                            return;
                        }
                    }
                    titleBarMap.getRightLayout().setClickable(true);
                    lastLocation = location;
                    aMap.clear();
                    showMap(lastLocation.getLatitude(), lastLocation.getLongitude());
                }
            } else {
                showErrorToast("定位失败");
            }
        }
    };


    /**
     * 开始定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void startLocation() {
        if(locationClient!=null){
            // 设置定位参数
            locationClient.setLocationOption(locationOption);
            // 启动定位
            locationClient.startLocation();
        }
    }

    /**
     * 停止定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void stopLocation() {
        if(locationClient!=null){
            locationClient.stopLocation();
        }
    }

    /**
     * 销毁定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void destroyLocation() {
        if (null != locationClient) {
            /**
             * 如果AMapLocationClient是在当前Activity实例化的，
             * 在Activity的onDestroy中一定要执行AMapLocationClient的onDestroy
             */
            locationClient.onDestroy();
            locationClient = null;
            locationOption = null;
        }
    }
    /**
     * show error message
     *
     * @param message
     */
    protected void showErrorToast(String message) {
        Toast.makeText(EaseBaiduMapActivity.this, message, Toast.LENGTH_SHORT).show();
    }
}
