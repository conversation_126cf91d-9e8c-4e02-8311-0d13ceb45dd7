package com.kisoft.yuejianli.im;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.fragment.app.FragmentTransaction;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.im.base.BaseActivity;
import com.kisoft.yuejianli.im.conversation.ConversationListFragment;

/**
 * 会话
 */
public class ConversationListActivity extends BaseActivity {

    TextView tvTitle;
    TextView tvRemindNum;

    public static void launch(Activity activity) {
        Intent intent = new Intent(activity, ConversationListActivity.class);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(getLayoutId());
        initView();
    }

    public int getLayoutId() {
        return R.layout.activity_conversation_list;
    }

    private void initView() {
        tvTitle = findViewById(R.id.tv_title);
        tvRemindNum = findViewById(R.id.tvRemindNum);
        tvTitle.setText("会话");

        TextView tvSubmit = findViewById(R.id.tv_submit);
        tvSubmit.setText("发起");
        tvSubmit.setVisibility(View.VISIBLE);
        tvSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //IMContractListActivity.launch(ConversationListActivity.this);
                Intent intent = new Intent();
//                intent.putExtra("isChat",true);
//                intent.setClass(ConversationListActivity.this,CompanyAddressListActivity.class);
                intent.setClass(ConversationListActivity.this,IMCompanyContractListActivity.class);
                startActivity(intent);
            }
        });

        FragmentTransaction t = getSupportFragmentManager().beginTransaction();
        t.replace(R.id.flContainer, new ConversationListFragment()).commit();
        findViewById(R.id.iv_back).setOnClickListener(v -> finish());
    }
}