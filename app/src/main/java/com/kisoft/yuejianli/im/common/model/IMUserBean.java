package com.kisoft.yuejianli.im.common.model;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.hyphenate.util.HanziToPinyin;

import java.util.ArrayList;
import java.util.List;

/**
 * 服务器返回IM用户
 */
public class IMUserBean {

    /**
     * IMaccount : 150121165340445289b7a9fe868ce32d
     * IMname : 杨南辉
     * account : yangnanhui
     */

    private String IMaccount;
    private String IMname;
    private String account;

    public String getIMaccount() {
        return IMaccount;
    }

    public void setIMaccount(String IMaccount) {
        this.IMaccount = IMaccount;
    }

    public String getIMname() {
        return IMname;
    }

    public void setIMname(String IMname) {
        this.IMname = IMname;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

}
