package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ResourceManagementListContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ContractPerformance;
import com.kisoft.yuejianli.entity.EmpProfess;
import com.kisoft.yuejianli.entity.FilingCertificate;
import com.kisoft.yuejianli.entity.PersonPerformance;
import com.kisoft.yuejianli.entity.ResourceManagementSearch;
import com.kisoft.yuejianli.model.ResourceManagementListModel;
import com.kisoft.yuejianli.views.ResourceManagementListActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ResourceManagementListPresenter extends BasePresenter<ResourceManagementListActivity,
        ResourceManagementListModel> implements ResourceManagementListContract.Presenter {
    public ResourceManagementListPresenter(ResourceManagementListActivity mView, ResourceManagementListModel model) {
        super(mView, model);
    }

    private int page=1;
    private boolean isFirstLoad=true;

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getContractList(ResourceManagementSearch managementSearch, int count, final int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getContractList(managementSearch,count,page,10).enqueue(new Callback<NetworkResponse<BaseList<ContractPerformance>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<BaseList<ContractPerformance>>> call, Response<NetworkResponse<BaseList<ContractPerformance>>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showContractList(response.body().getData(), type);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<BaseList<ContractPerformance>>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getSupPerformance(ResourceManagementSearch managementSearch, int count, final int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getSupPerformance(managementSearch,count,page,10).enqueue(new Callback<NetworkResponse<BaseList<PersonPerformance>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<BaseList<PersonPerformance>>> call, Response<NetworkResponse<BaseList<PersonPerformance>>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showSupPerformance(response.body().getData(), type);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<BaseList<PersonPerformance>>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getPersonList(ResourceManagementSearch managementSearch, int count, final int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getPersonList(managementSearch,count,page,10).enqueue(new Callback<NetworkResponse<BaseList<PersonPerformance>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<BaseList<PersonPerformance>>> call, Response<NetworkResponse<BaseList<PersonPerformance>>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showSupPerformance(response.body().getData(), type);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<BaseList<PersonPerformance>>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getEmpProfess(ResourceManagementSearch managementSearch, int count, final int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getEmpProfess(managementSearch,count,page,10).enqueue(new Callback<NetworkResponse<BaseList<EmpProfess>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<BaseList<EmpProfess>>> call, Response<NetworkResponse<BaseList<EmpProfess>>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showEmpProfess(response.body().getData(), type);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<BaseList<EmpProfess>>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getCertificate(ResourceManagementSearch managementSearch, int count, final int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getCertificate(managementSearch,count,page,10).enqueue(new Callback<NetworkResponse<BaseList<FilingCertificate>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<BaseList<FilingCertificate>>> call, Response<NetworkResponse<BaseList<FilingCertificate>>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showCertificate(response.body().getData(), type);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<BaseList<FilingCertificate>>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }
}
