package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.Count1Contract;
import com.kisoft.yuejianli.entity.ProjectprobPieDto;
import com.kisoft.yuejianli.model.Count1Model;
import com.kisoft.yuejianli.views.CountFragment1;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by yan<PERSON> on 2018/12/28.
 */
public class Count1Presenter extends BasePresenter<CountFragment1, Count1Model> implements
        Count1Contract.Count1PresenterContract{

    public Count1Presenter(CountFragment1 mView, Count1Model mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getProjectProbPie(String projectId, String month) {
        mModel.getProjectProbPie(projectId, month).enqueue(new Callback<NetworkResponse<List<ProjectprobPieDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ProjectprobPieDto>>> call, Response<NetworkResponse<List<ProjectprobPieDto>>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.dataBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ProjectprobPieDto>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
