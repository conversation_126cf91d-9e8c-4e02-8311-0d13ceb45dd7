package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.CompanyPhoneDirectoryContract;
import com.kisoft.yuejianli.entity.Communication;
import com.kisoft.yuejianli.model.CompanyPhoneDirecoryModel;
import com.kisoft.yuejianli.views.CompanyPhoneDirectoryActivity;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class CompanyPhoneDirectoryPresnter extends BasePresenter<CompanyPhoneDirectoryActivity, CompanyPhoneDirecoryModel> implements CompanyPhoneDirectoryContract.CompanyPhoneDirectoryPresenterContract {
    public CompanyPhoneDirectoryPresnter(CompanyPhoneDirectoryActivity mView, CompanyPhoneDirecoryModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getPhoneInfo(String uid) {
        mModel.getPhoneInfo(uid).enqueue(new Callback<NetworkResponse<List<Communication>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<Communication>>> call, Response<NetworkResponse<List<Communication>>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showPhoneInfo(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<Communication>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }


}
