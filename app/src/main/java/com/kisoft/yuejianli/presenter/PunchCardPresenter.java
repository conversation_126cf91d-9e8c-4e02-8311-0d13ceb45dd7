package com.kisoft.yuejianli.presenter;

import android.util.Log;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.contract.PunchCardContract;
import com.kisoft.yuejianli.entity.PunchCardInfo;
import com.kisoft.yuejianli.model.PunchCardModel;
import com.kisoft.yuejianli.views.PunchCardActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import com.kisoft.yuejianli.base.BasePresenter;

/**
 * Created by tudou on 2018/3/20.
 */

public class PunchCardPresenter extends BasePresenter<PunchCardActivity, PunchCardModel>
        implements PunchCardContract.PunchCardPresenterContract {


    public PunchCardPresenter(PunchCardActivity mView, PunchCardModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }


    @Override
    public void getTodayPunchCardInfo(String uid, String time ,String projectId) {
        mModel.getTodayPunchcardInfo(uid, time,projectId).enqueue(new Callback<NetworkResponse<PunchCardInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<PunchCardInfo>> call, Response<NetworkResponse<PunchCardInfo>> response) {
                if(response.body() != null  && response.body().getCode() == NetworkResponse.OK){
//                    Log.d("请求下来的用户名",response.body().getData().getUserName());
                    mView.initPunchCardStatus(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<PunchCardInfo>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void punchStartCard(String uid, String projectId,  PunchCardInfo info) {
        mModel.punchStartCard(uid, projectId, info ).enqueue(new Callback<NetworkResponse<PunchCardInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<PunchCardInfo>> call, Response<NetworkResponse<PunchCardInfo>> response) {
                if(response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    if(response.body().getData() != null){
                        // 打卡成功、
                        mView.initPunchCardStatus(response.body().getData());
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<PunchCardInfo>> call, Throwable t) {
                t.printStackTrace();
                mView.showToast("打卡失败");
            }
        });
    }

}
