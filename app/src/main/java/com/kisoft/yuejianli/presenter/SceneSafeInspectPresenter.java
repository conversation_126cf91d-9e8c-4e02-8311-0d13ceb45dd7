package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.SceneSafeInspectContract;
import com.kisoft.yuejianli.entity.ProjectSafeInspection;
import com.kisoft.yuejianli.model.SceneSafeInspectModel;
import com.kisoft.yuejianli.utils.PhotoUtil;
import com.kisoft.yuejianli.views.SceneSafeInspectActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/5/3.
 */

public class SceneSafeInspectPresenter extends BasePresenter<SceneSafeInspectActivity,SceneSafeInspectModel>
        implements SceneSafeInspectContract.SceneSafeInspectPresenterContract{

    public SceneSafeInspectPresenter(SceneSafeInspectActivity mView, SceneSafeInspectModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void uploadPhotoImage(String photoPath) {
        try {
            PhotoUtil.uploadPhoto1(photoPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void commitSceneSafeInspect(String uid, String projectId,String receive,ProjectSafeInspection inspection,boolean isAdd) {
        mModel.commitSceneSafeInspect(uid, projectId,receive,inspection,isAdd).enqueue(new Callback<NetworkResponse<Boolean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Boolean>> call, Response<NetworkResponse<Boolean>> response) {
                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    mView.showCommitResulte(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Boolean>> call, Throwable t) {
                t.printStackTrace();
                mView.showCommitResulte(false);
            }
        });
    }
}
