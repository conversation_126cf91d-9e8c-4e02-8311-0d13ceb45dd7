package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ProjectCheckContract;
import com.kisoft.yuejianli.entity.ProjectApproverInfo;
import com.kisoft.yuejianli.entity.ProjectCheckInfo;
import com.kisoft.yuejianli.model.ProjectCheckModel;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/5/3.
 */

public class ProjectCheckPresenter extends BasePresenter<ProjectCheckContract.View, ProjectCheckModel>
        implements ProjectCheckContract.Presenter{

    public ProjectCheckPresenter(ProjectCheckContract.View mView, ProjectCheckModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getProjectcheckById(String pcId) {
        mView.showProgress();
        mModel.getProjectcheckById(pcId).enqueue(new Callback<NetworkResponse<ProjectCheckInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProjectCheckInfo>> call, Response<NetworkResponse<ProjectCheckInfo>> response) {
                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    mView.showProjectcheck(response.body().getData());
                }
                mView.dismissProgress();
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProjectCheckInfo>> call, Throwable t) {
                t.printStackTrace();
                mView.showError();
                mView.dismissProgress();
            }
        });
    }

    @Override
    public void saveProjectcheck(ProjectCheckInfo info) {
        mView.showProgress();
        mModel.saveProjectcheck(info).enqueue(new Callback<NetworkResponse<Boolean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Boolean>> call, Response<NetworkResponse<Boolean>> response) {
                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    mView.showCommitResult(response.body().getData());
                }else {
                    mView.showCommitResult(false);
                }
                mView.dismissProgress();
            }

            @Override
            public void onFailure(Call<NetworkResponse<Boolean>> call, Throwable t) {
                t.printStackTrace();
                mView.showCommitResult(false);
                mView.dismissProgress();
            }
        });
    }

    @Override
    public void updateTrack(ProjectCheckInfo info) {
        mView.showProgress();
        mModel.updateTrack(info).enqueue(new Callback<NetworkResponse<Boolean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Boolean>> call, Response<NetworkResponse<Boolean>> response) {
                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    mView.showCommitResult(response.body().getData());
                }else {
                    mView.showCommitResult(false);
                }
                mView.dismissProgress();
            }

            @Override
            public void onFailure(Call<NetworkResponse<Boolean>> call, Throwable t) {
                t.printStackTrace();
                mView.showCommitResult(false);
                mView.dismissProgress();
            }
        });
    }

    @Override
    public void updateFeedback(ProjectCheckInfo info) {
        mView.showProgress();
        mModel.updateFeedback(info).enqueue(new Callback<NetworkResponse<Boolean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Boolean>> call, Response<NetworkResponse<Boolean>> response) {
                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    mView.showCommitResult(response.body().getData());
                }else {
                    mView.showCommitResult(false);
                }
                mView.dismissProgress();
            }

            @Override
            public void onFailure(Call<NetworkResponse<Boolean>> call, Throwable t) {
                t.printStackTrace();
                mView.showCommitResult(false);
                mView.dismissProgress();
            }
        });
    }

    @Override
    public void getUserIdsAndNamesByProjectId(String projectId) {
        mModel.getUserIdsAndNamesByProjectId(projectId).enqueue(new Callback<NetworkResponse<List<ProjectApproverInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ProjectApproverInfo>>> call, Response<NetworkResponse<List<ProjectApproverInfo>>> response) {
                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    mView.showUserIdsAndNamesByProjectId(response.body().getData());
                }
                mView.dismissProgress();
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ProjectApproverInfo>>> call, Throwable t) {
                t.printStackTrace();
                mView.dismissProgress();
            }
        });
    }
}
