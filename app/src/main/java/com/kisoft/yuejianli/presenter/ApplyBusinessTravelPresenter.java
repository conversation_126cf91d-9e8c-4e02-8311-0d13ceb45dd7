package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ApplyBusinessTravelContract;
import com.kisoft.yuejianli.entity.ApplyBusinessTravelInfo;
import com.kisoft.yuejianli.entity.BusinessTravelType;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.model.ApplyBusinessTravelModel;
import com.kisoft.yuejianli.views.ApplyBusinessTravelFragment;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ApplyBusinessTravelPresenter extends BasePresenter<ApplyBusinessTravelFragment,
        ApplyBusinessTravelModel> implements ApplyBusinessTravelContract.Presenter {
    public ApplyBusinessTravelPresenter(ApplyBusinessTravelFragment mView, ApplyBusinessTravelModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getBusinessTravelType() {
        mView.showProgress();
        mModel.getBusinessTravelType().enqueue(new Callback<NetworkResponse<List<BusinessTravelType>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<BusinessTravelType>>> call, Response<NetworkResponse<List<BusinessTravelType>>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.typeListBack(response.body().getData());
                }else {
                    mView.typeListBack(null);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<BusinessTravelType>>> call, Throwable t) {
                mView.dismissProgress();
                t.printStackTrace();
                mView.typeListBack(null);
            }
        });
    }

    @Override
    public void submitApply(ApplyBusinessTravelInfo info) {
        mView.showProgress();
        mModel.submitApplyInfo(info).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.applyBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getInfo(ProcessListBean bean) {

        mView.showProgress();
        mModel.getInfo(bean).enqueue(new Callback<NetworkResponse<ApplyBusinessTravelInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ApplyBusinessTravelInfo>> call, Response<NetworkResponse<ApplyBusinessTravelInfo>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.infoBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ApplyBusinessTravelInfo>> call, Throwable t) {
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }
}
