package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ApproveHandleContract;
import com.kisoft.yuejianli.entity.MaterialInspect;
import com.kisoft.yuejianli.entity.QualityAcceptance;
import com.kisoft.yuejianli.entity.QualityApprove;
import com.kisoft.yuejianli.entity.QualityInspection;
import com.kisoft.yuejianli.entity.QualityInvisibility;
import com.kisoft.yuejianli.entity.SideReport;
import com.kisoft.yuejianli.entity.SupervisionLog;
import com.kisoft.yuejianli.model.ApproveHandleModel;
import com.kisoft.yuejianli.views.ApproveHandleActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/4/24.
 */

public class ApproveHandlePresenter extends BasePresenter<ApproveHandleActivity,
        ApproveHandleModel> implements ApproveHandleContract.ApproveHandlePresenterContract {

    public ApproveHandlePresenter(ApproveHandleActivity mView, ApproveHandleModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getQualityAcceptance(String userId, String projectId, String businessId) {
        mModel.getQualityAcceptance(userId, projectId, businessId).enqueue(new Callback<NetworkResponse<QualityAcceptance>>() {
            @Override
            public void onResponse(Call<NetworkResponse<QualityAcceptance>> call, Response<NetworkResponse<QualityAcceptance>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    if(response.body().getData() != null){
                        mView.showQualityAcceptance(response.body().getData());
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<QualityAcceptance>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getSideReport(String userId, final String projectId, String businessId) {
        mModel.getSideReport(userId, projectId, businessId).enqueue(new Callback<NetworkResponse<SideReport>>() {
            @Override
            public void onResponse(Call<NetworkResponse<SideReport>> call, Response<NetworkResponse<SideReport>> response) {
                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    if(response.body().getData() != null){
                        mView.showSideReport(response.body().getData());
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<SideReport>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getQualityInvisibility(String userId, String projectId, String businessId) {
            mModel.getQualityInvisibility(userId, projectId, businessId).enqueue(new Callback<NetworkResponse<QualityInvisibility>>() {
                @Override
                public void onResponse(Call<NetworkResponse<QualityInvisibility>> call, Response<NetworkResponse<QualityInvisibility>> response) {
                    if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                        if(response.body().getData() != null){
                            mView.showQualityInvisiblity(response.body().getData());
                        }
                    }
                }

                @Override
                public void onFailure(Call<NetworkResponse<QualityInvisibility>> call, Throwable t) {
                    t.printStackTrace();
                }
            });
    }

    @Override
    public void getSupervisionLog(String userId, String projectId, String businessId) {
        mModel.getSupervisionLog(userId, projectId, businessId).enqueue(new Callback<NetworkResponse<SupervisionLog>>() {
            @Override
            public void onResponse(Call<NetworkResponse<SupervisionLog>> call, Response<NetworkResponse<SupervisionLog>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    if(response.body().getData() != null){
                        mView.showSupervisionLog(response.body().getData());
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<SupervisionLog>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getQualityInspection(String userId, String projectId, String businessId) {
        mModel.getQualityInspection(userId, projectId, businessId).enqueue(new Callback<NetworkResponse<QualityInspection>>() {
            @Override
            public void onResponse(Call<NetworkResponse<QualityInspection>> call, Response<NetworkResponse<QualityInspection>> response) {
                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    if(response.body().getData() != null){
                        mView.showQualityInspection(response.body().getData());
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<QualityInspection>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getMaterialInspect(String userId, String projectId, String businessId) {
        mModel.getMaterialInspect(userId, projectId, businessId).enqueue(new Callback<NetworkResponse<MaterialInspect>>() {
            @Override
            public void onResponse(Call<NetworkResponse<MaterialInspect>> call, Response<NetworkResponse<MaterialInspect>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK ){
                    if(response.body().getData() != null){
                        mView.showMaterialInspect(response.body().getData());
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<MaterialInspect>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void updateApprove(String userId, String projectId, QualityApprove approve) {
        mModel.updateApprove(userId, projectId, approve).enqueue(new Callback<NetworkResponse<QualityApprove>>() {
            @Override
            public void onResponse(Call<NetworkResponse<QualityApprove>> call, Response<NetworkResponse<QualityApprove>> response) {
                if(response.body() != null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showUpdateResulte(true);
                }else {
                    mView.showUpdateResulte(false);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<QualityApprove>> call, Throwable t) {
                t.printStackTrace();
                mView.showUpdateResulte(false);
            }
        });
    }
}
