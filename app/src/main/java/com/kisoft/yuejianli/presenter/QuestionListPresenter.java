package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.QuestionListContract;
import com.kisoft.yuejianli.entity.QuestionListDto;
import com.kisoft.yuejianli.model.QuestionListModel;
import com.kisoft.yuejianli.views.QuestionListFragment;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/21.
 */
public class QuestionListPresenter extends BasePresenter<QuestionListFragment, QuestionListModel> implements
        QuestionListContract.QuestionListPresenterContract {

    public QuestionListPresenter(QuestionListFragment mView, QuestionListModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void toReferList(String userId, String projectId, String pageSize, String page, String count, int type, int pageType) {
        mModel.toReferList(userId, projectId, pageSize, page, count, type + "", pageType).enqueue(new Callback<NetworkResponse<QuestionListDto>>() {
            @Override
            public void onResponse(Call<NetworkResponse<QuestionListDto>> call, Response<NetworkResponse<QuestionListDto>> response) {
                mView.finshRefresh();
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    mView.toReferList(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<QuestionListDto>> call, Throwable t) {
                mView.finshRefresh();
                t.printStackTrace();
            }
        });
    }
}
