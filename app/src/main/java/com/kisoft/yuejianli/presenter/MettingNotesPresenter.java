package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.MettingsNotesContract;
import com.kisoft.yuejianli.entity.MeetingInfo;
import com.kisoft.yuejianli.model.MettingNotesModel;
import com.kisoft.yuejianli.views.MeetingsActivity;

/**
 * Created by tudou on 2018/3/26.
 */

public class MettingNotesPresenter extends BasePresenter<MeetingsActivity, MettingNotesModel>
        implements MettingsNotesContract.MettingNotesPresenterContract{

    public MettingNotesPresenter(MeetingsActivity mView, MettingNotesModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getMettingInfo(String ids) {
        mModel.getMettingInfo(ids).enqueue(new Callback<NetworkResponse<List<MeetingInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<MeetingInfo>>> call, Response<NetworkResponse<List<MeetingInfo>>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    if(response.body().getData() != null && response.body().getData().size() >0){
                        mView.showmettingInfo(response.body().getData());
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<MeetingInfo>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
