package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ComSourseListContract;
import com.kisoft.yuejianli.entity.ComSourseListBean;
import com.kisoft.yuejianli.entity.ComSourseListDto;
import com.kisoft.yuejianli.model.ComSourseListModel;
import com.kisoft.yuejianli.utils.GsonUtil;
import com.kisoft.yuejianli.views.ComSourseListActivity;

import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/30.
 */

public class ComSourseListPresenter extends BasePresenter<ComSourseListActivity, ComSourseListModel>
        implements ComSourseListContract.ComSourseListPresenterContract {

    public ComSourseListPresenter(ComSourseListActivity mView, ComSourseListModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getList(String userId, String userName, String folderGuid, int count, int page, int pageSize) {
        // 获取当日巡检报告
        mModel.getList(userId, userName, folderGuid, count, page, pageSize).enqueue(new Callback<NetworkResponse<ComSourseListDto>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ComSourseListDto>> call, Response<NetworkResponse<ComSourseListDto>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    mView.listDataBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ComSourseListDto>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getFilePath(String id, String name) {
        mModel.getFilePath(id, name).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    mView.filePathBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getFilePreview(Map<String,Object> map) {
        mModel.getFilePreview(map).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    Map map = (Map) response.body().getData();
//                    Map<String, Object> map1 = GsonUtil.GsonToMaps(response.body().getData().toString());
                    mView.FilePreviewBack(map);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
