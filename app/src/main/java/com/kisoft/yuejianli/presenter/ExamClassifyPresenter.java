package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ExamClassifyContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ExamQuestBank;
import com.kisoft.yuejianli.model.ExamClassifyModel;
import com.kisoft.yuejianli.views.ExamClassifyTestActivity;
import com.kisoft.yuejianli.views.ExamRecordActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ExamClassifyPresenter extends BasePresenter<ExamClassifyTestActivity,
        ExamClassifyModel> implements ExamClassifyContract.Presenter {
    public ExamClassifyPresenter(ExamClassifyTestActivity mView, ExamClassifyModel model) {
        super(mView, model);
    }

    private int page=1;
    private boolean isFirstLoad=true;

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getQuestbankList(int count, final int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getQuestbankList(count,page,10).enqueue(new Callback<NetworkResponse<BaseList<ExamQuestBank>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<BaseList<ExamQuestBank>>> call, Response<NetworkResponse<BaseList<ExamQuestBank>>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showQuestbankList(response.body().getData(), type);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<BaseList<ExamQuestBank>>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }
}
