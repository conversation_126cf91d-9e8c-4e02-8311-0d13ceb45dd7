package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.entity.ApproveInfos;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ApproveToUserContract;
import com.kisoft.yuejianli.model.ApproveToUserModel;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.views.ApproveToUserFragment;

/**
 * Created by tudou on 2018/4/23.
 */

public class ApproveToUserPresenter extends BasePresenter<ApproveToUserFragment,
        ApproveToUserModel> implements ApproveToUserContract.ApproveToUserPresenterContract {

    public ApproveToUserPresenter(ApproveToUserFragment mView, ApproveToUserModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getUserApproves(String projectId, String userId, String pageSize, String page, final String count) {
        mModel.getUserApproves(projectId, userId, pageSize, page,count).enqueue(new Callback<NetworkResponse<ApproveInfos>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ApproveInfos>> call, Response<NetworkResponse<ApproveInfos>> response) {
                if(response.body() != null && response.body().getCode() ==NetworkResponse.OK){
                    if(StringUtil.isEqual("0", count)){
                        mView.showUserApproves(response.body().getData(), 0);
                    }else{
                        mView.showUserApproves(response.body().getData(),1);
                    }

                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ApproveInfos>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
