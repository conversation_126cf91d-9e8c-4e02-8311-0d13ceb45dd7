package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.NoticeListContract;
import com.kisoft.yuejianli.entity.NoticeInfo;
import com.kisoft.yuejianli.entity.NoticeInfo;
import com.kisoft.yuejianli.model.NoticeListModel;
import com.kisoft.yuejianli.views.NoticeListFragment;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/21.
 */
public class NoticeListPresenter extends BasePresenter<NoticeListFragment, NoticeListModel> implements
        NoticeListContract.NoticeListPresenterContract {

    public NoticeListPresenter(NoticeListFragment mView, NoticeListModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void toReferList(String type,String userId,String snStatus, String month, String projectID, String pageSize, final String page, String count) {
        mModel.toReferList(type,userId,snStatus,month,projectID,pageSize,page,count).enqueue(new Callback<NetworkResponse<NoticeInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<NoticeInfo>> call, Response<NetworkResponse<NoticeInfo>> response) {
                mView.finshRefresh();
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    mView.toReferList(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<NoticeInfo>> call, Throwable t) {
                mView.finshRefresh();
                t.printStackTrace();
            }
        });
    }
}
