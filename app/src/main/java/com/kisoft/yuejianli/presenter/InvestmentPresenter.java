package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.InvestmentContract;
import com.kisoft.yuejianli.entity.ProConstructInvestment;
import com.kisoft.yuejianli.entity.ProjectConstructPayCertificate;
import com.kisoft.yuejianli.entity.ProjectConstructSafePayCertificate;
import com.kisoft.yuejianli.model.InvestmentModel;
import com.kisoft.yuejianli.views.InvestmentActivity;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/15.
 */

public class InvestmentPresenter extends BasePresenter<InvestmentActivity, InvestmentModel> implements InvestmentContract.InvestmentPresenterContract {

    public InvestmentPresenter(InvestmentActivity mView, InvestmentModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getProjectInvsetmentInfo(String uid, String projectId) {
        mModel.getProjectInvestmentInfo(uid, projectId).enqueue(new Callback<NetworkResponse<ProConstructInvestment>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProConstructInvestment>> call, Response<NetworkResponse<ProConstructInvestment>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showInvsetmentInfo(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProConstructInvestment>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getPayCertificate(String uid, String projectId) {
        mModel.getPayCertificate(uid, projectId).enqueue(new Callback<NetworkResponse<List<ProjectConstructPayCertificate>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ProjectConstructPayCertificate>>> call, Response<NetworkResponse<List<ProjectConstructPayCertificate>>> response) {
                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    mView.showPayCertificate(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ProjectConstructPayCertificate>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getSafePayCertificate(String uid, String projectId) {
        mModel.getSafePayCertificate(uid, projectId).enqueue(new Callback<NetworkResponse<List<ProjectConstructSafePayCertificate>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ProjectConstructSafePayCertificate>>> call, Response<NetworkResponse<List<ProjectConstructSafePayCertificate>>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showSafePayCertificate(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ProjectConstructSafePayCertificate>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
