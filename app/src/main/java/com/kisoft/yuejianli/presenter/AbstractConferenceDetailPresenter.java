package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.AbstractConferenceDetailContract;
import com.kisoft.yuejianli.contract.EnclosureListContract;
import com.kisoft.yuejianli.entity.AbstractConferenceDto;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.model.AbstractConferenceDetailModel;
import com.kisoft.yuejianli.views.AbstractConferenceDetailActivity;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Description:
 */
public class AbstractConferenceDetailPresenter extends BasePresenter<AbstractConferenceDetailActivity,AbstractConferenceDetailModel>
        implements AbstractConferenceDetailContract.AbstractConferenceDetailPresenterContract{

    public AbstractConferenceDetailPresenter(AbstractConferenceDetailActivity mView, AbstractConferenceDetailModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getAbstractConferenceDetail(String mmId) {
        mModel.getAbstractConferenceDetail(mmId).enqueue(new Callback<NetworkResponse<AbstractConferenceDto.AbstractConferenceBean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<AbstractConferenceDto.AbstractConferenceBean>> call, Response<NetworkResponse<AbstractConferenceDto.AbstractConferenceBean>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.abstractConferenceDetailBack(response.body().getData());
                } else {
                    mView.abstractConferenceDetailBack(null);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<AbstractConferenceDto.AbstractConferenceBean>> call, Throwable t) {
                t.printStackTrace();
                mView.abstractConferenceDetailBack(null);
            }
        });

    }

    @Override
    public void getEnclosureList(String mmId) {
        mModel.getEnclosureList(mmId).enqueue(new Callback<NetworkResponse<List<EnclosureListDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<EnclosureListDto>>> call, Response<NetworkResponse<List<EnclosureListDto>>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.EnclosureListBack(response.body().getData());
                } else {
                    mView.EnclosureListBack(null);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<EnclosureListDto>>> call, Throwable t) {
                t.printStackTrace();
                mView.EnclosureListBack(null);
            }
        });
    }
}
