package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ProjectPrepareInfoContract;
import com.kisoft.yuejianli.entity.ProjectFilingFirtMeeting;
import com.kisoft.yuejianli.entity.ProjectFilingFirtMeetingInfo;
import com.kisoft.yuejianli.entity.ProjectFilingSupDetailBlameInfo;
import com.kisoft.yuejianli.entity.ProjectFilingSupPlanInfo;
import com.kisoft.yuejianli.model.ProjectPrepareInfoModel;
import com.kisoft.yuejianli.views.ProjectPrepareInfoActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/22.
 */

public class ProjectPrepareInfoPresenter extends BasePresenter<ProjectPrepareInfoActivity, ProjectPrepareInfoModel> implements
        ProjectPrepareInfoContract.ProjectPrepareInfoPresenterContract{

    public ProjectPrepareInfoPresenter(ProjectPrepareInfoActivity mView, ProjectPrepareInfoModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getSupPlanCount(String uid, String projectId, String mType) {
        mModel.getSupPlanCount(uid, projectId, mType).enqueue(new Callback<NetworkResponse<ProjectFilingSupPlanInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProjectFilingSupPlanInfo>> call, Response<NetworkResponse<ProjectFilingSupPlanInfo>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showSupPlanCount(response.body().getData().getCount());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProjectFilingSupPlanInfo>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getSafeSupPlanCount(String uid, String projectId, String mType) {
        mModel.getSafeSupPlanCount(uid, projectId, mType).enqueue(new Callback<NetworkResponse<ProjectFilingSupPlanInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProjectFilingSupPlanInfo>> call, Response<NetworkResponse<ProjectFilingSupPlanInfo>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showSafeSupPlanCount(response.body().getData().getCount());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProjectFilingSupPlanInfo>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getSupBlameCount(String uid, String projectId, String mType) {
        mModel.getSupBlameCount(uid, projectId, mType).enqueue(new Callback<NetworkResponse<ProjectFilingSupDetailBlameInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProjectFilingSupDetailBlameInfo>> call, Response<NetworkResponse<ProjectFilingSupDetailBlameInfo>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showSupBlameCount(response.body().getData().getCount());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProjectFilingSupDetailBlameInfo>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getSafeSupBlameCount(String uid, String projectId, String mType) {
        mModel.getSafeSupBlameCount(uid, projectId, mType).enqueue(new Callback<NetworkResponse<ProjectFilingSupDetailBlameInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProjectFilingSupDetailBlameInfo>> call, Response<NetworkResponse<ProjectFilingSupDetailBlameInfo>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showSafeSupBlameCount(response.body().getData().getCount());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProjectFilingSupDetailBlameInfo>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getOnsideSupPlanCount(String uid, String projectId, String mType) {
        mModel.getOnsideSupPlanCount(uid, projectId, mType).enqueue(new Callback<NetworkResponse<ProjectFilingSupPlanInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProjectFilingSupPlanInfo>> call, Response<NetworkResponse<ProjectFilingSupPlanInfo>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showOnsideSupPlanCount(response.body().getData().getCount());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProjectFilingSupPlanInfo>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getTakeSampleCount(String uid, String projectId, String mType) {
        mModel.getTakeSampleCount(uid, projectId, mType).enqueue(new Callback<NetworkResponse<ProjectFilingSupPlanInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProjectFilingSupPlanInfo>> call, Response<NetworkResponse<ProjectFilingSupPlanInfo>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showTakeSampleCount(response.body().getData().getCount());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProjectFilingSupPlanInfo>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getFirtMeetingCount(String uid, String projectId) {
        mModel.getFirtMeetingCount(uid, projectId).enqueue(new Callback<NetworkResponse<ProjectFilingFirtMeetingInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProjectFilingFirtMeetingInfo>> call, Response<NetworkResponse<ProjectFilingFirtMeetingInfo>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){

                    ProjectFilingFirtMeeting meeting = null;
                    if(response.body().getData().getCount() >0){
                        meeting = response.body().getData().getList().get(0);
                    }
                    mView.showFirtMeetingCount(response.body().getData().getCount() ,meeting);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProjectFilingFirtMeetingInfo>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
