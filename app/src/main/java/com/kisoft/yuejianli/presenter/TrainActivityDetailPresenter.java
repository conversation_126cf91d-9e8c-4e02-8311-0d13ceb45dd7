package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.TrainActivityDetailContract;
import com.kisoft.yuejianli.entity.TrainActivity;
import com.kisoft.yuejianli.model.TrainActivityDetailModel;
import com.kisoft.yuejianli.views.TrainActivityDetailActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/21.
 */

public class TrainActivityDetailPresenter extends BasePresenter<TrainActivityDetailActivity, TrainActivityDetailModel> implements
        TrainActivityDetailContract.TrainActivityDetailPresenterContract{

    public TrainActivityDetailPresenter(TrainActivityDetailActivity mView, TrainActivityDetailModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getTrainActivity(String uid, String taId) {
        mModel.getTrainActivity(uid, taId).enqueue(new Callback<NetworkResponse<TrainActivity>>() {
            @Override
            public void onResponse(Call<NetworkResponse<TrainActivity>> call, Response<NetworkResponse<TrainActivity>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showTrainActivityDetail(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<TrainActivity>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
