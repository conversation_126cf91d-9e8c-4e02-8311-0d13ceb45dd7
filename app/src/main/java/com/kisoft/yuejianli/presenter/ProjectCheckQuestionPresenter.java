package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ProjectCheckQuestionContract;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.model.ProjectCheckQuestionModel;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ProjectCheckQuestionPresenter extends BasePresenter<ProjectCheckQuestionContract.View,
        ProjectCheckQuestionModel> implements ProjectCheckQuestionContract.Presenter {
    public ProjectCheckQuestionPresenter(ProjectCheckQuestionContract.View mView, ProjectCheckQuestionModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getProbNameList() {
        mModel.getProbNameList().enqueue(new Callback<NetworkResponse<List<ApplyType>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ApplyType>>> call, Response<NetworkResponse<List<ApplyType>>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showProbNameList(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ApplyType>>> call, Throwable t) {
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getProbClassList() {
        mModel.getProbClassList().enqueue(new Callback<NetworkResponse<List<ApplyType>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ApplyType>>> call, Response<NetworkResponse<List<ApplyType>>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showProbClassList(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ApplyType>>> call, Throwable t) {
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getProbLevelList() {
        mModel.getProbLevelList().enqueue(new Callback<NetworkResponse<List<ApplyType>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ApplyType>>> call, Response<NetworkResponse<List<ApplyType>>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showProbLevelList(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ApplyType>>> call, Throwable t) {
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }
}
