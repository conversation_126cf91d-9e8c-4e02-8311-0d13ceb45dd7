package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.NewsFragmentContract;
import com.kisoft.yuejianli.entity.MessageTodoReceive;
import com.kisoft.yuejianli.entity.News;
import com.kisoft.yuejianli.model.NewsFragmentModel;
import com.kisoft.yuejianli.views.NewsFragment;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/3/30.
 */

public class NewsFragmentPresnter extends BasePresenter<NewsFragment, NewsFragmentModel> implements
        NewsFragmentContract.NewsFragmentPresenterContract {

    public NewsFragmentPresnter(NewsFragment mView, NewsFragmentModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getNotes(String userId) {
        mModel.getNotes(userId).enqueue(new Callback<NetworkResponse<List<News>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<News>>> call, Response<NetworkResponse<List<News>>> response) {
                if(response.body() != null && response.body().getCode() ==NetworkResponse.OK){
                    if(response.body().getData() != null){
                        mView.showNews(response.body().getData());
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<News>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getTodoNotice(String uid, String state) {
        mModel.getTodoNotice(uid, state).enqueue(new Callback<NetworkResponse<List<MessageTodoReceive>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<MessageTodoReceive>>> call, Response<NetworkResponse<List<MessageTodoReceive>>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showTodoNotice(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<MessageTodoReceive>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getSupNoticeCount(String uid, String projectId) {
        mModel.getSupNoticeCount(uid, projectId).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                if(response.body() != null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showSupNoticeCount(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getQualityReformCount(String uid, String projectId) {
        mModel.getQualityReformCount(uid, projectId).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                if (response .body() != null && response.body().getCode() == NetworkResponse.OK){
                    mView.showQualityReformCount(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
