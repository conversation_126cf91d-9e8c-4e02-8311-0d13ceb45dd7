package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.AddProgressContract;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.ProjectProcess;
import com.kisoft.yuejianli.entity.TProgDetailDTO;
import com.kisoft.yuejianli.model.AddProgressModel;
import com.kisoft.yuejianli.views.AddProgressActivity;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/5/21.
 */

public class AddProgressPresenter extends BasePresenter<AddProgressActivity,AddProgressModel> implements
        AddProgressContract.AddProgressPresenterContract {

    public AddProgressPresenter(AddProgressActivity mView, AddProgressModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void submitProcess(String uid, String projectId, TProgDetailDTO dto) {
        mModel.submitProcess(uid, projectId, dto).enqueue(new Callback<NetworkResponse<Boolean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Boolean>> call, Response<NetworkResponse<Boolean>> response) {
                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    mView.showSubmitResulte(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Boolean>> call, Throwable t) {
                t.printStackTrace();
                mView.showSubmitResulte(false);
            }
        });
    }

    @Override
    public void getType() {
        mModel.getType().enqueue(new Callback<NetworkResponse<List<ApplyType>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ApplyType>>> call, Response<NetworkResponse<List<ApplyType>>> response) {
                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    mView.typeBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ApplyType>>> call, Throwable t) {
                t.printStackTrace();
                mView.showSubmitResulte(false);
            }
        });
    }

    @Override
    public void getDeTailModify(String pdId) {
        mModel.getDeTailModify(pdId).enqueue(new Callback<NetworkResponse<TProgDetailDTO>>() {
            @Override
            public void onResponse(Call<NetworkResponse<TProgDetailDTO>> call, Response<NetworkResponse<TProgDetailDTO>> response) {
                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    mView.dataBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<TProgDetailDTO>> call, Throwable t) {
                t.printStackTrace();
                mView.showSubmitResulte(false);
            }
        });
    }
}
