package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.DocumentBasicDraftContract;
import com.kisoft.yuejianli.entity.ArchivesDto;
import com.kisoft.yuejianli.entity.ArchivesDtos;
import com.kisoft.yuejianli.entity.ToArchivesBean;
import com.kisoft.yuejianli.model.DocumentBasicDraftModel;
import com.kisoft.yuejianli.views.DocumentBasicDraftFragment;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Description: 公文起草 基本信息
 * Author     : yanlu
 * Date       : 2019/1/6 11:29
 */

public class DocumentBasicDraftPresenter extends BasePresenter<DocumentBasicDraftFragment,DocumentBasicDraftModel>
                        implements DocumentBasicDraftContract.DocumentBasicDraftPresenterContract {
    public DocumentBasicDraftPresenter(DocumentBasicDraftFragment mView, DocumentBasicDraftModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void toArchivesAdd(String temId, String fmoduleId) {
        mModel.toArchivesAdd(temId,fmoduleId).enqueue(new Callback<NetworkResponse<ToArchivesBean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ToArchivesBean>> call, Response<NetworkResponse<ToArchivesBean>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showArchivesAdd(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ToArchivesBean>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getSecAndUrg() {
        mModel.getSecAndUrg().enqueue(new Callback<NetworkResponse<ArchivesDtos>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ArchivesDtos>> call, Response<NetworkResponse<ArchivesDtos>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showSecAndUrg(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ArchivesDtos>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void addArchives(ArchivesDto dto) {
        mView.showProgress();
        mModel.addArchives(dto).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.addArchives(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                mView.dismissProgress();
                t.printStackTrace();
            }
        });
    }
}
