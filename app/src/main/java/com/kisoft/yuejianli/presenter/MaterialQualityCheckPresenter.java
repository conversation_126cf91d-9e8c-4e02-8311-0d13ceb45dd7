package com.kisoft.yuejianli.presenter;

import android.graphics.Bitmap;
import android.net.Uri;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.Material;
import com.kisoft.yuejianli.views.MaterialQualityCheckActivity;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.MaterailQualityCheckContract;
import com.kisoft.yuejianli.entity.MaterialInspect;
import com.kisoft.yuejianli.model.MaterialQualityCheckModel;
import com.kisoft.yuejianli.utils.PhotoUtil;

/**
 * Created by tudou on 2018/4/10.
 */

public class MaterialQualityCheckPresenter extends BasePresenter<MaterialQualityCheckActivity,
        MaterialQualityCheckModel> implements MaterailQualityCheckContract.MaterialQualityCheckPresenterContract {

    public MaterialQualityCheckPresenter(MaterialQualityCheckActivity mView, MaterialQualityCheckModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void uploadPhotoImage(String photoPath) {
        try {
            PhotoUtil.uploadPhoto1(photoPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void commitCheck(MaterialInspect inspect, String receiverId) {
        mModel.commitCheck(inspect, receiverId).enqueue(new Callback<NetworkResponse>() {
            @Override
            public void onResponse(Call<NetworkResponse> call, Response<NetworkResponse> response) {
                if(response.body() != null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showCheckCommitResulte(true);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse> call, Throwable t) {
                t.printStackTrace();
                mView.showCheckCommitResulte(false);
            }
        });
    }

    @Override
    public void getMaterialList(String materialType) {
        mModel.getMaterialList(materialType).enqueue(new Callback<NetworkResponse<List<Material>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<Material>>> call, Response<NetworkResponse<List<Material>>> response) {
                if(response.body() != null && response.body().getCode() ==NetworkResponse.OK){
                    if(response.body().getData() != null){
                        mView.showMaterials(response.body().getData());
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<Material>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }


}
