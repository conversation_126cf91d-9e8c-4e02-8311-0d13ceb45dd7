package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ProjectSupPlanContract;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.model.ProjectSupPlanModel;
import com.kisoft.yuejianli.views.ProjectSupPlanActivity;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ProjectSupPlanPresenter extends BasePresenter<ProjectSupPlanActivity, ProjectSupPlanModel> implements ProjectSupPlanContract.ProjectSupPlanPresenterContract {
    public ProjectSupPlanPresenter(ProjectSupPlanActivity mView, ProjectSupPlanModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getEnclosureList(String id) {
        mModel.getEnclosureList(id).enqueue(new Callback<NetworkResponse<List<EnclosureListDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<EnclosureListDto>>> call, Response<NetworkResponse<List<EnclosureListDto>>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.EnclosureListBack(response.body().getData());
                } else {
                    mView.EnclosureListBack(null);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<EnclosureListDto>>> call, Throwable t) {
                t.printStackTrace();
                mView.EnclosureListBack(null);
            }
        });
    }
}
