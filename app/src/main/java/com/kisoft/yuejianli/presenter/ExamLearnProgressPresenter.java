package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ExamLearnProgressContract;
import com.kisoft.yuejianli.entity.ExamLearnProgressList;
import com.kisoft.yuejianli.model.ExamLearnProgressModel;
import com.kisoft.yuejianli.views.ExamLearnProgressActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ExamLearnProgressPresenter extends BasePresenter<ExamLearnProgressActivity,
        ExamLearnProgressModel> implements ExamLearnProgressContract.Presenter {
    public ExamLearnProgressPresenter(ExamLearnProgressActivity mView, ExamLearnProgressModel examLearnProgressModel) {
        super(mView, examLearnProgressModel);
    }

    private int page=1;
    private boolean isFirstLoad=true;

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getLearnProgressList(int count, int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getLearnProgressList(count,page,10).enqueue(new Callback<NetworkResponse<ExamLearnProgressList>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ExamLearnProgressList>> call, Response<NetworkResponse<ExamLearnProgressList>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showLearnProgressList(response.body().getData(), type);
                }else {
                    mView.showError();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ExamLearnProgressList>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }
}
