package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.Count2Contract;
import com.kisoft.yuejianli.entity.ProjectprobPieDto;
import com.kisoft.yuejianli.model.Count2Model;
import com.kisoft.yuejianli.views.CountFragment2;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by yan<PERSON> on 2018/12/28.
 */
public class Count2Presenter extends BasePresenter<CountFragment2, Count2Model> implements
        Count2Contract.Count2PresenterContract{

    public Count2Presenter(CountFragment2 mView, Count2Model mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getProjectProbChain(String projectId, String month, String flag) {
        mModel.getProjectProbChain(projectId, month, flag).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.dataBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
