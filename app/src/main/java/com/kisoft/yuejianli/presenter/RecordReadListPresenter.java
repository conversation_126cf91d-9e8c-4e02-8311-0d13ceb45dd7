package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.RecordReadListContract;
import com.kisoft.yuejianli.entity.RecordReadListDto;
import com.kisoft.yuejianli.model.RecordReadListModel;
import com.kisoft.yuejianli.views.RecordReadListFragment;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Description:
 * Author     : yanlu
 * Date       : 2019/1/15 11:00
 */
public class RecordReadListPresenter extends BasePresenter<RecordReadListFragment,RecordReadListModel>
        implements RecordReadListContract.RecordReadListPresenterContract{

    public RecordReadListPresenter(RecordReadListFragment mView, RecordReadListModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getRecordReadList(String id) {
        mModel.getRecordReadList(id).enqueue(new Callback<NetworkResponse<List<RecordReadListDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<RecordReadListDto>>> call, Response<NetworkResponse<List<RecordReadListDto>>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.RecordReadListBack(response.body().getData());
                } else {
                    mView.RecordReadListBack(null);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<RecordReadListDto>>> call, Throwable t) {
                t.printStackTrace();
                mView.RecordReadListBack(null);
            }
        });
    }
}
