package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.contract.SuperNoticeAllContract;
import com.kisoft.yuejianli.entity.SuperNoticeInfos;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.model.SuperNoticeAllModel;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.views.SuperNoticeAllFragment;

/**
 * Created by tudou on 2018/5/15.
 */

public class SuperNoticeAllPresenter extends BasePresenter<SuperNoticeAllFragment,SuperNoticeAllModel> implements
        SuperNoticeAllContract.SuperNoticeAllPresenterContract {

    public SuperNoticeAllPresenter(SuperNoticeAllFragment mView, SuperNoticeAllModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getAllSuperNotices(String uid, String projectId, String month, String count, String pageSize, final String page) {
        mModel.getAllSuperNotices(uid, projectId, month, count, pageSize, page).enqueue(new Callback<NetworkResponse<SuperNoticeInfos>>() {
            @Override
            public void onResponse(Call<NetworkResponse<SuperNoticeInfos>> call, Response<NetworkResponse<SuperNoticeInfos>> response) {
                if(response.body() != null && response.body().getCode() ==NetworkResponse.OK){
                    if(StringUtil.isEqual("1",page)){
                        mView.showSuperNotices(response.body().getData(), 0);
                    }else {
                        mView.showSuperNotices(response.body().getData(), 1);
                    }

                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<SuperNoticeInfos>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getHandelCount(String uid, String projectId) {
        mModel.getHandelCount(uid, projectId).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                if(response.body() != null &&  response.body().getCode() ==NetworkResponse.OK){
                    mView.showHandelCount(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
