package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ApplyConSettlContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ConCostTicketShow;
import com.kisoft.yuejianli.entity.ConPayment;
import com.kisoft.yuejianli.entity.ConSettlApply;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.model.ApplyConSettlModel;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ApplyConSettlPresenter extends BasePresenter<ApplyConSettlContract.View,
        ApplyConSettlModel> implements ApplyConSettlContract.Presenter {
    public ApplyConSettlPresenter(ApplyConSettlContract.View mView, ApplyConSettlModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void submitApply(ConSettlApply info) {
        mView.showProgress();
        mModel.submitApplyInfo(info).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.applyBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getInfo(ProcessListBean bean) {
        mView.showProgress();
        mModel.getInfo(bean).enqueue(new Callback<NetworkResponse<ConSettlApply>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ConSettlApply>> call, Response<NetworkResponse<ConSettlApply>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.infoBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ConSettlApply>> call, Throwable t) {
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getConCostTicketShowList(String csiId) {
        mView.showProgress();
        mModel.getConCostTicketShowList(csiId).enqueue(new Callback<NetworkResponse<BaseList<ConCostTicketShow>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<BaseList<ConCostTicketShow>>> call, Response<NetworkResponse<BaseList<ConCostTicketShow>>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showCostTicketShowList(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<BaseList<ConCostTicketShow>>> call, Throwable t) {
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getConPaymentListSByCiId(String ciId) {
        mView.showProgress();
        mModel.getConPaymentListSByCiId(ciId).enqueue(new Callback<NetworkResponse<BaseList<ConPayment>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<BaseList<ConPayment>>> call, Response<NetworkResponse<BaseList<ConPayment>>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showConPaymentList(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<BaseList<ConPayment>>> call, Throwable t) {
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

}
