package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.entity.Communication;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ProjectModel;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ProjectContract;
import com.kisoft.yuejianli.views.ProjectFragment;

/**
 * Created by tudou on 2018/4/4.
 */

public class ProjectPresenter extends BasePresenter<ProjectFragment ,ProjectModel>
            implements ProjectContract.ProjectPresenterContract{

    public ProjectPresenter(ProjectFragment mView, ProjectModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getProjectOrgInfo(String projectId) {
        mModel.getProjectOrgInfo(projectId).enqueue(new Callback<NetworkResponse<List<Communication>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<Communication>>> call, Response<NetworkResponse<List<Communication>>> response) {
                if(response.body()!= null && response.body().getCode()== NetworkResponse.OK){
                    if(response.body().getData() != null){
                        List<String> ids  =new ArrayList<>();
                        for(Communication com: response.body().getData()){
                            ids.add(com.getUserId());
                        }
                        mView.showProjectOrgInfo(ids);
                        SettingManager.getInstance().saveProjectOrgInfo(response.body().getData());

                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<Communication>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

}
