package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.EnclosureListContract;
import com.kisoft.yuejianli.contract.KnowledgeDetailContract;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.model.KnowledgeDetailModel;
import com.kisoft.yuejianli.views.KnowledgeDetailActivity;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Description:
 * Author     : yanlu
 * Date       : 2019/1/15 11:00
 */
public class KnowledgeDetailPresenter extends BasePresenter<KnowledgeDetailActivity,KnowledgeDetailModel>
        implements KnowledgeDetailContract.KnowledgeDetailPresenterContract{

    public KnowledgeDetailPresenter(KnowledgeDetailActivity mView, KnowledgeDetailModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getEnclosureList(String id) {
        mModel.getEnclosureList(id).enqueue(new Callback<NetworkResponse<List<EnclosureListDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<EnclosureListDto>>> call, Response<NetworkResponse<List<EnclosureListDto>>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.EnclosureListBack(response.body().getData());
                } else {
                    mView.EnclosureListBack(null);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<EnclosureListDto>>> call, Throwable t) {
                t.printStackTrace();
                mView.EnclosureListBack(null);
            }
        });
    }
}
