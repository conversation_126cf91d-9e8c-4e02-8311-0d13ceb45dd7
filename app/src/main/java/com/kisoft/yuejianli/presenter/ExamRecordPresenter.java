package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ExamRecordContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ExamRecord;
import com.kisoft.yuejianli.model.ExamRecordModel;
import com.kisoft.yuejianli.views.ExamRecordActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ExamRecordPresenter extends BasePresenter<ExamRecordActivity,
        ExamRecordModel> implements ExamRecordContract.Presenter {
    public ExamRecordPresenter(ExamRecordActivity mView, ExamRecordModel model) {
        super(mView, model);
    }

    private int page=1;
    private boolean isFirstLoad=true;

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getExamRecordList(String onlineState, int count, final int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getExamRecordList(onlineState,count,page,10).enqueue(new Callback<NetworkResponse<BaseList<ExamRecord>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<BaseList<ExamRecord>>> call, Response<NetworkResponse<BaseList<ExamRecord>>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showExamRecordList(response.body().getData(), type);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<BaseList<ExamRecord>>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }
}
