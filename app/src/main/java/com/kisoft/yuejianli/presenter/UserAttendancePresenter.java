package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.views.UserAttendanceFragment;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.UserAttendanceContract;
import com.kisoft.yuejianli.entity.PunchCardInfo;
import com.kisoft.yuejianli.model.UserAttendanceModel;

/**
 * Created by tudou on 2018/4/3.
 */

public class UserAttendancePresenter extends BasePresenter<UserAttendanceFragment, UserAttendanceModel>
        implements UserAttendanceContract.UserAttendancePresenterContract{


    public UserAttendancePresenter(UserAttendanceFragment mView, UserAttendanceModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getRecord(String uid, String date,String projectId) {
        mModel.getRecord(uid, date,projectId).enqueue(new Callback<NetworkResponse<List<PunchCardInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<PunchCardInfo>>> call, Response<NetworkResponse<List<PunchCardInfo>>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    if(response.body().getData() != null){
                        mView.showRecord(response.body().getData());
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<PunchCardInfo>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
