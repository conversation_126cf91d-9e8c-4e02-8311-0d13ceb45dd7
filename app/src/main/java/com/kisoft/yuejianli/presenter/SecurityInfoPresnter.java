package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.SecurityInfoContract;
import com.kisoft.yuejianli.model.SecurityInfoModel;
import com.kisoft.yuejianli.views.SecurityInfoActivity;

/**
 * Created by tudou on 2018/6/7.
 */
public class SecurityInfoPresnter extends BasePresenter<SecurityInfoActivity, SecurityInfoModel> implements
        SecurityInfoContract.SecurityInfoPresnterContract {

    public SecurityInfoPresnter(SecurityInfoActivity mView, SecurityInfoModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }
//
//    @Override
//    public void getContracts(String uid, String projectId) {
//        mModel.getContracts(uid, projectId).enqueue(new Callback<NetworkResponse<List<ContractInfo>>>() {
//            @Override
//            public void onResponse(Call<NetworkResponse<List<ContractInfo>>> call, Response<NetworkResponse<List<ContractInfo>>> response) {
//                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
//                    mView.showContracts(response.body().getData());
//                }
//            }
//
//            @Override
//            public void onFailure(Call<NetworkResponse<List<ContractInfo>>> call, Throwable t) {
//                t.printStackTrace();
//            }
//        });
//    }
}
