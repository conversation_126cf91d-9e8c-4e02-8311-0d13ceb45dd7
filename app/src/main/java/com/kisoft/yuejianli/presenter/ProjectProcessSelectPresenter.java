package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ProjectProcessSelectContract;
import com.kisoft.yuejianli.entity.ProjectProcess;
import com.kisoft.yuejianli.model.ProjectProcessSelectModel;
import com.kisoft.yuejianli.views.ProjectProcessSelectActivity;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/12.
 */

public class ProjectProcessSelectPresenter extends BasePresenter<ProjectProcessSelectActivity, ProjectProcessSelectModel> implements
        ProjectProcessSelectContract.ProjectProcessSelectPresenterContract{

    public ProjectProcessSelectPresenter(ProjectProcessSelectActivity mView, ProjectProcessSelectModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getProjectProcess(String uid, String projectId) {
        mModel.getProjectProcess(uid, projectId).enqueue(new Callback<NetworkResponse<List<ProjectProcess>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ProjectProcess>>> call, Response<NetworkResponse<List<ProjectProcess>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    mView.showProjectProcess(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ProjectProcess>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
