package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.TaskBaseInfoContract;
import com.kisoft.yuejianli.entity.TPwTasktype;
import com.kisoft.yuejianli.entity.TaskDTO;
import com.kisoft.yuejianli.model.TaskBaseInfoModel;
import com.kisoft.yuejianli.views.TaskBaseInfoFragment;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by yan<PERSON> on 2018/12/28.
 */

public class TaskBaseInfoPresenter extends BasePresenter<TaskBaseInfoFragment, TaskBaseInfoModel> implements
        TaskBaseInfoContract.TaskBaseInfoPresenterContract{

    public TaskBaseInfoPresenter(TaskBaseInfoFragment mView, TaskBaseInfoModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void addTask(TaskDTO dto) {
        mView.showProgress();
        mModel.addTask(dto).enqueue(new Callback<NetworkResponse<TaskDTO>>() {
            @Override
            public void onResponse(Call<NetworkResponse<TaskDTO>> call, Response<NetworkResponse<TaskDTO>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showAddTask(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<TaskDTO>> call, Throwable t) {
                mView.dismissProgress();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getTaskTypeList() {
        mModel.getTaskTypeList().enqueue(new Callback<NetworkResponse<List<TPwTasktype>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<TPwTasktype>>> call, Response<NetworkResponse<List<TPwTasktype>>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showTaskTypeList(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<TPwTasktype>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getTaskInfoById(String taskId) {
        mModel.getTaskInfoById(taskId).enqueue(new Callback<NetworkResponse<TaskDTO>>() {
            @Override
            public void onResponse(Call<NetworkResponse<TaskDTO>> call, Response<NetworkResponse<TaskDTO>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.taskInfoBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<TaskDTO>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
