package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ApplyOfficialContract;
import com.kisoft.yuejianli.entity.ApplyOfficialInfo;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.model.ApplyOfficialModel;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ApplyOfficialPresenter extends BasePresenter<ApplyOfficialContract.View,
        ApplyOfficialModel> implements ApplyOfficialContract.Presenter {
    public ApplyOfficialPresenter(ApplyOfficialContract.View mView, ApplyOfficialModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void submitApply(ApplyOfficialInfo info) {
        mView.showProgress();
        mModel.submitApplyInfo(info).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.applyBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void updateApply(ApplyOfficialInfo info) {
        mView.showProgress();
        mModel.updateApply(info).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.updateBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getInfo(ProcessListBean bean) {
        mView.showProgress();
        mModel.getInfo(bean).enqueue(new Callback<NetworkResponse<ApplyOfficialInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ApplyOfficialInfo>> call, Response<NetworkResponse<ApplyOfficialInfo>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.infoBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ApplyOfficialInfo>> call, Throwable t) {
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }
}
