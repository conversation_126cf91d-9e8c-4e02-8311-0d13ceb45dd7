package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.DocumentUpcomContract;
import com.kisoft.yuejianli.entity.ArcWorkDTO1;
import com.kisoft.yuejianli.entity.ArchivesTempletDto;
import com.kisoft.yuejianli.entity.httpresult.ProcessHttpResult;
import com.kisoft.yuejianli.model.DocumentUpcomModel;
import com.kisoft.yuejianli.views.DocumentUpcomActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by yan<PERSON> on 2019/1/4.
 */

public class DocumentUpcomPresenter extends BasePresenter<DocumentUpcomActivity,DocumentUpcomModel> implements
        DocumentUpcomContract.DocumentUpcomPresenterContract{

    public DocumentUpcomPresenter(DocumentUpcomActivity mView, DocumentUpcomModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getArcWorkList(String userId, String type,String count, String page, String pageSize) {
        /*
        mModel.getArcWorkList(userId, type).enqueue(new Call<NetworkResponse<ArcWorkDTO1>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ArcWorkDTO>>> call, Response<NetworkResponse<List<ArcWorkDTO>>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showArcWorkList(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ArcWorkDTO>>> call, Throwable t) {
                t.printStackTrace();
                mView.showArcWorkList(null);
            }
        });
        */
        mModel.getArcWorkList(userId,type,count,page,pageSize).enqueue(new Callback<NetworkResponse<ArcWorkDTO1>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ArcWorkDTO1>> call,
                                   Response<NetworkResponse<ArcWorkDTO1>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showArcWorkList(response.body().getData());
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<ArcWorkDTO1>> call, Throwable t) {
                t.printStackTrace();
                mView.showArcWorkList((ArcWorkDTO1) null);
            }
        });
    }

    // 待办收文
    @Override
    public void showShouWenWorkList(String workType, String count, String page, String pageSize) {
        mModel.showShouWenWorkList(workType,count,page,pageSize).enqueue(new Callback<NetworkResponse<ProcessHttpResult>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProcessHttpResult>> call,
                                   Response<NetworkResponse<ProcessHttpResult>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showShouWenWorkList(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProcessHttpResult>> call, Throwable t) {
                t.printStackTrace();
                mView.showShouWenWorkList(null);
            }
        });
    }

    @Override
    public void listArchives(String userId, String userRoles, String count, String page, String pageSize) {
        mModel.listArchives(userId,userRoles,count,page,pageSize).enqueue(new Callback<NetworkResponse<ArchivesTempletDto>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ArchivesTempletDto>> call, Response<NetworkResponse<ArchivesTempletDto>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showListArchives(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ArchivesTempletDto>> call, Throwable t) {

            }
        });
    }
}
