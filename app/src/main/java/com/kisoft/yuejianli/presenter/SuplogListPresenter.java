package com.kisoft.yuejianli.presenter;

import android.util.Log;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.SuplogListContract;
import com.kisoft.yuejianli.entity.MaterialInspect;
import com.kisoft.yuejianli.entity.ProjectSafeInspection;
import com.kisoft.yuejianli.entity.QualityAcceptance;
import com.kisoft.yuejianli.entity.QualityInspection;
import com.kisoft.yuejianli.entity.QualityInvisibility;
import com.kisoft.yuejianli.entity.SideReport;
import com.kisoft.yuejianli.model.SuplogListModel;
import com.kisoft.yuejianli.views.SuplogListActivity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/30.
 */

public class SuplogListPresenter extends BasePresenter<SuplogListActivity, SuplogListModel>
        implements SuplogListContract.SuplogListPresenterContract {

    ArrayList<Integer> listTypes;
    Map<String, Object> pramaras; // 统一的参数

    @Override
    public void setListTypes(ArrayList<Integer> listTypes) {
        this.listTypes = listTypes;
        getListData();
    }

    private void getListData(){
        if (listTypes == null || listTypes.isEmpty()) {
            return;
        }
        if (pramaras == null) {
            mView.showToast("请求参数为空");
            Log.e("pramaras", "SuplogListPresenter->请求参数为空");
            return;
        }
        switch (listTypes.get(0)) {
            case Constant.VIEWTYPE_TODAY_INSPECTION:
                getQualityInspection();
                break;
            case Constant.VIEWTYPE_TODAY_ONSIDE:
                getOnsideInspection();
                break;
            case Constant.VIEWTYPE_TODAY_QUALITY_ACCEPTANCE:
                getQualityAcceptance();
                break;
            case Constant.VIEWTYPE_TODAY_MATERIAL_INSPECTS:
                getTodayMaterialInspects();
                break;
            case Constant.VIEWTYPE_TODAY_SAFE_INSPECT:
                getTodayTSafInspect();
                break;
            case Constant.VIEWTYPE_TODAY_TQ_INVISIVILITY:
                getTodayTQuInvisibility();
            default:
                break;
        }
        listTypes.remove(0);
    }

    @Override
    public void setPramaras(String uid, String projectId, String currDate) {
        pramaras = new HashMap<>();
        pramaras.put("userId", uid);
        pramaras.put("projectId", projectId);
        pramaras.put("currDate", currDate);
    }

    public SuplogListPresenter(SuplogListActivity mView, SuplogListModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    public void getQualityInspection() {
        // 获取当日巡检报告
        mModel.getQualityInspection(pramaras).enqueue(new Callback<NetworkResponse<List<QualityInspection>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<QualityInspection>>> call, Response<NetworkResponse<List<QualityInspection>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    List<Object> info = new ArrayList<>();
                    if (response.body().getData() != null && response.body().getData().size() > 0) {
                        for (QualityInspection inspection : response.body().getData()) {
                            info.add(inspection);
                        }
                    }
                    mView.listDataBack(info);

                    getListData();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<QualityInspection>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    public void getQualityAcceptance() {
        // 获取当日质量验收报告
        mModel.getQualityAcceptance(pramaras).
                enqueue(new Callback<NetworkResponse<List<QualityAcceptance>>>() {
                    @Override
                    public void onResponse
                            (Call<NetworkResponse<List<QualityAcceptance>>> call, Response<NetworkResponse<List<QualityAcceptance>>> response) {
                        if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                            List<Object> info = new ArrayList<>();
                            if (response.body().getData() != null && response.body().getData().size() > 0) {
                                for (QualityAcceptance acceptance : response.body().getData()) {
                                    info.add(acceptance);
                                }
                            }
                            mView.listDataBack(info);

                            getListData();
                        }
                    }

                    @Override
                    public void onFailure(Call<NetworkResponse<List<QualityAcceptance>>> call, Throwable
                            t) {
                        t.printStackTrace();
                    }
                });
    }

    public void getOnsideInspection() {
        // 获取当日施工旁站报告
        mModel.getOnsideInspection(pramaras).enqueue(new Callback<NetworkResponse<List<SideReport>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<SideReport>>> call, Response<NetworkResponse<List<SideReport>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    List<Object> info = new ArrayList<>();
                    if (response.body().getData() != null && response.body().getData().size() > 0) {
                        for (SideReport sideReport : response.body().getData()) {
                            info.add(sideReport);
                        }
                    }
                    mView.listDataBack(info);

                    getListData();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<SideReport>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    public void getTodayMaterialInspects() {
        // 材料设备进场报告
        mModel.getTodayMaterialInspects(pramaras).enqueue(new Callback<NetworkResponse<List<MaterialInspect>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<MaterialInspect>>> call, Response<NetworkResponse<List<MaterialInspect>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    List<Object> info = new ArrayList<>();
                    if (response.body().getData() != null && response.body().getData().size() > 0) {
                        for (MaterialInspect data : response.body().getData()) {
                            info.add(data);
                        }
                    }
                    mView.listDataBack(info);

                    getListData();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<MaterialInspect>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    public void getTodayTQuInvisibility() {
        mModel.getTodayTQuInvisibility(pramaras).enqueue(new Callback<NetworkResponse<List<QualityInvisibility>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<QualityInvisibility>>> call, Response<NetworkResponse<List<QualityInvisibility>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    List<Object> info = new ArrayList<>();
                    if (response.body().getData() != null && response.body().getData().size() > 0) {
                        for (QualityInvisibility data : response.body().getData()) {
                            info.add(data);
                        }
                    }
                    mView.listDataBack(info);

                    getListData();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<QualityInvisibility>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    public void getTodayTSafInspect() {
        mModel.getTodayTSafInspect(pramaras).enqueue(new Callback<NetworkResponse<List<ProjectSafeInspection>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ProjectSafeInspection>>> call, Response<NetworkResponse<List<ProjectSafeInspection>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    List<Object> info = new ArrayList<>();
                    if (response.body().getData() != null && response.body().getData().size() > 0) {
                        for (ProjectSafeInspection data : response.body().getData()) {
                            info.add(data);
                        }
                    }
                    mView.listDataBack(info);

                    getListData();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ProjectSafeInspection>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
