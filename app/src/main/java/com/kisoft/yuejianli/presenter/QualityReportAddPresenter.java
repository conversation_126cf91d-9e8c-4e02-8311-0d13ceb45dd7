package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.QualityReportAddContract;
import com.kisoft.yuejianli.contract.QualityReportContract;
import com.kisoft.yuejianli.entity.QualityReportInfo;
import com.kisoft.yuejianli.model.QualityReportAddModel;
import com.kisoft.yuejianli.utils.PhotoUtil;
import com.kisoft.yuejianli.views.ApplyZLPGBGFragment;
import com.kisoft.yuejianli.views.QualityReportAddActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class QualityReportAddPresenter extends BasePresenter<QualityReportAddActivity,
        QualityReportAddModel> implements QualityReportAddContract.QualityReportAddPresenterContract {

    public QualityReportAddPresenter(QualityReportAddActivity mView, QualityReportAddModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void addQualityReportAdd(QualityReportInfo qualityReportInfo, String userId, String projectId) {
        mModel.addQualityReportAdd(qualityReportInfo,userId,projectId).enqueue(new Callback<NetworkResponse<Boolean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Boolean>> call, Response<NetworkResponse<Boolean>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showQualityReportAddResult(true);
                }else {
                    mView.showQualityReportAddResult(false);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Boolean>> call, Throwable t) {

            }
        });
    }

    @Override
    public void uploadPhotoImage(String photoPath) {
        try {
            PhotoUtil.uploadPhoto1(photoPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }
}
