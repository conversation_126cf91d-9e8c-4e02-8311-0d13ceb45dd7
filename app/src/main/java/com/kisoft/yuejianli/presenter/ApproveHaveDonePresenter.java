package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ApproveHaveDoneContract;
import com.kisoft.yuejianli.entity.ApproveInfos;
import com.kisoft.yuejianli.model.ApproveHaveDoneModel;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.views.ApproveHaveDoneFragment;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/4/23.
 */

public class ApproveHaveDonePresenter extends BasePresenter<ApproveHaveDoneFragment,
        ApproveHaveDoneModel> implements ApproveHaveDoneContract.ApproveHaveDonePresenterContract {

    public ApproveHaveDonePresenter(ApproveHaveDoneFragment mView, ApproveHaveDoneModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getUserApproves(String projectId, String userId, String pageSize, String page, final String count) {
        mModel.getUserApproves(projectId, userId, pageSize, page,count).enqueue(new Callback<NetworkResponse<ApproveInfos>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ApproveInfos>> call, Response<NetworkResponse<ApproveInfos>> response) {
                if(response.body() != null && response.body().getCode() ==NetworkResponse.OK){
                    if(StringUtil.isEqual("0", count)){
                        mView.showUserApproves(response.body().getData(), 0);
                    }else{
                        mView.showUserApproves(response.body().getData(),1);
                    }

                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ApproveInfos>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
