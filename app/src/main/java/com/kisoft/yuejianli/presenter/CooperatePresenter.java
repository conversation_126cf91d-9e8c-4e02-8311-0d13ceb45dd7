package com.kisoft.yuejianli.presenter;

import android.util.Log;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.CooperateContract;
import com.kisoft.yuejianli.entity.ApproveInfos;
import com.kisoft.yuejianli.entity.ArcWorkDTO1;
import com.kisoft.yuejianli.entity.MailCount;
import com.kisoft.yuejianli.entity.MessageTodoReceive;
import com.kisoft.yuejianli.entity.ProjectcheckCount;
import com.kisoft.yuejianli.entity.TaskDTO;
import com.kisoft.yuejianli.entity.WorkFlowTaskInfo;
import com.kisoft.yuejianli.entity.httpresult.ProcessHttpResult;
import com.kisoft.yuejianli.model.CooperateModel;
import com.kisoft.yuejianli.utils.GsonUtil;
import com.kisoft.yuejianli.views.CooperateActivity;

import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/21.
 */

public class CooperatePresenter extends BasePresenter<CooperateActivity,CooperateModel> implements
        CooperateContract.CooperatePresenterContract{

    public CooperatePresenter(CooperateActivity mView, CooperateModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getTaskCount(String month, String userRoles) {
        mModel.getTaskSendCount(month, userRoles).enqueue(new Callback<NetworkResponse<Integer>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Integer>> call, Response<NetworkResponse<Integer>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showTaskSendCount(response.body().getData());
                }else {
                    mView.showTaskSendCount(0);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Integer>> call, Throwable t) {
                mView.showTaskSendCount(0);
                t.printStackTrace();
            }
        });

        mModel.getTaskTodoCount(month, userRoles).enqueue(new Callback<NetworkResponse<Integer>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Integer>> call, Response<NetworkResponse<Integer>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showTaskTodoCount(response.body().getData());
                }else {
                    mView.showTaskTodoCount(0);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Integer>> call, Throwable t) {
                mView.showTaskTodoCount(0);
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getTodoNotice(String uid, String state) {
        mModel.getTodoNotice(uid, state).enqueue(new Callback<NetworkResponse<List<MessageTodoReceive>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<MessageTodoReceive>>> call, Response<NetworkResponse<List<MessageTodoReceive>>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showTodoNotice(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<MessageTodoReceive>>> call, Throwable t) {
                t.printStackTrace();
                Log.e("Cooperate","getTodoNotice --"+ t.getMessage());
            }
        });
    }

    @Override
    public void getUserSendApproves(String projectId, String userId, String pageSize, String page, String count) {
        mModel.getUserSendApproves(projectId, userId, pageSize, page, count).enqueue(new Callback<NetworkResponse<ApproveInfos>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ApproveInfos>> call, Response<NetworkResponse<ApproveInfos>> response) {
                if ((response.body() != null && response.body().getCode() == NetworkResponse.OK)){
                    if(response.body().getData() != null){
                        mView.showUserSendCount(response.body().getData().getCount());
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ApproveInfos>> call, Throwable t) {
                t.printStackTrace();
                Log.e("Cooperate","getUserSendApproves --"+ t.getMessage());
            }
        });
    }

    @Override
    public void getToUserApproves(String projectId, String userId, String pageSize, String page, String count) {
        mModel.getToUserApproves(projectId, userId, pageSize, page, count).enqueue(new Callback<NetworkResponse<ApproveInfos>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ApproveInfos>> call, Response<NetworkResponse<ApproveInfos>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    if(response.body().getData() != null){
                        mView.showToUserCount(response.body().getData().getCount());
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ApproveInfos>> call, Throwable t) {
                t.printStackTrace();
                Log.e("Cooperate","getToUserApproves --"+ t.getMessage());
            }
        });
    }

    @Override
    public void getOfficelSealTodoCount(String uid, String businessTypes) {
        mModel.getOfficelSealTodoCount(uid, businessTypes).enqueue(new Callback<NetworkResponse<WorkFlowTaskInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<WorkFlowTaskInfo>> call, Response<NetworkResponse<WorkFlowTaskInfo>> response) {
                if (response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    if (response.body().getData() != null){
                        mView.showOfficelSealTodoCount(response.body().getData().getCount());
                    }
                }

            }

            @Override
            public void onFailure(Call<NetworkResponse<WorkFlowTaskInfo>> call, Throwable t) {
                t.printStackTrace();
                Log.e("Cooperate","getOfficelSealTodoCount --"+ t.getMessage());
            }
        });
    }


    @Override
    public void getTaskToDoByUserId(String month, String userRoles, String pageSize, String page, String count) {
        mModel.getTaskToDoByUserId(month, userRoles, pageSize, page, count).enqueue(new Callback<NetworkResponse<List<TaskDTO>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<TaskDTO>>> call, Response<NetworkResponse<List<TaskDTO>>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showTaskToDoByUserId(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<TaskDTO>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getArcWorkList(String month, String userId, String userRoles, String projectId, String pageSize, String page, String count) {
        /*
        mModel.getArcWorkList1(month, userId, userRoles, projectId).enqueue(new Callback<NetworkResponse<List<ArcWorkDTO>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ArcWorkDTO>>> call, Response<NetworkResponse<List<ArcWorkDTO>>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showArcWorkList1(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ArcWorkDTO>>> call, Throwable t) {
                t.printStackTrace();
            }
        });

                mModel.getArcWorkList2(userId).enqueue(new Callback<NetworkResponse<List<ArcWorkDTO>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ArcWorkDTO>>> call, Response<NetworkResponse<List<ArcWorkDTO>>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showArcWorkList2(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ArcWorkDTO>>> call, Throwable t) {
                t.printStackTrace();
            }
        });

         */
        //类型（1=待办、2=已办、3=已发）
        mModel.getArcWorkList1("1",month, userId, userRoles, projectId,pageSize,page,count).enqueue(new Callback<NetworkResponse<ArcWorkDTO1>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ArcWorkDTO1>> call,
                                   Response<NetworkResponse<ArcWorkDTO1>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showArcWorkListDaiBan(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ArcWorkDTO1>> call, Throwable t) {
                t.printStackTrace();
            }
        });

        mModel.getArcWorkList1("3",month, userId, userRoles, projectId,pageSize,page,count).enqueue(new Callback<NetworkResponse<ArcWorkDTO1>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ArcWorkDTO1>> call,
                                   Response<NetworkResponse<ArcWorkDTO1>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showArcWorkListQiCao(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ArcWorkDTO1>> call, Throwable t) {
                t.printStackTrace();
            }
        });


    }


    @Override
    public void getProcessList(String workType) {
        mModel.getProcessList(workType).enqueue(new Callback<NetworkResponse<ProcessHttpResult>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProcessHttpResult>> call, Response<NetworkResponse<ProcessHttpResult>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.processListBack(response.body().getData());
                } else {
                    mView.processListBack(null);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProcessHttpResult>> call, Throwable t) {
                t.printStackTrace();
                mView.processListBack(null);
            }
        });
    }

    @Override
    public void getMailCount(String userId) {
        mModel.getMailCount(userId).enqueue(new Callback<NetworkResponse<MailCount>>() {
            @Override
            public void onResponse(Call<NetworkResponse<MailCount>> call, Response<NetworkResponse<MailCount>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showMailCount(response.body().getData());
                } else {
                    mView.showMailCount(null);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<MailCount>> call, Throwable t) {
                t.printStackTrace();
                mView.showMailCount(null);
            }
        });
    }

    @Override
    public void getProjectcheckCount(String userId) {
        mModel.getProjectcheckCount(userId).enqueue(new Callback<NetworkResponse<ProjectcheckCount>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProjectcheckCount>> call, Response<NetworkResponse<ProjectcheckCount>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showProjectcheckCount(response.body().getData());
                } else {
                    mView.showProjectcheckCount(null);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProjectcheckCount>> call, Throwable t) {
                t.printStackTrace();
                mView.showProjectcheckCount(null);
            }
        });
    }

    // 收文待办
    @Override
    public void getProcessShouWenCount(String workType) {
        mModel.getProcessShouWenCount(workType).enqueue(new Callback<NetworkResponse<ProcessHttpResult>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProcessHttpResult>> call,
                                   Response<NetworkResponse<ProcessHttpResult>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.processShouWenListBack(response.body().getData());
                } else {
                    mView.processShouWenListBack(null);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProcessHttpResult>> call, Throwable t) {
                t.printStackTrace();
                mView.processShouWenListBack(null);
            }
        });
    }

    @Override
    public void getCarbonCopyCount(String workType) {
        mModel.getCarbonCopyCount(workType).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    Map<String, Object> map = GsonUtil.GsonToMaps(response.body().getData().toString());
                    Double count = Double.valueOf(map.get("count").toString());
                    mView.getCarbonCopyCount(count.intValue() + "");
                } else {
                    mView.getCarbonCopyCount("0");
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
                mView.getCarbonCopyCount("0");
            }
        });
    }


    @Override
    public void getOnsiteAppCounts(String month, String userId, String projectId, final int flag) {
        mModel.getOnsiteAppCounts(month, userId, projectId, flag).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.OnsiteAppCounts(flag, response.body().getData());
                } else {
                    mView.OnsiteAppCounts(flag, "0");
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                t.printStackTrace();
                mView.OnsiteAppCounts(flag, "0");
            }
        });
    }
}
