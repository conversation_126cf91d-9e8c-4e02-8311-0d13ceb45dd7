package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.InstructionsContract;
import com.kisoft.yuejianli.entity.InstructionsInfo;
import com.kisoft.yuejianli.entity.InstructionsInfoList;
import com.kisoft.yuejianli.model.InstructionsModel;
import com.kisoft.yuejianli.views.InstructionsActivity;
import com.kisoft.yuejianli.views.InstructionsListActivity;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class InstructionsPresenter extends BasePresenter<InstructionsListActivity, InstructionsModel> implements InstructionsContract.InstructionsPresenterContract {
    public InstructionsPresenter(InstructionsListActivity mView, InstructionsModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void getInstructionsList(String uid, String projectId,final String count,final String page, String pageSize, String selectType) {
        mModel.getInstructionsList(uid,projectId,count,page,pageSize,selectType).enqueue(new Callback<NetworkResponse<InstructionsInfoList>>() {
            @Override
            public void onResponse(Call<NetworkResponse<InstructionsInfoList>> call, Response<NetworkResponse<InstructionsInfoList>> response) {
                mView.finshRefresh();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    if(Integer.valueOf(page) == 1){
                        mView.showInstructionsList(response.body().getData(), 0);
                    }else if(Integer.valueOf(page) > 1){
                        mView.showInstructionsList(response.body().getData() ,1);
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<InstructionsInfoList>> call, Throwable t) {
                mView.finshRefresh();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }
}
