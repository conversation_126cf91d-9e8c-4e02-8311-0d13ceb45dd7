package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ItemSelectContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ConInfo;
import com.kisoft.yuejianli.entity.ConSettlinfo;
import com.kisoft.yuejianli.entity.EmpCompactList;
import com.kisoft.yuejianli.entity.EmpProfessList;
import com.kisoft.yuejianli.entity.FilingCertificateList;
import com.kisoft.yuejianli.entity.ImageProgressList;
import com.kisoft.yuejianli.model.ItemSelectModel;
import com.kisoft.yuejianli.views.CertificateListActivity;
import com.kisoft.yuejianli.views.ItemSelectMorePageActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class CertificateListPresenter extends BasePresenter<CertificateListActivity,
        ItemSelectModel> implements ItemSelectContract.Presenter {
    public CertificateListPresenter(CertificateListActivity mView, ItemSelectModel mItemSelectModel) {
        super(mView, mItemSelectModel);
    }

    private int page=1;
    private boolean isFirstLoad=true;

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getFilingCertificateList(String fcName, int count, final int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getFilingCertificateList(fcName,count,page,10).enqueue(new Callback<NetworkResponse<FilingCertificateList>>() {
            @Override
            public void onResponse(Call<NetworkResponse<FilingCertificateList>> call, Response<NetworkResponse<FilingCertificateList>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showFilingCertificateList(response.body().getData(), type);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<FilingCertificateList>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getEmpProfessList(String name, String ipId, int count, final int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getEmpProfessListByAPP(name,ipId,count,page,10).enqueue(new Callback<NetworkResponse<EmpProfessList>>() {
            @Override
            public void onResponse(Call<NetworkResponse<EmpProfessList>> call, Response<NetworkResponse<EmpProfessList>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showEmpProfessList(response.body().getData(), type);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<EmpProfessList>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getEmpCompactList(String name, int count, final int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getEmpCompactListByAPP(name,count,page,10).enqueue(new Callback<NetworkResponse<EmpCompactList>>() {
            @Override
            public void onResponse(Call<NetworkResponse<EmpCompactList>> call, Response<NetworkResponse<EmpCompactList>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showEmpCompactList(response.body().getData(), type);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<EmpCompactList>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getConInfoList(String name, int count, final int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getConInfoListByAPP(name,count,page,10).enqueue(new Callback<NetworkResponse<BaseList<ConInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<BaseList<ConInfo>>> call, Response<NetworkResponse<BaseList<ConInfo>>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showConInfoList(response.body().getData(), type);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<BaseList<ConInfo>>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getDeTaildesList(String projectId, int count, final int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getDeTaildesList(projectId,count,page,10).enqueue(new Callback<NetworkResponse<ImageProgressList>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ImageProgressList>> call, Response<NetworkResponse<ImageProgressList>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showImageProgressList(response.body().getData(), type);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ImageProgressList>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getConSettlInfoList(int count, final int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getConSettlInfoList(count,page,10).enqueue(new Callback<NetworkResponse<BaseList<ConSettlinfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<BaseList<ConSettlinfo>>> call, Response<NetworkResponse<BaseList<ConSettlinfo>>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showConSettlInfoList(response.body().getData(), type);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<BaseList<ConSettlinfo>>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }
}
