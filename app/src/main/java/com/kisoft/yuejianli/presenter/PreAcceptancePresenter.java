package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.PreAcceptanceContract;
import com.kisoft.yuejianli.entity.PreAcceptanceInfo;
import com.kisoft.yuejianli.model.PreAcceptanceModel;
import com.kisoft.yuejianli.utils.PhotoUtil;
import com.kisoft.yuejianli.views.PreAcceptAddActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class PreAcceptancePresenter extends BasePresenter<PreAcceptAddActivity,
        PreAcceptanceModel> implements PreAcceptanceContract.PreAcceptancePresenterContract {

    public PreAcceptancePresenter(PreAcceptAddActivity mView, PreAcceptanceModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void uploadPhotoImage(String photoPath) {
        try {
            PhotoUtil.uploadPhoto1(photoPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    @Override
    public void addPreAcceptance(PreAcceptanceInfo preaccInfo, String userId,String projectId) {
        mModel.addPreAcceptance(preaccInfo,userId,projectId).enqueue(new Callback<NetworkResponse>() {
            @Override
            public void onResponse(Call<NetworkResponse> call, Response<NetworkResponse> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showAddResult(true);
                }else {
                    mView.showAddResult(false);
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse> call, Throwable t) {
                t.printStackTrace();


            }
        });


    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

}
