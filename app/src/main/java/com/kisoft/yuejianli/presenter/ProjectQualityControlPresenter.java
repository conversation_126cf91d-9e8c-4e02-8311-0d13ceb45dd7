package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ProjectQualityControlContract;
import com.kisoft.yuejianli.model.ProjectQualityControlModel;
import com.kisoft.yuejianli.views.ProjectQualityControlActivity;

import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/7/3.
 */

public class ProjectQualityControlPresenter extends BasePresenter<ProjectQualityControlActivity,ProjectQualityControlModel>
        implements ProjectQualityControlContract.ProjectQualityControlPresenterContract{

    public ProjectQualityControlPresenter(ProjectQualityControlActivity mView, ProjectQualityControlModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getQualityInfo(String uid, String projectId, String time, String mType) {
        mModel.getQualityInfo(uid, projectId, time, mType).enqueue(new Callback<NetworkResponse<Map<String, Object>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Map<String, Object>>> call, Response<NetworkResponse<Map<String, Object>>> response) {
                if (response.body()!=null && response.body().getCode() == NetworkResponse.OK){
                    mView.showInfo(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Map<String, Object>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
