package com.kisoft.yuejianli.presenter;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ArchivesTransferContract;
import com.kisoft.yuejianli.entity.ArchivesTransferList;
import com.kisoft.yuejianli.model.ArchivesTransferModel;
import com.kisoft.yuejianli.views.ArchivesTransferActivity;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ArchivesTransferPresenter extends BasePresenter<ArchivesTransferActivity, ArchivesTransferModel> implements ArchivesTransferContract.ArchivesTransferPresenterContract {
    public ArchivesTransferPresenter(ArchivesTransferActivity mView, ArchivesTransferModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void getArchivesTransferList(String uid, String projectId,final String count,final String page, String pageSize) {
        mModel.getArchivesTransferList(uid,projectId, count, page, pageSize).enqueue(new Callback<NetworkResponse<ArchivesTransferList>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ArchivesTransferList>> call, Response<NetworkResponse<ArchivesTransferList>> response) {
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    if(Integer.valueOf(page) == 1){
                        mView.showArchivesTransferList(response.body().getData(), 0);
                    }else if(Integer.valueOf(page) > 1){
                        mView.showArchivesTransferList(response.body().getData() ,1);
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ArchivesTransferList>> call, Throwable t) {

            }
        });
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }
}
