package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.QualitySecurityListContract;
import com.kisoft.yuejianli.entity.ContractInfo;
import com.kisoft.yuejianli.model.QualitySecurityListModel;
import com.kisoft.yuejianli.views.QualitySecurityListActivity;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/7.
 */
public class QualitySecurityListPresnter extends BasePresenter<QualitySecurityListActivity, QualitySecurityListModel> implements
        QualitySecurityListContract.QualitySecurityListPresnterContract {

    public QualitySecurityListPresnter(QualitySecurityListActivity mView, QualitySecurityListModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getContracts(String uid, String projectId) {
        mModel.getContracts(uid, projectId).enqueue(new Callback<NetworkResponse<List<ContractInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ContractInfo>>> call, Response<NetworkResponse<List<ContractInfo>>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.showContracts(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ContractInfo>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
