package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.CompanyOrgZationContract;
import com.kisoft.yuejianli.entity.ComPanyOrgInfo;
import com.kisoft.yuejianli.model.CompanyOrgZationModel;
import com.kisoft.yuejianli.views.CompanyOrgInfoActivity;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/7/23.
 */

public class CompanyOrgZationPresenter extends BasePresenter<CompanyOrgInfoActivity,CompanyOrgZationModel> implements
        CompanyOrgZationContract.CompanyOrgZationPresenterContract{

    public CompanyOrgZationPresenter(CompanyOrgInfoActivity mView, CompanyOrgZationModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getOrg(String uid) {
        mView.showProgress();
        mModel.getOrg(uid).enqueue(new Callback<NetworkResponse<List<ComPanyOrgInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ComPanyOrgInfo>>> call, Response<NetworkResponse<List<ComPanyOrgInfo>>> response) {
                mView.dismissProgress();
                if (response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showOrg(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ComPanyOrgInfo>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
