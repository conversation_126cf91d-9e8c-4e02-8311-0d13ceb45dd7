package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.CustomerVisitContract;
import com.kisoft.yuejianli.entity.CustomerVisitInfo;
import com.kisoft.yuejianli.model.CustomerVisitModel;
import com.kisoft.yuejianli.views.CustomerVisitFragment;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/19.
 */

public class CustomerVisitPresenter extends BasePresenter<CustomerVisitFragment, CustomerVisitModel> implements
        CustomerVisitContract.CustomerVisitPresenterContract{

    public CustomerVisitPresenter(CustomerVisitFragment mView, CustomerVisitModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getCustomerVisits(String uid, String companyId, String count, final String page, String pageSize) {
        mModel.getCustomerVisits(uid, companyId, count, page, pageSize).enqueue(new Callback<NetworkResponse<CustomerVisitInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<CustomerVisitInfo>> call, Response<NetworkResponse<CustomerVisitInfo>> response) {
                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    if(Integer.valueOf(page) == 1){
                        mView.showCustomerVisits(response.body().getData(), 0);
                    }else {
                        mView.showCustomerVisits(response.body().getData() ,1);
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<CustomerVisitInfo>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
