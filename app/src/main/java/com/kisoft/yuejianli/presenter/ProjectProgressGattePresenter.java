package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ProjectProgressGatteContract;
import com.kisoft.yuejianli.entity.GatteEntity;
import com.kisoft.yuejianli.model.ProjectProgressGatteModel;
import com.kisoft.yuejianli.views.ProjectProgressGatteFragment;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/5/22.
 */

public class ProjectProgressGattePresenter extends BasePresenter<ProjectProgressGatteFragment, ProjectProgressGatteModel> implements

        ProjectProgressGatteContract.ProjectProgressGattePresenterContract {

    public ProjectProgressGattePresenter(ProjectProgressGatteFragment mView, ProjectProgressGatteModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getGatteData(String month, String projectId) {
            mModel.getGatteData(month, projectId).enqueue(new Callback<NetworkResponse<List<GatteEntity>>>() {
                @Override
                public void onResponse(Call<NetworkResponse<List<GatteEntity>>> call, Response<NetworkResponse<List<GatteEntity>>> response) {
                    if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                        mView.gatteDataBack(response.body().getData());
                    }
                }

                @Override
                public void onFailure(Call<NetworkResponse<List<GatteEntity>>> call, Throwable t) {
                        t.printStackTrace();
                }
            });
    }
}
