package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ApplyXXSSContract;
import com.kisoft.yuejianli.entity.InformationInfo;
import com.kisoft.yuejianli.model.ApplyXXSSModel;
import com.kisoft.yuejianli.utils.PhotoUtil;
import com.kisoft.yuejianli.views.ApplyXXSSFragment;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ApplyXXSSPresenter extends BasePresenter<ApplyXXSSFragment, ApplyXXSSModel> implements ApplyXXSSContract.ApplyXXSSPresenterContract {


    public ApplyXXSSPresenter(ApplyXXSSFragment mView, ApplyXXSSModel mModel) {
        super(mView, mModel);
    }
    @Override
    public void uploadPhotoImage(String photoPath) {
        try {
            PhotoUtil.uploadPhoto1(photoPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void getApplyXXSSInfo(String saId, String wfTaskId, String workflow_type, String wfTaskState, String businessId, String userId) {
        mModel.getApplyXXSSInfo(saId,wfTaskId,workflow_type,wfTaskState,businessId,userId).enqueue(new Callback<NetworkResponse<InformationInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<InformationInfo>> call, Response<NetworkResponse<InformationInfo>> response) {
                mView.dismissProgress();
                if(response.body()!=null&&response.body().getCode()==NetworkResponse.OK){
                    mView.applyXXSSInfoBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<InformationInfo>> call, Throwable t) {
                mView.dismissProgress();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void submitApplyXXSS(InformationInfo informationInfo) {
        mView.showProgress();
        mModel.submitApplyXXSS(informationInfo).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.applyXXSSBack(response.body().getData());
                }else {
                    mView.applyXXSSBack(response.body().getMessage());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                mView.dismissProgress();
                t.printStackTrace();
            }
        });

    }

    @Override
    public void destroy() {

    }

    @Override
    public void create() {

    }
}
