package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ExamOnlineContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ExamUserInfo;
import com.kisoft.yuejianli.model.ExamOnlineModel;
import com.kisoft.yuejianli.views.ExamOnlineActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ExamOnlinePresenter extends BasePresenter<ExamOnlineActivity,
        ExamOnlineModel> implements ExamOnlineContract.Presenter {
    public ExamOnlinePresenter(ExamOnlineActivity mView, ExamOnlineModel mModel) {
        super(mView, mModel);
    }

    private int page=1;
    private boolean isFirstLoad=true;

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getMyExamTaskList(int count, final int type) {
        if(isFirstLoad){
            isFirstLoad=false;
            mView.showProgress();
        }
        if(type== Constant.LOAD_DATA_NORMAL){
            page=1;
            count=0;
        }else {
            page++;
        }
        mModel.getMyExamTaskList(count,page,10).enqueue(new Callback<NetworkResponse<BaseList<ExamUserInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<BaseList<ExamUserInfo>>> call, Response<NetworkResponse<BaseList<ExamUserInfo>>> response) {
                mView.finishRefresh();
                mView.dismissProgress();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    mView.showMyExamTaskList(response.body().getData(), type);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<BaseList<ExamUserInfo>>> call, Throwable t) {
                mView.finishRefresh();
                mView.dismissProgress();
                mView.showError();
                t.printStackTrace();
            }
        });
    }
}
