package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.CommitItemSelectContract;
import com.kisoft.yuejianli.entity.ReviewMode;
import com.kisoft.yuejianli.model.CommitItemSelectModel;
import com.kisoft.yuejianli.views.ItemSelectActivity;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/5/3.
 */

public class CommitItemSelectPresenter extends BasePresenter<ItemSelectActivity, CommitItemSelectModel>
        implements CommitItemSelectContract.Presenter{

    public CommitItemSelectPresenter(ItemSelectActivity mView, CommitItemSelectModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

//    @Override
//    public void getDeTaildesList(String projectId) {
//        mView.showProgress();
//        mModel.getDeTaildesList(projectId).enqueue(new Callback<NetworkResponse<List<ImageProgress>>>() {
//            @Override
//            public void onResponse(Call<NetworkResponse<List<ImageProgress>>> call, Response<NetworkResponse<List<ImageProgress>>> response) {
//                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
//                    mView.showDeTaildesList(response.body().getData());
//                }else {
//                    mView.showError();
//                }
//                mView.dissmisProgress();
//            }
//
//            @Override
//            public void onFailure(Call<NetworkResponse<List<ImageProgress>>> call, Throwable t) {
//                t.printStackTrace();
//                mView.showError();
//                mView.dissmisProgress();
//            }
//        });
//    }

    @Override
    public void getObjectItemListByCode() {
        mView.showProgress();
        mModel.getObjectItemListByCode().enqueue(new Callback<NetworkResponse<List<ReviewMode>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ReviewMode>>> call, Response<NetworkResponse<List<ReviewMode>>> response) {
                if(response.body() != null && response.body().getCode() == NetworkResponse.OK){
                    mView.showObjectItemList(response.body().getData());
                }else {
                    mView.showError();
                }
                mView.dismissProgress();
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ReviewMode>>> call, Throwable t) {
                t.printStackTrace();
                mView.showError();
                mView.dismissProgress();
            }
        });
    }
}
