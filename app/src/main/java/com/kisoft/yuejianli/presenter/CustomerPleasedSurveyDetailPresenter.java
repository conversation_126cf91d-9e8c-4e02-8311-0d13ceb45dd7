package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.CustomerPleasedSurveyDetailContract;
import com.kisoft.yuejianli.entity.CustomerPleasedSurvey;
import com.kisoft.yuejianli.model.CustomerPleasedSurveyDetailModel;
import com.kisoft.yuejianli.views.CustomerPleasedSurveyDetailActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/26.
 */

public class CustomerPleasedSurveyDetailPresenter extends BasePresenter<CustomerPleasedSurveyDetailActivity,CustomerPleasedSurveyDetailModel>
    implements CustomerPleasedSurveyDetailContract.CustomerPleasedSurveyDetailPresenterContract{

    public CustomerPleasedSurveyDetailPresenter(CustomerPleasedSurveyDetailActivity mView, CustomerPleasedSurveyDetailModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void submitpleasedSurvey(String uid, CustomerPleasedSurvey survey) {
        mModel.submitpleasedSurvey(uid, survey).enqueue(new Callback<NetworkResponse<Boolean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Boolean>> call, Response<NetworkResponse<Boolean>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.showResult(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Boolean>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getPleasedSurvey(String uid, String crpGuid) {
        mModel.getPleasedSurvey(uid, crpGuid).enqueue(new Callback<NetworkResponse<CustomerPleasedSurvey>>() {
            @Override
            public void onResponse(Call<NetworkResponse<CustomerPleasedSurvey>> call, Response<NetworkResponse<CustomerPleasedSurvey>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.dataBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<CustomerPleasedSurvey>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
