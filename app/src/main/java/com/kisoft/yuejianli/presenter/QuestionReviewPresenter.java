package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.QuestionReviewContract;
import com.kisoft.yuejianli.entity.ProreiviewDto;
import com.kisoft.yuejianli.model.QuestionReviewModel;
import com.kisoft.yuejianli.utils.PhotoUtil;
import com.kisoft.yuejianli.views.QuestionReviewFragment;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class QuestionReviewPresenter extends BasePresenter<QuestionReviewFragment,QuestionReviewModel>
                        implements QuestionReviewContract.QuestionReviewPresenterContract {
    public QuestionReviewPresenter(QuestionReviewFragment mView, QuestionReviewModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void submitData(ProreiviewDto data, String userId, String userName) {
        mModel.submitData(data, userId, userName).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.submitBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void getData(String ppId) {
        mModel.getData(ppId).enqueue(new Callback<NetworkResponse<ProreiviewDto>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProreiviewDto>> call, Response<NetworkResponse<ProreiviewDto>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.dataBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProreiviewDto>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void uploadPhotoImage(String photoPath) {
        try {
            PhotoUtil.uploadPhoto1(photoPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
