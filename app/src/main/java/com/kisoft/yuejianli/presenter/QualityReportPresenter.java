package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.QualityReportContract;
import com.kisoft.yuejianli.entity.QualityReportList;
import com.kisoft.yuejianli.model.QualityReportModel;
import com.kisoft.yuejianli.views.QualityReportActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class QualityReportPresenter extends BasePresenter<QualityReportActivity, QualityReportModel> implements QualityReportContract.QualityReportPresenterContract {
    public QualityReportPresenter(QualityReportActivity mView, QualityReportModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void getQualityReportList(String uid, String projectId,final String count,final String page, String pageSize) {
        mModel.getQualityReportList(uid,projectId, count, page, pageSize).enqueue(new Callback<NetworkResponse<QualityReportList>>() {
            @Override
            public void onResponse(Call<NetworkResponse<QualityReportList>> call, Response<NetworkResponse<QualityReportList>> response) {
                mView.finshRefresh();
                if (response.body()!= null  && response.body().getCode() == NetworkResponse.OK){
                    if(Integer.valueOf(page) == 1){
                        mView.showQualityReportList(response.body().getData(), 0);
                    }else if(Integer.valueOf(page) > 1){
                        mView.showQualityReportList(response.body().getData() ,1);
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<QualityReportList>> call, Throwable t) {
                mView.finshRefresh();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }
}
