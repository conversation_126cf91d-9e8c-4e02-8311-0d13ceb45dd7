package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.TrainScheduleContract;
import com.kisoft.yuejianli.entity.TrainActivity;
import com.kisoft.yuejianli.model.TrainScheduleModel;
import com.kisoft.yuejianli.views.TrainScheduleFragment;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/20.
 */

public class TrainSchedulePresenter extends BasePresenter<TrainScheduleFragment, TrainScheduleModel> implements
        TrainScheduleContract.TrainSchedulePresenterContract {

    public TrainSchedulePresenter(TrainScheduleFragment mView, TrainScheduleModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getTrains(String uid, String status) {
            mModel.getTrains(uid, status).enqueue(new Callback<NetworkResponse<List<TrainActivity>>>() {
                @Override
                public void onResponse(Call<NetworkResponse<List<TrainActivity>>> call, Response<NetworkResponse<List<TrainActivity>>> response) {
                    if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                        mView.showTrains(response.body().getData());
                    }
                }

                @Override
                public void onFailure(Call<NetworkResponse<List<TrainActivity>>> call, Throwable t) {
                    t.printStackTrace();
                }
            });
    }
}
