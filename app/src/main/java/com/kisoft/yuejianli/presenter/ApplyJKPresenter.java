package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ApplyJKContract;
import com.kisoft.yuejianli.entity.ApplyJKInfo;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.model.ApplyJKModel;
import com.kisoft.yuejianli.views.ApplyJKFragment;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ApplyJKPresenter extends BasePresenter<ApplyJKFragment,ApplyJKModel> implements ApplyJKContract.ApplyJKPresenterContract{
    public ApplyJKPresenter(ApplyJKFragment mView, ApplyJKModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void submitApply(ApplyJKInfo jkInfo) {
        mView.showProgress();
        mModel.submitApply(jkInfo).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.applyBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                mView.dismissProgress();
                t.printStackTrace();
            }
        });
    }


    @Override
    public void getInfo(ProcessListBean bean) {
        mView.showProgress();
        mModel.getInfo(bean).enqueue(new Callback<NetworkResponse<ApplyJKInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ApplyJKInfo>> call, Response<NetworkResponse<ApplyJKInfo>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.infoBack(response.body().getData());
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<ApplyJKInfo>> call, Throwable t) {
                mView.dismissProgress();
                t.printStackTrace();
            }
        });
    }
}
