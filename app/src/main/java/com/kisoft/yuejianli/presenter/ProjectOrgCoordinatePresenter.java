package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ProjectOrgCoordinateContract;
import com.kisoft.yuejianli.entity.WorkContactSheetInfo;
import com.kisoft.yuejianli.model.ProjectOrgCoordinateModel;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.views.ProjectOrgCoordinateActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/14.
 */

public class ProjectOrgCoordinatePresenter extends BasePresenter<ProjectOrgCoordinateActivity, ProjectOrgCoordinateModel> implements
        ProjectOrgCoordinateContract.ProjectOrgCoordinatePresenterContract{

    public ProjectOrgCoordinatePresenter(ProjectOrgCoordinateActivity mView, ProjectOrgCoordinateModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getWorkContactSheets(String uid, String projectId, String sheetType, String count, String pageSize, final String page) {
        mModel.getWorkContactSheets(uid, projectId, sheetType, count, pageSize, page).enqueue(new Callback<NetworkResponse<WorkContactSheetInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<WorkContactSheetInfo>> call, Response<NetworkResponse<WorkContactSheetInfo>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    if(Integer.valueOf(page) == 1){
                        mView.showWorkContactSheets(response.body().getData(), 0);
                    }else {
                        mView.showWorkContactSheets(response.body().getData(), 1);
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<WorkContactSheetInfo>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
