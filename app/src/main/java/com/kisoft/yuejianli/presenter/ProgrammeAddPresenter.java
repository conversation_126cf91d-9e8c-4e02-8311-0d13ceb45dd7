package com.kisoft.yuejianli.presenter;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ProgrammeAddContract;
import com.kisoft.yuejianli.entity.ObjectItemInfo;
import com.kisoft.yuejianli.entity.ProgrammeInfo;
import com.kisoft.yuejianli.entity.ProgrammeInfoList;
import com.kisoft.yuejianli.model.ProgrammeAddModel;
import com.kisoft.yuejianli.views.ProgrammeAddActivity;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ProgrammeAddPresenter extends BasePresenter<ProgrammeAddActivity, ProgrammeAddModel> implements ProgrammeAddContract.ProgrammeAddPresenterContract {
    public ProgrammeAddPresenter(ProgrammeAddActivity mView, ProgrammeAddModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void getObjectItemInfoList(String typeCode) {
        mModel.getObjectItemInfoList(typeCode).enqueue(new Callback<NetworkResponse<List<ObjectItemInfo.DataBean>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ObjectItemInfo.DataBean>>> call, Response<NetworkResponse<List<ObjectItemInfo.DataBean>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    mView.showObjectItemInfoList(response.body().getData());
                } else {
                    mView.showObjectItemInfoList(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ObjectItemInfo.DataBean>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @Override
    public void addProgrammeAdd(ProgrammeInfo programmeInfo,Boolean isUpdate) {
        mModel.addProgrammeAdd(programmeInfo,isUpdate).enqueue(new Callback<NetworkResponse<Boolean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Boolean>> call, Response<NetworkResponse<Boolean>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    mView.showProgrammeAddResult(true);
                } else {
                    mView.showProgrammeAddResult(false);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Boolean>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    //获取日程详情
    @Override
    public void getProgrammeInfo(String cldGuid) {

        mModel.getProgrammeInfo(cldGuid).enqueue(new Callback<NetworkResponse<ProgrammeInfoList.DataBean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProgrammeInfoList.DataBean>> call, Response<NetworkResponse<ProgrammeInfoList.DataBean>> response) {
                mView.dismissProgress();
                if(response.body()!=null&&response.body().getCode()==NetworkResponse.OK){
                    mView.showProgrammeInfoBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProgrammeInfoList.DataBean>> call, Throwable t) {
                mView.dismissProgress();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }
}
