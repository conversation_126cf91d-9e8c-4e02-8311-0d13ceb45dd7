package com.kisoft.yuejianli.presenter;

import android.util.Log;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.UnitListContract;
import com.kisoft.yuejianli.entity.UnitListBean;
import com.kisoft.yuejianli.model.UnitListModel;
import com.kisoft.yuejianli.ui.UnitListActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class UnitListPresenter extends BasePresenter<UnitListActivity, UnitListModel> implements UnitListContract.UnitListPresnterContract {
    public UnitListPresenter(UnitListActivity mView, UnitListModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getProjectUnitList() {

        mModel.getProjectUnitList().enqueue(new Callback<NetworkResponse<UnitListBean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<UnitListBean>> call,
                                   Response<NetworkResponse<UnitListBean>> response) {
                Log.i("单位工程", "onResponse: " + response.body().getData().toString());
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.listBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<UnitListBean>> call, Throwable t) {
                t.printStackTrace();
            }
        });

//        mModel.getProjectUnitList().enqueue(new Callback<NetworkResponse>() {
//            @Override
//            public void onResponse(Call<NetworkResponse> call, Response<NetworkResponse> response) {
//                Log.i("单位工程", "onResponse: " + response.toString());
//                Gson gson = new Gson();
//                // 序列化为字符串
//                String userStr = gson.toJson(response);
//                Log.i("单位工程", "onCreate: " + userStr);
//
////                // 反序列化为对象
////                UnitListBean bean = gson.fromJson(userStr,UnitListBean.class);
////                Log.i("反序列化为对象", "user2: " + bean + bean.getName());
//
//                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
//
//                }
//            }
//
//            @Override
//            public void onFailure(Call<NetworkResponse> call, Throwable t) {
//                t.printStackTrace();
//            }
//        });




    }
}
