package com.kisoft.yuejianli.presenter;

import android.graphics.Bitmap;
import android.net.Uri;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.QualityAccidentContract;
import com.kisoft.yuejianli.entity.QualityAccident;
import com.kisoft.yuejianli.model.QualityAccidentModel;
import com.kisoft.yuejianli.utils.PhotoUtil;
import com.kisoft.yuejianli.views.QualityAccidentActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/4/25.
 */

public class QualityAccidentPresenter extends BasePresenter<QualityAccidentActivity,
        QualityAccidentModel> implements QualityAccidentContract.QualityAccidentPresenterContract {

    public QualityAccidentPresenter(QualityAccidentActivity mView, QualityAccidentModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void uploadPhoto(String projectId, Bitmap photo , Uri uri) {
        try {
            PhotoUtil.uploadPhoto(photo, projectId, uri);
        } catch (Exception e) {
            e.printStackTrace();

        }
    }

    @Override
    public void sendSupNotice(String uid, String projectId, QualityAccident accident) {
        mModel.sendSupNotice(uid, projectId, accident).enqueue(new Callback<NetworkResponse<Boolean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Boolean>> call, Response<NetworkResponse<Boolean>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                        mView.showSendSupNoticeResult(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Boolean>> call, Throwable t) {
                t.printStackTrace();
                mView.showSendSupNoticeResult(false);
            }
        });
    }
}
