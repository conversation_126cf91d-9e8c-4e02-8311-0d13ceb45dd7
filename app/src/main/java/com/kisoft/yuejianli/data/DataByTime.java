package com.kisoft.yuejianli.data;

import com.kisoft.yuejianli.base.Base;

public class DataByTime extends Base {
    private String startHour;
    private String startMin;
    private String endHour;
    private String endMin;

    @Override
    public String toString() {
        return "DataByTime{" +
                "startHour='" + startHour + '\'' +
                ", startMin='" + startMin + '\'' +
                ", endHour='" + endHour + '\'' +
                ", endMin='" + endMin + '\'' +
                '}';
    }

    public DataByTime() {
    }

    public String getStartHour() {
        return startHour;
    }

    public void setStartHour(String startHour) {
        this.startHour = startHour;
    }

    public String getStartMin() {
        return startMin;
    }

    public void setStartMin(String startMin) {
        this.startMin = startMin;
    }

    public String getEndHour() {
        return endHour;
    }

    public void setEndHour(String endHour) {
        this.endHour = endHour;
    }

    public String getEndMin() {
        return endMin;
    }

    public void setEndMin(String endMin) {
        this.endMin = endMin;
    }
}
