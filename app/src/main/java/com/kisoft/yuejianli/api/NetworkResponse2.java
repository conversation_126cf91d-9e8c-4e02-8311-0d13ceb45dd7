package com.kisoft.yuejianli.api;


import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.kisoft.yuejianli.im.common.enums.Status;
import com.kisoft.yuejianli.im.common.net.ErrorCode;

import java.util.List;
import java.util.Map;

/**
 * Created by tudou on 2018/3/9.
 */

public class NetworkResponse2<T> {

    public static final int OK = 200;
    public static final int NOT_LOGIN = 402;

    private int code;
    private String message;
    private String flag;
    private NetworkResponse2Data data;

    public NetworkResponse2() {
    }

    public int getCode(){
        return code;
    }

    public void setCode(int code){
        this.code = code;
    }

    public String getMessage(){
        return message;
    }

    public void setMessage(String message){
        this.message = message;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

//    public Map<String, Object> getData() {
//        return data;
//    }
//
//    public void setData(Map<String, Object> data) {
//        this.data = data;
//    }


    public NetworkResponse2Data getData() {
        return data;
    }

    public void setData(NetworkResponse2Data data) {
        this.data = data;
    }

    public boolean isCodeInvalid(){
        return code != OK&&code!=401;
    }


    public class NetworkResponse2Data {
        private int total;
        private List<Map<String,Object>> data;

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }

        public List<Map<String, Object>> getData() {
            return data;
        }

        public void setData(List<Map<String, Object>> data) {
            this.data = data;
        }
    }


//    public Status status;
//    private int messageId;
//
//    public NetworkResponse2(Status status, Map<String, Object> data, int errorCode) {
//        this.status = status;
//        this.data = data;
//        this.code = errorCode;
//        this.messageId = ErrorCode.Error.parseMessage(errorCode).getMessageId();
//    }
//
//    public NetworkResponse2(Status status, Map<String, Object> data, int errorCode, String message) {
//        this.status = status;
//        this.data = data;
//        this.code = errorCode;
//        this.message = message;
//    }
//
//    public static <T>NetworkResponse2 success(@Nullable Map<String, Object> data) {
//        return new NetworkResponse2(Status.SUCCESS, data, ErrorCode.EM_NO_ERROR);
//    }
//

//    public static <T> NetworkResponse2 success(@Nullable T data) {
//        return new NetworkResponse2(Status.SUCCESS, data, ErrorCode.EM_NO_ERROR);
//    }
//
//    public static <T> NetworkResponse2<T> error(int code, @Nullable T data) {
//        return new NetworkResponse2<>(Status.ERROR, data, code);
//    }
//
//    public static <T> NetworkResponse2<T> error(int code, String message, @Nullable T data) {
//        return TextUtils.isEmpty(message) ?
//                new NetworkResponse2<>(Status.ERROR, data, code) :
//                new NetworkResponse2<>(Status.ERROR, data, code, message);
//    }
//
//    public static <T> NetworkResponse2<T> loading(@Nullable T data) {
//        return new NetworkResponse2<>(Status.LOADING, data, ErrorCode.EM_NO_ERROR);
//    }
}
