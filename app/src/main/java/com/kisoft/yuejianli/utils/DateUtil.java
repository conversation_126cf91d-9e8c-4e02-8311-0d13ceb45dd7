package com.kisoft.yuejianli.utils;

import android.util.Log;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * Created by tudou on 2018/3/16.
 */

public class DateUtil {

    public static final SimpleDateFormat YMD = new SimpleDateFormat("yyyy-MM-dd");
    public static final SimpleDateFormat YM = new SimpleDateFormat("yyyy-MM");
    public static final SimpleDateFormat YMD_HMS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    public static final SimpleDateFormat YMD_HM = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    public static final SimpleDateFormat HMS = new SimpleDateFormat("HH:mm");
    public static final SimpleDateFormat hms = new SimpleDateFormat("HH:mm:ss");
    public static final SimpleDateFormat MS = new SimpleDateFormat("mm:ss");
    public static final SimpleDateFormat MD = new SimpleDateFormat("MM-dd");

    public static final SimpleDateFormat YMD_1 = new SimpleDateFormat("yyyy/MM/dd");
    public static final SimpleDateFormat YM1 = new SimpleDateFormat("yyyyMM");

    public static final SimpleDateFormat YMD_1_HMS = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

    public static final SimpleDateFormat M = new SimpleDateFormat("mm");

    public static final SimpleDateFormat H = new SimpleDateFormat("HH");

    public static final SimpleDateFormat MM = new SimpleDateFormat("MM");

    public static final SimpleDateFormat dd = new SimpleDateFormat("dd");

    public static final SimpleDateFormat M_D_EEEE = new SimpleDateFormat("MM月dd日 EEEE");


    public static String getEEEEForDate(Date date){
        String today = M_D_EEEE.format(date);
        return today;
    }

    public static String getTodayDate() {
        Date date = new Date();
        String today = YMD.format(date);
        return today;
    }

    public static String getTodayDate(SimpleDateFormat format){
        Date date = new Date();
        String today = format.format(date);
        return today;
    }

    public static String getTime(SimpleDateFormat format){
        Date date = new Date();
        String time = format.format(date);
        return time;
    }

    public static String getMonthDate() {
        Date date = new Date();
        String month = YM.format(date);
        return month;
    }

    public static String getFileMonthDate() {
        Date date = new Date();
        String month = YM1.format(date);
        return month;
    }


    public static String getFormatDate(String time, SimpleDateFormat format) {
        String month = "";
        try {
            month = format.format(stringToDate(time, format));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return month;
    }

    public static String getNowTime() {
        Date date = new Date();
        String time = YMD_HMS.format(date);
        return time;
    }

    public static String getHmTime() {
        Date date = new Date();
        String hmTime = HMS.format(date);
        return hmTime;
    }

    public static String YmdToHmTime(String d) {
        Date date = null;
        try {
            date = stringToDate(d, YMD_HMS);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (date != null) {
            return dateToString(date, HMS);
        }
        return getHmTime();

    }


    public static String getMonthForDate(Date date) {
        String hmTime = MM.format(date);
        return hmTime;
    }


    public static String getDayForDate(Date date) {
        String hmTime = dd.format(date);
        return hmTime;
    }


    public static String longToString(long currentTime, SimpleDateFormat formatType)
            throws ParseException {
        Date date = longToDate(currentTime, formatType); // long类型转成Date类型
        String strTime = dateToString(date, formatType); // date类型转成String
        return strTime;
    }


    public static Date longToDate(long currentTime, SimpleDateFormat formatType)
            throws ParseException {
        Date dateOld = new Date(currentTime); // 根据long类型的毫秒数生命一个date类型的时间
        String sDateTime = dateToString(dateOld, formatType); // 把date类型的时间转换为string
        Date date = stringToDate(sDateTime, formatType); // 把String类型转换为Date类型
        return date;
    }

    public static String dateToString(Date data, SimpleDateFormat formatType) {
        return formatType.format(data);
    }

    public static Date stringToDate(String strTime, SimpleDateFormat formatType)
            throws ParseException {
        Date date = null;
        if(StringUtil.isEmpty(strTime)){
            date = new Date();
        }else {
            date = formatType.parse(strTime);
        }
        return date;
    }

    public static long stringToLong(String strTime, SimpleDateFormat formatType)
            throws ParseException {
        Date date = stringToDate(strTime, formatType); // String类型转成date类型
        if (date == null) {
            return 0;
        } else {
            long currentTime = dateToLong(date); // date类型转成long类型
            return currentTime;
        }
    }

    public static long dateToLong(Date date) {
        return date.getTime();
    }

    // 判断时间是否是过去时间
    public static boolean isLastDate(String time) {
        Boolean isLast = false;
        long itemTime = 0;
        try {
            itemTime = stringToLong(time, YMD_HMS);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (System.currentTimeMillis() > itemTime) {
            isLast = true;
        }
        return isLast;
    }
    public static boolean isLastDate(String time , SimpleDateFormat format) {
        Boolean isLast = false;
        long itemTime = 0;
        try {
            itemTime = stringToLong(time, format);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (System.currentTimeMillis() > itemTime) {
            isLast = true;
        }
        return isLast;
    }


    // 判断时间是否是今天
    public static boolean isToday(String date, SimpleDateFormat dateFormat) {
        Boolean isToday = false;
        Calendar calendar = Calendar.getInstance();
        Calendar calendar1 = Calendar.getInstance();
        try {
            calendar.setTime(stringToDate(date, dateFormat));
            if (calendar.get(Calendar.YEAR) == calendar1.get(Calendar.YEAR) && calendar.get(Calendar.MONTH) == calendar1.get(Calendar.MONTH) &&
                    calendar.get(Calendar.DAY_OF_YEAR) == calendar1.get(Calendar.DAY_OF_YEAR)){
                isToday = true;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return isToday;
    }

    // 判断时间是否是明天
    public static boolean isTomorrow(String date) {
        boolean isTomorrow = false;
        String itemDate = YMD.format(date);
        Date date1 = new Date();
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date1);
        calendar.add(calendar.DATE, 1);//把日期往后增加一天.整数往后推,负数往前移动
        date1 = calendar.getTime(); //这个时间就是日期往后推一天的结果
        String dateString = YMD.format(date1);
        isTomorrow = StringUtil.isEqual(dateString, itemDate);
        return isTomorrow;
    }

    // 获得星期日期
    public static String getWeekDate(String tiem) {

        String weekDate = "";
        Calendar cal = Calendar.getInstance();
        cal.setFirstDayOfWeek(Calendar.MONDAY);//设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        try {
            cal.setTime(stringToDate(tiem, YMD));
            int dayWeek = cal.get(Calendar.DAY_OF_WEEK);//获得当前日期是一个星期的第几天
            //判断要计算的日期是否是周日，如果是则减一天计算周六的，否则会出问题，计算到下一周去了
//            if (1 == dayWeek) {
//                cal.add(Calendar.DAY_OF_MONTH, -1);
//
//            }
            int day = cal.get(Calendar.DAY_OF_WEEK);//获得当前日期是一个星期的第几天
            switch (day) {
                case 1:
                    weekDate = "星期天";
                    break;
                case 2:
                    weekDate = "星期一";
                    break;
                case 3:
                    weekDate = "星期二";
                    break;
                case 4:
                    weekDate = "星期三";
                    break;
                case 5:
                    weekDate = "星期四";
                    break;
                case 6:
                    weekDate = "星期五";
                    break;
                case 7:
                    weekDate = "星期六";
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return weekDate;
    }

    /**
     * 获得上
     */


    /**
     * 获得上月
     */
    public static String getLsatMonth(String month) {
        Calendar calendar = Calendar.getInstance();
        String mon = "";
        try {
            calendar.setTime(stringToDate(month, YM));
            calendar.add(Calendar.MONTH, -1);
            mon = YM.format(calendar.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mon;
    }

    /**
     * 获得下月
     */
    public static String getAfterMonth(String month) {
        Calendar calendar = Calendar.getInstance();
        String mon = "";
        try {
            calendar.setTime(stringToDate(month, YM));
            calendar.add(Calendar.MONTH, 1);
            mon = YM.format(calendar.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mon;
    }

    /**
     * 获得上一天
     */
    public static String getLastDay(String day) {
        String dayTime = "";
        Calendar calendar = Calendar.getInstance();
        try {

            calendar.setTime(stringToDate(day, YMD));
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            dayTime = YMD.format(calendar.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dayTime;
    }

    /**
     * 获得下一天
     */
    public static String getNextDay(String day) {
        String dayTime = "";
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(stringToDate(day, YMD));
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            dayTime = YMD.format(calendar.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dayTime;
    }

    public static String getDay(String day, int afterNum) {
        String dayTime = "";
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(stringToDate(day, YMD));
            calendar.add(Calendar.DAY_OF_MONTH, afterNum);
            dayTime = YMD.format(calendar.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dayTime;
    }

    /**
     * 获得年，月，日拼接事件
     */
    public static String getToday(int year, int month, int day) {
        String today = "";
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.set(year, month-1, day);
            today = YMD.format(calendar.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }

        return today;
    }

    /**
     * 获得年，月拼接事件
     */
    public static String getTodayDate(int year, int month, int day) {
        String today = "";
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.set(year, month-1, day);
            today = YM.format(calendar.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }

        return today;
    }


    /**
     * 获得年
     */
    public static final String getTimeStr(int type) {
        Calendar calendar = Calendar.getInstance();
        int str = 0;
        switch (type) {
            case 1:     // 年
                str = calendar.get(Calendar.YEAR);
                break;

            case 2:     // 月
                str = calendar.get(Calendar.MONTH)+1;
                break;

            case 3:     // 日
                str = calendar.get(Calendar.DAY_OF_MONTH);

                break;

            case 4:       //  时
                str = calendar.get(Calendar.HOUR_OF_DAY);
                break;

            case 5:       // 分
                str = calendar.get(Calendar.MINUTE);
                break;

            case 6:       // 秒
                str = calendar.get(Calendar.SECOND);

                break;

            default:

                break;
        }

        return str+"";

    }

    public static final String getTimeStrByTime(String time, SimpleDateFormat format,int type ){
        int str = 0;
        Calendar calendar = Calendar.getInstance();
        Date date = null;
        try {
            date = stringToDate(time ,format);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if(date != null){
            calendar.setTime(date);
        }
        switch (type){

        }

        switch (type) {
            case 1:     // 年
                str = calendar.get(Calendar.YEAR);
                break;

            case 2:     // 月
                str = calendar.get(Calendar.MONTH)+1 ;
                break;

            case 3:     // 日
                str = calendar.get(Calendar.DAY_OF_MONTH);

                break;

            default:

                break;
        }

        return str+"";
    }

    /**
     * 判断时间相等(同一天)
     */
    public static boolean timeIsEq(String time1,String time2){
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();

        Log.i("000", "----------待办时间------"+time1);
        Log.i("000", "----------日历时间------"+time2);
        try {
            if(StringUtil.isEmpty(time1)){
                return true;
            }
            Date date1= YMD.parse(time1);
            Date date2 = YMD.parse(time2);
            calendar1.setTime(date1);
            calendar2.setTime(date2);
            if(calendar1.get(Calendar.YEAR) == calendar2.get(Calendar.YEAR)  && calendar2.get(Calendar.MONTH)== calendar1.get(Calendar.MONTH)
                    && calendar1.get(Calendar.DAY_OF_MONTH) == calendar2.get(Calendar.DAY_OF_MONTH)){
                return true;
            }else {
                return false;
            }
        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static String getMonthDayByTime(String time, SimpleDateFormat format){

        String monthDay = "";
        Calendar calendar  = Calendar.getInstance();
        try {
            Date date = format.parse(time);
            calendar.setTime(date);
            int month = calendar.get(Calendar.MONTH)+1;
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            return month+"-"+day;

        } catch (ParseException e) {
            e.printStackTrace();
        }

        return monthDay;
    }

    public static String getHmsTimeByTime(String time ,SimpleDateFormat format){
        String hmTime = "";
        Calendar calendar  = Calendar.getInstance();
        try {
            Date date = format.parse(time);
            calendar.setTime(date);
            int h = calendar.get(Calendar.HOUR_OF_DAY);
            int m = calendar.get(Calendar.MINUTE);
            if(m<10){
                return h+":0"+m;
            }else {
                return h+":"+m;
            }


        } catch (ParseException e) {
            e.printStackTrace();
        }

        return hmTime;

    }

    public static String getYmdByTime(String time){
        String ymdTime = "";
        String[] s = time.split(" ");
        if(s.length>0){
            ymdTime = s[0];
        }
        return ymdTime;
    }

    public static String getYmByTime(String time){
        String ymTIme  = "";
       return getFormatDate(getYmdByTime(time), YM);
    }

    /**
     * 根据培训信息，获得培训日程
     */

    public static List<String> getTrainDays(String begDay ,String endDay){

        List<String> days = new ArrayList<>();
        Date dBegin;
        Date dEnd;
        try {
            dBegin = stringToDate(begDay, YMD_1);
        } catch (ParseException e) {
            e.printStackTrace();
            dBegin = new Date();
        }
        try {
            dEnd = stringToDate(endDay, YMD_1);
        } catch (ParseException e) {
            e.printStackTrace();
            dEnd = new Date();
        }

        days.add(begDay);
        Calendar calBegin = Calendar.getInstance();
        calBegin.setTime(dBegin);

        Calendar calEnd = Calendar.getInstance();
        calEnd.setTime(dEnd);
        // 测试此日期是否在指定日期之后
        while (dEnd.after(calBegin.getTime()))
        {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            days.add(dateToString(calBegin.getTime(), YMD_1));
        }
        return  days;
    }

    public static String getNumber(){
        StringBuffer num = new StringBuffer();
        for (int i=1; i<7;i++){
            if (i == 3){
                num.append(getTimeStr(i)+"-");
            }else {
                num.append(getTimeStr(i));
            }

        }

        return num.toString();
    }

    /**
     * 获得一年的某天时间
     */
    public static String getYearDay(int year ,int dayIndex){
        String day  = "";
        Calendar calendar = Calendar.getInstance();
        if (dayIndex == 0){              //获得第一天
            calendar.set(Calendar.YEAR, year);
            calendar.set(Calendar.MONTH, 0);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
          day = YMD.format( calendar.getTime()) ;
        }else if (dayIndex == 1){
            calendar.set(Calendar.YEAR, year);
            calendar.set(Calendar.MONTH, 11);
            calendar.set(Calendar.DAY_OF_MONTH, 31);
            day = YMD.format(calendar.getTime());
        }
       return day;
    }


    public static String showCountTime(long time){
        String h,m,s;
        h=checkTime((int)((time / (1000 * 60 * 60))));  //时
        m=checkTime((int)((time % (1000 * 60 * 60)) / (1000 * 60)));  //分
        s=checkTime((int)((time % (1000 * 60)) / 1000));     //秒
        return h+":"+m+":"+s;
    }

    private static String checkTime(int i){
        if(i<10){
            return "0"+i;
        }
        return String.valueOf(i);
    }



}
