package com.kisoft.yuejianli.utils;

import android.app.Activity;
import android.content.Context;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.util.Log;
import android.view.View;

import androidx.core.content.ContextCompat;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ExamQuestionInfo;
import com.kisoft.yuejianli.entity.MaterialUnit;
import com.kisoft.yuejianli.entity.ProjectEmployer;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.views.WebActivity;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by tudou on 2018/3/10.
 */

public class StringUtil {

    public static boolean isEmpty(String s){
        if(s == null || s.isEmpty()){
            return true;
        }else {
            return false;
        }
    }

    public static boolean isEqual(String a, String b){
        if(a.equals(b)){
            return true;
        }else {
            return  false;
        }
    }



    public static String ArryToJSon(Object bean) {
        Gson gson = new Gson();
        Log.i("gson = ",gson.toJson(bean));
        return gson.toJson(bean);
    }

    public static <T> T jsonToObject(String json, Class<T> tClass) {
        Gson gson = new Gson();
        Log.i("000", json.toString());
        return gson.fromJson(json, tClass);
    }

    public static String objectToJson(Object o){
        Gson gson = new Gson();
        Log.i("gson = ",gson.toJson(o));
        return gson.toJson(o);
    }

    private static final Type JSON_OBJECT_TYPE = new TypeToken<List<JsonObject>>() {
    }.getType();

    public static <T> List<T> jsonToArry(String json, Class<T> tClass) {
        Gson gson = new Gson();
        List<JsonObject> jsonObjectList = gson.fromJson(json, JSON_OBJECT_TYPE);
        List<T> list = new ArrayList<>();
        for (JsonObject jsonObject : jsonObjectList) {
            list.add(jsonToObject(jsonObject.toString(), tClass));
        }
        return list;
    }

    public static String byteToString(byte[] bytes) {
        String res = null;
        try {
            res = new String(bytes, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return res;
    }

    public static String stringToUtf8(String s){
        String res = null;
        try{
            res = new String(s.getBytes("iso-8859-1"),"utf-8");
        }catch (Exception e){
            e.printStackTrace();
        }
        return res;
    }

    public static List<String> stringToArry(String str){
        List<String> data = new ArrayList<>();
        String[] strData = str.split(",");
        if(strData != null && strData.length > 0){
            for(int i=0; i<strData.length; i++){
                data.add(strData[i]);
            }
        }
        return data;
    }

    public static String arryToString(List<String> data ){
        StringBuffer stringBuffer = new StringBuffer();
        for(String s: data){
            if(stringBuffer.length() > 0){
                stringBuffer.append(",");
            }
            stringBuffer.append(s);
        }

        return stringBuffer.toString();
    }

    public static List<String> stringToImageArry(String str){
        String st = str.replaceAll("#", ",");
        List<String> data=  new ArrayList<>();
        String[] strData  = st.split(",");
        if(strData != null && strData.length >0){
            for(int i=0; i<strData.length; i++){
                data.add(strData[i]);
            }
        }
        return  data;
    }

    public static String imageArryToString(List<String> data){
        StringBuffer stringBuffer = new StringBuffer();
        for(String s: data){
            if(stringBuffer.length() >0){
                stringBuffer.append(",");
            }
            stringBuffer.append(s);
        }
        return stringBuffer.toString();
    }


    public static List<MaterialUnit> getUnit(){
        String[] names = {"吨","千克","包","米","毫米","立方米","平方米","个","根","卷","张","套","樘",
                "把","支","块","棵","株","台","辆","只","节","件"};
        List<MaterialUnit> units = new ArrayList<>();
        for(int i=0; i<names.length; i++){
            units.add(new MaterialUnit(names[i]));
        }
        return units;
    }

    public static List<MaterialUnit> getShootpublic(){

        String[] names = {"公开","仅自己","指定用户"};
        List<MaterialUnit> units = new ArrayList<>();
        for(int i=0; i<names.length; i++){
            units.add(new MaterialUnit(names[i]));
        }
        return units;

    }

    /**
     * 去除首尾指定字符
     * @param stream 字符串
     * @param trimStr 需去除的字符
     * @return 处理后字符串
     */
    public static String trimString(String stream, String trimStr){
        // null或者空字符串的时候不处理
        if (stream == null || stream.length() == 0 || trimStr == null || trimStr.length() == 0) {
            return stream;
        }
        // 结束位置
        int ePos = 0;
        // 正规表达式
        String regPattern = "[" + trimStr + "]*+";
        Pattern pattern = Pattern.compile(regPattern, Pattern.CASE_INSENSITIVE);
        // 去掉结尾的指定字符
        StringBuffer buffer = new StringBuffer(stream).reverse();
        Matcher matcher = pattern.matcher(buffer);
        if (matcher.lookingAt()) {
            ePos = matcher.end();
            stream = new StringBuffer(buffer.substring(ePos)).reverse().toString();
        }
        // 去掉开头的指定字符
        matcher = pattern.matcher(stream);
        if (matcher.lookingAt()) {
            ePos = matcher.end();
            stream = stream.substring(ePos);
        }
        // 返回处理后的字符串
        return stream;
    }

    public static String getPersonNameById(String id){
        String name = "";
        List<ProjectEmployer> employers = SettingManager.getInstance().getProjectEmployer();
        if(employers != null){
            for(ProjectEmployer e: employers){
                if(isEqual(id, e.getId())){
                    name = e.getName();
                }
            }
        }
        return  name;
    }

    public static String getApprovers(List<String> ids){
        StringBuffer approvers = new StringBuffer();
        for(String s:ids){
            if(approvers.length() >0){
                approvers.append("、");
            }
            approvers.append(getPersonNameById(s));
        }
        return approvers.toString();
    }

    public static String getLogContent(int index, String content){
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(Integer.toString(index)+"、");
        stringBuffer.append(content).append("\r\n");
        return stringBuffer.toString();
    }

    public static String getInvoiceTypeStr(String invoiceType) {
        if("1".equals(invoiceType)){
            return "增值税专用发票";
        }else if("2".equals(invoiceType)){
            return "电子普通发票";
        }else {
            return "增值税普通发票";
        }
    }

    public static String getQuestionTypeName(String qustType) {
        String type="";
        if(isEmpty(qustType)){
            qustType=ExamQuestionInfo.Q_TYPE_SINGLE_SELECT;
        }
        switch (qustType){
            case ExamQuestionInfo.Q_TYPE_SINGLE_SELECT:
                type="单选题";
                break;
            case ExamQuestionInfo.Q_TYPE_MORE_SELECT:
                type="多选题";
                break;
            case ExamQuestionInfo.Q_TYPE_JUDGE:
                type="判断";
                break;
            case ExamQuestionInfo.Q_TYPE_GAP_FILLING:
                type="填空";
                break;
            case ExamQuestionInfo.Q_TYPE_SHORT_ANSWER:
                type="简答";
                break;
        }
        return type;
    }

    public static String getDoubleShow(double num) {
//        NumberFormat nf = NumberFormat.getInstance();
        DecimalFormat df=new DecimalFormat("###################.###########");
        BigDecimal b = new BigDecimal(num);
        double price = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        return df.format(price);
    }

    //处理返回链接 浏览器跑后台不识别 \ \\
    public static String handlerUrl(String path) {
        String replace = path.replace("\\\\", URLEncoder.encode("\\\\"));
        return replace.replace("\\", URLEncoder.encode("\\"));
    }

    /**
     * 获得指定数目的UUID
     * @param number int 需要获得的UUID数量
     * @return String[] UUID数组
     */
    public static String[] getUUID(int number){
        if(number < 1){
            return null;
        }
        String[] retArray = new String[number];
        for(int i=0;i<number;i++){
            retArray[i] = getUUID();
        }
        return retArray;
    }

    /**
     * 获得一个UUID
     * @return String UUID
     */
    public static String getUUID(){
        String uuid = UUID.randomUUID().toString();
        //去掉“-”符号
        return uuid.replaceAll("-", "");
    }

    public static String getPlayTime(int time) {
        int m = (int) (time / android.text.format.DateUtils.MINUTE_IN_MILLIS);
        int s = (int) ((time / android.text.format.DateUtils.SECOND_IN_MILLIS) % 60);
        String mm = String.format(Locale.getDefault(), "%02d", m);
        String ss = String.format(Locale.getDefault(), "%02d", s);
        return "mm:ss".replace("mm", mm).replace("ss", ss);
//		int temp=0;
//		StringBuffer sb=new StringBuffer();
//		temp = time/3600;
//		sb.append((temp<10)?"0"+temp+":":""+temp+":");
//
//		temp=time%3600/60;
//		sb.append((temp<10)?"0"+temp+":":""+temp+":");
//
//		temp=time%3600%60;
//		sb.append((temp<10)?"0"+temp:""+temp);
//		return sb.toString();
    }

    /**
     *
     * @param time 秒
     * @return 返回 10：33
     */
    public static String getPlayVideoTime(int time) {
        int m = (int) (time / 60);
        int s = (int) (time  % 60);
        String mm = String.format(Locale.getDefault(), "%02d", m);
        String ss = String.format(Locale.getDefault(), "%02d", s);
        return "mm:ss".replace("mm", mm).replace("ss", ss);
//		int temp=0;
//		StringBuffer sb=new StringBuffer();
//		temp = time/3600;
//		sb.append((temp<10)?"0"+temp+":":""+temp+":");
//
//		temp=time%3600/60;
//		sb.append((temp<10)?"0"+temp+":":""+temp+":");
//
//		temp=time%3600%60;
//		sb.append((temp<10)?"0"+temp:""+temp);
//		return sb.toString();
    }

    /**
     *
     * @param time 秒
     * @return 返回 10：33
     */
    public static String transSecondTimeToHours(int time) {
		int temp = time;
		StringBuffer sb=new StringBuffer();
		temp = time/3600;
		sb.append((temp<10)?"0"+temp+":":""+temp+":");

		temp=time%3600/60;
		sb.append((temp<10)?"0"+temp+":":""+temp+":");

		temp=time%3600%60;
		sb.append((temp<10)?"0"+temp:""+temp);
		return sb.toString();
    }

    public static long getPlayTimeToLong(String time) {
        String[] split = time.split(":");
        if(split.length==2){
            return Integer.parseInt(split[0])*android.text.format.DateUtils.MINUTE_IN_MILLIS+Integer.parseInt(split[1])*android.text.format.DateUtils.SECOND_IN_MILLIS;
        }else if(split.length==3){
            return Integer.parseInt(split[0])*android.text.format.DateUtils.HOUR_IN_MILLIS+Integer.parseInt(split[1])*android.text.format.DateUtils.MINUTE_IN_MILLIS+
                    Integer.parseInt(split[2])*android.text.format.DateUtils.SECOND_IN_MILLIS;
        }
        return 0;
    }


    public static final String RES_TYPE_MIPMAP="mipmap";
    public static final String RES_TYPE_DRAWABLE="drawable";

    public static int getResource(String imageName,String type){
        int  resId = YueApplacation.getContext().getResources().getIdentifier(trimString(imageName,".png"), type, YueApplacation.getContext().getPackageName());
        //如果没有在"mipmap"下找到imageName,将会返回0
        return resId;
    }

    public static int getResource(String imageName){
        int  resId = YueApplacation.getContext().getResources().getIdentifier(trimString(imageName,".png"), RES_TYPE_DRAWABLE, YueApplacation.getContext().getPackageName());
        //如果没有在"mipmap"下找到imageName,将会返回0
        return resId;
    }

    public static SpannableString getRuleString() {
        return getRuleString();
    }

    public static SpannableString getRuleString(final Activity activity) {
        Context context=activity.getApplicationContext();
        String first=context.getString(R.string.protocol_confirm_remind);
        String content="《隐私政策和用户协议》";
        SpannableString ss = new SpannableString(first+content+context.getString(R.string.protocol_confirm_remind_end));
//		ss.setSpan(new ForegroundColorSpan(context.getResources().getColor(R.color.text_light)), 0, first.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        TextPaint tp = new TextPaint();
        tp.linkColor = context.getResources().getColor(R.color.colorPrimary);
        tp.setColor(ContextCompat.getColor(context,R.color.colorPrimary));
        tp.setUnderlineText(false);
        ClickableSpan span=new ClickableSpan() {
            @Override
            public void onClick(View view) {
                if(!StringUtil.isEmpty(Constant.PROTOCOL_URL))
                    WebActivity.launch(activity,Constant.PROTOCOL_URL,"用户协议");
            }
        };
        span.updateDrawState(tp);
        ss.setSpan(span, first.length(), first.length()+content.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//		ss.setSpan(new ForegroundColorSpan(context.getResources().getColor(R.color.text_light)), first.length()+content.length(), ss.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return ss;
    }


    public static String encodeUrl(String name) throws UnsupportedEncodingException {
        //中文正则匹配
//        Pattern p = Pattern.compile("([\u4e00-\u9fa5]+)");
//        Matcher m = p.matcher(name);
//        String mv = null;
//        List<String> list=new ArrayList<String>();
//        while (m.find()) {
//            mv = m.group(0);
//            list.add(getURLEncoderString(mv));
//        }
        return new String(name.getBytes(), StandardCharsets.UTF_8);
//        return URLEncoder.encode(name, "UTF-8").replaceAll("\\+", "%20").replaceAll("%28", "\\(")
//                .replaceAll("%29", "\\)").replaceAll("%3B", ";").replaceAll("%40", "@").replaceAll("%23", "\\#")
//                .replaceAll("%26", "\\&");
    }



    // 金额大写
    /**
     * 数字转换中文大写 人民币大写单位银行规定用"元" 无零头金额后跟"整"，有则不跟 角为零时不写角（如：零叁分） 四舍五入到分为减少判读疑惑（一般对大写金额预期较高）和 体现人民币基本单位为元，金额低于壹圆前仍加"零元"
     * 整数转换若干零后若跟非零值，只显示一个零，否则不显示 万(亿)前有零后不加零，因亿、万为一完整单位，（如：拾万贰仟 比 拾万零贰仟 更顺些） 亿为汉语计数最大单位，只要进位到总是显示（如：壹亿亿）
     * 万为次最大单位，亿万之间必须有非零值方显示万（如"壹亿"不可显示为"壹亿万"） 为减少被窜改的可能性，十进位总发壹音，这和下面的习惯读法不一样 （十进位处于第一位不发壹音，如"拾元"非"壹拾元"，
     * 十进位处前有零是否不发壹音不太确定， 如"叁仟零壹拾元"还是"叁仟零拾元"？） 用"拾万"不用"壹拾万"，因为每个整数进位后都有进位单位（拾佰仟万亿） 这样即使金额前没有附防窜改的前缀如"人民币"字样也难窜改些
     * 因为至少要加添两个汉字并且改动后数字必须进位才能窜改成 （如"拾万"可改成"叁拾万"，而"壹拾万"至少要改成"壹佰壹拾万"）
     *
     * @param
     * @return
     */
    public static String getChinessMoney1(String strNum) {
        int n, m, k, i, j, q, p, r, s = 0;
        int length, subLength, pstn;
        String change, output, subInput, input = strNum;
        output = "";
        if (strNum.equals("")) {
            return "";
        } else {
            length = input.length();
            pstn = input.indexOf('.');// 小数点的位置
            if (pstn == -1) {
                subLength = length;// 获得小数点前的数字
                subInput = input;
            } else {
                subLength = pstn;
                subInput = input.substring(0, subLength);
            }
            char[] array = new char[4];
            char[] array2 = {'仟', '佰', '拾'};
            char[] array3 = {'亿', '万', '元', '角', '分'};
            n = subLength / 4;// 以千为单位
            m = subLength % 4;
            if (m != 0) {
                for (i = 0; i < (4 - m); i++) {
                    subInput = '0' + subInput;// 补充首位的零以便处理
                }
                n = n + 1;
            }
            k = n;
            for (i = 0; i < n; i++) {
                p = 0;
                change = subInput.substring(4 * i, 4 * (i + 1));
                array = change.toCharArray();// 转换成数组处理
                for (j = 0; j < 4; j++) {
                    output += formatC(array[j]);// 转换成中文
                    if (j < 3) {
                        output += array2[j];// 补进单位，当为零是不补（千百十）
                    }
                    p++;
                }
                if (p != 0) {
                    output += array3[3 - k];// 补进进制（亿万元分角）
                }
                // 把多余的零去掉
                String[] str = {"零仟", "零佰", "零拾"};
                for (s = 0; s < 3; s++) {
                    while (true) {
                        q = output.indexOf(str[s]);
                        if (q != -1) {
                            output = output.substring(0, q) + "零" + output.substring(q + str[s].length());
                        } else {
                            break;
                        }
                    }
                }
                while (true) {
                    q = output.indexOf("零零");
                    if (q != -1) {
                        output = output.substring(0, q) + "零" + output.substring(q + 2);
                    } else {
                        break;
                    }
                }
                String[] str1 = {"零亿", "零万", "零元"};
                for (s = 0; s < 3; s++) {
                    while (true) {
                        q = output.indexOf(str1[s]);
                        if (q != -1) {
                            output = output.substring(0, q) + output.substring(q + 1);
                        } else {
                            break;
                        }
                    }
                }
                k--;
            }
            if (pstn != -1)// 小数部分处理
            {
                for (i = 1; i < length - pstn; i++) {
                    if (input.charAt(pstn + i) != '0') {
                        output += formatC(input.charAt(pstn + i));
                        output += array3[2 + i];
                    } else if (i < 2) {
                        output += "零";
                    } else {
                        output += "";
                    }
                }
            }
            if (output.substring(0, 1).equals("零")) {
                output = output.substring(1);
            }
            if (output.substring(output.length() - 1, output.length()).equals("零")) {
                output = output.substring(0, output.length() - 1);
            }
            return output += "整";
        }
    }


    private static String formatC(char x) {
        String a = "";
        switch (x) {
            case '0':
                a = "零";
                break;
            case '1':
                a = "壹";
                break;
            case '2':
                a = "贰";
                break;
            case '3':
                a = "叁";
                break;
            case '4':
                a = "肆";
                break;
            case '5':
                a = "伍";
                break;
            case '6':
                a = "陆";
                break;
            case '7':
                a = "柒";
                break;
            case '8':
                a = "捌";
                break;
            case '9':
                a = "玖";
                break;
        }
        return a;
    }


    //public static String digitUppercase(double n) {
    public static String getChinessMoney(String strNum) {
        double n = Double.parseDouble(strNum);

        if (n >= 1000000000){
            return "您输入金额太大了";
        }

        String fraction[] = { "角", "分"};
        String digit[] = { "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};
        String unit[][] = { { "元", "万", "亿"}, { "", "拾", "佰", "仟"}};

        String head = n < 0 ? "负" : "";
        n = Math.abs(n);

        String s = "";
        for (int i = 0; i < fraction.length; i++) {
            s += (digit[(int) (Math.floor(n * 10 * Math.pow(10, i)) % 10)] + fraction[i]).replaceAll("(零.)+", "");
        }
        if (s.length() < 1) {
            s = "整";
        }
        int integerPart = (int) Math.floor(n);

        for (int i = 0; i < unit[0].length && integerPart > 0; i++) {
            String p = "";
            for (int j = 0; j < unit[1].length && n > 0; j++) {
                p = digit[integerPart % 10] + unit[1][j] + p;
                integerPart = integerPart / 10;
            }
            s = p.replaceAll("(零.)*零$", "").replaceAll("^$", "零") + unit[0][i] + s;
        }
        return head + s.replaceAll("(零.)*零元", "元").replaceFirst("(零.)+", "").replaceAll("(零.)+", "零").replaceAll("^整$", "零元整");
    }


}
