/*
 *
 * This file is part of the NTKO Mobile Office project.
 * Copyright (c) 2014-2020 NTKO.com
 * Author: <PERSON><PERSON><PERSON><PERSON>, <EMAIL>.
 *
 * For more information, please contact NTKO ltd. at this
 * address: <EMAIL>
 *
 */

package com.kisoft.yuejianli.utils.office.provider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class CryptoUtils {

    private byte[] key;
    private static final String ALGORITHM_KEY = "AES";
    private static final String ALGORITHM = "AES/CTR/NoPadding";

    public CryptoUtils(byte[] key) {
        this.key = key;
    }

    /**
     * Encrypts the given plain text
     *
     * @param plainText The plain text to encrypt
     */
    public byte[] encrypt(byte[] plainText) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(key, ALGORITHM_KEY);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        IvParameterSpec iv = new IvParameterSpec(key);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);

        return cipher.doFinal(plainText);
    }

    /**
     * Decrypts the given byte array
     *
     * @param cipherText The data to decrypt
     */
    public byte[] decrypt(byte[] cipherText) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(key, ALGORITHM_KEY);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        IvParameterSpec iv = new IvParameterSpec(key);
        cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);

        return cipher.doFinal(cipherText);
    }
}
