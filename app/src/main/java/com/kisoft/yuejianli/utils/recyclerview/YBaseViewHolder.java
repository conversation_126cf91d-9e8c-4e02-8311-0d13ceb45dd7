package com.kisoft.yuejianli.utils.recyclerview;

import android.view.View;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.utils.ImageLoadUtil;

/**
 * BaseQuickAdapter 复写方法
 */
public class YBaseViewHolder extends BaseViewHolder {

    public YBaseViewHolder(View view) {
        super(view);
    }

    @Override
    public YBaseViewHolder setVisible(int viewId, boolean visible) {
        View view = getView(viewId);
        view.setVisibility(visible ? View.VISIBLE : View.GONE);
        return this;
    }

    public YBaseViewHolder setSelected(int viewId, boolean isSelect) {
        View view = getView(viewId);
        view.setSelected(isSelect);
        return this;
    }

    public YBaseViewHolder setUrlImage(int viewId, String url) {
        ImageLoadUtil.loading(YueApplacation.getContext(), url, (ImageView) getView(viewId));
        return this;
    }
    public YBaseViewHolder setUrlImage(int viewId, String url, int error) {
        ImageLoadUtil.loading(YueApplacation.getContext(), url, error,(ImageView) getView(viewId));
        return this;
    }
}
