package com.kisoft.yuejianli.utils;

import android.app.Activity;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import androidx.core.content.FileProvider;
import android.text.format.DateFormat;
import android.util.Log;

//import net.bither.util.NativeUtil;

import java.io.File;
import java.util.Calendar;
import java.util.Locale;

/**
 * 图片工具类
 * 1、相机或相册获取的照片，并可以裁剪（固定大小或者自由裁剪）
 *
 */
public class CameraUtil {

    /**相机选择**/
    public final static int PHOTO_REQUEST_CAMERA=100;

    /**相册选择**/
    public final static int PHOTO_REQUEST_GALLERY=200;

    /**裁剪**/
    public final static int PHOTO_REQUEST_CROP=300;

    /**图片所在文件夹**/
    public static String picDir = Environment.getExternalStorageDirectory() + "/photo/";

    /**调用相机拍照成功的图片路径**/
    public static String photoCameraPath ="";

    /**调用系统裁剪成功的图片路径**/
    public static String photoCropPath ="";

    /**
     * 初始化
     */
    public static void init(){
       FileUtil.isExists(picDir);
    }


    /**
     * 调用系统的拍照功能
     * @param activity
     */
    public static void toCamera(Activity activity){
        //图片路径
        photoCameraPath = picDir + DateFormat.format("yyyyMMdd_hhmmss", Calendar.getInstance(Locale.CHINA)) + ".jpg";
        File photoFile = new File(photoCameraPath);

        Uri photoUri=null;
        //判断当前Android版本号是否大于等于24（7.0）
        if(Build.VERSION.SDK_INT >= 24){
            //如果是则使用FileProvider
            photoUri = FileProvider.getUriForFile(activity,activity.getPackageName()+".fileProvider", photoFile);
        }else{
            photoUri= Uri.fromFile(photoFile);
        }

        Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
        intent.putExtra("outputFormat", Bitmap.CompressFormat.JPEG.toString());
        activity.startActivityForResult(intent, PHOTO_REQUEST_CAMERA);
    }

    /**
     * 调用系统的相册
     * @param activity
     */
    public static void toGallery(Activity activity){
        Intent intent = new Intent(Intent.ACTION_PICK, null);
        intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*");
        activity.startActivityForResult(intent, PHOTO_REQUEST_GALLERY);
    }

    /**
     * 调用系统的裁剪
     * @param activity
     * @param path  需要裁剪的图片路径
     * @param isFixed  是否固定比例
     * @param width  isFixed=true 有用
     * @param height  isFixed=true 有用
     */
    public static void toCropPhoto(Activity activity, String path, boolean isFixed, int width, int height){
        Log.i("ldd","裁剪图片路径===="+path);

        Intent intent = new Intent("com.android.camera.action.CROP");
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        intent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION);

        Uri photoUri=null;

        //判断当前Android版本号是否大于等于24（7.0）
        if(Build.VERSION.SDK_INT >= 24){
            //如果是则使用FileProvider
            photoUri = FileProvider.getUriForFile(activity,activity.getPackageName()+".fileProvider", new File(path));
        }else{
            photoUri= Uri.fromFile(new File(path));
        }
        intent.setDataAndType(photoUri, "image/*");
        // crop为true是设置在开启的intent中设置显示的view可以剪裁
        intent.putExtra("crop", "true");

        if(isFixed){
            // aspectX aspectY 是宽高的比例
            intent.putExtra("aspectX", width);
            intent.putExtra("aspectY", height);
            // outputX,outputY 是剪裁图片的宽高
            intent.putExtra("outputX", width);
            intent.putExtra("outputY", height);
        }

        //true的话直接返回bitmap，可能会很占内存，会超过intent参数内存限制
        intent.putExtra("return-data", false);
        //输出格式
        intent.putExtra("outputFormat", Bitmap.CompressFormat.JPEG.toString());

        photoCropPath = picDir + DateFormat.format("yyyyMMdd_hhmmss", Calendar.getInstance(Locale.CHINA)) + "_crop.jpg";
        //裁切成的图片的储存路径
        intent.putExtra(MediaStore.EXTRA_OUTPUT, Uri.fromFile(new File(photoCropPath)));

        activity.startActivityForResult(intent, PHOTO_REQUEST_CROP);
    }


    /**
     * 从相册选择成功后，返回图片地址
     */
    public static String getGalleryPhotoPath(Activity context, Intent data){
        String path = "";
        if (null != data) {

            //转码--防止中文乱码
            String path1 = data.getDataString();
            String str_uri = Uri.decode(path1);

            //有些机型的uri不相同
            if ("file".equals(str_uri.substring(0, str_uri.indexOf(":")))) {
                path = str_uri.substring(str_uri.indexOf("s"), str_uri.length());
            } else {
                //选择图片
                Uri uri = data.getData();
                String[] proj = {MediaStore.Images.Media.DATA};
                Cursor cursor = context.managedQuery(uri, proj, null, null, null);
                //获得用户选择的图片的索引值
                int column_index = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
                //将光标移至开头 ，这个很重要，不小心很容易引起越界
                cursor.moveToFirst();
                //最后根据索引值获取图片路径
                path = cursor.getString(column_index);
            }

        }
        return  path;
    }


    /**
     * 图片压缩
     *
     * @param path
     * @return
     */
//    public static String compressBitmap(String path) {
//        String compressImagePath = null;
//        File vFile = new File(picDir, "compress" + String.valueOf(System.currentTimeMillis()) + ".jpg");
//        if (hasSdcard()) {
//            if (!vFile.exists()) {
//                File vDirPath = vFile.getParentFile();
//                vDirPath.mkdirs();
//            } else {
//                if (vFile.exists()) {
//                    vFile.delete();
//                }
//            }
//            compressImagePath = vFile.getPath();
//            NativeUtil.compressBitmap(path, compressImagePath, 50);
//        } else {
//            //没有SDCard
//            Log.i("CameraUtil","===没有SDCard");
//        }
//        return compressImagePath;
//    }
    /**
     * 检查设备是否存在SDCard的工具方法
     */
    public static boolean hasSdcard() {
        String state = Environment.getExternalStorageState();
        if (state.equals(Environment.MEDIA_MOUNTED)) {
            // 有存储的SDCard
            return true;
        } else {
            return false;
        }
    }



}
