package com.kisoft.yuejianli.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.media.MediaPlayer;
import android.os.SystemClock;
import android.view.View;
import android.view.WindowManager;
import android.widget.Toast;

import com.kisoft.yuejianli.api.HuaweiService;
import com.kisoft.yuejianli.entity.AsrBean;
import com.kisoft.yuejianli.service.RecordingService;

import org.xutils.common.util.LogUtil;

import java.io.File;
import java.io.IOException;
import java.nio.IntBuffer;

public class VoiceRecordUtil {

    private Activity mContext;
    private VoiceRecordListener voiceRecordListener;
    //超时时间
    private long outTime=59000;
    private long startRecordTime;
    private String soundFilePath;
    private int recordCode;
    private MediaPlayer mMediaPlayer;
    private boolean isPlayIng;
    public static String SOUND_DIR;

    public VoiceRecordUtil(Activity context, VoiceRecordListener voiceRecordListener) {
        this.mContext = context;
        this.voiceRecordListener = voiceRecordListener;
        SOUND_DIR=context.getExternalCacheDir().getAbsolutePath()+"/sound/";
    }

    public void setOutTime(long outTime) {
        this.outTime = outTime;
    }

    // 开始录音
    public void startRecording(int code) {
        recordCode=code;
        startRecordTime=System.currentTimeMillis();
        if(voiceRecordListener!= null){
            voiceRecordListener.startRecord(code);
        }
        Intent intent = new Intent(mContext, RecordingService.class);
        // start recording
        Toast.makeText(mContext, "开始录音...", Toast.LENGTH_SHORT).show();
        File folder = new File(SOUND_DIR);
        if (!folder.exists()) {
            //folder /SoundRecorder doesn't exist, create the folder
            folder.mkdir();
        }
        //start RecordingService
        ServiceUtil.safeStartService(mContext, intent);
    }

    // 停止录音
    public void stopRecording() {
        Intent intent = new Intent(mContext, RecordingService.class);
        mContext.stopService(intent);
        //allow the screen to turn off again once recording is finished
        mContext.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        soundFilePath = RecordingService.RECORD_SOUND_PATH;
        RecordingService.RECORD_SOUND_PATH = "";
        LogUtil.i("stopRecording="+soundFilePath);
        if(System.currentTimeMillis()-startRecordTime<1000){
            Toast.makeText(mContext, "录音时间过短", Toast.LENGTH_SHORT).show();
            deleteSound();
        }
        if(voiceRecordListener!= null){
            voiceRecordListener.recordSuccess(soundFilePath,System.currentTimeMillis()-startRecordTime>outTime,recordCode);
        }
    }

    //删除录音
    public void deleteSound() {
        File file = new File(soundFilePath);
        LogUtil.i("deleteSound "+"soundFilePath=" + soundFilePath);
        if (file.exists() && file.isFile()) {
            boolean delete = file.delete();
            LogUtil.i("delete=" + delete);
        }
        soundFilePath = "";
    }
    public void deleteSoundAll() {
        FileUtil.deleteAllFile(SOUND_DIR);
    }
    //删除录音
    public void deleteSound(String soundPath) {
        File file = new File(soundPath);
        LogUtil.i("deleteSound "+"soundFilePath=" + soundPath);
        if (file.exists() && file.isFile()) {
            boolean delete = file.delete();
            LogUtil.i("delete=" + delete);
        }
    }

    private int playCode;
    //开始播放录音
    public void startPlaying(String soundPath,int code) {
        if(StringUtil.isEmpty(soundPath)||!new File(soundPath).exists()){
            Toast.makeText(mContext, "播放地址不存在", Toast.LENGTH_SHORT).show();
            return;
        }
        playCode=code;
        if(isPlayIng){
            stopPlaying();
        };
        isPlayIng=true;
        mMediaPlayer = new MediaPlayer();
        try {
            mMediaPlayer.setDataSource(soundPath);
            mMediaPlayer.prepare();

            mMediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
                @Override
                public void onPrepared(MediaPlayer mp) {
                    mMediaPlayer.start();
                    if(voiceRecordListener!=null){
                        voiceRecordListener.startPlay(playCode);
                    }
//                    soundElpased = mMediaPlayer.getDuration();
                }
            });
        } catch (IOException e) {
            LogUtil.i("prepare() failed");
        }

        mMediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                stopPlaying();
            }
        });
        //keep screen on while playing audio
        mContext.getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
    }

    public void resumePlaying() {
        if(mMediaPlayer==null)return;
        mMediaPlayer.start();
    }

    public void pausePlaying() {
        if(mMediaPlayer==null)return;
        mMediaPlayer.pause();
    }

    public void stopPlaying() {
        isPlayIng=false;
        mMediaPlayer.stop();
        mMediaPlayer.reset();
        mMediaPlayer.release();
        mMediaPlayer = null;
        //allow the screen to turn off again once audio is finished playing
        mContext.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        if(voiceRecordListener!=null){
            voiceRecordListener.stopPlay(playCode);
        }
    }

    public interface VoiceRecordListener{
        void startRecord(int recordCode);
        void recordSuccess(String recordPath,boolean timeOut,int recordCode);

        void startPlay(int playCode);
        void stopPlay(int playCode);
    }

}
