package com.kisoft.yuejianli.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import androidx.core.content.FileProvider;
import android.text.TextUtils;
import android.util.Base64;

import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.PhotoUploadResulte;
import com.kisoft.yuejianli.entity.PhotoUploadResulte1;
import com.kisoft.yuejianli.manager.SettingManager;

import org.greenrobot.eventbus.EventBus;
import org.xutils.common.util.LogUtil;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/4/9.
 */

public class PhotoUtil {

    public static final int REQUESTCODE_GET_PHOTO = 1;
    public static final int REQUESTCODE_TAKE_PHOTO = 2;
    public static final int REQUESTCODE_CUT_IMAGE = 3;


    public static void uploadPhoto(Bitmap photo, String pId , final Uri uri) throws Exception{
        byte[] bytes = bitmapToBytes(photo);
        final RequestBody requestBody = RequestBody.create(MediaType.parse("image/jpeg") ,bytes);
        MultipartBody.Part part = MultipartBody.Part.createFormData("file", "file.jpg", requestBody);

        Api.getApiServer().uploadPhoto(pId,part).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    EventBus.getDefault().post(new PhotoUploadResulte(true, response.body().getData(), response.body().getMessage()));
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                t.printStackTrace();
                EventBus.getDefault().post(new PhotoUploadResulte(false,uri.toString(),"图片上传失败！"));

            }
        });
    }

    public static void uploadPhoto1(final String photoPath) throws Exception{
        Bitmap photo = BitmapFactory.decodeFile(photoPath);


        byte[] bytes = bitmapToBytes(photo);
        final RequestBody requestBody = RequestBody.create(MediaType.parse("image/jpeg") ,bytes);
        MultipartBody.Part part = MultipartBody.Part.createFormData("upload", "file.jpg", requestBody);

        Api.getApiServer().uploadPhoto(part).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    EventBus.getDefault().post(new PhotoUploadResulte(true, response.body().getData(), response.body().getMessage()));
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                t.printStackTrace();
                EventBus.getDefault().post(new PhotoUploadResulte(false,photoPath,"图片上传失败！"));

            }
        });

    }

    public static void uploadPhoto2(final String photoPath) throws Exception{
        Bitmap photo = BitmapFactory.decodeFile(photoPath);


        byte[] bytes = bitmapToBytes(photo);
        final RequestBody requestBody = RequestBody.create(MediaType.parse("image/jpeg") ,bytes);
        MultipartBody.Part part = MultipartBody.Part.createFormData("upload", "file.jpg", requestBody);

        Api.getApiServer().uploadPhoto(part).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    EventBus.getDefault().post(new PhotoUploadResulte1(true, response.body().getData(), response.body().getMessage()));
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                t.printStackTrace();
                EventBus.getDefault().post(new PhotoUploadResulte1(false,photoPath,"图片上传失败！"));

            }
        });

    }

    public static byte[] bitmapToBytes(Bitmap bitmap){
        ByteArrayOutputStream baOut = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.PNG, 80,baOut);
        return baOut.toByteArray();
    }

    public static void saveBitmap(Bitmap bm, File targetFile){
        if(!targetFile.getParentFile().exists()){
            targetFile.getParentFile().mkdirs();
        }
        try {
            FileOutputStream fos=new FileOutputStream(targetFile);
            bm.compress(Bitmap.CompressFormat.JPEG, 100, fos);
            fos.flush();
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void saveBytePhoto(byte[] imageData, File targetFile){
        if(!targetFile.getParentFile().exists()){
            targetFile.getParentFile().mkdirs();
        }
        try {
            LogUtil.e("saveBytePhoto="+targetFile.getAbsolutePath());
            OutputStream os = new FileOutputStream(targetFile.getAbsoluteFile());
            os.write(imageData, 0, imageData.length);
            os.flush();
            os.close();
        } catch (IOException e) {
            e.printStackTrace();
            LogUtil.e("saveBytePhoto="+e.getMessage());
        }
    }


    public static Bitmap loadBitmap(String path){
        File targetFile=new File(path);
        if(!targetFile.exists())return null;
        return BitmapFactory.decodeFile(path);
    }

    /**
     * 转化成bmp
     */
    public static Bitmap bytes2Bitmap(byte[] b) {
        if (b.length != 0) {
            return BitmapFactory.decodeByteArray(b, 0, b.length);
        } else {
            return null;
        }
    }


    public static String getPhotofileName(){
      return  System.currentTimeMillis()+".png";
    }


    public static boolean isUpload(String imageUrl){
        if (imageUrl.contains(SettingManager.getInstance().getBaseUrl())){
            return true;
        }else {
            return false;
        }
    }


    public static Uri mCutUri;

    /**
     * 拍照之后，启动裁剪
     *
     * @param camerapath 路径
     * @param imgname    img 的名字
     * @return
     */
    public static Intent cutForCamera(Context context, String camerapath, String imgname) {
        try {
            //设置裁剪之后的图片路径文件
            File cutfile = new File(Environment.getExternalStorageDirectory().getPath(),
                    PhotoUtil.getPhotofileName()); //随便命名一个
            if (cutfile.exists()) { //如果已经存在，则先删除,这里应该是上传到服务器，然后再删除本地的，没服务器，只能这样了
                cutfile.delete();
            }
            cutfile.createNewFile();
            //初始化 uri
            Uri imageUri = null; //返回来的 uri
            Uri outputUri = null; //真实的 uri
            Intent intent = new Intent("com.android.camera.action.CROP");
            //拍照留下的图片
            File camerafile = new File(camerapath, imgname);
            if (Build.VERSION.SDK_INT >= 24) {
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                imageUri = FileProvider.getUriForFile(context, "com.ky.fileprovider", camerafile);
            } else {
                imageUri = Uri.fromFile(camerafile);
            }
            outputUri = Uri.fromFile(cutfile);
            //把这个 uri 提供出去，就可以解析成 bitmap了
            mCutUri = outputUri;
            // crop为true是设置在开启的intent中设置显示的view可以剪裁
            intent.putExtra("crop", true);
            // aspectX,aspectY 是宽高的比例，这里设置正方形
            intent.putExtra("aspectX", 3);
            intent.putExtra("aspectY", 2);
            //设置要裁剪的宽高
            intent.putExtra("outputX", PhoneUtil.dpTopx(YueApplacation.mContext, 360));
            intent.putExtra("outputY", PhoneUtil.dpTopx(YueApplacation.mContext, 240));
            intent.putExtra("scale", true);
            //如果图片过大，会导致oom，这里设置为false
            intent.putExtra("return-data", false);
            if (imageUri != null) {
                intent.setDataAndType(imageUri, "image/*");
            }
            if (outputUri != null) {
                intent.putExtra(MediaStore.EXTRA_OUTPUT, outputUri);
            }
            intent.putExtra("noFaceDetection", true);
            //压缩图片
            intent.putExtra("outputFormat", Bitmap.CompressFormat.JPEG.toString());
            return intent;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 拍照之后，启动裁剪
     *
     * @param camerapath 路径
     * @param imgname    img 的名字
     * @return
     */
    public static Intent cutForCamera1(Context context, String camerapath, String imgname) {
        try {
            //设置裁剪之后的图片路径文件
            File cutfile = new File(Environment.getExternalStorageDirectory().getPath(),
                    PhotoUtil.getPhotofileName()); //随便命名一个
            if (cutfile.exists()) { //如果已经存在，则先删除,这里应该是上传到服务器，然后再删除本地的，没服务器，只能这样了
                cutfile.delete();
            }
            cutfile.createNewFile();
            //初始化 uri
            Uri imageUri = null; //返回来的 uri
            Uri outputUri = null; //真实的 uri
            Intent intent = new Intent("com.android.camera.action.CROP");
            //拍照留下的图片
            File camerafile = new File(camerapath, imgname);
            if (Build.VERSION.SDK_INT >= 24) {
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                imageUri = FileProvider.getUriForFile(context, "com.ky.fileprovider", camerafile);
            } else {
                imageUri = Uri.fromFile(camerafile);
            }
            outputUri = Uri.fromFile(cutfile);
            //把这个 uri 提供出去，就可以解析成 bitmap了
            mCutUri = outputUri;
            // crop为true是设置在开启的intent中设置显示的view可以剪裁
            intent.putExtra("crop", true);
            // aspectX,aspectY 是宽高的比例，这里设置正方形
            intent.putExtra("aspectX", 1);
            intent.putExtra("aspectY", 1);
            //设置要裁剪的宽高
            intent.putExtra("outputX", PhoneUtil.dpTopx(YueApplacation.mContext, 100));
            intent.putExtra("outputY", PhoneUtil.dpTopx(YueApplacation.mContext, 100));
            intent.putExtra("scale", true);
            //如果图片过大，会导致oom，这里设置为false
            intent.putExtra("return-data", false);
            if (imageUri != null) {
                intent.setDataAndType(imageUri, "image/*");
            }
            if (outputUri != null) {
                intent.putExtra(MediaStore.EXTRA_OUTPUT, outputUri);
            }
            intent.putExtra("noFaceDetection", true);
            //压缩图片
            intent.putExtra("outputFormat", Bitmap.CompressFormat.JPEG.toString());
            return intent;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 启动照相机
     * @param context
     */
    public static void takePhoto(Activity context){
        File imageFile = new File(context.getExternalCacheDir(), "output.png");
        try {
            if (imageFile.exists()) {
                imageFile.delete();
            }
            imageFile.createNewFile();
        } catch (Exception e) {
            e.printStackTrace();
        }

        Uri imageUri;
        // 7.0 处理uri安全问题
        if (Build.VERSION.SDK_INT >= 24) {
            imageUri = FileProvider.getUriForFile(context, "com.ky.fileprovider", imageFile);

        } else {
            imageUri = Uri.fromFile(imageFile);
        }
        Intent intent = new Intent("android.media.action.IMAGE_CAPTURE");
        intent.putExtra(MediaStore.EXTRA_OUTPUT, imageUri);
        context.startActivityForResult(intent, PhotoUtil.REQUESTCODE_TAKE_PHOTO);
    }

    /*
     * bitmap转base64
     * */
    private static String bitmapToBase64(Bitmap bitmap) {
        String result = null;
        ByteArrayOutputStream baos = null;
        try {
            if (bitmap != null) {
                baos = new ByteArrayOutputStream();
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, baos);

                baos.flush();
                baos.close();

                byte[] bitmapBytes = baos.toByteArray();
                result = Base64.encodeToString(bitmapBytes, Base64.DEFAULT);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (baos != null) {
                    baos.flush();
                    baos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 将图片转换成Base64编码的字符串
     */
    public static String imageToBase64(String path){
        if(TextUtils.isEmpty(path)){
            return null;
        }
        InputStream is = null;
        byte[] data = null;
        String result = null;
        try{
            is = new FileInputStream(path);
            //创建一个字符流大小的数组。
            data = new byte[is.available()];
            //写入数组
            is.read(data);
            //用默认的编码格式进行编码
//            result = com.kisoft.yuejianli.utils.encoder.Base64.encodeBase64String(data);
            result = Base64.encodeToString(data,Base64.NO_WRAP);
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if(null !=is){
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }
        return result;
    }

    //正为顺时针
    public static Bitmap rotateBitmap(Bitmap bitmap,float degree){
        Matrix matrix=new Matrix();
        matrix.postRotate(degree);
        return Bitmap.createBitmap(bitmap,0,0,bitmap.getWidth(),bitmap.getHeight(),matrix,true);
    }
}
