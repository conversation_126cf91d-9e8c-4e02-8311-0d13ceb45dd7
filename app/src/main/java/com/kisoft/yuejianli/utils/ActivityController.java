package com.kisoft.yuejianli.utils;

import android.app.Activity;

import java.util.ArrayList;
import java.util.List;

/**
 * activity控制器
 */

public class ActivityController {

    //所有的activity
    public static List<Activity> activities = new ArrayList<>();
    //操作过程中需要单独处理的临时activity
    public static List<Activity> tempActivityList = new ArrayList<>();

    public static void addActivity(Activity activity) {
        activities.add(activity);
    }

    public static void removeActivity(Activity activity) {
        activities.remove(activity);
        activity.finish();
    }

    public static void finishAll() {
        for (Activity activity : activities) {
            if (!activity.isFinishing()) {
                activity.finish();
            }
        }
    }

    public static void finishTempActivity() {
        try {
            for (Activity activity : tempActivityList) {
                activity.finish();
            }
            tempActivityList = null;
            tempActivityList = new ArrayList<>();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {

        }
    }

}
