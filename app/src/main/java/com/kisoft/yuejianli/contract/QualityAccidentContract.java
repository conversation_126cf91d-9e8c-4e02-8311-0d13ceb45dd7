package com.kisoft.yuejianli.contract;

import android.graphics.Bitmap;
import android.net.Uri;

import retrofit2.Call;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.QualityAccident;

/**
 * Created by tudou on 2018/4/25.
 */

public interface QualityAccidentContract {

    interface QualityAccidentViewContract{

        void showSendSupNoticeResult(boolean isOk);

    }

    interface QualityAccidentPresenterContract{

        void uploadPhoto(String projectId, Bitmap photo , Uri uri);

        void sendSupNotice(String uid, String projectId, QualityAccident accident);



    }

    interface QualityAccidentModelContract{
        Call<NetworkResponse<Boolean>> sendSupNotice(String uid, String projectId, QualityAccident accident);
    }
}
