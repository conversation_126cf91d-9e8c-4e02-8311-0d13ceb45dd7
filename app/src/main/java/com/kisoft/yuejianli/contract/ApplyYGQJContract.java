package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ApplyLeaveInfo;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.LeaveInfo;
import com.kisoft.yuejianli.entity.ObjectItemInfo;
import com.kisoft.yuejianli.entity.ProcessListBean;

import java.util.List;

import retrofit2.Call;

/**
 * Created by yan<PERSON> on 2018/6/22.
 */

public interface ApplyYGQJContract {

    interface ApplyYGQJViewContract{

        void leaveTypeBack(List<ApplyType> leaveTypes);

        void applyBack(String str);

        void infoBack(LeaveInfo info);


        //获取请假开始时间和结束时间数组
        void showObjectItemInfoList(List<ObjectItemInfo.DataBean> infos);
        //void finshRefresh();
    }


    interface ApplyYGQJPresenterContract{

        void getLeaveType();

        void submitApplyLeave(ApplyLeaveInfo applyLeaveInfo);

        void getInfo(ProcessListBean bean);

        //获取请假开始时间和结束时间数组
        void getObjectItemInfoList(String typeCode);
    }


    interface ApplyYGQJModelContract{

        /**
         * 获取请假类型
         * @return
         */
        Call<NetworkResponse<List<ApplyType>>> getLeaveType();

        /**
         * 提交请假申请
         * @param applyLeaveInfo 请假申请信息
         * @return
         */
        Call<NetworkResponse<String>> submitApplyLeave(ApplyLeaveInfo applyLeaveInfo);

        Call<NetworkResponse<LeaveInfo>> getInfo(ProcessListBean bean);

        //获取请假开始时间和结束时间数组
        Call<NetworkResponse<List<ObjectItemInfo.DataBean>>> getObjectItemInfoList(String typeCode);


    }
}
