package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ContractInfo;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/7.
 */

public interface InvestmentListContract {

    interface InvestmentListViewContract{
        void showContracts(List<ContractInfo> infos);
    }

    interface InvestmentListPresnterContract{

        void getContracts(String uid, String projectId);
    }

    interface InvestmentListModelContract{
        Call<NetworkResponse<List<ContractInfo>>> getContracts(String uid, String projectId);
     }
}
