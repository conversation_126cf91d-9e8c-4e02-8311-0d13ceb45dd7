package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.RecordReadListDto;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/21.
 */

public interface RecordReadListContract {

    interface RecordReadListViewContract{
        void RecordReadListBack(List<RecordReadListDto> dtoList);
    }

    interface RecordReadListPresenterContract{
        void getRecordReadList(String id);
    }

    interface RecordReadListModelContract{
        Call<NetworkResponse<List<RecordReadListDto>>> getRecordReadList(String id);
    }
}
