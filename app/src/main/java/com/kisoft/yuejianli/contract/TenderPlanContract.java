package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.TenderPlan;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/27.
 */

public interface TenderPlanContract {

    interface TenderPlanViewContract{

        void showTenderPlan(List<TenderPlan> plans);
    }

    interface TenderPlanPresenterContract{

        void getTenderPlan(String uid, String month);
    }

    interface TenderPlanModelContract{

        Call<NetworkResponse<List<TenderPlan>>> getTenderPlan(String uid, String month);

    }
}
