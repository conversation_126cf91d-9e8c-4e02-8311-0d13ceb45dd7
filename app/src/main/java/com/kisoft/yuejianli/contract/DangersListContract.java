package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.ContractInfo;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/7.
 */
public interface DangersListContract {

    interface DangersListViewContract {
        void listBack(List<ApplyType> types);
    }

    interface DangersListPresnterContract {
        void getObjectItemListByCode(String uid, String projectId, String itemcode);
    }

    interface DangersListModelContract {
        Call<NetworkResponse<List<ApplyType>>> getObjectItemListByCode(String uid, String projectId, String itemcode);
     }
}
