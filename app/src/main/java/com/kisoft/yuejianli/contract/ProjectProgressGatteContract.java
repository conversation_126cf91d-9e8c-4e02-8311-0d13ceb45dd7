package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.GatteEntity;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudou on 2018/5/22.
 */

public interface ProjectProgressGatteContract {

    interface  ProjectProgressGatteViewContract{
        void gatteDataBack(List<GatteEntity> list);
    }


    interface ProjectProgressGattePresenterContract{
        void getGatteData(String month, String projectId);
    }

    interface ProjectProgressGatteModelConctract{
        Call<NetworkResponse<List<GatteEntity>>> getGatteData(String month, String projectId);
    }
}
