package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ArchivesTransferInfo;

import retrofit2.Call;

public interface ApplyWBZLYJContract {
    interface ApplyWBZLYJViewContract{

        void applyWBZLYJBack(String str);

        void applyWBZLYJInfoBack(ArchivesTransferInfo info);
    }
    interface ApplyWBZLYJPresenterContract{

        void uploadPhotoImage(String photoPath);

        void submitApplyWBZLYJ(ArchivesTransferInfo archivesTransferInfo, String userId, String projectId);

        void getApplyWBZLYJInfo(String userId,String ptId,String  wfTaskId,String  workflow_type,String  wfTaskState,String businessId,String transType);
    }

    interface ApplyWBZLYJModelContract{
        /**
         * 提交外部资料
         * @param archivesTransferInfo  外部资料（竣工档案）提交信息
         * @return
         */
        Call<NetworkResponse<String>> submitApplyWBZLYJ(ArchivesTransferInfo archivesTransferInfo, String userId, String projectId);

        //获取外部资料信息
        Call<NetworkResponse<ArchivesTransferInfo>> getApplyWBZLYJInfo(String userId,String ptId,String  wfTaskId,String  workflow_type,String  wfTaskState,String businessId,String transType);
    }
}
