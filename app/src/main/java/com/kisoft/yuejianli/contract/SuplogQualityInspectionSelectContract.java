package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.QualityAcceptance;
import com.kisoft.yuejianli.entity.QualityInspection;
import com.kisoft.yuejianli.entity.SideReport;
import com.kisoft.yuejianli.entity.SuplogQualityInspection;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/30.
 */

public interface SuplogQualityInspectionSelectContract {

    interface SuplogQualityInspectionSelectViewContract{

        void showQualityInspection(List<SuplogQualityInspection> inspections ,int type);
    }

    interface SuplogQualityInspectionSelectPresenterContract{

        void getSuplogQualityInspection(String uid, String projectId, String time, int type);
    }


    interface SuplogQualityInspectionSelectModelContract{

        Call<NetworkResponse<List<QualityInspection>>> getQualityInspection(String uid, String projectId, String time);

        Call<NetworkResponse<List<SideReport>>> getOnsideInspection(String uid, String projectId, String time);

        Call<NetworkResponse<List<QualityAcceptance>>> getQualityAcceptance(String uid, String projectId, String time);
    }
}
