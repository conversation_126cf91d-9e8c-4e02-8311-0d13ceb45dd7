package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.CustomerVisitInfo;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/19.
 */

public interface CustomerVisitContract {

    interface CustomerVisitViewContract{
        void showCustomerVisits(CustomerVisitInfo info, int type);
    }


    interface CustomerVisitPresenterContract{
        void getCustomerVisits(String uid, String companyId, String count, String page, String pageSize);
    }


    interface CustomerVisitModelContract{
        Call<NetworkResponse<CustomerVisitInfo>> getCustomerVisits(String uid, String companyId, String count, String page, String pageSize);
    }
}
