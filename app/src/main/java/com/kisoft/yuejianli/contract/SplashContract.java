package com.kisoft.yuejianli.contract;

import java.io.File;
import java.util.List;

import okhttp3.ResponseBody;
import retrofit2.Call;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.AppVersionInfo;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.ComPanyOrgInfo;
import com.kisoft.yuejianli.entity.ObjectItemInfo;
import com.kisoft.yuejianli.entity.ProjectInfo;

/**
 * Created by tudou on 2018/6/6.
 */

public interface SplashContract {

    interface SplashViewConrtact{

        void showUserprojects(List<ProjectInfo> projects);

        void showAppVersion(AppVersionInfo versionInfo);

        void installApk(File file);

        void updateProress(int progress);

        void showDownProgress();

        void hideDownProgress();

        //返回选择人信息
        void showOrg(List<ComPanyOrgInfo> infos);

        //请假类型返回
        void leaveTypeBack(List<ApplyType> leaveTypes);


        //获取请假开始时间和结束时间数组
        void showObjectItemInfoList(List<ObjectItemInfo.DataBean> infos);
    }


    interface SplashPresenterConrtact{

        void getUserProjects(String uid, String orgId, String orgRalation);

        void getAppVersionInfo(String uid, int system, int versionCode);

        void getNewApk(String url);

        //获取选择人
        void getOrg(String uid);
        //获取请假类型
        void getLeaveType(String code,String spKey);

        //获取请假开始时间和结束时间数组
        void getObjectItemInfoList(String typeCode);
    }


    interface SplashModelConrtact{

        Call<NetworkResponse<List<ProjectInfo>>> getAllProjects(String uid, String orgId, String orgRalation);

        Call<NetworkResponse<AppVersionInfo>> getAppVersionInfo(String uid, int system, int versionCode);

        //获取选择人（包括部门，人员名称）
        Call<NetworkResponse<List<ComPanyOrgInfo>>> getOrg(String uid);

//        Call<ResponseBody> getNewApk(String url);


        /**
         * 获取请假类型
         * @return
         */
        Call<NetworkResponse<List<ApplyType>>> getLeaveType(String code);


        //获取请假开始时间和结束时间数组
        Call<NetworkResponse<List<ObjectItemInfo.DataBean>>> getObjectItemInfoList(String typeCode);
    }

}
