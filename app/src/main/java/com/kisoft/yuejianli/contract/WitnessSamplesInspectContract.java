package com.kisoft.yuejianli.contract;


import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ProjectApproverInfo;
import com.kisoft.yuejianli.entity.WitnessSamplesInspectInfo;

import java.util.List;

import retrofit2.Call;

/**
 * 见证取样送检
 */

public interface WitnessSamplesInspectContract {

    interface View {
        void showCommitResult(Boolean isOk);
        void showWitnessSamplesInspect(WitnessSamplesInspectInfo info);
        void showUserIdsAndNamesByProjectId(List<ProjectApproverInfo> list);
        void showError();
    }


    interface Presenter {

        void uploadPhotoImage(String photoPath);

        void commitWitnessSamplesInspect(WitnessSamplesInspectInfo info,boolean isAdd);

        void getWitnessSamplesInspect(String wsId);

        void getUserIdsAndNamesByProjectId(String projectId);

    }


    interface Model {

        Call<NetworkResponse<Boolean>> commitWitnessSamplesInspect(WitnessSamplesInspectInfo inspection,boolean isAdd);

        Call<NetworkResponse<WitnessSamplesInspectInfo>> getWitnessSamplesInspect(String wsId);

        Call<NetworkResponse<List<ProjectApproverInfo>>> getUserIdsAndNamesByProjectId(String projectId);
    }
}
