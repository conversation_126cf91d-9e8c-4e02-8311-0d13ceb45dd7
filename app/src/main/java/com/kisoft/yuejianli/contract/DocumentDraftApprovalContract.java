package com.kisoft.yuejianli.contract;


import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ApproveArchivesInfo;
import com.kisoft.yuejianli.entity.ApproveInfo;
import com.kisoft.yuejianli.entity.JDData;

import java.util.List;

import retrofit2.Call;

/**
 * Created by yan<PERSON> on 2019/1/7.
 */

public interface DocumentDraftApprovalContract {

    interface DocumentDraftApprovalViewContract{
        void arcDataBack(ApproveArchivesInfo approveArchivesInfo);

        void initWfBack(ApproveArchivesInfo approveArchivesInfo);

        void startBack();

        void assignNodeListBack(List<JDData> jdDataList);
    }

    interface DocumentDraftApprovalPresenterContract{
        // 通过公文id获取公文信息
        void getArcData(String arcId);

        void initWf(String arcId, String wftId, String wfId);

        void startWf(ApproveInfo info);

        void getAssignNodeList(String wfTaskId);
    }

    interface DocumentDraftApprovalModelContract{

        Call<NetworkResponse<ApproveArchivesInfo>> getArcData(String arcId);

        Call<NetworkResponse<ApproveArchivesInfo>> initWf(String arcId, String wftId, String wfId);

        Call<NetworkResponse<String>> startWf(ApproveInfo info);

        Call<NetworkResponse<List<JDData>>> getAssignNodeList(String wfTaskId);

    }

}
