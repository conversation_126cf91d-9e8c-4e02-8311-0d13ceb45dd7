package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.UnitListBean;

import java.util.List;

import retrofit2.Call;

public interface UnitListContract {

    interface UnitListViewContract {
        void listBack(UnitListBean types);
    }

    interface UnitListPresnterContract {
        void getProjectUnitList();
    }

    interface UnitListModelContract {
        Call<NetworkResponse<UnitListBean>> getProjectUnitList();
    }
}
