package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.QualityReportInfo;

import retrofit2.Call;

public interface ApplyZLPGBGContract {

    interface ApplyZLPGBGViewContract{

        void applyZLPGBGBack(String str);

        void applyZLPGBGInfoBack(QualityReportInfo info);
    }

    interface ApplyZLPGBGPresenterContract{

        void uploadPhotoImage(String photoPath);

        void submitApplyZLPGBG(QualityReportInfo archivesTransferInfo, String userId, String projectId);

        void getApplyZLPGBGInfo(String userId,String qualityId,String  wfTaskId,String  workflow_type,String  wfTaskState,String businessId,String transType);
    }

    interface ApplyZLPGBGModelContract{
        // 提交质量评估报告信息
        Call<NetworkResponse<String>> submitApplyZLPGBG(QualityReportInfo qualityReportInfo, String userId, String projectId);

        //获取质量评估报告信息
        Call<NetworkResponse<QualityReportInfo>> getApplyZLPGBGInfo(String userId,String qualityId,String  wfTaskId,String  workflow_type,String  wfTaskState,String businessId,String transType);
    }
}
