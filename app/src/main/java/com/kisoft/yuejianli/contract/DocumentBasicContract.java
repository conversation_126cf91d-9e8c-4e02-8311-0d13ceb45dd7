package com.kisoft.yuejianli.contract;


import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ArchivesDto;

import retrofit2.Call;

/**
 * Created by yan<PERSON> on 2018/12/28.
 */

public interface DocumentBasicContract {

    interface DocumentBasicViewContract{

        void toArchivesHandel(ArchivesDto dto);
    }

    interface DocumentBasicPresenterContract{

        void toArchivesHandel(String arcId, String wfTaskId, String wfTaskState, String businessId, String type);
    }


    interface DocumentBasicModelContract{

        //String arcId; //公文Id
//        String wfTaskId; //任务Id
//        String wfTaskState;//工作流状态
//        String businessId;//业务Id

        Call<NetworkResponse<ArchivesDto>> toArchivesHandel(String arcId, String wfTaskId, String wfTaskState, String businessId, String type);

    }


}
