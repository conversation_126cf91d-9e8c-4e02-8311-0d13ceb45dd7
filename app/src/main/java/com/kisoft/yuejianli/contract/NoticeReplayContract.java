package com.kisoft.yuejianli.contract;

import android.graphics.Bitmap;
import android.net.Uri;

import com.kisoft.yuejianli.api.NetworkResponse;

import retrofit2.Call;

/**
 * Created by yan<PERSON> on 2018/6/22.
 */

public interface NoticeReplayContract {

    interface NoticeReplayViewContract{

        void showAddSupervisionRepply(Boolean b);

    }


    interface NoticeReplayPresenterContract{


        void addSupervisionRepply(String snId, String snNumber1, String snOpinion);
        void uploadPhoto(String projectId, Bitmap photo, Uri uri);

    }


    interface NoticeReplayModelContract{


        Call<NetworkResponse<Boolean>>addSupervisionRepply(String snId, String snNumber1, String snOpinion);
    }
}
