package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ApplyBusinessTravelInfo;
import com.kisoft.yuejianli.entity.BusinessTravelType;
import com.kisoft.yuejianli.entity.ProcessListBean;

import java.util.List;

import retrofit2.Call;

/**
 * 出差申请
 */
public interface ApplyBusinessTravelContract {

    interface View {

        void typeListBack(List<BusinessTravelType> leaveTypes);

        void applyBack(String str);

        void infoBack(ApplyBusinessTravelInfo info);

        void showError();
    }
    interface Presenter {

        void getBusinessTravelType();

        void submitApply(ApplyBusinessTravelInfo info);

        void getInfo(ProcessListBean bean);

    }
    interface Model {
        /**
         * 获取出差类型
         */
        Call<NetworkResponse<List<BusinessTravelType>>> getBusinessTravelType();

        Call<NetworkResponse<String>> submitApplyInfo(ApplyBusinessTravelInfo info);

        Call<NetworkResponse<ApplyBusinessTravelInfo>> getInfo(ProcessListBean bean);
    }
}
