package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.EnclosureListDto;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/21.
 */

public interface KnowledgeDetailContract {

    interface KnowledgeDetailViewContract{
        void EnclosureListBack(List<EnclosureListDto> dtoList);
    }

    interface KnowledgeDetailPresenterContract{
        void getEnclosureList(String id);
    }

    interface KnowledgeDetailModelContract{
        Call<NetworkResponse<List<EnclosureListDto>>> getEnclosureList(String id);
    }
}
