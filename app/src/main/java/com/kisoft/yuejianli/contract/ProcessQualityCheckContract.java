package com.kisoft.yuejianli.contract;

import android.graphics.Bitmap;
import android.net.Uri;

import com.kisoft.yuejianli.api.NetworkResponse;

import retrofit2.Call;

import com.kisoft.yuejianli.entity.QualityInspection;

/**
 * Created by tudou on 2018/4/9.
 */

public interface ProcessQualityCheckContract {

    interface ProcessQualityCheckViewContract {

        void showCommitResulte(boolean isOk);
    }

    interface ProcessQualityCheckPresenterContract {
        void uploadPhotoImage(String photoPath);

        void commitIspect(QualityInspection inspection, String receiver,boolean isAdd);

    }

    interface ProcessQualityCheckModelContract {

        Call<NetworkResponse> commitIspect(QualityInspection inspection, String receiver,boolean isAdd);
    }
}
