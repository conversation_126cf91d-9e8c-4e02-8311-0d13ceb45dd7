package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.AppPermission;
import com.kisoft.yuejianli.entity.FaceData;
import com.kisoft.yuejianli.entity.InfoIssueDto;
import com.kisoft.yuejianli.entity.News;
import com.kisoft.yuejianli.entity.ProgrammeInfoList;
import com.kisoft.yuejianli.entity.httpresult.ProgrammeMonthInfo;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/8.
 */

public interface EnterContract  {

    interface EnterViewContract{

        void showNewsNum(InfoIssueDto news);
        void showNews(List<News> news);
        void getFaceDataBack(FaceData faceData) ;
        //获取本月日程信息
        void showProgrammeByMonthBack(List<ProgrammeMonthInfo.DataBean> info);
    }


    interface EnterPresenterContract{

        void getNotes(String userId);
        void getNotesNum();
        void getFaceData();
        //获取本月日程信息
        void getProgrammeByMonth(String month,String userId);
        void getAppPermission();
    }


    interface EnterModelContract{

        Call<NetworkResponse<List<News>>> getNotes(String userId);
        Call<NetworkResponse<InfoIssueDto>> getNotesNum();

        Call<NetworkResponse<FaceData>> getFaceData();
        //获取本月日程信息
        Call<NetworkResponse<List<ProgrammeMonthInfo.DataBean>>> getProgrammeByMonth(String month, String userId);
        //app权限
        Call<NetworkResponse<List<AppPermission>>> getAppPermission();
    }
}
