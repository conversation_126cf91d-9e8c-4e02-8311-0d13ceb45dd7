package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.TaskDTO;
import com.kisoft.yuejianli.entity.TaskListDto;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudo<PERSON> on 2018/6/21.
 */

public interface TaskListContract {

    interface TaskListViewContract{
        void taskListBack(TaskListDto taskListDto);
    }

    interface TaskListPresenterContract{
        void getTaskList(int page, int count);
    }

    interface TaskListModelContract{
        Call<NetworkResponse<TaskListDto>> getTaskList(int page, int count);
    }
}
