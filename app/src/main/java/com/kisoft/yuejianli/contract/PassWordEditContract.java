package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.api.NetworkResponse1;

import retrofit2.Call;

/**
 * Created by tudo<PERSON> on 2018/6/14.
 */

public interface PassWordEditContract {

    interface PassWordEditViewContract {

        void showResult(String isOk);
    }

    interface PassWordEditPresenterContract {

        void changePassWord(String uid, String oldPassword, String newPassword);
    }


    interface PassWordEditModelontract {
        Call<NetworkResponse<Object>> changePassWord(String uid, String oldPassword, String newPassword);
    }
}
