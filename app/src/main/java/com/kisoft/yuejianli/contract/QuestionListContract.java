package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.MessageDto;
import com.kisoft.yuejianli.entity.QuestionListDto;

import retrofit2.Call;

/**
 * Created by tudo<PERSON> on 2018/6/21.
 */

public interface QuestionListContract {

    interface QuestionListViewContract{

        void toReferList(QuestionListDto dtoList);

        void finshRefresh();
    }

    interface QuestionListPresenterContract{

        void toReferList(String userId, String projectId, String pageSize, String page, String count, int type, int pageType);
    }

    interface QuestionListModelContract{

        Call<NetworkResponse<QuestionListDto>> toReferList(String userId, String projectId, String pageSize, String page, String count, String flag, int pageType);
    }
}
