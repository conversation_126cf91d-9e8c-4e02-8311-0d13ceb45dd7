package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;

import retrofit2.Call;

import com.kisoft.yuejianli.entity.ProjectSafeProblemInfo;

/**
 * Created by tudou on 2018/5/23.
 */

public interface ProjectSafeProblemCountMonthContract {

    interface ProjectSafeProblemCountMonthViewContract{
        void showHandelCount(String count);

        void showProblems(ProjectSafeProblemInfo info , int type);
    }

    interface ProjectSafeProblemCountMonthPresenterContract{
        void getHandelCount(String uid, String projectId, String month, String status);

        void getProblems(String uid, String projectId, String month, String status,String count, String pageSize, String page);
    }

    interface ProjectSafeProblemCountMonthModelContract{
        Call<NetworkResponse<String>> getHandelCount(String uid, String projectId, String month, String status);

        Call<NetworkResponse<ProjectSafeProblemInfo>> getProblems(String uid, String projectId, String month, String status,String count, String pageSize, String page);
    }
}
