package com.kisoft.yuejianli.contract;

import android.graphics.Bitmap;
import android.net.Uri;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.SideReport;

import retrofit2.Call;

/**
 * Created by tudou on 2018/4/12.
 */

public interface SideReportContract  {

    interface SideReportViewContract{
        void showReportCommitResult(boolean isOK);
    }


    interface SideReportPresenterContract{
        void uploadPhotoImage(String photoPath);

        void commitReport(SideReport report, String receiver,boolean isAdd);
    }

    interface SideReportMoidelContract{

        Call<NetworkResponse> commitReport(SideReport report , String receiver,boolean isAdd);

    }
}
