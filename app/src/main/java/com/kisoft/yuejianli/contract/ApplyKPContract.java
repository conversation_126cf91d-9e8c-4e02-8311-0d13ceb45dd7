package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ApplyKPInfo;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.ProcessListBean;

import java.util.List;

import retrofit2.Call;

public interface ApplyKPContract {

    interface ApplyKPViewContract {

        void applyBack(String str);

        void infoBack(ApplyKPInfo info);
    }
    interface ApplyKPPresenterContract {

        void submitApply(ApplyKPInfo info);

        void getInfo(ProcessListBean bean);

    }
    interface ApplyKPModelContract {

        Call<NetworkResponse<String>> submitApplyInfo(ApplyKPInfo info);

        Call<NetworkResponse<ApplyKPInfo>> getInfo(ProcessListBean bean);
    }
}
