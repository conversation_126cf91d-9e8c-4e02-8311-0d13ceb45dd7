package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.SuperNoticeInfos;

import retrofit2.Call;

/**
 * Created by tudo<PERSON> on 2018/5/15.
 */

public interface SuperNoticeMothContract {

    interface SuperNoticeMothViewContract{

        void showSuperNotices(SuperNoticeInfos infos, int type);

        void showHandelCount(String count);
    }

    interface SuperNoticeMothPresenterContract{

        void getAllSuperNotices(String uid, String projectId, String month, String count, String pageSize, String page);

        void getHandelCount(String uid, String projectId, String month);
    }

    interface SuperNoticeMothModelContract{

        Call<NetworkResponse<SuperNoticeInfos>> getAllSuperNotices(String uid, String projectId, String month,
                                                                   String count, String pageSize, String page);

        Call<NetworkResponse<String>> getHandelCount(String uid, String projectId, String month);
    }
}
