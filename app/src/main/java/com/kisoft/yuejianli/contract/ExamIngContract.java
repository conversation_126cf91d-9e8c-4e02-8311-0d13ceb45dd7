package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ExamDetailTest;
import com.kisoft.yuejianli.entity.ExamQuestionCommit;
import com.kisoft.yuejianli.entity.ExamQuestionInfo;
import com.kisoft.yuejianli.entity.ExamSubmitInfo;
import com.kisoft.yuejianli.entity.ExamSubmitResult;

import java.util.List;

import retrofit2.Call;

/**
 * 考试中
 */

public interface ExamIngContract {

    interface View {

        void showQuestionList(BaseList<ExamQuestionInfo> examUserInfoList, int type);
        void showQuestionList(List<ExamQuestionInfo> examUserInfoList);
        void showQuestionListError();
        void showExamTask(ExamDetailTest.ExamTaskBean examTaskBean);
        void submitExamPaperSuccess(ExamSubmitResult examSubmitResult);
        void saveFavoriteSuccess();
        void deleFavoriteSuccess();
        void finishRefresh();
        void showError();
    }


    interface Presenter {
        //获取试卷所属试题
        void getQuestionListByEpId(String epId,int count, int type);
        //获取考试记录
        void getDetailTest(String erId,String etId,int count, int type);
        //获取分类练习
        void getTypePractice(String eqtId,int count, int type);
        //获取顺序练习
        void getOrderPractice(String qustType,int count, int type);
        //获取每日一练
        void getDailyPractice(int count, int type);
        //获取随机练习
        void getRandomPractice(int count, int type);
        //获取错题本
        void getWrongbookList(int count, int type);
        //收藏题目
        void getFavoriteList();
        //	添加收藏
        void saveFavorite(String eqId);
        //	移除收藏
        void deleFavorite(String eqId);
        //	保存单个练习题
        void savePracticeRecord(ExamQuestionCommit questionCommit);
        //提交试题
        void submitExamPaper(ExamSubmitInfo examSubmitInfo);
    }


    interface Model {

        Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getQuestionListByEpId(String epId,int count, int page, int pageSize);

        Call<NetworkResponse<ExamDetailTest>> getDetailTest(String erId, String etId, int count, int page, int pageSize);

        Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getTypePractice(String eqtId,int count, int page, int pageSize);

        Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getOrderPractice(String qustType,int count, int page, int pageSize);

        Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getDailyPractice(int count, int page, int pageSize);

        Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getRandomPractice(int count, int page, int pageSize);

        Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getWrongbookList(int count, int page, int pageSize);

        Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getFavoriteList();

        Call<NetworkResponse<Object>> saveFavorite(String eqId);

        Call<NetworkResponse<Object>> deleFavorite(String eqId);

        Call<NetworkResponse<Object>> savePracticeRecord(ExamQuestionCommit questionCommit);

        Call<NetworkResponse<ExamSubmitResult>> submitExamPaper(ExamSubmitInfo examSubmitInfo);

    }
}
