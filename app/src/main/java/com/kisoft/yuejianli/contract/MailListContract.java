package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseView;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.MailInfo;

import retrofit2.Call;

/**
 * 邮件列表
 */

public interface MailListContract {

    interface View extends BaseView {

        void showInBoxList(BaseList<MailInfo> mailInfoBaseList, int type);
        void showOutBoxList(BaseList<MailInfo> mailInfoBaseList, int type);
        void finishRefresh();
        void showError();
    }


    interface Presenter{
        //收件箱
        void getInBoxList(String fsubjectNew,String fcreateNameNew,String fstateNew, int count, int type);
        //发件箱
        void listOutBox(String fsubjectNew,String freceiverNameNew, int count, int type);
    }


    interface Model{

        Call<NetworkResponse<BaseList<MailInfo>>> getInBoxList(String fsubjectNew,String fcreateNameNew,String fstateNew, int count, int page, int pageSize);

        Call<NetworkResponse<BaseList<MailInfo>>> listOutBox(String fsubjectNew, String freceiverNameNew, int count, int page, int pageSize);

    }
}
