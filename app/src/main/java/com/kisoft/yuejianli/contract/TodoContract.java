package com.kisoft.yuejianli.contract;


import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.TaskDTO;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudo<PERSON> on 2018/5/10.
 */

public interface TodoContract {

    interface TodoViewContract{

        //void showMessageTodo(List<MessageTodo> info);

        void showTaskToDoByUserId(List<TaskDTO> dtoList);


    }

    interface TodoPresenterContract{

        //void getTodoMessage(String uid, List<String> mtdIds);

        void getTaskToDoByUserId(String month, String userRoles, String pageSize, String page, String count);


    }


    interface TodoModelContract{

        //Call<NetworkResponse<List<MessageTodo>>> getTodoMessage(String uid, List<String> mtdIds);
        Call<NetworkResponse<List<TaskDTO>>> getTaskToDoByUserId(String month, String userRoles, String pageSize, String page, String count);
    }


}
