package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.Communication;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudou on 2018/7/3.
 */

public interface ProjectChoseContract {

    interface ProjectChoseViewContract{
        void projectsBack(List<Communication> communications);
        void modifyBack(Boolean success);
        void loginBack(boolean ok, UserInfo userInfo);
        void projectBack(ProjectInfo projectInfo);
    }

    interface ProjectChosePresenterContract {

        void getProjectOrgInfo(String projectId);

        void modifyFavoriteProject(String projectId, String userId);
        void login();
        void getProjectInfo(String projectId);
    }

    interface ProjectChoseModelContract {

        Call<NetworkResponse<List<Communication>>> getProjectOrgInfo(String projectId);

        Call<NetworkResponse<Boolean>> modifyFavoriteProject(String projectId, String userId);

        Call<NetworkResponse<UserInfo>> login();

        Call<NetworkResponse<ProjectInfo>> getProjectInfo(String projectId);
    }


}
