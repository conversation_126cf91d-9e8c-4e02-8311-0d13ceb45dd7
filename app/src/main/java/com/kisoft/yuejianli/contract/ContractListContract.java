package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;

import java.util.List;

import retrofit2.Call;

import com.kisoft.yuejianli.entity.ContractInfo;

/**
 * Created by tudou on 2018/6/7.
 */

public interface ContractListContract {

    interface ContractListViewContract{
        void showContracts(List<ContractInfo> infos);
    }

    interface ContractListPresnterContract{

        void getContracts(String uid, String projectId, String month, String contractGenre);
    }

    interface ContractListModelContract{
        Call<NetworkResponse<List<ContractInfo>>> getContracts(String uid, String projectId, String month, String contractGenre);
     }
}
