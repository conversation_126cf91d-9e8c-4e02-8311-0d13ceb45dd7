package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseView;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.PeEvaluateDetail;

import retrofit2.Call;

/**
 * 考核自评详情
 */

public interface PeEvaluateDetailContract {

    interface View extends BaseView {

        void showEvaluatedetailView(BaseList<PeEvaluateDetail> evaluateDetailBaseList);
        void saveEvaluatedetailView(boolean isOk);
        void showError();
    }


    interface Presenter{
        //考核自评
        void getEvaluatedetailView(String pdGuid,String ptGuid);
        //考核自评
        void saveEvaluatedetail(PeEvaluateDetail peEvaluateDetail);
    }


    interface Model{

        Call<NetworkResponse<BaseList<PeEvaluateDetail>>> getEvaluatedetailView(String pdGuid,String ptGuid);

        Call<NetworkResponse<Boolean>> saveEvaluatedetail(PeEvaluateDetail peEvaluateDetail);

    }
}
