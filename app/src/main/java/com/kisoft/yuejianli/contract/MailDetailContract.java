package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseView;
import com.kisoft.yuejianli.entity.MailInfoDetail;

import org.jsoup.Connection;

import retrofit2.Call;

/**
 * 邮件详情
 */

public interface MailDetailContract {

    interface View extends BaseView {

        void showMailDetail(MailInfoDetail mailInfoDetail);
        void transOk(MailInfoDetail mailInfoDetail);
        void sendMail(boolean isOk);
        void showError();
        void showProgress();
        void dismissProgress();
    }


    interface Presenter{
        //邮件明细
        void viewInBox(String fmailId);
        //去转发邮件
        void transImailInBox(String fmailId);
        //发邮件
        void sendMail(MailInfoDetail mailInfoDetail);
    }


    interface Model{

        Call<NetworkResponse<MailInfoDetail>> viewInBox(String fmailId);

        Call<NetworkResponse<MailInfoDetail>> transImailInBox(String fmailId);

        Call<NetworkResponse<Boolean>> sendMail(MailInfoDetail mailInfoDetail);

    }
}
