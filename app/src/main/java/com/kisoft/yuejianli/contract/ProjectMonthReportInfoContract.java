package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.MonthlySupervisionInfo;

import retrofit2.Call;

/**
 * Created by tudo<PERSON> on 2018/7/4.
 */

public interface ProjectMonthReportInfoContract {

    interface ProjectMonthReportInfoViewContrct{

        void showMonthReport(MonthlySupervisionInfo info, int type);

        void finshRefresh();
    }

    interface ProjectMonthReportInfoPresenterContract{

        void getMonthReport(String uid, String projectId, String count, String page, String pageSize);
    }

    interface ProjectMonthReportInfoModelContract{
        Call<NetworkResponse<MonthlySupervisionInfo>> getMonthReport(String uid, String projectId, String count, String page, String pageSize);
    }
}
