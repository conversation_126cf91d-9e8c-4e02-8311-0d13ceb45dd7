package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ProgTendDto;

import java.util.List;

import retrofit2.Call;

public interface ProjectProgressTrendContract {

    interface   ProjectProgressTrendViewContract {
        void progTendBack(List<ProgTendDto> list);
    }
    interface ProjectProgressTrendPresenterContract {
        void getProgTendByProjectId();
    }
    interface ProjectProgressTrendModelContract {

        Call<NetworkResponse<List<ProgTendDto>>> getProgTendByProjectId();
    }
}
