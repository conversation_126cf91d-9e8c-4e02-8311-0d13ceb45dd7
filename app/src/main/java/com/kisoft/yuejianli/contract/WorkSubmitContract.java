package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.UnSubmitInfo;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudo<PERSON> on 2018/6/23.
 */

public interface WorkSubmitContract {

    interface WorkSubmitViewContract{

        void showSubmitResult(Long workId, boolean isOk);

        void showUnSubmitInfo(List<UnSubmitInfo> info);
    }

    interface WorkSubmitPresenterContact{

        void SubmitWork(Long workId, UnSubmitInfo info);

        void getUnSubmitInfo();
    }


    interface WorkSubmitModelContract{

        Call<NetworkResponse> SubmitWork(UnSubmitInfo info);

        List<UnSubmitInfo> getUnSubmitInfo();
    }


}
