package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.CompleteCheckList;
import com.kisoft.yuejianli.entity.TakePictureInfoList;

import retrofit2.Call;

public interface TakePictureContract {
    interface TakePictureViewContract{

        void showTakePictureList(TakePictureInfoList infos, int type);
        void finshRefresh();
    }
    interface TakePicturePresenterContract{

        void getTakePictureList(String projectId, String count, String page, String pageSize,String uid,String userOrRelation,String title);
    }

    interface TakePictureModelContract{
        Call<NetworkResponse<TakePictureInfoList>> getTakePictureList
                (String projectId, String count, String page, String pageSize,String uid,String userOrRelation,String title);
    }
}
