package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ComPanyOrgInfo;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudou on 2018/7/23.
 */

public interface CompanyOrgZationContract  {


    interface CompanyOrgZationViewContract{

        void showOrg(List<ComPanyOrgInfo> infos);
    }


    interface CompanyOrgZationPresenterContract{

        void getOrg(String uid);
    }

    interface CompanyOrgZationModelContract{

        Call<NetworkResponse<List<ComPanyOrgInfo>>> getOrg(String uid);
    }
}
