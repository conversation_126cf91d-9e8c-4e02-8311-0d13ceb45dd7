package com.kisoft.yuejianli.contract;


import com.kisoft.yuejianli.api.NetworkResponse;

import retrofit2.Call;

import com.kisoft.yuejianli.entity.Communication;
import com.kisoft.yuejianli.entity.FaceData;
import com.kisoft.yuejianli.entity.LoginUrlBean;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;

import java.util.List;

/**
 * Created by tudou on 2018/3/10.
 */

public interface LoginContract {

    interface LoginViewContract{

        void companyUrlBack(LoginUrlBean loginUrlBean,boolean getFaceData);

        void showloginResult(boolean ok, UserInfo userInfo);

        void showloginResult(int code,String msg);

        void projectsBack(ProjectInfo projectInfo);

        void getFaceDataBack(FaceData faceData,boolean goFace);

        void saveMeidApplyBack(boolean success);
    }

    interface LoginPresenterContract{
        void getCompanyUrl(String companyCode,boolean getFaceData);

        void login(String name, String password, String registrationID,String faceId);

        void getProjectOrgInfo(String projectId);

        void getFaceData(boolean goFace);

        void saveMeidApply(String name,String pwd,String faceId);
    }

    interface LoginModelContract{

        Call<NetworkResponse<LoginUrlBean>> getCompanyUrl(String companyCode);

        Call<NetworkResponse<UserInfo>> login(String name, String password, String registrationID,String faceId);

        Call<NetworkResponse<ProjectInfo>> getProjectOrgInfo(String projectId);

        Call<NetworkResponse<FaceData>> getFaceData();

        Call<NetworkResponse<String>> saveMeidApply(String name,String pwd,String faceId);
    }


}
