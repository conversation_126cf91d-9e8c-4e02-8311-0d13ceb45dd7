package com.kisoft.yuejianli.contract;



import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.AccidentInfos;

import retrofit2.Call;

/**
 * Created by tudo<PERSON> on 2018/4/18.
 */

public interface ProjectQualityProblemContract {

    interface ProjectQualityProblemViewContract{
        void showQualityAccident(AccidentInfos infos, int type);
    }

    interface ProjectQualityProblemPresenterContract{
        void getQualityAccident(String uid,String projectId,String count,String pageSize,String page);
    }

    interface ProjectQualityProblemModelContract{
        Call<NetworkResponse<AccidentInfos>> getQualityAccident(String uid, String projectId ,
                                                                String count, String pageSize, String page);
    }
}
