package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ExamPaperInfo;

import retrofit2.Call;

/**
 * 模拟考试
 */

public interface ExamPracticeContract {

    interface View {

        void showPaperList(BaseList<ExamPaperInfo> paperInfoBaseList, int type);
        void finishRefresh();
        void showError();
    }


    interface Presenter {
        //试卷列表
        void getPaperList(int count, int type);
    }


    interface Model {

        Call<NetworkResponse<BaseList<ExamPaperInfo>>> getPaperList(int count, int page, int pageSize);

    }
}
