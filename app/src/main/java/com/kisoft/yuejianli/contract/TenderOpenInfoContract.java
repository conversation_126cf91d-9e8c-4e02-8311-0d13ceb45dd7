package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.TenderInfoInfo;

import retrofit2.Call;

/**
 * Created by tudou on 2018/7/16.
 */

public interface TenderOpenInfoContract  {

    interface TenderOpenInfoViewContract{

        void showTenderOpenInfo(TenderInfoInfo infos, int type);
    }

    interface TenderOpenInfoPresenterContract{

        void getTenderOpenInfo(String uid, String count, String page, String pageSize);
    }


    interface TenderOpenInfoModelContract{

        Call<NetworkResponse<TenderInfoInfo>> getTenderOpenInfo(String uid, String count, String page, String pageSize);
    }
}
