package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.TenderDeposit;
import com.kisoft.yuejianli.utils.StringUtil;

import retrofit2.Call;

/**
 * Created by tudou on 2018/7/6.
 */

public interface TenderDepositDetailContract {

    interface TenderDepositDetailViewContract{

        void showReturnInfo(TenderDeposit.DepositReturn depositReturn);
    }

    interface TenderDepositDetailPresenterContract{

        void getReturnInfo(String uid, String planId);
    }

    interface TenderDepositDetailModelContract{

        Call<NetworkResponse<TenderDeposit.DepositReturn>> getReturnInfo(String uid, String planId);
    }
}
