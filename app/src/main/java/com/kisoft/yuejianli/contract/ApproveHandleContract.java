package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.MaterialInspect;
import com.kisoft.yuejianli.entity.QualityAcceptance;
import com.kisoft.yuejianli.entity.QualityApprove;
import com.kisoft.yuejianli.entity.QualityInspection;
import com.kisoft.yuejianli.entity.QualityInvisibility;
import com.kisoft.yuejianli.entity.SideReport;
import com.kisoft.yuejianli.entity.SupervisionLog;

import retrofit2.Call;

/**
 * Created by tudou on 2018/4/24.
 */

public interface ApproveHandleContract {

    interface ApproveHandleViewContract {
        void showQualityAcceptance(QualityAcceptance acceptance);

        void showSideReport(SideReport sideReport);

        void showQualityInvisiblity(QualityInvisibility invisibility);

        void showSupervisionLog(SupervisionLog log);

        void showQualityInspection(QualityInspection inspection);

        void showMaterialInspect(MaterialInspect inspect);

        void showUpdateResulte(boolean isOk);

    }


    interface ApproveHandlePresenterContract {

        void getQualityAcceptance(String userId, String projectId, String businessId);

        void getSideReport(String userId, String projectId, String businessId);

        void getQualityInvisibility(String userId, String projectId, String businessId);

        void getSupervisionLog(String userId, String projectId, String businessId);

        void getQualityInspection(String userId, String projectId, String businessId);

        void getMaterialInspect(String userId, String projectId, String businessId);

        void updateApprove(String userId, String projectId, QualityApprove approve);
    }


    interface ApproveHandleModelContract {

        Call<NetworkResponse<QualityAcceptance>> getQualityAcceptance(String userId, String projectId,
                                                                      String businessId);

        Call<NetworkResponse<SideReport>> getSideReport(String userId, String projectId,
                                                               String businessId);

        Call<NetworkResponse<QualityInvisibility>> getQualityInvisibility(String userId, String projectId,
                                                                          String businessId);

        Call<NetworkResponse<SupervisionLog>> getSupervisionLog(String userId, String projectId,
                                                                   String businessId);

        Call<NetworkResponse<QualityInspection>> getQualityInspection(String userId, String projectId,
                                                                      String businessId);

        Call<NetworkResponse<MaterialInspect>> getMaterialInspect(String userId, String projectId,
                                                                    String businessId);

        Call<NetworkResponse<QualityApprove>> updateApprove(String userId, String projectId, QualityApprove approve);
    }
}
