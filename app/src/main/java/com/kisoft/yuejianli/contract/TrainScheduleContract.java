package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.TrainActivity;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/20.
 */

public interface TrainScheduleContract {

    interface TrainScheduleViewContract{

        void showTrains(List<TrainActivity> info);
    }

    interface TrainSchedulePresenterContract{

        void getTrains(String uid, String status );
    }

    interface TrainScheduleModelContract{

        Call<NetworkResponse<List<TrainActivity>>> getTrains(String uid, String status );
    }
}
