package com.kisoft.yuejianli.contract;

import android.telecom.Call;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.CustomerPleasedSurveyInfo;

/**
 * Created by tudou on 2018/6/19.
 */

public interface CustomerPleasedSurveyContract {

    interface CustomerPleasedSurveyViewContract{
        void showCustomerPleasedSurveys(CustomerPleasedSurveyInfo info, int type);
    }


    interface CustomerPleasedSurveyPresenterContract{
        void getCustomerPleaseedSurveys(String uid, String companyId, String count, String page, String pageSize);
    }

    interface CustomerPleasedSurveyModelContract{
        retrofit2.Call<NetworkResponse<CustomerPleasedSurveyInfo>> getCustomerPleaseedSurveys(String uid, String companyId, String count, String page, String pageSize);
    }
}
