package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ApplyLeaveInfo;
import com.kisoft.yuejianli.entity.ApplyType;

import java.util.List;

import retrofit2.Call;

/**
 * Created by yan<PERSON> on 2018/6/22.
 */

public interface ProcessLeaveContract {

    interface NoticeReplayViewContract{

        void leaveTypeBack(List<ApplyType> leaveTypes);

        void submitApplyLeaveBack(boolean success);
    }


    interface NoticeReplayPresenterContract{

        void getLeaveType();

        void submitApplyLeave(ApplyLeaveInfo applyLeaveInfo);

    }


    interface NoticeReplayModelContract{

        /**
         * 获取请假类型
         * @return
         */
        Call<NetworkResponse<List<ApplyType>>> getLeaveType();

        /**
         * 提交请假申请
         * @param applyLeaveInfo 请假申请信息
         * @return
         */
        Call<NetworkResponse<Boolean>> submitApplyLeave(ApplyLeaveInfo applyLeaveInfo);

    }
}
