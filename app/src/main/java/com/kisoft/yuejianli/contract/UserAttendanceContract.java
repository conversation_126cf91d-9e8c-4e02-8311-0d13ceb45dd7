package com.kisoft.yuejianli.contract;
import com.kisoft.yuejianli.api.NetworkResponse;

import java.util.List;

import retrofit2.Call;

import com.kisoft.yuejianli.entity.PunchCardInfo;


/**
 * Created by tudou on 2018/4/3.
 */

public interface UserAttendanceContract  {

    interface UserAttendanceViewContract{

        void showRecord(List<PunchCardInfo> infos);


    }

    interface UserAttendancePresenterContract{
        void getRecord(String uid, String date,String projectId);
    }

    interface UserAttendanceModelContract{
        Call<NetworkResponse<List<PunchCardInfo>>> getRecord(String uid, String date,String projectId);
    }
}
