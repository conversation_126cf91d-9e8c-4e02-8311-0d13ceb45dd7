package com.kisoft.yuejianli.contract;


import android.graphics.Bitmap;
import android.net.Uri;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ChangeDto;
import com.kisoft.yuejianli.entity.TPaymentCertifiDto;

import retrofit2.Call;

/**
 * Created by tudou on 2018/4/14.
 */

public interface AddInvestmentListContract {

    interface AddInvestmentListViewContract{
        void showCommitResult(boolean isOk);
    }

    interface AddInvestmentListPresenterContract{

        void uploadPhoto(String projectId, Bitmap photo, Uri uri);

        void commitData(TPaymentCertifiDto data, String createId);
    }


    interface AddInvestmentListModelContract{
        Call<NetworkResponse<Boolean>> commitData(TPaymentCertifiDto data, String createId);
    }
}
