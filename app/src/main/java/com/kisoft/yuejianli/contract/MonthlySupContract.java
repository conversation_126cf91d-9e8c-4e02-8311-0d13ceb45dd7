package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.MonthlySupervision;

import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/7/4.
 */

public interface MonthlySupContract {

    interface MonthlySupViewContract{

        void showCommitResult(String str);

        void showMonthCountInfo(String info);

        void checkMonthlySupBack(Boolean has);

        void monthlyInfoBack(MonthlySupervision info);

        void showUpdateResult(boolean isOk);

        void infobackMonthlySupervision(MonthlySupervision info);

        void EnclosureListBack(List<EnclosureListDto> dtoList);
    }

    interface MonthlySupPresenterContract{

        void commit(String uid, String projectId, MonthlySupervision supervision);

        void getMonthCountInfo(String uid, String projectId, String month);

        void checkMonthlySup(String projectId, String month);

        void getMonthlyInfo(String date, String userId,String projectId, String wfId);

        void updateMonthlyInfo(MonthlySupervision supervision);

        void getMonthlySupervision(String msId,String  wfTaskId,String  workflow_type,String  wfTaskState,String  businessId,String userId);

        void getEnclosureList(String businessId);
    }

    interface MonthlySupModelContract{

        Call<NetworkResponse<String>> commit(String uid, String projectId, MonthlySupervision supervision);

        Call<NetworkResponse<Map<String, Object>>> getMonthCountInfo(String uid, String projectId, String month);

        Call<NetworkResponse<Boolean>> checkMonthlySup(String projectId, String month);

        //通过wfId等查询条件获取要修改的监理月报详情信息
        Call<NetworkResponse<MonthlySupervision>> getMonthlyInfo(String date, String userId,String projectId, String wfId);

        //修改的监理月报详情信息
        Call<NetworkResponse<Boolean>> updateMonthlyInfo(MonthlySupervision supervision);

        //获取带流程的月报信息详情
        Call<NetworkResponse<MonthlySupervision>> getMonthlySupervision(String msId,String  wfTaskId,String  workflow_type,String  wfTaskState,String  businessId,String userId);

        //获取月报信息附件
        Call<NetworkResponse<List<EnclosureListDto>>> getEnclosureList(String businessId);
    }
}
