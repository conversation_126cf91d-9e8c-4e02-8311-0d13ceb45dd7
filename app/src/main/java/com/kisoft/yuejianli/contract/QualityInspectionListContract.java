package com.kisoft.yuejianli.contract;

import retrofit2.Call;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.QualityInspectionInfo;

/**
 * Created by tudou on 2018/5/28.
 */

public interface QualityInspectionListContract {

    interface QualityInspectionListViewContract{

        void showInspections(QualityInspectionInfo info ,int type);
    }

    interface QualityInspectionListPresenterContract{

        void getInspections(String uid, String projectId, String month , String count,
                            String pageSize, String page  ,String selectType);
    }


    interface QualityInspectionListModelConrtact{

        Call<NetworkResponse<QualityInspectionInfo>> getInspections(String uid, String projectId, String month , String count,
                                                                    String pageSize, String page ,String selectType);
    }
}
