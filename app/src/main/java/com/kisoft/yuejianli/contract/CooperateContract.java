package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ApproveInfos;
import com.kisoft.yuejianli.entity.ArcWorkDTO;
import com.kisoft.yuejianli.entity.ArcWorkDTO1;
import com.kisoft.yuejianli.entity.MailCount;
import com.kisoft.yuejianli.entity.MessageTodoReceive;
import com.kisoft.yuejianli.entity.ProjectcheckCount;
import com.kisoft.yuejianli.entity.TaskDTO;
import com.kisoft.yuejianli.entity.WorkFlowTaskInfo;
import com.kisoft.yuejianli.entity.httpresult.ProcessHttpResult;

import java.util.List;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/21.
 */

public interface CooperateContract {

    interface CooperateViewContract{

        void showTaskSendCount(Integer count);

        void showTaskTodoCount(Integer count);


        void showTodoNotice(List<MessageTodoReceive> list);

        void showUserSendCount(int count);

        void showToUserCount(int count);

        void showOfficelSealTodoCount(int count);

        void showMailCount(MailCount mailCount);

        void showProjectcheckCount(ProjectcheckCount projectcheckCount);

        void OnsiteAppCounts(int flag, String num);


        void showTaskToDoByUserId(List<TaskDTO> dtoList);
//        void showArcWorkList1(List<ArcWorkDTO> dtoList);
//void showArcWorkList2(List<ArcWorkDTO> dtoList);
        void showArcWorkListDaiBan(ArcWorkDTO1 dto);
        void showArcWorkListQiCao(ArcWorkDTO1 dto);
        void processListBack(ProcessHttpResult processListBeans);

        // 获取收文待办数量
        void processShouWenListBack(ProcessHttpResult processListBeans);

        // 获取待我查阅数量
        void getCarbonCopyCount(String count);
    }


    interface CooperatePresenterContract{
        void getTaskCount(String month, String userRoles);

        void getTodoNotice(String uid, String state);

        void getUserSendApproves(String projectId, String userId, String pageSize, String page, String count);

        void getToUserApproves(String projectId, String userId, String pageSize, String page, String count);

        void getOfficelSealTodoCount(String uid , String businessTypes);
        void getOnsiteAppCounts(String month, String userId, String projectId, int flag);



        void getTaskToDoByUserId(String month, String userRoles, String pageSize, String page, String count);
        void getArcWorkList(String month, String userId, String userRoles, String projectId, String pageSize, String page, String count);
//        void getArcWorkList1(String month, String userId, String userRoles, String projectId, String pageSize, String page, String count);



        void getProcessList(String workType);

        void getMailCount(String userId);

        void getProjectcheckCount(String userId);

        // 获取收文待办数量
        void getProcessShouWenCount(String workType);

        // 获取待我查阅数量
        void getCarbonCopyCount(String workType);
    }

    interface CooperateModelContract{

        Call<NetworkResponse<List<MessageTodoReceive>>> getTodoNotice(String uid, String state);

        Call<NetworkResponse<ApproveInfos>> getUserSendApproves(String projectId, String userId, String pageSize, String page, String count);

        Call<NetworkResponse<ApproveInfos>> getToUserApproves(String projectId, String userId, String pageSize, String page, String count);

        Call<NetworkResponse<WorkFlowTaskInfo>> getOfficelSealTodoCount(String uid , String businessTypes);

        Call<NetworkResponse<MailCount>> getMailCount(String userId);

        Call<NetworkResponse<ProjectcheckCount>> getProjectcheckCount(String userId);




        Call<NetworkResponse<List<TaskDTO>>> getTaskToDoByUserId(String month, String userRoles, String pageSize, String page, String count);
//      Call<NetworkResponse<ArcWorkDTO>> getArcWorkList1(String month, String userId, String userRoles, String projectId);
        Call<NetworkResponse<ArcWorkDTO1>> getArcWorkList1(String workType,String month, String userId, String userRoles, String projectId, String pageSize, String page, String count);

        Call<NetworkResponse<Integer>> getTaskSendCount(String month, String userRoles);
        Call<NetworkResponse<Integer>> getTaskTodoCount(String month, String userRoles);
        Call<NetworkResponse<String>> getOnsiteAppCounts(String month, String userId, String projectId, int flag);

        Call<NetworkResponse<List<ArcWorkDTO>>> getArcWorkList2(String userId);

        //类型（1=待办、2=已办、3=已发）
        Call<NetworkResponse<ProcessHttpResult>> getProcessList(String workType);

        // 获取收文待办数量
        public Call<NetworkResponse<ProcessHttpResult>> getProcessShouWenCount(String workType);

        // 获取待我查阅数量
        public Call<NetworkResponse<Object>> getCarbonCopyCount(String workType);
    }
}
