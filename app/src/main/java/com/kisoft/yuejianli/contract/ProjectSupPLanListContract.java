package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ProjectFilingSupPlanInfo;

import retrofit2.Call;

/**
 * Created by tudo<PERSON> on 2018/6/22.
 */

public interface ProjectSupPLanListContract {


    interface ProjectSupPLanListViewContract{

        void showSupPlans(ProjectFilingSupPlanInfo info, int type);
    }

    interface ProjectSupPLanListPresenterContract{

        void getSupPlans(String uid, String projectId, String mType, String count, String page, String pageSize);
    }


    interface ProjectSupPLanListModelContract{

        Call<NetworkResponse<ProjectFilingSupPlanInfo>> getSupPlans(String uid, String projectId, String mType, String count, String page, String pageSize);
    }
}
