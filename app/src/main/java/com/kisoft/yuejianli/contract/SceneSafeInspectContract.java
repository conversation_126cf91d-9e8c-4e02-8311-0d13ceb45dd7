package com.kisoft.yuejianli.contract;


import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ProjectSafeInspection;

import retrofit2.Call;

/**
 * Created by tudou on 2018/5/3.
 */

public interface SceneSafeInspectContract {


    interface SceneSafeInspectViewContract {
        void showCommitResulte(Boolean isOk);
    }


    interface SceneSafeInspectPresenterContract {

        void uploadPhotoImage(String photoPath);

        void commitSceneSafeInspect(String uid, String projectId, String receive,
                                    ProjectSafeInspection inspection,boolean isAdd);

    }


    interface SceneSafeInspectModelContract {


        Call<NetworkResponse<Boolean>> commitSceneSafeInspect(String uid, String projectId, String receive,
                                                              ProjectSafeInspection inspection,boolean isAdd);
    }
}
