package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ApplyLeaveInfo;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.ApproveArchivesInfo;
import com.kisoft.yuejianli.entity.ApproveInfo;
import com.kisoft.yuejianli.entity.JDData;

import java.util.List;

import retrofit2.Call;

public interface ApplySPContract {

    interface ApplySPViewContract{

        void processListBack(List<JDData> leaveTypes);

        void nextListBack(List<JDData> leaveTypes);

        void initWfBack(String back);

        void submitApplySPBack(String str);
    }


    interface ApplySPPresenterContract{

        void getProcessList(String businessType, String callBackName);

        void getNextList(String businessType, String callBackName, String wfTaskId);

        void initWf(String businessId, String wftId, String wfId, String wfName, String businessType, String callBackName);

        void submitApplySP(ApproveInfo info);

    }


    interface ApplySPModelContract{
        /**
         * 获取请假类型
         * @return
         */
        Call<NetworkResponse<List<JDData>>> getProcessList(String businessType, String callBackName);

        /**
         * 获取请假类型
         * @return
         */
        Call<NetworkResponse<List<JDData>>> getNextList(String businessType, String callBackName, String wfTaskId);

        Call<NetworkResponse<String>> initWf(String businessId, String wftId, String wfId, String wfName, String businessType, String callBackName);

        Call<NetworkResponse<String>> submitApplySP(ApproveInfo info);

    }
}
