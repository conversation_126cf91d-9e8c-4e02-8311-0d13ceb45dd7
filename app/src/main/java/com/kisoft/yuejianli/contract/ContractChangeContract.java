package com.kisoft.yuejianli.contract;


import android.graphics.Bitmap;
import android.net.Uri;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ChangeDto;
import com.kisoft.yuejianli.entity.QualityAcceptance;

import retrofit2.Call;

/**
 * Created by tudou on 2018/4/14.
 */

public interface ContractChangeContract {

    interface ContractChangeViewContract{

        void showCommitResult(boolean isOk);

    }

    interface ContractChangePresenterContract{


        void uploadPhoto(String projectId, Bitmap photo, Uri uri);

        void commitData(ChangeDto data, String createId);
    }


    interface ContractChangeModelContract{
        Call<NetworkResponse<Boolean>> commitData(ChangeDto data, String createId);
    }
}
