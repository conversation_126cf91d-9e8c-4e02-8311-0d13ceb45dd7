package com.kisoft.yuejianli.contract;


import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ProjectprobPieDto;

import java.util.List;

import retrofit2.Call;

/**
 * Created by yan<PERSON> on 2018/12/28.
 */

public interface Count2Contract {

    interface Count2ViewContract{
        void dataBack(String datas);
    }

    interface Count2PresenterContract{

        void getProjectProbChain(String projectId, String month, String flag);
    }


    interface Count2ModelContract{

        Call<NetworkResponse<String>> getProjectProbChain(String projectId, String month, String flag);
    }


}
