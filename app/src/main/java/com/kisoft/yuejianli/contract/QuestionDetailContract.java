package com.kisoft.yuejianli.contract;


import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.ArchivesDto;
import com.kisoft.yuejianli.entity.ArchivesDtos;
import com.kisoft.yuejianli.entity.ProjectApproverInfo;
import com.kisoft.yuejianli.entity.ProjectProbDto;
import com.kisoft.yuejianli.entity.ToArchivesBean;

import java.util.List;

import retrofit2.Call;

/**
 * Created by yan<PERSON> on 2018/12/28.
 */

public interface QuestionDetailContract {

    interface QuestionDetailViewContract{
        void typesBack(List<ApplyType> types);
        void unitProjectTypesBack(List<ApplyType> types);
        void submitBack(String back);

        void showUserIdsAndNamesByProjectId(List<ProjectApproverInfo> list);
    }

    interface QuestionDetailPresenterContract{
        void submitData(ProjectProbDto data, String userId, String userName);
        void getTypes();
        void getUnitProjectTypes();
        void uploadPhotoImage(String photoPath);

        void getUserIdsAndNamesByProjectId(String projectId);
    }


    interface QuestionDetailModelContract{
        Call<NetworkResponse<String>> submitData(ProjectProbDto data, String userId, String userName);
        Call<NetworkResponse<List<ApplyType>>> getTypes(String typeCode);

        Call<NetworkResponse<List<ProjectApproverInfo>>> getUserIdsAndNamesByProjectId(String projectId);
    }


}
