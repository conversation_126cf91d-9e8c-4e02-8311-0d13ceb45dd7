package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.data.AllData;
import com.kisoft.yuejianli.entity.ArchivesTransferInfo;
import com.kisoft.yuejianli.entity.ObjectItemInfo;
import com.kisoft.yuejianli.entity.ObjectItemInfoList;
import com.kisoft.yuejianli.entity.ProgrammeInfo;
import com.kisoft.yuejianli.entity.ProgrammeInfoList;
import com.kisoft.yuejianli.entity.TakePictureInfoList;

import java.util.List;

import retrofit2.Call;

public interface ProgrammeAddContract {
    interface ProgrammeAddViewContract{

        //获取日程开始时间和结束时间数组
        void showObjectItemInfoList(List<ObjectItemInfo.DataBean> infos);
        //void finshRefresh();

        //新增日程
        void showProgrammeAddResult(boolean isok);

        //获取日程信息
        void showProgrammeInfoBack(ProgrammeInfoList.DataBean info);


    }
    interface ProgrammeAddPresenterContract{


        //获取日程开始时间和结束时间数组
        void getObjectItemInfoList(String typeCode);

        //新增日程
        void addProgrammeAdd(ProgrammeInfo programmeInfo,Boolean isUpdata);

        //获取日程信息
        void getProgrammeInfo(String cldGuid);
    }

    interface ProgrammeAddModelContract{
        //获取日程开始时间和结束时间数组
        Call<NetworkResponse<List<ObjectItemInfo.DataBean>>> getObjectItemInfoList
                (String typeCode);

        //增加日程
        Call<NetworkResponse<Boolean>> addProgrammeAdd(ProgrammeInfo programmeInfo,Boolean isUpdata);


        //获取日程详细信息
        Call<NetworkResponse<ProgrammeInfoList.DataBean>> getProgrammeInfo(String cldGuid);
    }
}
