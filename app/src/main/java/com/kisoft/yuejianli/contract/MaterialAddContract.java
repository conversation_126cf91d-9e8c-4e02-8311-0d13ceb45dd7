package com.kisoft.yuejianli.contract;


import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.Material;

import retrofit2.Call;

/**
 * Created by tudou on 2018/4/8.
 */

public interface MaterialAddContract {

    interface MaterialAddViewContract{
       void showAddResult(boolean isok);
    }


    interface MaterialAddPresenterContract{

        void addMaterial(Material material);
    }

    interface MaterialAddModelContract{
        Call<NetworkResponse> addMaterial(Material material);
    }
}
