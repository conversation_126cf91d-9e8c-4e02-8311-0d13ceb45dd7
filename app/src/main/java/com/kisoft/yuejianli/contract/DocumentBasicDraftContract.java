package com.kisoft.yuejianli.contract;


import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ArchivesDto;
import com.kisoft.yuejianli.entity.ArchivesDtos;
import com.kisoft.yuejianli.entity.ToArchivesBean;

import retrofit2.Call;

/**
 * Created by ya<PERSON><PERSON> on 2018/12/28.
 */

public interface DocumentBasicDraftContract {

    interface DocumentBasicDraftViewContract{
        void showArchivesAdd(ToArchivesBean dto);
        void showSecAndUrg(ArchivesDtos dtos);
        void addArchives(String str);
    }

    interface DocumentBasicDraftPresenterContract{
        void toArchivesAdd(String temId, String fmoduleId);
        void getSecAndUrg();
        void addArchives(ArchivesDto dto);
    }


    interface DocumentBasicDraftModelContract{
        Call<NetworkResponse<ToArchivesBean>> toArchivesAdd(String temId, String fmoduleId);

        Call<NetworkResponse<ArchivesDtos>> getSecAndUrg();

        Call<NetworkResponse<String>> addArchives(ArchivesDto dto);
    }


}
