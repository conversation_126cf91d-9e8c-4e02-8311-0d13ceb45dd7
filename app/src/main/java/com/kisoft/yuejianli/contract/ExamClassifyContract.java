package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ExamQuestBank;

import retrofit2.Call;

/**
 * 试题分类
 */

public interface ExamClassifyContract {

    interface View {

        void showQuestbankList(BaseList<ExamQuestBank> questBankBaseList, int type);
        void finishRefresh();
        void showError();
    }


    interface Presenter {
        //题库列表
        void getQuestbankList(int count, int type);
    }


    interface Model {

        Call<NetworkResponse<BaseList<ExamQuestBank>>> getQuestbankList(int count, int page, int pageSize);

    }
}
