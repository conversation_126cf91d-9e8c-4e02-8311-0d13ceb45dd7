package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseView;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ProjectCheckInfo;

import retrofit2.Call;

/**
 * 工程检查记录列表
 */

public interface ProjectCheckRecordContract {

    interface View extends BaseView {

        void showProjectcheckList(BaseList<ProjectCheckInfo> projectCheckInfoBaseList, int type);
        void finishRefresh();
        void showError();
    }


    interface Presenter {
        //工程检查记录列表
        void getProjectcheckList(int count, int type);
        //问题跟踪列表
        void getTrackList(int count, int type);
        //问题复查列表
        void getFeedbackList(int count, int type);
    }


    interface Model {

        Call<NetworkResponse<BaseList<ProjectCheckInfo>>> getProjectcheckList(int count, int page, int pageSize);

        Call<NetworkResponse<BaseList<ProjectCheckInfo>>> getTrackList(int count, int page, int pageSize);

        Call<NetworkResponse<BaseList<ProjectCheckInfo>>> getFeedbackList(int count, int page, int pageSize);
    }
}
