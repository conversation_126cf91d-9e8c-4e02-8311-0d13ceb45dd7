package com.kisoft.yuejianli.contract;

import retrofit2.Call;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.PunchCardInfo;

import java.text.ParseException;

/**
 * Created by tudou on 2018/3/20.
 */

public interface PunchCardContract {


    interface PunchCardViewContract {

        void initPunchCardStatus(PunchCardInfo info) throws ParseException;

    }

    interface PunchCardPresenterContract {
        void getTodayPunchCardInfo(String uid, String time, String projectId);

        void punchStartCard(String uid, String time, PunchCardInfo info);


    }

    interface PunchCardModelContract {
        Call<NetworkResponse<PunchCardInfo>> getTodayPunchcardInfo(String uid, String time, String projectId);

        Call<NetworkResponse<PunchCardInfo>> punchStartCard(String uid, String time, PunchCardInfo info);

    }
}
