package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.CompleteCheckInfo;
import com.kisoft.yuejianli.entity.InstructionsInfo;

import java.util.List;

import retrofit2.Call;

public interface InstructionsInfoContract {
    interface InstructionsInfoViewContract{
        void typesBack(List<ApplyType> types);
        void showAddResult(boolean isok);
        void showInstructions(InstructionsInfo info);
        void showRepAddResult(boolean isok);

        /*void unitProjectTypesBack(List<ApplyType> types);
        void submitBack(String back);*/
    }

    interface InstructionsInfoPresenterContract{
        void getTypes();
        void addInstructions(InstructionsInfo instructionsInfo, String userId, String userName);
        void getInstructions(String siId);
        void uploadPhotoImage(String photoPath);
        void uploadPhotoImageRep(String photoPath);
        void addRepInstructions(InstructionsInfo instructionsInfo, String userId, String userName);
        /*void submitData(ProjectProbDto data, String userId, String userName);
        void getUnitProjectTypes();
        void uploadPhotoImage(String photoPath);*/
    }


    interface InstructionsInfoModelContract{
        Call<NetworkResponse<List<ApplyType>>> getTypes(String typeCode);
        Call<NetworkResponse<Boolean>> addInstructions(InstructionsInfo instructionsInfo, String userId, String userName);
        Call<NetworkResponse<InstructionsInfo>> getInstructions(String siId);
        Call<NetworkResponse<Boolean>> addRepInstructions(InstructionsInfo instructionsInfo, String userId, String userName);

        /*Call<NetworkResponse<String>> Instructions(ProjectProbDto data, String userId, String userName);*/

    }

}
