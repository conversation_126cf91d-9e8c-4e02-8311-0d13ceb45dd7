package com.kisoft.yuejianli.contract;


import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.TaskDTO;
import com.kisoft.yuejianli.entity.TaskTrackDto;
import com.kisoft.yuejianli.model.TaskRecordDto;

import java.util.List;

import retrofit2.Call;

/**
 * Created by yan<PERSON> on 2018/12/28.
 */

public interface TaskRecordContract {

    interface TaskRecordViewContract{

        void taskInfoBack(TaskDTO taskDTO);
        void showTaskTrackList(List<TaskRecordDto> dtoList);
        void showAddTaskTrack(Boolean result);
    }

    interface TaskRecordPresenterContract{

        void getTaskTrackList(String tskGuid);
        void addTaskTrack(TaskTrackDto dto);
        void getTaskInfoById(String taskId);
    }


    interface TaskRecordModelContract{

        Call<NetworkResponse<TaskDTO>> getTaskInfoById(String tskGuid);
        Call<NetworkResponse<List<TaskRecordDto>>> getTaskTrackList(String tskGuid);
        Call<NetworkResponse<Boolean>> addTaskTrack(TaskTrackDto dto);

    }


}
