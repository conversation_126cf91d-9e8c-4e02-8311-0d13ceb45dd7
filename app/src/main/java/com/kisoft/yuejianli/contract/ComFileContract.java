package com.kisoft.yuejianli.contract;


import com.kisoft.yuejianli.api.NetworkResponse;

import java.util.List;

import retrofit2.Call;

import com.kisoft.yuejianli.entity.ComFile;
import com.kisoft.yuejianli.entity.ComFileType;

/**
 * Created by tudou on 2018/6/5.
 */

public interface ComFileContract {

    interface ComFileViewContract{

        void showComFileType(List<ComFileType> comFileTypes);


        void showConFile(List<ComFile> files);
    }


    interface ComFilePresenterContract{


        void getComFileType(String userId);


        void getComFile(String userId);
    }


    interface ComFileModelContract{


        Call<NetworkResponse<List<ComFileType>>> getComFileType(String userId);


        Call<NetworkResponse<List<ComFile>>> getComFile(String userId);
    }
}
