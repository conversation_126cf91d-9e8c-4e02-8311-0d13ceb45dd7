package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.QualityReportDto;
import com.kisoft.yuejianli.entity.httpresult.ProcessHttpResult;

import java.util.List;

import retrofit2.Call;

public interface ArchProcessListContract {
    interface ArchProcessListViewContract{
        //返回的竣工档案移交or外部资料移交
        void archProcessListBack(List<ProcessListBean> processListBeans);

        //质量评估报告
        void qualityReportListBack(List<ProcessListBean> processListBeans);

        //返回的监理月报审批List
        void monthReporListBack(ProcessHttpResult processListBeans);
    }

    interface ArchProcessListPresenterContract{
        //竣工档案移交or外部资料移交
        void getArchProcessList(String workType,String transType,String count, String page, String pageSize);

        //质量评估报告
        void getqualityReportList(String workType,String transType,String count, String page, String pageSize);

        //监理月报审批List
        void getMonthReportList(String workType,String count, String page, String pageSize);
    }

    interface ArchProcessListModelContract{

        //竣工档案移交or外部资料移交List
        Call<NetworkResponse<List<ProcessListBean>>> getArchProcessList(String workType, String transType, String count, String page, String pageSize);

        //质量评估报告
        Call<NetworkResponse<QualityReportDto>> getqualityReportList(String workType, String transType, String count, String page, String pageSize);

        //监理月报审批List
        Call<NetworkResponse<ProcessHttpResult>> getMonthReportList(String workType, String count, String page, String pageSize);
    }
}
