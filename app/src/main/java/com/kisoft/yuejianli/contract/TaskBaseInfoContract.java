package com.kisoft.yuejianli.contract;


import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.TPwTasktype;
import com.kisoft.yuejianli.entity.TaskDTO;

import java.util.List;

import retrofit2.Call;

/**
 * Created by yan<PERSON> on 2018/12/28.
 */

public interface TaskBaseInfoContract {

    interface TaskBaseInfoViewContract{

        void showAddTask(TaskDTO info);
        void showTaskTypeList(List<TPwTasktype> taskTypeList);
        void taskInfoBack(TaskDTO taskDTO);
    }

    interface TaskBaseInfoPresenterContract{

        void addTask(TaskDTO dto);
        void getTaskTypeList();
        void getTaskInfoById(String taskId);
    }


    interface TaskBaseInfoModelContract{

        Call<NetworkResponse<TaskDTO>> addTask(TaskDTO dto);
        Call<NetworkResponse<List<TPwTasktype>>> getTaskTypeList();
        Call<NetworkResponse<TaskDTO>> getTaskInfoById(String taskId);
    }


}
