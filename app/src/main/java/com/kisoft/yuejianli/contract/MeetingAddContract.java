package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.AbstractConferenceDto;
import com.kisoft.yuejianli.entity.CompleteCheckInfo;

import retrofit2.Call;

public interface MeetingAddContract {
    interface MeetingAddViewContract{
        void showAddResult(boolean isok);
    }

    interface MeetingAddPresenterContract{
        void uploadPhotoImage(String photoPath);

        void addMeeting(AbstractConferenceDto.AbstractConferenceBean abstractConferenceBean);
    }


    interface MeetingAddModelContract{

        Call<NetworkResponse<Boolean>> addMeeting(AbstractConferenceDto.AbstractConferenceBean abstractConferenceBean);

    }

}
