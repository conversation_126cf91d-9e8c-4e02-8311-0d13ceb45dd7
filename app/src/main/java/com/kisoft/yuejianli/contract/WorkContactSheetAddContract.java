package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.WorkContactSheet;

import retrofit2.Call;

public interface WorkContactSheetAddContract {

    interface WorkContactSheetAddViewContract{
        void showSubmitResult(boolean isOk);
    }

    interface WorkContactSheetAddPresenterContract{
        void submitPlan(WorkContactSheet sheet);
    }

    interface WorkContactSheetAddModelContract{
        Call<NetworkResponse<Boolean>> submitPlan(WorkContactSheet sheet);
    }
}
