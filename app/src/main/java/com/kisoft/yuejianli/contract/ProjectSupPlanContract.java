package com.kisoft.yuejianli.contract;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.EnclosureListDto;

import java.util.List;

import retrofit2.Call;

public interface ProjectSupPlanContract {
    interface ProjectSupPlanViewContract{
        void EnclosureListBack(List<EnclosureListDto> dtoList);
    }

    interface ProjectSupPlanPresenterContract{
        void getEnclosureList(String id);
    }

    interface ProjectSupPlanModelContract{
        Call<NetworkResponse<List<EnclosureListDto>>> getEnclosureList(String id);
    }
}
