package com.kisoft.yuejianli.contract;


import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.entity.ApproveArchivesInfo;
import com.kisoft.yuejianli.entity.ApproveInfo;
import com.kisoft.yuejianli.entity.JDData;

import java.util.List;

import retrofit2.Call;

/**
 * Created by yan<PERSON> on 2018/12/28.
 */

public interface DocumentApprovalContract {

    interface DocumentApprovalViewContract{

        void toWfExecute(ApproveArchivesInfo approveArchivesInfo);

        void sendWfBack(ApproveArchivesInfo backInfo);

        void assignNodeListBack(List<JDData> jdDataList);

        void assignNodeListBack1(List<JDData> jdDataList);
    }

    interface DocumentApprovalPresenterContract{

        void toWfExecute(String arcId,String wfTaskId);

        void sendWf(ApproveInfo submitInfo);

        void getAssignNodeList(String wfTaskId);

        void getAssignNodeList1(String wfTaskId);
    }


    interface DocumentApprovalModelContract{

        Call<NetworkResponse<ApproveArchivesInfo>> toWfExecute(String arcId, String wfTaskId);

        Call<NetworkResponse<ApproveArchivesInfo>> sendWf(ApproveInfo submitInfo);

        Call<NetworkResponse<List<JDData>>> getAssignNodeList(String wfTaskId);

        Call<NetworkResponse<List<JDData>>> getAssignNodeList1(String wfTaskId);
    }


}
