package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.TakePictureContract;
import com.kisoft.yuejianli.entity.TakePictureInfoList;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;


public class TakePictureModel extends BaseModel implements TakePictureContract.TakePictureModelContract{
    public TakePictureModel(Context context) {
        super(context);
    }
    @Override
    public Call<NetworkResponse<TakePictureInfoList>>
    getTakePictureList(String projectId, String count, String page, String pageSize, String uid, String userOrRelation, String title) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("projectId", projectId);
        pramaras.put("count" ,count);
        pramaras.put("page" ,page);
        pramaras.put("pageSize" ,pageSize);
        pramaras.put("userId" ,uid);
        pramaras.put("userOrRelation", userOrRelation);
        pramaras.put("title", title);
        return Api.getGbkApiserver().getTakePictureList(Constant.HTTP_GET_SHOOTFREELIST ,pramaras);
    }
}
