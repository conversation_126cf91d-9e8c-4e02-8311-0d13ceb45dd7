package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.OfficialSealApplyInfoContract;
import com.kisoft.yuejianli.entity.OfficialSealApply;
import com.kisoft.yuejianli.entity.WorkFlowTask;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/7/26.
 */

public class OfficialSealApplyInfoModel extends BaseModel implements OfficialSealApplyInfoContract.
        OfficialSealApplyInfoModelContract {

    public OfficialSealApplyInfoModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<OfficialSealApply>> getApplyInfo(String uid, String id) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId" , uid);
        pramaras.put("siId", id);
        return Api.getGbkApiserver().getOfficialApplyInfo(Constant.HTTP_GET_OFFICIAL_SEAL_APPLY_INFO ,pramaras);
    }

    @Override
    public Call<NetworkResponse<Boolean>> updateWorkTask(String uid, String taskId, String remark, String opinion) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId" , uid);
        pramaras.put("taskId", taskId);
        pramaras.put("opinion", opinion);
        pramaras.put("remark", remark);
        return Api.getGbkApiserver().updateWorkTask(Constant.HTTP_UPDATE_WORK_TASK ,pramaras);
    }

    @Override
    public Call<NetworkResponse<List<WorkFlowTask>>> getWorkTaskList(String uid, String wfId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId" , uid);
        pramaras.put("wfId", wfId);
        return Api.getGbkApiserver().getWorkTaskList(Constant.HTTP_GET_WORK_TASK_LIST ,pramaras);
    }
}
