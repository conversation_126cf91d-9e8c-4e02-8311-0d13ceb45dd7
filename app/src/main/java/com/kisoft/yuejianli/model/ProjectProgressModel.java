package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.entity.ProjectProcessProgress;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectProgressContract;
import com.kisoft.yuejianli.entity.ProjectProgress;

/**
 * Created by tudou on 2018/5/22.
 */

public class ProjectProgressModel extends BaseModel implements ProjectProgressContract.ProjectProgressModelConctract {

    public ProjectProgressModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<String>> getProjectProgress(String uid, String projectId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId", uid);
        pramaras.put("projectId", projectId);

        return Api.getGbkApiserver().getProjectPogreessPlan(Constant.HTTP_GET_PROJECT_PROGRESS, pramaras);
    }
}
