package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ExamOnlineContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ExamUserInfo;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;


public class ExamOnlineModel extends BaseModel implements ExamOnlineContract.Model {

    public ExamOnlineModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<BaseList<ExamUserInfo>>> getMyExamTaskList(int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
//        parameters.put("userId", "150121165340445289b7a9fe868ce32d");
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
        parameters.put("pageSize" ,String.valueOf(pageSize));
        return Api.getGbkApiserver().getMyExamTaskList(Constant.HTTP_GET_EXAM_TASK_LIST ,parameters);
    }
}
