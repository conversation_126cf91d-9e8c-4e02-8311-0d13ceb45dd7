package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.TenderUnitListContract;
import com.kisoft.yuejianli.entity.TenderUnitDto;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/5/28.
 */
public class TenderUnitListModel extends BaseModel implements TenderUnitListContract.TenderUnitListModelConrtact {

    public TenderUnitListModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<TenderUnitDto>> getTenderUnitList(String comName, String count, String pageSize, final String page) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("comName", StringUtil.isEmpty(comName) ? "" : comName);
        pramaras.put("count", count);
        pramaras.put("pageSize", pageSize);
        pramaras.put("page", page);
        return Api.getGbkApiserver().getTenderUnitList(Constant.HTTP_GET_TENDER_UNIT_LIST, pramaras);
    }
    @Override
    public Call<NetworkResponse<String>> saveInvCompany(String company) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("comName", company);
        pramaras.put("userId", SettingManager.getInstance().getUserInfo().getId());
        pramaras.put("userName", SettingManager.getInstance().getUserInfo().getName());
        return Api.getGbkApiserver().getSupNoticeCount(Constant.HTTP_GET_SAVE_INVCOMPANY, pramaras);
    }
}
