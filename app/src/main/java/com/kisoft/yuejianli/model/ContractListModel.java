package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ContractListContract;
import com.kisoft.yuejianli.entity.ContractInfo;
import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/6/7.
 */

public class ContractListModel extends BaseModel implements ContractListContract.ContractListModelContract {

    public ContractListModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<ContractInfo>>> getContracts(String uid, String projectId, String month , String contractGenre) {

        Map<String, Object> pramars = new HashMap<>();
        pramars.put("userId" , uid);
        pramars.put("projectId" , projectId);
        if(!StringUtil.isEmpty(month)){
            pramars.put("month" , month);
        }
        pramars.put("contractGenre" ,contractGenre);

        return Api.getGbkApiserver().getProjectContracts(Constant.HTTP_GET_CONTRACT_LIST, pramars);
    }
}
