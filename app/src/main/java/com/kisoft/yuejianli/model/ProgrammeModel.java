package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProgrammeContract;
import com.kisoft.yuejianli.entity.ProgrammeInfo;
import com.kisoft.yuejianli.entity.ProgrammeInfoList;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

public class ProgrammeModel extends BaseModel implements ProgrammeContract.ProgrammeModelContract{
    public ProgrammeModel(Context context) {
        super(context);
    }
    @Override
    public Call<NetworkResponse<List<ProgrammeInfoList.DataBean>>> getCompleteCheckList(String userId, String currDate) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId", userId);
        pramaras.put("currDate" ,currDate);
        return Api.getGbkApiserver().getProgrammeList(Constant.HTTP_GET_CURRCALENDAR ,pramaras);
    }
}
