package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ExamClassifyContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ExamQuestBank;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;


public class ExamClassifyModel extends BaseModel implements ExamClassifyContract.Model {

    public ExamClassifyModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<BaseList<ExamQuestBank>>> getQuestbankList(int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
        parameters.put("pageSize" ,String.valueOf(pageSize));
        return Api.getGbkApiserver().getQuestbankList(Constant.HTTP_GET_QUEST_BANK_LIST ,parameters);
    }
}
