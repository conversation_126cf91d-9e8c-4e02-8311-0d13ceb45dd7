package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProgrammeAddContract;
import com.kisoft.yuejianli.entity.ObjectItemInfo;
import com.kisoft.yuejianli.entity.ObjectItemInfoList;
import com.kisoft.yuejianli.entity.ProgrammeInfo;
import com.kisoft.yuejianli.entity.ProgrammeInfoList;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

public class ProgrammeAddModel extends BaseModel implements ProgrammeAddContract.ProgrammeAddModelContract {

    public ProgrammeAddModel(Context context) {
        super(context);
    }
    @Override
    public Call<NetworkResponse<List<ObjectItemInfo.DataBean>>> getObjectItemInfoList(String typeCode) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("typeCode", typeCode);
        return Api.getGbkApiserver().getObjectItemInfoList(Constant.HTTP_GET_OBJECTITEMLIST ,pramaras);
    }

    @Override
    public Call<NetworkResponse<Boolean>> addProgrammeAdd(ProgrammeInfo programmeInfo,Boolean isUpdate) {
        //添加日程
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("data" , StringUtil.objectToJson(programmeInfo));
        if(isUpdate){//更新
            return Api.getGbkApiserver().addProgrammeAdd(Constant.HTTP_UPDATE_PROGRAMME, pramaras);
        }else{//增加
            return Api.getGbkApiserver().addProgrammeAdd(Constant.HTTP_ADD_PROGRAMME, pramaras);
        }
    }

    @Override
    public Call<NetworkResponse<ProgrammeInfoList.DataBean>> getProgrammeInfo(String cldGuid) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("cldGuid" , cldGuid);
        return Api.getGbkApiserver().getProgrammeInfo(Constant.HTTP_GET_CALENDAR_INFO,pramaras);
    }
}