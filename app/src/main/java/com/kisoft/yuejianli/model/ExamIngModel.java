package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ExamIngContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ExamDetailTest;
import com.kisoft.yuejianli.entity.ExamQuestionCommit;
import com.kisoft.yuejianli.entity.ExamQuestionInfo;
import com.kisoft.yuejianli.entity.ExamSubmitInfo;
import com.kisoft.yuejianli.entity.ExamSubmitResult;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;


public class ExamIngModel extends BaseModel implements ExamIngContract.Model {

    public ExamIngModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getQuestionListByEpId(String epId, int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("epId", epId);
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
//        parameters.put("pageSize" ,String.valueOf(pageSize));
        return Api.getGbkApiserver().getQuestionList(Constant.HTTP_GET_EXAM_QUESTION_LIST_BY_ID ,parameters);
    }

    @Override
    public Call<NetworkResponse<ExamDetailTest>> getDetailTest(String erId, String etId, int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("erId", erId);
        parameters.put("etId", etId);
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
//        parameters.put("pageSize" ,String.valueOf(pageSize));
        return Api.getGbkApiserver().getExamDetailTest(Constant.HTTP_GET_EXAM_QUESTION_LIST_DETAIL_TEST ,parameters);
    }

    @Override
    public Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getTypePractice(String eqtId, int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("eqtId", eqtId);
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
//        parameters.put("pageSize" ,String.valueOf(pageSize));
        return Api.getGbkApiserver().getQuestionList(Constant.HTTP_GET_EXAM_QUESTION_LIST_TYPE_PRACTICE ,parameters);
    }

    @Override
    public Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getOrderPractice(String qustType, int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
//        parameters.put("qustType", qustType);
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
//        parameters.put("pageSize" ,String.valueOf(pageSize));
        return Api.getGbkApiserver().getQuestionList(Constant.HTTP_GET_EXAM_QUESTION_LIST_ORDER_PRACTICE ,parameters);
    }

    @Override
    public Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getDailyPractice(int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
//        parameters.put("pageSize" ,String.valueOf(pageSize));
        return Api.getGbkApiserver().getQuestionList(Constant.HTTP_GET_EXAM_QUESTION_LIST_DAILY_PRACTICE ,parameters);
    }

    @Override
    public Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getRandomPractice(int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
//        parameters.put("pageSize" ,String.valueOf(pageSize));
        return Api.getGbkApiserver().getQuestionList(Constant.HTTP_GET_EXAM_QUESTION_LIST_RANDOM_PRACTICE ,parameters);
    }

    @Override
    public Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getWrongbookList(int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
//        parameters.put("pageSize" ,String.valueOf(pageSize));
        return Api.getGbkApiserver().getQuestionList(Constant.HTTP_GET_EXAM_QUESTION_LIST_WRONG_BOOK ,parameters);
    }

    @Override
    public Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getFavoriteList() {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        return Api.getGbkApiserver().getQuestionList(Constant.HTTP_GET_FAVORITE_LIST ,parameters);
    }

    @Override
    public Call<NetworkResponse<Object>> saveFavorite(String eqId) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("eqId", eqId);
        parameters.put("userId", SettingManager.getInstance().getUserId());
        if(SettingManager.getInstance().getUserInfo()!=null){
            parameters.put("userName", SettingManager.getInstance().getUserInfo().getName());
        }
        return Api.getGbkApiserver().objectApi(Constant.HTTP_GET_SAVE_FAVORITE_QUESTION ,parameters);
    }

    @Override
    public Call<NetworkResponse<Object>> deleFavorite(String eqId) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("eqId", eqId);
        parameters.put("userId", SettingManager.getInstance().getUserId());
        if(SettingManager.getInstance().getUserInfo()!=null){
            parameters.put("userName", SettingManager.getInstance().getUserInfo().getName());
        }
        return Api.getGbkApiserver().objectApi(Constant.HTTP_GET_DELE_FAVORITE_QUESTION ,parameters);
    }

    @Override
    public Call<NetworkResponse<Object>> savePracticeRecord(ExamQuestionCommit questionCommit) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("data", StringUtil.objectToJson(questionCommit));
        return Api.getGbkApiserver().objectApi(Constant.HTTP_SAVE_PRACTICE_RECORD ,parameters);
    }

    @Override
    public Call<NetworkResponse<ExamSubmitResult>> submitExamPaper(ExamSubmitInfo examSubmitInfo) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("data", StringUtil.objectToJson(examSubmitInfo));
        return Api.getGbkApiserver().submitExamPaper(Constant.HTTP_GET_EXAM_PAPER_SUBMIT ,parameters);
    }
    public Call<Object> submitExamPaperObject(ExamSubmitInfo examSubmitInfo) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("data", StringUtil.objectToJson(examSubmitInfo));
        return Api.getGbkApiserver().submitExamPaperObject(Constant.HTTP_GET_EXAM_PAPER_SUBMIT ,parameters);
    }
}
