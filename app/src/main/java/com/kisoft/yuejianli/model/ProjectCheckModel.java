package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectCheckContract;
import com.kisoft.yuejianli.entity.ProjectApproverInfo;
import com.kisoft.yuejianli.entity.ProjectCheckInfo;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/5/3.
 */

public class ProjectCheckModel extends BaseModel implements ProjectCheckContract.Model {

    public ProjectCheckModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<ProjectCheckInfo>> getProjectcheckById(String pcId) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("pcId" , pcId);
        return Api.getGbkApiserver().getProjectcheck(Constant.HTTP_GET_PROJECT_CHECK ,parameters);
    }

    @Override
    public Call<NetworkResponse<Boolean>> saveProjectcheck(ProjectCheckInfo info) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("data" , StringUtil.objectToJson(info));
        return Api.getGbkApiserver().commitInfo(Constant.HTTP_SAVE_PROJECT_CHECK ,parameters);
    }

    @Override
    public Call<NetworkResponse<Boolean>> updateTrack(ProjectCheckInfo info) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("data" , StringUtil.objectToJson(info));
        return Api.getGbkApiserver().commitInfo(Constant.HTTP_UPDATE_TRACK ,parameters);
    }

    @Override
    public Call<NetworkResponse<Boolean>> updateFeedback(ProjectCheckInfo info) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("data" , StringUtil.objectToJson(info));
        return Api.getGbkApiserver().commitInfo(Constant.HTTP_UPDATE_FEEDBACK ,parameters);
    }

    @Override
    public Call<NetworkResponse<List<ProjectApproverInfo>>> getUserIdsAndNamesByProjectId(String projectId) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("projectId" , projectId);
        return Api.getGbkApiserver().getUserIdsAndNamesByProjectId(Constant.HTTP_GET_USERIDS_AND_NAMES_BY_PROJECTID,parameters);
    }
}
