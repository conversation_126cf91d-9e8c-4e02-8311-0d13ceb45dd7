package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.WitnessSamplesInspectContract;
import com.kisoft.yuejianli.entity.ProjectApproverInfo;
import com.kisoft.yuejianli.entity.WitnessSamplesInspectInfo;
import com.kisoft.yuejianli.utils.StringUtil;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/5/3.
 */

public class WitnessSamplesInspectModel extends BaseModel implements WitnessSamplesInspectContract.Model {

    public WitnessSamplesInspectModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<Boolean>> commitWitnessSamplesInspect(WitnessSamplesInspectInfo inspection,boolean isAdd) {
        Map<String,Object> parameters = new HashMap<>();
        //parameters.put("data" , StringUtil.objectToJson(inspection));
        try {
            parameters.put("data" , URLEncoder.encode(StringUtil.objectToJson(inspection),"UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        if (isAdd){
            return Api.getGbkApiserver().commitWitnessSamplesInspect(Constant.HTTP_COMMIT_WITNESS_SAMPLES_INSPECT ,parameters);
        }else {
            return Api.getGbkApiserver().commitWitnessSamplesInspect(Constant.HTTP_UPDATE_WITNESS_SAMPLES_INSPECT ,parameters);
        }

    }

    @Override
    public Call<NetworkResponse<WitnessSamplesInspectInfo>> getWitnessSamplesInspect(String wsId) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("wsId" , wsId);
        return Api.getGbkApiserver().getWitnessSamplesInspect(Constant.HTTP_GET_WITNESS_SAMPLES_INSPECT ,parameters);
    }

    @Override
    public Call<NetworkResponse<List<ProjectApproverInfo>>> getUserIdsAndNamesByProjectId(String projectId) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("projectId" , projectId);
        return Api.getGbkApiserver().getUserIdsAndNamesByProjectId(Constant.HTTP_GET_USERIDS_AND_NAMES_BY_PROJECTID,parameters);
    }
}
