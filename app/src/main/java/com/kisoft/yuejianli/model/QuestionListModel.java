package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.QuestionListContract;
import com.kisoft.yuejianli.entity.QuestionListDto;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/21.
 */

public class QuestionListModel extends BaseModel implements QuestionListContract.QuestionListModelContract {

    public QuestionListModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<QuestionListDto>> toReferList(String userId, String projectId, String pageSize, String page, String count, String flag, int pageType) {

        Map<String, Object> paramars = new HashMap<>();
        paramars.put("userId", userId);
        paramars.put("projectId", projectId);
        paramars.put("pageSize", pageSize);
        paramars.put("page", page);
        paramars.put("count", count);
        // pageType;   // 页面类型 0全部     1质量     2安全 //3看板
        if (pageType == 3){
            paramars.put("month",DateUtil.dateToString(new Date(), DateUtil.YM));
        }else {
            paramars.put("flag", flag);
        }
        switch (pageType) {
            case 1:
            case 2:
                paramars.put("probType", pageType + "");
                break;
        }
        return Api.getGbkApiserver().toReferQuestList(Constant.HTTP_GET_QUESTION_LIST, paramars);
    }
}
