package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ApplyConContract;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.ConInfo;
import com.kisoft.yuejianli.entity.ProcessListBean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

public class ApplyConModel extends BaseModel implements ApplyConContract.Model {
    public ApplyConModel(Context context) {
        super(context);
    }

    //实施方式:typeCode="108.120"
    //合同类别:typeCode="006.002"
    //业务性质:typeCode="108.205"
    //获取方式:typeCode="108.206"
    //资金来源:typeCode="108.207"
    //履约金形式:typeCode="108.286"

    @Override
    public Call<NetworkResponse<String>> submitApplyInfo(ConInfo info) {
        return Api.getGbkApiserver().submitApply1(Constant.HTTP_SUBMIT_APPLY_CON_INFO, info.getParameters());
    }

    @Override
    public Call<NetworkResponse<ConInfo>> getInfo(ProcessListBean bean) {
        return Api.getGbkApiserver().getConInfoDetail(Constant.HTTP_GET_CON_INFO, bean.getParameters());
    }

    @Override
    public Call<NetworkResponse<List<ApplyType>>> getConClassList() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("typeCode" , "108.120");
        return Api.getGbkApiserver().getLeaveType(Constant.HTTP_GET_LEAVE_TYPE, parameters);
    }

    @Override
    public Call<NetworkResponse<List<ApplyType>>> getConTypeList() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("typeCode" , "006.002");
        return Api.getGbkApiserver().getLeaveType(Constant.HTTP_GET_LEAVE_TYPE, parameters);
    }

    @Override
    public Call<NetworkResponse<List<ApplyType>>> getBusiNatureList() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("typeCode" , "108.205");
        return Api.getGbkApiserver().getLeaveType(Constant.HTTP_GET_LEAVE_TYPE, parameters);
    }

    @Override
    public Call<NetworkResponse<List<ApplyType>>> getAcquiWayList() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("typeCode" , "108.206");
        return Api.getGbkApiserver().getLeaveType(Constant.HTTP_GET_LEAVE_TYPE, parameters);
    }

    @Override
    public Call<NetworkResponse<List<ApplyType>>> getSourceFondsList() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("typeCode" , "108.207");
        return Api.getGbkApiserver().getLeaveType(Constant.HTTP_GET_LEAVE_TYPE, parameters);
    }

    @Override
    public Call<NetworkResponse<List<ApplyType>>> getBondTypeList() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("typeCode" , "108.286");
        return Api.getGbkApiserver().getLeaveType(Constant.HTTP_GET_LEAVE_TYPE, parameters);
    }
}
