package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

import com.kisoft.yuejianli.contract.SuperNoticeAnswerConstract;
import com.kisoft.yuejianli.entity.SuperNoticeAnswer;
import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/5/18.
 */

public class SuperNoticeAnswerModel extends BaseModel implements SuperNoticeAnswerConstract.SuperNoticeAnswerModelConstract {

    public SuperNoticeAnswerModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<SuperNoticeAnswer>> getAnserInfo(String uid, String projectId, String answerType, String businessId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId" , uid);
        pramaras.put("projectId", projectId);
        pramaras.put("mType" ,answerType);
        pramaras.put("snId",businessId);
        return Api.getGbkApiserver().getSuperNoticeAnswerById(Constant.HTTP_GET_SUP_NOTICE_ANSWER_INFO, pramaras);
    }

    @Override
    public Call<NetworkResponse<Boolean>> submitAnswer(String uid, String projectId, SuperNoticeAnswer answer) {

        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId" , uid);
        pramaras.put("projectId", projectId);
        pramaras.put("data" , StringUtil.objectToJson(answer));
        return Api.getGbkApiserver().addSuperNoticeAnswer(Constant.HTTP_ADD_SUP_NOTICE_ANSWER,pramaras);
    }
}
