package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ExamMaterialContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ExamLearnFile;
import com.kisoft.yuejianli.entity.FileSource;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;


public class ExamMaterialModel extends BaseModel implements ExamMaterialContract.Model {

    public ExamMaterialModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<BaseList<FileSource>>> getExamFolderAndFileList(String folderGuid, int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("folderGuid" ,folderGuid);
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
        parameters.put("pageSize" ,String.valueOf(pageSize));
        return Api.getGbkApiserver().getExamFolderAndFileList(Constant.HTTP_GET_EXAM_FOLDER_FILE_LIST ,parameters);
    }

    @Override
    public Call<NetworkResponse<ExamLearnFile>> getFilePath(String id, String name) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("fileTypeFlag", "F");
        parameters.put("fileName", name);
        parameters.put("fileGuid", id);
        return Api.getGbkApiserver().getExamLearnFile(Constant.HTTP_GET_EXAM_PATH ,parameters);
    }
}
