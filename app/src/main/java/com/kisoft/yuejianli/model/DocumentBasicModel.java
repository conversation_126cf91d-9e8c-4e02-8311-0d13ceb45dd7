package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.DocumentBasicContract;
import com.kisoft.yuejianli.entity.ArchivesDto;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by yan<PERSON> on 2018/1/5.
 */

public class DocumentBasicModel extends BaseModel implements DocumentBasicContract.DocumentBasicModelContract {

    public DocumentBasicModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<ArchivesDto>> toArchivesHandel(String arcId, String wfTaskId, String wfTaskState, String businessId, String type) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("arcId", arcId);
        if (!StringUtil.isEmpty(wfTaskId))
            pramaras.put("wfTaskId", wfTaskId);
        pramaras.put("wfTaskState", wfTaskState);
        pramaras.put("businessId", businessId);
        String api = Constant.HTTP_TO_ARCHIVES_HANDEL;
        switch (type) {
            case "2":
            case "3":
                api = Constant.HTTP_TO_ARCHIVES_HANDEL1;
                break;
        }
        return Api.getGbkApiserver().toArchivesHandel(api, pramaras);
    }
}
