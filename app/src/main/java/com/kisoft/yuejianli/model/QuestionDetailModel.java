package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.QuestionDetailContract;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.ProjectApproverInfo;
import com.kisoft.yuejianli.entity.ProjectProbDto;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

public class QuestionDetailModel extends BaseModel implements QuestionDetailContract.QuestionDetailModelContract{
    public QuestionDetailModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<String>> submitData(ProjectProbDto data, String userId, String userName) {
        Map<String, Object> pramaras = new HashMap();
        pramaras.put("data", StringUtil.objectToJson(data));
        pramaras.put("userId", userId);
        pramaras.put("userName", userName);
        return Api.getGbkApiserver().getAllHandelAccidentCount(Constant.HTTP_SUBMIT_QUESTION_DATA, pramaras);
    }

    @Override
    public Call<NetworkResponse<List<ApplyType>>> getTypes(String typeCode) {
        Map<String, Object> pramaras = new HashMap();
        pramaras.put("typeCode", typeCode);
        return Api.getGbkApiserver().getLeaveType(Constant.HTTP_GET_QUIT_TYPE, pramaras);
    }

    @Override
    public Call<NetworkResponse<List<ProjectApproverInfo>>> getUserIdsAndNamesByProjectId(String projectId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("projectId" , projectId);
        return Api.getGbkApiserver().getUserIdsAndNamesByProjectId(Constant.HTTP_GET_USERIDS_AND_NAMES_BY_PROJECTID,pramaras);
    }
}
