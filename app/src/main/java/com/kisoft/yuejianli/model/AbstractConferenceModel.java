package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.AbstractConferenceContract;
import com.kisoft.yuejianli.entity.AbstractConferenceDto;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

public class AbstractConferenceModel extends BaseModel implements AbstractConferenceContract.AbstractConferenceModelContract {

    public AbstractConferenceModel(Context context) {
        super(context);
    }


    @Override
    public Call<NetworkResponse<AbstractConferenceDto>> toReferList(String projectId, String pageSize, String page, String count) {
        Map<String, Object> paramars = new HashMap<>();
        paramars.put("projectId" , projectId);
        paramars.put("pageSize" , pageSize);
        paramars.put("page" , page);
        paramars.put("count" , count);
        return Api.getGbkApiserver().toReferAbstractConferenceList(Constant.HTTP_GET_ABSTRACT_CONFERENCE_LIST, paramars);
    }
}
