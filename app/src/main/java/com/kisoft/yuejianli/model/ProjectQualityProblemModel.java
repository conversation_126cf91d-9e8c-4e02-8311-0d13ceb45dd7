package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectQualityProblemContract;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

import com.kisoft.yuejianli.entity.AccidentInfos;

/**
 * Created by tudou on 2018/4/26.
 */

public class ProjectQualityProblemModel extends BaseModel implements ProjectQualityProblemContract.ProjectQualityProblemModelContract {

    public ProjectQualityProblemModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<AccidentInfos>> getQualityAccident(String uid, String projectId,
                                                                   String count, String pageSize, String page) {
        Map<String,Object> pramaras = new HashMap<>();
        pramaras.put("userId" ,uid);
        pramaras.put("projectId", projectId);
        pramaras.put("count", count);
        pramaras.put("pageSize" ,pageSize);
        pramaras.put("page" ,page);
        pramaras.put("handel" ,"0");
        return Api.getGbkApiserver().getQualityAccidents(Constant.HTTP_GET_QUALITY_ACCIDENT,pramaras);
    }
}
