package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectProgressGatteContract;
import com.kisoft.yuejianli.entity.GatteEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/5/22.
 */

public class ProjectProgressGatteModel extends BaseModel implements ProjectProgressGatteContract.ProjectProgressGatteModelConctract {

    public ProjectProgressGatteModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<GatteEntity>>> getGatteData(String month, String projectId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("month", month);
        pramaras.put("projectId", projectId);

        return Api.getGbkApiserver().getProjectGatteChart(Constant.HTTP_GET_PROJECT_GATTE_CHART, pramaras);
    }
}
