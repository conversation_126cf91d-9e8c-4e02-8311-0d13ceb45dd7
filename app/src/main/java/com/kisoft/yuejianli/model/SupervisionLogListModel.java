package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.entity.SupervisionLogInfo;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.SupervisionLogListContract;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/5/28.
 */

public class SupervisionLogListModel extends BaseModel implements SupervisionLogListContract.
        SupervisionLogListModelContract {

    public SupervisionLogListModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<SupervisionLogInfo>> getSupLogList(String uid, String projectId, String month, String count, String pageSize, String page) {
        Map<String, Object> pramaras = new HashMap<>();

        pramaras.put("userId", uid);
        pramaras.put("projectId" , projectId);
        if(!StringUtil.isEmpty(month)){
            pramaras.put("month",month);
        }
        pramaras.put("count", count);
        pramaras.put("pageSize", pageSize);
        pramaras.put("page" ,page);

        return Api.getGbkApiserver().getSuperVisionLogList(Constant.HTTP_GET_SUPERVISION_LOG_LIST,pramaras);
    }

    @Override
    public Call<NetworkResponse<String>> checkSupLog(String uid, String projectId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("createId", uid);
        pramaras.put("projectId" , projectId);
        pramaras.put("createTime" , DateUtil.dateToString(new Date(), DateUtil.YMD));

        return Api.getGbkApiserver().getAllHandelAccidentCount(Constant.HTTP_GET_CHECK_SUPLOG,pramaras);
    }
}
