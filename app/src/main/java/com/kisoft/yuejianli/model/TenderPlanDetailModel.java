package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.TenderPlanDetailContract;
import com.kisoft.yuejianli.entity.TenderPeopleInfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/7/16.
 */

public class TenderPlanDetailModel extends BaseModel implements TenderPlanDetailContract.TenderPlanDetailModelContract {

    public TenderPlanDetailModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<TenderPeopleInfo>>> getTenderEmps(String uid, String ipId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId", uid);
        pramaras.put("ipId" , ipId);
        return Api.getGbkApiserver().getTenderEmpInfos(Constant.HTTP_GET_TENDER_EMP_INFO, pramaras);
    }
}
