package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ApproveToUserContract;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

import com.kisoft.yuejianli.entity.ApproveInfos;

/**
 * Created by tudou on 2018/4/23.
 */

public class ApproveToUserModel extends BaseModel implements ApproveToUserContract.ApproveToUserModelContract {

    public ApproveToUserModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<ApproveInfos>> getUserApproves(String projectId, String userId, String pageSize, String page, String count) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId" , userId );
        pramaras.put("projectId" ,projectId );
        pramaras.put("pageSize" , pageSize);
        pramaras.put("page" , page);
        pramaras.put("count" ,count);
        pramaras.put("workflowState" ,"" );
        pramaras.put("approveType" , "2");   //送审的

        return Api.getGbkApiserver().getUserApproves(Constant.HTTP_GET_APPROVES, pramaras);
    }
}
