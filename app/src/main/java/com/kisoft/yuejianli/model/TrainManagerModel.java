package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.TrainManagerContract;
import com.kisoft.yuejianli.entity.TrainActivityInfo;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/20.
 */

public class TrainManagerModel extends BaseModel implements TrainManagerContract.TrainManagerModelContract{

    public TrainManagerModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<TrainActivityInfo>> getTrains(String uid, String status, String count, String page, String pageSize) {

        Map<String,Object> pramaras = new HashMap<>();
        pramaras.put("userId" , uid);
        pramaras.put("mType" , status);
        pramaras.put("count" , count);
        pramaras.put("page" , page);
        pramaras.put("pageSize" , pageSize);

        return Api.getGbkApiserver().getTrainManagerInfo(Constant.HTTP_GET_TRAIN_ACTIVITY_INFO, pramaras);
    }
}
