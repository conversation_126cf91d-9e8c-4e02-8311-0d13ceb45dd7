package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.SupervisionNoticeContract;
import com.kisoft.yuejianli.entity.SurpervisionNotice;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/5/8.
 */

public class SupervisionNoticeModel extends BaseModel implements
        SupervisionNoticeContract.SupervisionNoticeModelContract {

    public SupervisionNoticeModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<Boolean>> commitSupNotice(String uid, String projectId, String approveId,
                                                          SurpervisionNotice notice) {
        Map<String,Object> pramaras = new HashMap<>();
        pramaras.put("userId" , uid);
        pramaras.put("projectId", projectId);
        pramaras.put("approveId", approveId);
        pramaras.put("data", StringUtil.objectToJson(notice));
        return Api.getGbkApiserver().addSupNotice(Constant.HTTP_ADD_SUP_NOTICE, pramaras);
    }
}
