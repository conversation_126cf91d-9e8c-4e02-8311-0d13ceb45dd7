package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.contract.PunchCardMapContract;

/**
 * Author     : yanlu
 * Date       : 2018/12/13 17:17
 */

public class AttendanceRulesModel extends BaseModel{

    private String latAndLong; //经纬度
    private String effectRange; //有效范围
    private String beginTimeStr;  //上班时间
    private String endTimeStr;   //下班时间
    private String workFlexTimes; //上班弹性时间
    private String lateTimes; //严重迟到
    private String absentTimes; //旷工
    private String fieldState;   //允许外勤打卡
    private String scheduleWay;        //排班方式   // 0:固定四次班制 1:自由打卡 2：固定两次班制
    private String workDay;         //工作日
    private String userTel;         //手机端联系电话
    private String userPotoPath;    //用户图像路径
    private String beginTimeHou;    //上午上班时间小时
    private String beginTimeMin;    //上午上班时间分钟
    private String endTimeHou;    //上午下班时间小时
    private String endTimeMin;    //上午下班时间分钟
    private String beginTimeHou1;    //下午上班时间小时
    private String beginTimeMin1;    //下午上班时间分钟
    private String endTimeHou1;    //下午下班时间小时
    private String endTimeMin1;    //下午下班时间分钟

    public AttendanceRulesModel(Context context) {
        super(context);
    }

    public String getLatAndLong() {
        return latAndLong;
    }

    public void setLatAndLong(String latAndLong) {
        this.latAndLong = latAndLong;
    }

    public String getEffectRange() {
        return effectRange;
    }

    public void setEffectRange(String effectRange) {
        this.effectRange = effectRange;
    }

    public String getBeginTimeStr() {
        return beginTimeStr;
    }

    public void setBeginTimeStr(String beginTimeStr) {
        this.beginTimeStr = beginTimeStr;
    }

    public String getEndTimeStr() {
        return endTimeStr;
    }

    public void setEndTimeStr(String endTimeStr) {
        this.endTimeStr = endTimeStr;
    }

    public String getWorkFlexTimes() {
        return workFlexTimes;
    }

    public void setWorkFlexTimes(String workFlexTimes) {
        this.workFlexTimes = workFlexTimes;
    }

    public String getLateTimes() {
        return lateTimes;
    }

    public void setLateTimes(String lateTimes) {
        this.lateTimes = lateTimes;
    }

    public String getAbsentTimes() {
        return absentTimes;
    }

    public void setAbsentTimes(String absentTimes) {
        this.absentTimes = absentTimes;
    }

    public String getFieldState() {
        return fieldState;
    }

    public void setFieldState(String fieldState) {
        this.fieldState = fieldState;
    }

    public String getScheduleWay() {
        return scheduleWay;
    }

    public void setScheduleWay(String scheduleWay) {
        this.scheduleWay = scheduleWay;
    }

    public String getWorkDay() {
        return workDay;
    }

    public void setWorkDay(String workDay) {
        this.workDay = workDay;
    }

    public String getUserTel() {
        return userTel;
    }

    public void setUserTel(String userTel) {
        this.userTel = userTel;
    }

    public String getUserPotoPath() {
        return userPotoPath;
    }

    public void setUserPotoPath(String userPotoPath) {
        this.userPotoPath = userPotoPath;
    }

    public String getBeginTimeHou() {
        return beginTimeHou;
    }

    public void setBeginTimeHou(String beginTimeHou) {
        this.beginTimeHou = beginTimeHou;
    }

    public String getBeginTimeMin() {
        return beginTimeMin;
    }

    public void setBeginTimeMin(String beginTimeMin) {
        this.beginTimeMin = beginTimeMin;
    }

    public String getEndTimeHou() {
        return endTimeHou;
    }

    public void setEndTimeHou(String endTimeHou) {
        this.endTimeHou = endTimeHou;
    }

    public String getEndTimeMin() {
        return endTimeMin;
    }

    public void setEndTimeMin(String endTimeMin) {
        this.endTimeMin = endTimeMin;
    }

    public String getBeginTimeHou1() {
        return beginTimeHou1;
    }

    public void setBeginTimeHou1(String beginTimeHou1) {
        this.beginTimeHou1 = beginTimeHou1;
    }

    public String getBeginTimeMin1() {
        return beginTimeMin1;
    }

    public void setBeginTimeMin1(String beginTimeMin1) {
        this.beginTimeMin1 = beginTimeMin1;
    }

    public String getEndTimeHou1() {
        return endTimeHou1;
    }

    public void setEndTimeHou1(String endTimeHou1) {
        this.endTimeHou1 = endTimeHou1;
    }

    public String getEndTimeMin1() {
        return endTimeMin1;
    }

    public void setEndTimeMin1(String endTimeMin1) {
        this.endTimeMin1 = endTimeMin1;
    }
}
