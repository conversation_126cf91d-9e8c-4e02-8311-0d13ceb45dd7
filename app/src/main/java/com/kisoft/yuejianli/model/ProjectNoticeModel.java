package com.kisoft.yuejianli.model;

import android.content.Context;
import android.util.Log;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectNoticeContract;
import com.kisoft.yuejianli.entity.NoticeInfo;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by yan<PERSON> on 2018/12/25.
 */

public class ProjectNoticeModel extends BaseModel implements ProjectNoticeContract.ProjectNoticeModelContract {

    public ProjectNoticeModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<NoticeInfo>> getSupNoticeList(String type,String userId,String snStatus, String month, String projectID, String pageSize, String page, String count) {
        Map<String, Object> pramaras = new HashMap<>();

        //String snStatus; //通知单状态；(""=所有通知单；0=草稿；1=已提交；2=未审批；3=未回复；4=已完成（已回复）；5=已审批；
        //String month; //当前月份
        //String projectID; //当前项目Id
        //String pageSize;
        //String page;
        //String count;
        pramaras.put("userId" , userId);
        pramaras.put("snStatus", snStatus);
        pramaras.put("projectId", projectID);
        pramaras.put("month" , month);
        pramaras.put("count" , "0");
        pramaras.put("page" , "1");
        pramaras.put("pageSize" , "10");
        return Api.getGbkApiserver().getSupNoticeList(type ,pramaras);
    }


    @Override
    public Call<NetworkResponse<String>> getSupNoticeCount1(String month, String projectID, String userId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId" , userId);
        pramaras.put("snStatus", "1");
        pramaras.put("projectId", projectID);
        pramaras.put("month" , month);
        return Api.getGbkApiserver().getSupNoticeCount(Constant.HTTP_GET_SUPNOTICE_COUNT,pramaras);
    }

    @Override
    public Call<NetworkResponse<String>> getSupNoticeCount2(String month, String projectID, String userId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId" , userId);
        pramaras.put("snStatus", "2");
        pramaras.put("projectId", projectID);
        pramaras.put("month" , month);
        return Api.getGbkApiserver().getSupNoticeCount(Constant.HTTP_GET_SUPNOTICE_COUNT,pramaras);
    }


    @Override
    public Call<NetworkResponse<String>> getSupNoticeCount3(String month, String projectID, String userId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId" , userId);
        pramaras.put("snStatus", "3");
        pramaras.put("projectId", projectID);
        pramaras.put("month" , month);
        return Api.getGbkApiserver().getSupNoticeCount(Constant.HTTP_GET_SUPNOTICE_COUNT,pramaras);
    }
}
