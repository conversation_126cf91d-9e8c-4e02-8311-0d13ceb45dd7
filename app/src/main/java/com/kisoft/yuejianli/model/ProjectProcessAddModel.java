package com.kisoft.yuejianli.model;

import android.content.Context;
import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectProcessAddContract;
import com.kisoft.yuejianli.entity.ProjectProcess;
import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/5/21.
 */

public class ProjectProcessAddModel extends BaseModel implements ProjectProcessAddContract.
        ProjectProcessAddModelContract {

    public ProjectProcessAddModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<Boolean>> submitProcess(String uid, String projectId, ProjectProcess projectProcess) {

        Map<String ,Object> pramaras = new HashMap<>();
        pramaras.put("userId", uid);
        pramaras.put("projectId", projectId);
        pramaras.put("data", StringUtil.objectToJson(projectProcess));
        return Api.getGbkApiserver().addProjectProcess(Constant.HTTP_ADD_PROJECT_PROCESS, pramaras);
    }
}
