package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.MainActiivtyContract;
import com.kisoft.yuejianli.entity.Communication;
import com.kisoft.yuejianli.entity.ProjectWsInfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

import com.kisoft.yuejianli.entity.ProjectInfo;

/**
 * Created by tudou on 2018/3/22.
 */

public class MainActivityModel extends BaseModel implements MainActiivtyContract.MainActivityModelContract {

    public MainActivityModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<ProjectInfo>>> getAllProjects(String uid, String orgId,
                                                                   String orgRalation) {
        Map<String, Object> paramars = new HashMap<>();
        paramars.put("userId", uid);
        paramars.put("orgId", orgId);
        paramars.put("userOrgRelation",orgRalation);

        return Api.getGbkApiserver().getAllProjects(Constant.HTTP_GET_PROJECTS, paramars);
    }

    @Override
    public Call<NetworkResponse<List<Communication>>> getProjectOrgInfo(String projectId) {
        Map<String,Object> pramaras = new HashMap<>();
        pramaras.put("projectid" , projectId);
        return Api.getGbkApiserver().getUserLinkman(Constant.HTTP_GET_PROJECT_USERINFO,pramaras);
    }

    @Override
    public Call<NetworkResponse<List<ProjectWsInfo>>> getProjectWsInfo(String userId, String projectId) {

       Map<String, Object> pramaras = new HashMap<>();
       pramaras.put("userId" , userId);
       pramaras.put("projectId" ,projectId);
        return Api.getGbkApiserver().getProjectWsInfo(Constant.HTTP_GET_PROJECT_WORKERSPACE,  pramaras);
    }
}
