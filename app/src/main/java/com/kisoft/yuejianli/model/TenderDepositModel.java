package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.TenderDepositContract;
import com.kisoft.yuejianli.entity.TenderDeposit;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/7/6.
 */

public class TenderDepositModel extends BaseModel implements TenderDepositContract.TenderDepositModelContract {

    public TenderDepositModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<TenderDeposit>> getDeposit(String uid, String count, String page, String pageSize) {
        Map<String,Object> pramaras = new HashMap<>();
        pramaras.put("userId", uid);
        pramaras.put("count" ,count);
        pramaras.put("page" , page);
        pramaras.put("pageSize" ,pageSize);
        return Api.getGbkApiserver().getTenderDepositInfo(Constant.HTTP_GET_TENDER_DEPOSIT, pramaras);
    }
}
