package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ExamMainContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ExamQuestionInfo;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;


public class ExamMainModel extends BaseModel implements ExamMainContract.Model {

    public ExamMainModel(Context context) {
        super(context);
    }


    @Override
    public Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getWrongbookList(int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
//        parameters.put("pageSize" ,String.valueOf(pageSize));
        return Api.getGbkApiserver().getQuestionList(Constant.HTTP_GET_EXAM_QUESTION_LIST_WRONG_BOOK ,parameters);
    }

    @Override
    public Call<NetworkResponse<BaseList<ExamQuestionInfo>>> getFavoriteList() {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        return Api.getGbkApiserver().getQuestionList(Constant.HTTP_GET_FAVORITE_LIST ,parameters);
    }
}
