package com.kisoft.yuejianli.model;

import android.content.Context;
import android.util.Log;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ArchProcessListContract;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.QualityReportDto;
import com.kisoft.yuejianli.entity.httpresult.ProcessHttpResult;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

public class ArchProcessListModel extends BaseModel implements ArchProcessListContract.ArchProcessListModelContract {
    public ArchProcessListModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<ProcessListBean>>> getArchProcessList(String workType, String transType,
                                                                           String count, String page, String pageSize) {
        //workType类型（1=待办、2=已办、3=我发起的）
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("workType", workType);
        pramaras.put("userId", SettingManager.getInstance().getUserInfo().getId());
        pramaras.put("userRoles", SettingManager.getInstance().getUserInfo().getUserOrgRelation());
//        pramaras.put("userRoles" , userRoles);
        if (transType.equals("0") || transType.equals("1")) {
            pramaras.put("businessType", "t_sys_projecttrans");//竣工档案移交or外部资料移交
            pramaras.put("transType", transType);
        } else if (transType.equals("2")) {
            pramaras.put("businessType", "T_QUALITY_ASSESSMENT");//质量评估报告
        } else {
            pramaras.put("businessType", "t_monthly_supervision");//监理月报
            pramaras.put("count", count);
            pramaras.put("page", page);
            pramaras.put("pageSize", pageSize);
        }

        Log.d("参数", pramaras.toString());
        return Api.getGbkApiserver().getArchivesList(Constant.HTTP_GET_PROCESS_LIST1, pramaras);
    }

    @Override
    public Call<NetworkResponse<QualityReportDto>> getqualityReportList(String workType, String transType,
                                                                        String count, String page,
                                                                        String pageSize) {
        //workType类型（1=待办、2=已办、3=我发起的）
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("workType", workType);
        pramaras.put("userId", SettingManager.getInstance().getUserInfo().getId());
        pramaras.put("userRoles", SettingManager.getInstance().getUserInfo().getUserOrgRelation());

        pramaras.put("businessType", "T_QUALITY_ASSESSMENT");//质量评估报告

        Log.d("参数", pramaras.toString());
        return Api.getGbkApiserver().getQualityList(Constant.HTTP_GET_PROCESS_LIST1, pramaras);
    }

    @Override
    public Call<NetworkResponse<ProcessHttpResult>> getMonthReportList(String workType, String count, String page,
                                                                       String pageSize) {
        //workType类型（1=待办、2=已办、3=我发起的）
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("workType", workType);
        pramaras.put("userId", SettingManager.getInstance().getUserInfo().getId());
        pramaras.put("businessType", "t_monthly_supervision");//监理月报
        pramaras.put("count", count);
        pramaras.put("page", page);
        pramaras.put("pageSize", pageSize);
        return Api.getGbkApiserver().getMonthReportList(Constant.HTTP_GET_PROCESS_LIST1, pramaras);
    }
}
