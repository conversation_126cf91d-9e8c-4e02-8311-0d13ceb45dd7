package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.AnnNoticeContract;
import com.kisoft.yuejianli.entity.InfoIssueDto;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/21.
 */

public class AnnNoticeModel extends BaseModel implements AnnNoticeContract.AnnNoticeModelContract {

    public AnnNoticeModel(Context context) {
        super(context);
    }


    @Override
    public Call<NetworkResponse<InfoIssueDto>> toReferList(String month, String userId, String fsystemId, String userRoles, String pageSize, String page, String count) {
        Map<String, Object> paramars = new HashMap<>();
        paramars.put("fsystemId", fsystemId);

//        paramars.put("month", month);
        paramars.put("userId" , userId);
        paramars.put("userRoles" , userRoles);
        paramars.put("pageSize" , pageSize);
        paramars.put("page" , page);
        paramars.put("count" , count);
        return Api.getGbkApiserver().toReferList(Constant.HTTP_TO_REFER_LIST, paramars);
    }
}
