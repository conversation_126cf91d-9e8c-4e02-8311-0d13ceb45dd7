package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.InvestmentListContract;
import com.kisoft.yuejianli.entity.ContractInfo;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/7.
 */
public class InvestmentListModel extends BaseModel implements InvestmentListContract.InvestmentListModelContract {

    public InvestmentListModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<ContractInfo>>> getContracts(String uid, String projectId) {
        Map<String, Object> pramars = new HashMap<>();
        pramars.put("userId" , uid);
        pramars.put("projectId" , projectId);

        return Api.getGbkApiserver().getProjectContracts(Constant.HTTP_GET_INVEST_LIST, pramars);
    }
}
