package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.OnsideListContract;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

import com.kisoft.yuejianli.entity.QualityOnsideInfo;

/**
 * Created by tudou on 2018/5/28.
 */

public class OnsideListModel extends BaseModel implements OnsideListContract.OnsideListModelContract {

    public OnsideListModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<QualityOnsideInfo>> getOnsideInfo(String uid, String projectId, String month, String count, String pageSize, String page,String selectType) {
        Map<String, Object> pramaras = new HashMap<>();
        if (!StringUtil.isEmpty(selectType)){
            pramaras.put("selectType", selectType);
        }
        pramaras.put("userId", uid);
        pramaras.put("projectId" , projectId);
        if(!StringUtil.isEmpty(month)){
            pramaras.put("month",month);
        }
        pramaras.put("count", count);
        pramaras.put("pageSize", pageSize);
        pramaras.put("page" ,page);

        return Api.getGbkApiserver().getOnsideList(Constant.HTTP_GET_ONSIDE_LIST,pramaras);
    }
}
