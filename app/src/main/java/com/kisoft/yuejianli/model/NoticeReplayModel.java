package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.NoticeReplayContract;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by yanlu on 2018/12/25.
 */

public class NoticeReplayModel extends BaseModel implements NoticeReplayContract.NoticeReplayModelContract {

    public NoticeReplayModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<Boolean>> addSupervisionRepply(String snId, String snNumber1, String snOpinion) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("snId" , snId);
        pramaras.put("snNumber1", snNumber1);
        pramaras.put("snOpinion", snOpinion);
        return Api.getGbkApiserver().addSupervisionRepply(Constant.HTTP_ADD_SUPERVISION_REPPLY,pramaras);
    }
}
