package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectProgressTrendContract;
import com.kisoft.yuejianli.entity.ProgTendDto;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by Administrator on 2019/4/11 0011.
 */
public class ProjectProgressTrendModel extends BaseModel implements ProjectProgressTrendContract.ProjectProgressTrendModelContract {

    public ProjectProgressTrendModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<ProgTendDto>>> getProgTendByProjectId() {
        Map<String, Object> pramaras = new HashMap();
        pramaras.put("projectId", SettingManager.getInstance().getProject().getProjectId());
        return Api.getGbkApiserver().getProgTendByProjectId(Constant.HTTP_GET_PROGTEND, pramaras);
    }
}
