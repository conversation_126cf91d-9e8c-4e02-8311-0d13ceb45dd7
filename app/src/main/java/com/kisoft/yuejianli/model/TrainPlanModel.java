package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.TrainPlanContract;
import com.kisoft.yuejianli.entity.TrainInvitation;
import com.kisoft.yuejianli.entity.TrainInvitationInfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/20.
 */

public class TrainPlanModel extends BaseModel implements TrainPlanContract.TrainPlanModelContract {

    public TrainPlanModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<TrainInvitationInfo>>  getTainPlan(String uid ,String count, String page, String pageSize) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId", uid);
        pramaras.put("count", count);
        pramaras.put("page", page);
        pramaras.put("pageSize", pageSize);
        return Api.getGbkApiserver().getTrainInvitation(Constant.HTTP_GET_TRAIN_INVITATION, pramaras);
    }
}
