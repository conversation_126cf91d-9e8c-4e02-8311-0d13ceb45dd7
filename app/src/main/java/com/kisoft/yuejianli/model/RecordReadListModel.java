package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.RecordReadListContract;
import com.kisoft.yuejianli.entity.RecordReadListDto;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Description:
 * Author     : yanlu
 * Date       : 2019/1/15 10:59
 */
public class RecordReadListModel extends BaseModel implements RecordReadListContract.RecordReadListModelContract{

    public RecordReadListModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<RecordReadListDto>>> getRecordReadList(String id) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("fitemid", id);
        return Api.getGbkApiserver().getRecordReadList(Constant.HTTP_GET_RECORDREAD_LIST, pramaras);
    }
}
