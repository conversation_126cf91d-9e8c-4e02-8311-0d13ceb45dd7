package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ApplyHTSPContract;
import com.kisoft.yuejianli.entity.ConBeanInfo;
import com.kisoft.yuejianli.entity.ContractListInfo;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.MonthlySupervision;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

public class ApplyHTSPModel extends BaseModel implements ApplyHTSPContract.ApplyHTSPModelContract {
    public ApplyHTSPModel(Context context) {
        super(context);
    }

    //获取带流程的合同详情
    @Override
    public Call<NetworkResponse<ConBeanInfo>> getConInfo(String ciId, String wfTaskId, String workflow_type, String wfTaskState, String businessId, String userId) {
        Map<String,Object> pramaras=new HashMap<>();
        pramaras.put("ciId",ciId);
        pramaras.put("wfTaskId",wfTaskId);
        pramaras.put("workflow_type",workflow_type);
        pramaras.put("wfTaskState",wfTaskState);
        pramaras.put("businessId",businessId);
        pramaras.put("userId",userId);
        return Api.getGbkApiserver().getConInfo(Constant.HTTP_GET_CONINFO_BYID,pramaras);
    }

    //获取附件
    @Override
    public Call<NetworkResponse<List<EnclosureListDto>>> getEnclosureList(String id) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("businessId", id);
        pramaras.put("businessType", "t_con_info");
        return Api.getGbkApiserver().getEnclosureList(Constant.HTTP_GET_ENCLOSURE_LIST, pramaras);
    }
}
