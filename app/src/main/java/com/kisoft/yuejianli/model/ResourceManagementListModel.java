package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ResourceManagementListContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ContractPerformance;
import com.kisoft.yuejianli.entity.EmpProfess;
import com.kisoft.yuejianli.entity.FilingCertificate;
import com.kisoft.yuejianli.entity.PersonPerformance;
import com.kisoft.yuejianli.entity.ResourceManagementSearch;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;


public class ResourceManagementListModel extends BaseModel implements ResourceManagementListContract.Model {

    public ResourceManagementListModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<BaseList<ContractPerformance>>> getContractList(ResourceManagementSearch managementSearch, int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
        parameters.put("pageSize" ,String.valueOf(pageSize));
        if(managementSearch!=null){
            if(!StringUtil.isEmpty(managementSearch.getProjectName())){
                parameters.put("projectName" ,managementSearch.getProjectName());
            }
            if(!StringUtil.isEmpty(managementSearch.getStartDate())){
                parameters.put("startDate" ,managementSearch.getStartDate());
            }
            if(!StringUtil.isEmpty(managementSearch.getEndDate())){
                parameters.put("endDate" ,managementSearch.getEndDate());
            }
            if(!StringUtil.isEmpty(managementSearch.getProjectAmountMIn())){
                parameters.put("projectAmountMIn" ,managementSearch.getProjectAmountMIn());
            }
            if(!StringUtil.isEmpty(managementSearch.getProjectAmountMax())){
                parameters.put("projectAmountMax" ,managementSearch.getProjectAmountMax());
            }
            if(!StringUtil.isEmpty(managementSearch.getContractNo())){
                parameters.put("contractNo" ,managementSearch.getContractNo());
            }
        }
        return Api.getGbkApiserver().getContractList(Constant.HTTP_GET_CONTRACT_PERFOMANCE_LIST ,parameters);
    }

    @Override
    public Call<NetworkResponse<BaseList<PersonPerformance>>> getSupPerformance(ResourceManagementSearch managementSearch, int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
        parameters.put("pageSize" ,String.valueOf(pageSize));
        if(managementSearch!=null){
            if(!StringUtil.isEmpty(managementSearch.getUserName())){
                parameters.put("userName" ,managementSearch.getUserName());
            }
            if(!StringUtil.isEmpty(managementSearch.geteName())){
                parameters.put("eName" ,managementSearch.geteName());
            }
            if(!StringUtil.isEmpty(managementSearch.getDeptName())){
                parameters.put("deptName" ,managementSearch.getDeptName());
            }
        }
        return Api.getGbkApiserver().getSupPerformance(Constant.HTTP_GET_SUP_PERFORMANCE ,parameters);
    }

    @Override
    public Call<NetworkResponse<BaseList<PersonPerformance>>> getPersonList(ResourceManagementSearch managementSearch, int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
        parameters.put("pageSize" ,String.valueOf(pageSize));
        if(managementSearch!=null){
            if(!StringUtil.isEmpty(managementSearch.getUserName())){
                parameters.put("userName" ,managementSearch.getUserName());
            }
            if(!StringUtil.isEmpty(managementSearch.geteName())){
                parameters.put("eName" ,managementSearch.geteName());
            }
            if(!StringUtil.isEmpty(managementSearch.getDeptName())){
                parameters.put("deptName" ,managementSearch.getDeptName());
            }
        }
        return Api.getGbkApiserver().getSupPerformance(Constant.HTTP_GET_PERSON_LIST ,parameters);
    }

    @Override
    public Call<NetworkResponse<BaseList<EmpProfess>>> getEmpProfess(ResourceManagementSearch managementSearch, int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
        parameters.put("pageSize" ,String.valueOf(pageSize));
        if(managementSearch!=null){
            if(!StringUtil.isEmpty(managementSearch.getEmpname())){
                parameters.put("empname" ,managementSearch.getEmpname());
            }
            if(!StringUtil.isEmpty(managementSearch.getProtype())){
                parameters.put("protype" ,managementSearch.getProtype());
            }
            if(!StringUtil.isEmpty(managementSearch.getState())){
                parameters.put("state" ,managementSearch.getState());
            }
            if(!StringUtil.isEmpty(managementSearch.getEmpState())){
                parameters.put("empState" ,managementSearch.getEmpState());
            }
        }
        return Api.getGbkApiserver().getEmpProfess(Constant.HTTP_GET_EMP_PROFESS ,parameters);
    }

    @Override
    public Call<NetworkResponse<BaseList<FilingCertificate>>> getCertificate(ResourceManagementSearch managementSearch, int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
        parameters.put("pageSize" ,String.valueOf(pageSize));
        if(managementSearch!=null){
            if(!StringUtil.isEmpty(managementSearch.getFcNameNew())){
                parameters.put("fcNameNew" ,managementSearch.getFcNameNew());
            }
            if(!StringUtil.isEmpty(managementSearch.getFcNumberNew())){
                parameters.put("fcNumberNew" ,managementSearch.getFcNumberNew());
            }
            if(!StringUtil.isEmpty(managementSearch.getFcStyleNew())){
                parameters.put("fcStyleNew" ,managementSearch.getFcStyleNew());
            }
        }
        return Api.getGbkApiserver().getCertificate(Constant.HTTP_GET_CERTIFICATE ,parameters);
    }
}
