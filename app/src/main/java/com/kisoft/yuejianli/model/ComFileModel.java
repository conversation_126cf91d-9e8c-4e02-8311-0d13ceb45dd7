package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ComFileContract;
import com.kisoft.yuejianli.entity.ComFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

import com.kisoft.yuejianli.entity.ComFileType;

/**
 * Created by tudou on 2018/6/5.
 */

public class ComFileModel extends BaseModel implements ComFileContract.ComFileModelContract {

    public ComFileModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<ComFileType>>> getComFileType(String userId) {
        Map<String,Object> pramras = new HashMap<>();
        pramras.put("userId",userId);
        return Api.getGbkApiserver().getComTypes(Constant.HTTP_GET_COM_TYPE , pramras);
    }

    @Override
    public Call<NetworkResponse<List<ComFile>>> getComFile(String userId) {
        Map<String,Object> pramras = new HashMap<>();
        pramras.put("userId",userId);
        return Api.getGbkApiserver().getComFile(Constant.HTTP_GET_COM_FILES ,pramras);
    }
}
