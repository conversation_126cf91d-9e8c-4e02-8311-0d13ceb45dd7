package com.kisoft.yuejianli.model;

import android.content.Context;
import android.util.Log;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ApplyYGQJContract;
import com.kisoft.yuejianli.entity.ApplyLeaveInfo;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.LeaveInfo;
import com.kisoft.yuejianli.entity.ObjectItemInfo;
import com.kisoft.yuejianli.entity.ProcessListBean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Description:
 * Author     : yanlu
 * Date       : 2019/1/17 15:53
 */
public class ApplyYGQJModel extends BaseModel implements ApplyYGQJContract.ApplyYGQJModelContract {
    public ApplyYGQJModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<ObjectItemInfo.DataBean>>> getObjectItemInfoList(String typeCode) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("typeCode", typeCode);
        return Api.getGbkApiserver().getObjectItemInfoList(Constant.HTTP_GET_OBJECTITEMLIST ,pramaras);
    }

    @Override
    public Call<NetworkResponse<List<ApplyType>>> getLeaveType() {
        Map<String, Object> pramaras = new HashMap();
        pramaras.put("typeCode", "001.038");
        return Api.getGbkApiserver().getLeaveType(Constant.HTTP_GET_LEAVE_TYPE, pramaras);
    }

    @Override
    public Call<NetworkResponse<String>> submitApplyLeave(ApplyLeaveInfo applyLeaveInfo) {
        applyLeaveInfo.getParameters();
        return Api.getGbkApiserver().submitApply1(Constant.HTTP_SUBMIT_APPLY_LEAVE, applyLeaveInfo.getParameters());
    }

    @Override
    public Call<NetworkResponse<LeaveInfo>> getInfo(ProcessListBean bean) {
        bean.getParameters();
        Log.i("TAG", "getInfo: " + bean.getParameters());
        return Api.getGbkApiserver().getYGQJInfo(Constant.HTTP_GET_YGQJ_INFO, bean.getParameters());
    }
}