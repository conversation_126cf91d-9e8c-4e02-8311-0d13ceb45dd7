package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.DocumentDraftApprovalContract;
import com.kisoft.yuejianli.entity.ApproveArchivesInfo;
import com.kisoft.yuejianli.entity.ApproveInfo;
import com.kisoft.yuejianli.entity.JDData;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Description: 公文起草 审批办理
 * Author     : yanlu
 * Date       : 2019/1/6 11:29
 */

public class DocumentDraftApprovalModel extends BaseModel implements DocumentDraftApprovalContract.DocumentDraftApprovalModelContract{
    public DocumentDraftApprovalModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<ApproveArchivesInfo>> getArcData(String arcId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("arcId" ,arcId);
        pramaras.put("userId" , SettingManager.getInstance().getUserInfo().getId());

        return Api.getGbkApiserver().getArcData(Constant.HTTP_ADD_TOWF_APPROVE,pramaras);
    }

    @Override
    public Call<NetworkResponse<ApproveArchivesInfo>> initWf(String arcId, String wftId, String wfId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("businessId" ,arcId);
        pramaras.put("wftId" ,wftId);
        pramaras.put("wfId" ,wfId);
        pramaras.put("businessType" ,"t_doc_archives");
        pramaras.put("callBackName" ,"roa_wfCallBackService");
        pramaras.put("userId" , SettingManager.getInstance().getUserInfo().getId());

        return Api.getGbkApiserver().getArcData(Constant.HTTP_ADD_INITWF,pramaras);
    }

    @Override
    public Call<NetworkResponse<String>> startWf(ApproveInfo info) {
        return Api.getGbkApiserver().initWf(Constant.HTTP_ADD_STARTWF, info.getParameters());
    }

    @Override
    public Call<NetworkResponse<List<JDData>>> getAssignNodeList(String wfTaskId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("wfTaskId" ,wfTaskId);
        pramaras.put("userId" ,SettingManager.getInstance().getUserInfo().getId());
        return Api.getGbkApiserver().getAssignNodeList(Constant.HTTP_GET_ASSIGN_NODELIST, pramaras);
    }
}
