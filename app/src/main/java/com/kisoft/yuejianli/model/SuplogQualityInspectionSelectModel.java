package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.SuplogQualityInspectionSelectContract;
import com.kisoft.yuejianli.entity.QualityAcceptance;
import com.kisoft.yuejianli.entity.QualityInspection;
import com.kisoft.yuejianli.entity.SideReport;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/30.
 */

public class SuplogQualityInspectionSelectModel extends BaseModel implements SuplogQualityInspectionSelectContract.
        SuplogQualityInspectionSelectModelContract {

    public SuplogQualityInspectionSelectModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<QualityInspection>>> getQualityInspection(String uid, String projectId, String time) {

        Map<String, Object> pramaras  = new HashMap<>();
        pramaras.put("userId" ,uid);
        pramaras.put("projectId" , projectId);
        pramaras.put("time", time);
        return Api.getGbkApiserver().getTodayInspection(Constant.HTTP_GET_TODAY_INSPECTION, pramaras);
    }

    @Override
    public Call<NetworkResponse<List<SideReport>>> getOnsideInspection(String uid, String projectId, String time) {

        Map<String, Object> pramaras  = new HashMap<>();
        pramaras.put("userId" ,uid);
        pramaras.put("projectId" , projectId);
        pramaras.put("time", time);
        return Api.getGbkApiserver().getTodayOnside(Constant.HTTP_GET_TODAY_ONSIDE, pramaras);
    }

    @Override
    public Call<NetworkResponse<List<QualityAcceptance>>> getQualityAcceptance(String uid, String projectId, String time) {

        Map<String, Object> pramaras  = new HashMap<>();
        pramaras.put("userId" ,uid);
        pramaras.put("projectId" , projectId);
        pramaras.put("time", time);
        return Api.getGbkApiserver().getTodayQualityCheck(Constant.HTTP_GET_TODAY_QUALITY_ACCEPTANCE, pramaras);
    }
}
