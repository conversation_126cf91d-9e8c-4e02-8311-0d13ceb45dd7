package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.CustomerVisitContract;
import com.kisoft.yuejianli.entity.CustomerVisitInfo;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/19.
 */

public class CustomerVisitModel extends BaseModel implements CustomerVisitContract.CustomerVisitModelContract {

    public CustomerVisitModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<CustomerVisitInfo>> getCustomerVisits(String uid, String companyId, String count, String page, String pageSize) {
        Map<String,Object> pramaras = new HashMap<>();
        pramaras.put("userId" , uid);
        pramaras.put("companyId" ,companyId);
        pramaras.put("count" ,count);
        pramaras.put("page" ,page);
        pramaras.put("pageSize", pageSize);

        return Api.getGbkApiserver().getCustomerVisits(Constant.HTTP_GET_CUSTOMER_VISIT, pramaras);
    }
}
