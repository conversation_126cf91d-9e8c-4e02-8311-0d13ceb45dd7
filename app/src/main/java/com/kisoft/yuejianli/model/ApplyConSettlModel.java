package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ApplyConSettlContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ConCostTicketShow;
import com.kisoft.yuejianli.entity.ConPayment;
import com.kisoft.yuejianli.entity.ConSettlApply;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

public class ApplyConSettlModel extends BaseModel implements ApplyConSettlContract.Model {
    public ApplyConSettlModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<String>> submitApplyInfo(ConSettlApply info) {
        return Api.getGbkApiserver().submitApply1(Constant.HTTP_SUBMIT_APPLY_CON_SETTL_INFO, info.getParameters());
    }

    @Override
    public Call<NetworkResponse<ConSettlApply>> getInfo(ProcessListBean bean) {
        return Api.getGbkApiserver().getConSettlInfoDetail(Constant.HTTP_GET_CON_SETTL_INFO, bean.getParameters());
    }

    @Override
    public Call<NetworkResponse<BaseList<ConCostTicketShow>>> getConCostTicketShowList(String csiId) {
        Map<String,Object> parameters=new HashMap<>();
        parameters.put("companyId", SettingManager.getInstance().getUserInfo().getCompanyId());
        parameters.put("csiId",csiId);
        return Api.getGbkApiserver().getConCostTicketShow(Constant.HTTP_GET_CON_COST_TICKET_LIST, parameters);
    }

    @Override
    public Call<NetworkResponse<BaseList<ConPayment>>> getConPaymentListSByCiId(String ciId) {
        Map<String,Object> parameters=new HashMap<>();
        parameters.put("companyId", SettingManager.getInstance().getUserInfo().getCompanyId());
        parameters.put("ciId",ciId);
        return Api.getGbkApiserver().getConPaymentList(Constant.HTTP_GET_CON_PAYMENT_LIST, parameters);
    }

}
