package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.CompanyOrgZationContract;
import com.kisoft.yuejianli.entity.ComPanyOrgInfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/7/23.
 */

public class CompanyOrgZationModel extends BaseModel implements CompanyOrgZationContract.CompanyOrgZationModelContract {

    public CompanyOrgZationModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<ComPanyOrgInfo>>> getOrg(String uid) {
        Map<String, Object> pramras = new HashMap<>();
        pramras.put("userId", uid);
        return Api.getGbkApiserver().getOrgInfo(Constant.HTTP_GET_ORG_INFO, pramras);
    }
}
