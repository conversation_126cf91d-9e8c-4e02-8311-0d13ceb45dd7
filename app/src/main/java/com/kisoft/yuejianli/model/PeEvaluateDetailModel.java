package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.PeEvaluateDetailContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.PeEvaluateDetail;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;


public class PeEvaluateDetailModel extends BaseModel implements PeEvaluateDetailContract.Model{

    public PeEvaluateDetailModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<BaseList<PeEvaluateDetail>>> getEvaluatedetailView(String pdGuid, String ptGuid) {
        Map<String, Object> parameters = new HashMap<>();
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        if(userInfo!=null){
            parameters.put("userId", userInfo.getId());
        }
        parameters.put("pdGuid", pdGuid);
        parameters.put("ptGuid",ptGuid);
        return Api.getGbkApiserver().getEvaluatedetailList(Constant.HTTP_GET_EVALUATEDETAIL_VIEW ,parameters);
    }

    @Override
    public Call<NetworkResponse<Boolean>> saveEvaluatedetail(PeEvaluateDetail peEvaluateDetail) {
        Map<String, Object> parameters = new HashMap<>();
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        if(userInfo!=null){
            parameters.put("userId", userInfo.getId());
            parameters.put("userName", userInfo.getName());
        }
        parameters.put("data", StringUtil.objectToJson(peEvaluateDetail));
        return Api.getGbkApiserver().commitInfo(Constant.HTTP_SAVE_EVALUATEDETAIL_DETAIL ,parameters);
    }
}
