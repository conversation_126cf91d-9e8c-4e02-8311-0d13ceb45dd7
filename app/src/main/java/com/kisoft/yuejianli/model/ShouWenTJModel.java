package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ApplySPContract;
import com.kisoft.yuejianli.entity.ApproveInfo;
import com.kisoft.yuejianli.entity.JDData;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

public class ShouWenTJModel extends BaseModel implements ApplySPContract.ApplySPModelContract {
    public ShouWenTJModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<JDData>>> getProcessList(String businessType, String callBackName) {
        Map<String, Object> pramaras = new HashMap();
        pramaras.put("businessType", businessType);
        pramaras.put("callBackName", callBackName);
        pramaras.put("userId", SettingManager.getInstance().getUserInfo().getId());
        return Api.getGbkApiserver().getAssignNodeList(Constant.HTTP_SUBMIT_APPLY_SP, pramaras);
    }

    @Override
    public Call<NetworkResponse<List<JDData>>> getNextList(String businessType, String callBackName, String wfTaskId) {
        Map<String, Object> pramaras = new HashMap();
        pramaras.put("businessType", businessType);
        pramaras.put("callBackName", callBackName);
        pramaras.put("wfTaskId", wfTaskId);
        pramaras.put("userId", SettingManager.getInstance().getUserInfo().getId());
        return Api.getGbkApiserver().getAssignNodeList(Constant.HTTP_GET_ASSIGN_NODELIST, pramaras);
    }

    @Override
    public Call<NetworkResponse<String>> initWf(String businessId, String wftId, String wfId, String wfName,
                                                String businessType, String callBackName) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("businessId" ,businessId);
        pramaras.put("wfName" ,wfName);
        pramaras.put("wftId" ,wftId);
        pramaras.put("wfId" ,wfId);
        pramaras.put("businessType" ,businessType);
        pramaras.put("callBackName" ,callBackName);
        pramaras.put("userId" , SettingManager.getInstance().getUserInfo().getId());
        return Api.getGbkApiserver().initWf(Constant.HTTP_ADD_INITWF,pramaras);
    }



    /*
    @Override
    public Call<NetworkResponse<String>> initWf(String businessId, String wftId, String wfId, String wfName, String businessType, String callBackName) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("businessId" ,businessId);
        pramaras.put("wfName" ,wfName);
        pramaras.put("wftId" ,wftId);
        pramaras.put("wfId" ,wfId);
        pramaras.put("businessType" ,businessType);
        pramaras.put("callBackName" ,callBackName);
        pramaras.put("userId" , SettingManager.getInstance().getUserInfo().getId());
        return Api.getGbkApiserver().initWf(Constant.HTTP_ADD_INITWF,pramaras);
    }
    */


    @Override
    public Call<NetworkResponse<String>> submitApplySP(ApproveInfo info) {

        return Api.getGbkApiserver().submitApply1(Constant.HTTP_ADD_STARTWF, info.getParameters());
    }
}
