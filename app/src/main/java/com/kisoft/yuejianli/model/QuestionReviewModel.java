package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.QuestionReviewContract;
import com.kisoft.yuejianli.entity.ProreiviewDto;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

public class QuestionReviewModel extends BaseModel implements QuestionReviewContract.QuestionReviewModelContract{
    public QuestionReviewModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<String>> submitData(ProreiviewDto data, String userId, String userName) {
        Map<String, Object> pramaras = new HashMap();
        pramaras.put("data", StringUtil.objectToJson(data));
        pramaras.put("userId", userId);
        pramaras.put("userName", userName);
        return Api.getGbkApiserver().getAllHandelAccidentCount(Constant.HTTP_SUBMIT_REVIEW_DATA, pramaras);
    }

    @Override
    public Call<NetworkResponse<ProreiviewDto>> getData(String ppId) {
        Map<String, Object> pramaras = new HashMap();
        pramaras.put("ppId", ppId);
        return Api.getGbkApiserver().getReviewData(Constant.HTTP_GET_REVIEW_DATA, pramaras);
    }
}
