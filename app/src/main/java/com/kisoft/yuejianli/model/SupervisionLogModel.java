package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.api.WeatherResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.SupervisionLogContract;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.SupervisionLog;
import com.kisoft.yuejianli.entity.Weather;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/4/11.
 */

public class SupervisionLogModel extends BaseModel implements
        SupervisionLogContract.SupervisionLogModelContract {

    public SupervisionLogModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse> commitLog(SupervisionLog log, String receiver) {

        Map<String,Object> pramaras = new HashMap<>();
        pramaras.put("userId" , log.getCreateId());
        pramaras.put("pId" , log.getProjectId());
        pramaras.put("uName" , log.getCreateName());
        pramaras.put("weatherWarnLevel" , log.getWeatherWarnLevel());
        pramaras.put("weather" , log.getWeather());
        pramaras.put("temperature" , log.getTemperature());
        try {
            pramaras.put("position" , URLEncoder.encode(URLEncoder.encode(log.getPosition(),"UTF-8"),"UTF-8"));
            pramaras.put("problem" , URLEncoder.encode(URLEncoder.encode(log.getProblem(),"UTF-8"),"UTF-8"));
            pramaras.put("personnelStatus" , URLEncoder.encode(URLEncoder.encode(log.getPersonnelStatus(),"UTF-8"),"UTF-8"));
            pramaras.put("equipmentStatus" , URLEncoder.encode(URLEncoder.encode(log.getEquipmentStatus(),"UTF-8"),"UTF-8"));
            pramaras.put("other" , URLEncoder.encode(URLEncoder.encode(log.getOther(),"UTF-8"),"UTF-8"));
            pramaras.put("detectionSituation" , URLEncoder.encode(URLEncoder.encode(log.getDetectionSituation(),"UTF-8"),"UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
//        pramaras.put("position" , URLEncoder.encode(log.getPosition()));
//        pramaras.put("problem" , URLEncoder.encode(log.getProblem()));
//        pramaras.put("personnelStatus" , URLEncoder.encode(log.getPersonnelStatus()));
//        pramaras.put("equipmentStatus" , URLEncoder.encode(log.getEquipmentStatus()));
//        pramaras.put("other" , URLEncoder.encode(log.getOther()));
//        pramaras.put("detectionSituation" , URLEncoder.encode(log.getDetectionSituation()));

        pramaras.put("content" , log.getDetectionSituation());
        pramaras.put("enclosure" , log.getEnclosure());
        pramaras.put("receiver" ,receiver);
        return Api.getGbkApiserver().commitSupervisionLog(Constant.HTTP_GET_ADD_SUPERVISON_LOG, pramaras);
    }

    @Override
    public Call<NetworkResponse<List<ApplyType>>> getTypes() {
        Map<String, Object> pramaras = new HashMap();
        pramaras.put("typeCode", "108.200");
        return Api.getGbkApiserver().getLeaveType(Constant.HTTP_GET_QUIT_TYPE, pramaras);
    }

    @Override
    public Call<WeatherResponse<Weather>> getWeather(String city) {
        Map<String, Object> pramaras = new HashMap();
        pramaras.put("city", city);
        pramaras.put("key", "12070b383be1a5ffdf48e10766e5ddbe");
        return Api.getWeatherApiServer().getWeather(pramaras);
    }

    @Override
    public Call<NetworkResponse<SupervisionLog>> getLogInfo(String date, String userId, String projectId, String wfId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("date", date);
        pramaras.put("userId", userId);
        pramaras.put("projectId", projectId);
        pramaras.put("wfId", wfId);
        return Api.getGbkApiserver().getLogInfo(Constant.HTTP_SUP_LOG_BY_DATE, pramaras);
    }

    @Override
    public Call<NetworkResponse<SupervisionLog>> getLogInfo(String slId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("slId", slId);
        return Api.getGbkApiserver().getLogInfo(Constant.HTTP_SUP_LOG_BY_DATE, pramaras);
    }

    @Override
    public Call<NetworkResponse<Boolean>> updateLogInfo(String userId,String userName,SupervisionLog supervisionLog) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId", userId);
        pramaras.put("userName", userName);
        pramaras.put("data", StringUtil.objectToJson(supervisionLog));
        return Api.getGbkApiserver().updateLogSupervision(Constant.HTTP_UPDATE_LOG_REPORT, pramaras);
    }

    //判断当天是否上传日志
    @Override
    public Call<NetworkResponse<String>> checkSupLog(String uid, String projectId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("createId", uid);
        pramaras.put("projectId" , projectId);
        pramaras.put("createTime" , DateUtil.dateToString(new Date(), DateUtil.YMD));
        return Api.getGbkApiserver().getAllHandelAccidentCount(Constant.HTTP_GET_CHECK_SUPLOG,pramaras);
    }
}