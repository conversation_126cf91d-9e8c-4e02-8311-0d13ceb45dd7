package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ControllerListContract;
import com.kisoft.yuejianli.entity.QualityInspection;
import com.kisoft.yuejianli.entity.QualityInspectionInfo;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/5/28.
 */

public class ControllerListModel extends BaseModel implements ControllerListContract.ControllerListModelConrtact {

    public ControllerListModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<QualityInspectionInfo>> getInspections(String uid, String projectId, String month, String count, String pageSize, final String page, int pageType) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId", uid);
        pramaras.put("projectId" , projectId);
        if(!StringUtil.isEmpty(month)){
            pramaras.put("month",month);
        }
        pramaras.put("count", count);
        pramaras.put("pageSize", pageSize);
        pramaras.put("page" ,page);

        pramaras.put("status", "1,2");
        pramaras.put("flag", pageType + "");
        return Api.getGbkApiserver().getInspectionList(Constant.HTTP_GET_QUALITY_INSPECTION_LIST1, pramaras);
    }
}
