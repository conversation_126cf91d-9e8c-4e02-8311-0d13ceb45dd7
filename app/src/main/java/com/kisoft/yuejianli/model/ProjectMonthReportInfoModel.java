package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectMonthReportInfoContract;
import com.kisoft.yuejianli.entity.MonthlySupervisionInfo;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/7/5.
 */

public class ProjectMonthReportInfoModel extends BaseModel implements ProjectMonthReportInfoContract
        .ProjectMonthReportInfoModelContract{

    public ProjectMonthReportInfoModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<MonthlySupervisionInfo>> getMonthReport(String uid, String projectId, String count, String page, String pageSize) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId", uid);
        pramaras.put("projectId" ,projectId);
        pramaras.put("count" ,count);
        pramaras.put("page" ,page);
        pramaras.put("pageSize" ,pageSize);

        return Api.getGbkApiserver().getMonthlySupervisionList(Constant.HTTP_GET_MONTH_REPORT ,pramaras);
    }
}
