package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.UserInfoEditContract;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by yanlu on 2018/6/22.
 */

public class UserInfoEditModel extends BaseModel implements UserInfoEditContract.UserInfoEditModelContract {

    public UserInfoEditModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<Boolean>> uploadUserInfo(String userTel, String userPhotoPath) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId" , SettingManager.getInstance().getUserInfo().getId());
        pramaras.put("userTel" , userTel);
        pramaras.put("userPhotoPath" , userPhotoPath);

        return Api.getGbkApiserver().uploadUserInfo(Constant.HTTP_UPLOAD_USER_INFO, pramaras);
    }

    @Override
    public Call<NetworkResponse<Boolean>> bindFaceToUser(String faceId, String imageBase64) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId" , SettingManager.getInstance().getUserInfo().getId());
        pramaras.put("faceId",faceId);
        pramaras.put("imageBase64",imageBase64);

        return Api.getGbkApiserver().commitInfo(Constant.HTTP_BIND_USER_FACE, pramaras);
    }
}
