package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.TenderDepositAddContract;
import com.kisoft.yuejianli.contract.TenderDepositReturnAddContract;
import com.kisoft.yuejianli.entity.TenderDeposit;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/7/24.
 */

public class TenderDepositReturnAddModel extends BaseModel implements TenderDepositReturnAddContract.TenderDepositReturnModelContract {

    public TenderDepositReturnAddModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<Boolean>> submitDeposit(String uid, TenderDeposit.DepositReturn depositReturn) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId", uid);
        pramaras.put("data", StringUtil.objectToJson(depositReturn));

        return Api.getGbkApiserver().addTenderDeposit(Constant.HTTP_ADD_TENDER_DEPOSIT_RETURN, pramaras);
    }
}
