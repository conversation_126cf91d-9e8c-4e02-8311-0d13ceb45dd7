package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.AnnNoticeDetailContract;
import com.kisoft.yuejianli.entity.InfoIssueDto;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/7.
 */
public class AnnNoticeDetailModel extends BaseModel implements AnnNoticeDetailContract.AnnNoticeDetailModelContract {

    public AnnNoticeDetailModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<InfoIssueDto.InfoIssueBean>> requestData(String fitemId) {
        Map<String, Object> pramars = new HashMap<>();
        pramars.put("userId", SettingManager.getInstance().getUserInfo().getId());
        pramars.put("fitemId", fitemId);
        pramars.put("companyName", SettingManager.getInstance().getUserInfo().getCompanyName());
        pramars.put("fatherName", SettingManager.getInstance().getUserInfo().getFatherName());

        return Api.getGbkApiserver().lookInfoDetaile(Constant.HTTP_LOOK_INFO, pramars);
    }
}
