package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ApplyUseSealContract;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.ApplyUseSealInfo;
import com.kisoft.yuejianli.entity.ProcessListBean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by Administrator on 2019/4/11 0011.
 */

public class ApplyUseSealModel extends BaseModel implements ApplyUseSealContract.ApplyUseSealModelContract {

    public ApplyUseSealModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<ApplyType>>> getProjectUnits() {
        Map<String, Object> pramaras = new HashMap();
        pramaras.put("typeCode", "108.189");
        return Api.getGbkApiserver().getLeaveType(Constant.HTTP_GET_LEAVE_TYPE, pramaras);
    }

    @Override
    public Call<NetworkResponse<String>> submitApplyInfo(ApplyUseSealInfo info) {
        return Api.getGbkApiserver().submitApply1(Constant.HTTP_SUBMIT_APPLY_USESEAL, info.getParameters());
    }

    @Override
    public Call<NetworkResponse<ApplyUseSealInfo>> getInfo(ProcessListBean bean) {
        return Api.getGbkApiserver().getUseSealInfo(Constant.HTTP_GET_USESEAL_INFO, bean.getParameters());
    }
}
