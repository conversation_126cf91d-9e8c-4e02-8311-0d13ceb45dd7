package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.CompleteCheckAddContract;
import com.kisoft.yuejianli.entity.CompleteCheckInfo;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

public class CompleteCheckAddModel extends BaseModel implements CompleteCheckAddContract.CompleteCheckAddModelContract{
    public CompleteCheckAddModel(Context context) {
        super(context);
    }
    @Override
    public Call<NetworkResponse<Boolean>> addCompleteCheck(CompleteCheckInfo completeCheckInfo, String userId, String projectId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId" , userId);
        pramaras.put("projectId", projectId);
        pramaras.put("data" , StringUtil.objectToJson(completeCheckInfo));

//        Date date = new Date();
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
//        String format = dateFormat.format(date);
//        pramaras.put("projectTime", format);
        return Api.getGbkApiserver().addCompleteCheck(Constant.HTTP_ADD_FINISHEORK, pramaras);
    }
}
