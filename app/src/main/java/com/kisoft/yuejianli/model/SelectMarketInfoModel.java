package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.SelectMarketInfoContract;
import com.kisoft.yuejianli.entity.MarketInfoInfo;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/7/23.
 */

public class SelectMarketInfoModel extends BaseModel implements SelectMarketInfoContract.SelectMarketInfoModelContract {

    public SelectMarketInfoModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<MarketInfoInfo>> getMarketInfo(String uid, String p, String z, String c) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId",uid);
        pramaras.put("page",p);
        pramaras.put("pageSize",z);
        pramaras.put("count",c);
        return Api.getGbkApiserver().getMarketInfo(Constant.HTTP_GET_MARKET_INFO, pramaras);
    }
}
