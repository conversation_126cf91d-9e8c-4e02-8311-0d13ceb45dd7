package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.TakePictureAddContract;
import com.kisoft.yuejianli.entity.TakePictureInfo;
import com.kisoft.yuejianli.utils.StringUtil;

import org.xutils.common.util.LogUtil;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

public class TakePictureAddModel extends BaseModel implements TakePictureAddContract.TakePictureAddModelContract{
    public TakePictureAddModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<Boolean>> addTakePicture(TakePictureInfo takePictureInfo) {
        Map<String, Object> pramaras = new HashMap<>();
        try {
            LogUtil.i(StringUtil.objectToJson(takePictureInfo));
            pramaras.put("data" , URLEncoder.encode(URLEncoder.encode(StringUtil.objectToJson(takePictureInfo),"UTF-8"),"UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
//        pramaras.put("data" , StringUtil.objectToJson(takePictureInfo));
        return Api.getGbkApiserver().addTakePicture(Constant.HTTP_ADD_TAKEPICTURE, pramaras);
    }

    @Override
    public Call<NetworkResponse<TakePictureInfo>> getTakePictureInfo(String sfId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("sfId" , sfId);
        return Api.getGbkApiserver().getTakePictureInfo(Constant.HTTP_GET_TAKE_PICTURE_INFO,pramaras);
    }
}