package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.MailDetailContract;
import com.kisoft.yuejianli.entity.MailInfoDetail;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;


public class MailDetailModel extends BaseModel implements MailDetailContract.Model{

    public MailDetailModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<MailInfoDetail>> viewInBox(String fmailId) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("fmailId", fmailId);
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        if(userInfo!=null){
            parameters.put("userId", userInfo.getId());
        }
        return Api.getGbkApiserver().getMailDetail(Constant.HTTP_VIEW_IN_BOX ,parameters);
    }

    @Override
    public Call<NetworkResponse<MailInfoDetail>> transImailInBox(String fmailId) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("fmailId", fmailId);
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        if(userInfo!=null){
            parameters.put("userId", userInfo.getId());
            parameters.put("userName", userInfo.getName());
        }
        return Api.getGbkApiserver().getMailDetail(Constant.HTTP_TRANS_MAIL_IN_BOX ,parameters);
    }

    @Override
    public Call<NetworkResponse<Boolean>> sendMail(MailInfoDetail mailInfoDetail) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("data" , StringUtil.objectToJson(mailInfoDetail));
        return Api.getGbkApiserver().commitInfo(Constant.HTTP_SEND_MAIL ,parameters);
    }
}
