package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ApplyLZContract;
import com.kisoft.yuejianli.entity.ApplyLZInfo;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.ProcessListBean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by Administrator on 2019/4/11 0011.
 */
public class ApplyLZModel extends BaseModel implements ApplyLZContract.ApplyLZModelContract {

    public ApplyLZModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<ApplyType>>> getType() {
        Map<String, Object> pramaras = new HashMap();
        pramaras.put("typeCode", "001.039");
        return Api.getGbkApiserver().getLeaveType(Constant.HTTP_GET_QUIT_TYPE, pramaras);
    }

    @Override
    public Call<NetworkResponse<String>> submitApplyInfo(ApplyLZInfo info) {
        return Api.getGbkApiserver().submitApply1(Constant.HTTP_SUBMIT_APPLY_QUIT, info.getParameters());
    }

    @Override
    public Call<NetworkResponse<ApplyLZInfo>> getInfo(ProcessListBean bean) {
        return Api.getGbkApiserver().getLZInfo(Constant.HTTP_GET_LZ_INFO, bean.getParameters());
    }
}
