package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.CompanyPhoneDirectoryContract;
import com.kisoft.yuejianli.entity.Communication;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

public class CompanyPhoneDirecoryModel extends BaseModel implements CompanyPhoneDirectoryContract.CompanyPhoneDirectoryModelContract {
    public CompanyPhoneDirecoryModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<Communication>>> getPhoneInfo(String uid) {
        Map<String, Object> paramars = new HashMap<>();
        paramars.put("userId", uid);

        return Api.getGbkApiserver().getUserLinkman(Constant.HTTP_GET_COMPANY_USERINFO, paramars);
    }
}
