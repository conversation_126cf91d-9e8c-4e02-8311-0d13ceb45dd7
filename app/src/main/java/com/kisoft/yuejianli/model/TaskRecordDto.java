package com.kisoft.yuejianli.model;

import com.kisoft.yuejianli.base.Base;

/**
 * Author     : yanlu
 * Date       : 2019/1/12 10:47
 */

public class TaskRecordDto extends Base {


    /**
     * message : ok
     * data : [{"progress":"未开始","tsktckGuid":"190112104308705bbc77405f5239ab43","ttpercent":12,"username":"测试员02","status":"","createdate":{"nanos":0,"time":1547260989000,"minutes":43,"seconds":9,"hours":10,"month":0,"timezoneOffset":-480,"year":119,"day":6,"date":12},"report":"测试","ttpercentStr":"","reporttype":"负责人答复","tskGuid":"1901022157395370a7e811c58ce0a837","createDateStr":"","userGuid":"110602113756546aa7cfa4f9374df22a"},{"progress":"进行中","tsktckGuid":"1901121031370044bd4c761a45f4e287","ttpercent":12,"username":"测试员02","status":"","createdate":{"nanos":0,"time":1547260297000,"minutes":31,"seconds":37,"hours":10,"month":0,"timezoneOffset":-480,"year":119,"day":6,"date":12},"report":"测试","ttpercentStr":"","reporttype":"负责人答复","tskGuid":"1901022157395370a7e811c58ce0a837","createDateStr":"","userGuid":"110602113756546aa7cfa4f9374df22a"},{"progress":"进行中","tsktckGuid":"1901030510222652803617f915093cc5","ttpercent":60,"username":"测试员02","status":"","createdate":{"nanos":0,"time":1546463422000,"minutes":10,"seconds":22,"hours":5,"month":0,"timezoneOffset":-480,"year":119,"day":4,"date":3},"report":"4444","ttpercentStr":"","reporttype":"负责人答复","tskGuid":"1901022157395370a7e811c58ce0a837","createDateStr":"","userGuid":"110602113756546aa7cfa4f9374df22a"},{"progress":"进行中","tsktckGuid":"190102221122513597bad54cd7777fa3","ttpercent":10,"username":"测试员02","status":"","createdate":{"nanos":0,"time":1546438283000,"minutes":11,"seconds":23,"hours":22,"month":0,"timezoneOffset":-480,"year":119,"day":3,"date":2},"report":"6666","ttpercentStr":"","reporttype":"负责人答复","tskGuid":"1901022157395370a7e811c58ce0a837","createDateStr":"","userGuid":"110602113756546aa7cfa4f9374df22a"},{"progress":"进行中","tsktckGuid":"190102220850601e910758cc408b1302","ttpercent":90,"username":"测试员02","status":"","createdate":{"nanos":0,"time":1546438130000,"minutes":8,"seconds":50,"hours":22,"month":0,"timezoneOffset":-480,"year":119,"day":3,"date":2},"report":"44555","ttpercentStr":"","reporttype":"负责人答复","tskGuid":"1901022157395370a7e811c58ce0a837","createDateStr":"","userGuid":"110602113756546aa7cfa4f9374df22a"},{"progress":"进行中","tsktckGuid":"1901022208006609ad6f2872745c6afa","ttpercent":90,"username":"测试员02","status":"","createdate":{"nanos":0,"time":1546438080000,"minutes":8,"seconds":0,"hours":22,"month":0,"timezoneOffset":-480,"year":119,"day":3,"date":2},"report":"44555","ttpercentStr":"","reporttype":"负责人答复","tskGuid":"1901022157395370a7e811c58ce0a837","createDateStr":"","userGuid":"110602113756546aa7cfa4f9374df22a"}]
     * code : 200
     */

    /**
     * progress : 未开始
     * tsktckGuid : 190112104308705bbc77405f5239ab43
     * ttpercent : 12
     * username : 测试员02
     * status :
     * createdate : {"nanos":0,"time":1547260989000,"minutes":43,"seconds":9,"hours":10,"month":0,"timezoneOffset":-480,"year":119,"day":6,"date":12}
     * report : 测试
     * ttpercentStr :
     * reporttype : 负责人答复
     * tskGuid : 1901022157395370a7e811c58ce0a837
     * createDateStr :
     * userGuid : 110602113756546aa7cfa4f9374df22a
     */

    private String         progress;
    private String         tsktckGuid;
    private int            ttpercent;
    private String         username;
    private String         status;
    private CreatedateBean createdate;
    private String         report;
    private String         ttpercentStr;
    private String         reporttype;
    private String         tskGuid;
    private String         createDateStr;
    private String         userGuid;

    public String getProgress() {
        return progress;
    }

    public void setProgress(String progress) {
        this.progress = progress;
    }

    public String getTsktckGuid() {
        return tsktckGuid;
    }

    public void setTsktckGuid(String tsktckGuid) {
        this.tsktckGuid = tsktckGuid;
    }

    public int getTtpercent() {
        return ttpercent;
    }

    public void setTtpercent(int ttpercent) {
        this.ttpercent = ttpercent;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public CreatedateBean getCreatedate() {
        return createdate;
    }

    public void setCreatedate(CreatedateBean createdate) {
        this.createdate = createdate;
    }

    public String getReport() {
        return report;
    }

    public void setReport(String report) {
        this.report = report;
    }

    public String getTtpercentStr() {
        return ttpercentStr;
    }

    public void setTtpercentStr(String ttpercentStr) {
        this.ttpercentStr = ttpercentStr;
    }

    public String getReporttype() {
        return reporttype;
    }

    public void setReporttype(String reporttype) {
        this.reporttype = reporttype;
    }

    public String getTskGuid() {
        return tskGuid;
    }

    public void setTskGuid(String tskGuid) {
        this.tskGuid = tskGuid;
    }

    public String getCreateDateStr() {
        return createDateStr;
    }

    public void setCreateDateStr(String createDateStr) {
        this.createDateStr = createDateStr;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public static class CreatedateBean {
        /**
         * nanos : 0
         * time : 1547260989000
         * minutes : 43
         * seconds : 9
         * hours : 10
         * month : 0
         * timezoneOffset : -480
         * year : 119
         * day : 6
         * date : 12
         */

        private int  nanos;
        private long time;
        private int  minutes;
        private int  seconds;
        private int  hours;
        private int  month;
        private int  timezoneOffset;
        private int  year;
        private int  day;
        private int  date;

        public int getNanos() {
            return nanos;
        }

        public void setNanos(int nanos) {
            this.nanos = nanos;
        }

        public long getTime() {
            return time;
        }

        public void setTime(long time) {
            this.time = time;
        }

        public int getMinutes() {
            return minutes;
        }

        public void setMinutes(int minutes) {
            this.minutes = minutes;
        }

        public int getSeconds() {
            return seconds;
        }

        public void setSeconds(int seconds) {
            this.seconds = seconds;
        }

        public int getHours() {
            return hours;
        }

        public void setHours(int hours) {
            this.hours = hours;
        }

        public int getMonth() {
            return month;
        }

        public void setMonth(int month) {
            this.month = month;
        }

        public int getTimezoneOffset() {
            return timezoneOffset;
        }

        public void setTimezoneOffset(int timezoneOffset) {
            this.timezoneOffset = timezoneOffset;
        }

        public int getYear() {
            return year;
        }

        public void setYear(int year) {
            this.year = year;
        }

        public int getDay() {
            return day;
        }

        public void setDay(int day) {
            this.day = day;
        }

        public int getDate() {
            return date;
        }

        public void setDate(int date) {
            this.date = date;
        }
    }

}
