package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.DocumentUpcomContract;
import com.kisoft.yuejianli.entity.ArcWorkDTO1;
import com.kisoft.yuejianli.entity.ArchivesTempletDto;
import com.kisoft.yuejianli.entity.httpresult.ProcessHttpResult;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by yanlu on 2019/1/4.
 */

public class DocumentUpcomModel extends BaseModel implements DocumentUpcomContract.DocumentUpcomModelContract {

    public DocumentUpcomModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<ArcWorkDTO1>> getArcWorkList(String userId, String type, String count, String page,
                                                             String pageSize) {

        Map<String ,Object> pramaras = new HashMap<>();
        pramaras.put("count" ,count);
        pramaras.put("page" ,page);
        pramaras.put("pageSize" ,pageSize);
        pramaras.put("month" , DateUtil.getTodayDate(DateUtil.YM));
        pramaras.put("userRoles" , SettingManager.getInstance().getUserInfo().getUserOrgRelation());

        pramaras.put("userId" , userId);
        pramaras.put("workType" , type);     //类型（1=待办、2=已办、3=已发）
        return Api.getGbkApiserver().getArcWorkList1(Constant.HTTP_GET_ARC_WORK_LIST, pramaras);
    }

    @Override
    public Call<NetworkResponse<ProcessHttpResult>> showShouWenWorkList(String workType, String count, String page,
                                                                            String pageSize) {
        //workType类型（1=待办、2=已办、3=我发起的）
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("workType" ,workType);
        pramaras.put("userId" , SettingManager.getInstance().getUserInfo().getId());
        pramaras.put("businessType", "T_Doc_reArchives");
        pramaras.put("month" , DateUtil.getTodayDate(DateUtil.YM));
        pramaras.put("count" ,count);
        pramaras.put("page" ,page);
        pramaras.put("pageSize" ,pageSize);

        return Api.getGbkApiserver().getProcessList(Constant.HTTP_GET_PROCESS_LIST1, pramaras);
    }

    @Override
    public Call<NetworkResponse<ArchivesTempletDto>> listArchives(String userId, String userRoles, String count, String page, String pageSize) {
        Map<String ,Object> pramaras = new HashMap<>();
        pramaras.put("userId" , userId);
        pramaras.put("userRoles" , userRoles);
        pramaras.put("count" , "0");
        pramaras.put("page" , page);
        pramaras.put("pageSize" , pageSize);
        pramaras.put("fvtGuid" , "0");

        return Api.getGbkApiserver().listArchives(Constant.HTTP_GET_LIST_ARCHIVES, pramaras);
    }
}
