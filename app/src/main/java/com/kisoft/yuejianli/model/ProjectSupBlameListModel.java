package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectSupBlameListContract;
import com.kisoft.yuejianli.entity.ProjectFilingSupDetailBlameInfo;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/23.
 */

public class ProjectSupBlameListModel extends BaseModel implements ProjectSupBlameListContract.ProjectSupBlameListModelContract {

    public ProjectSupBlameListModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<ProjectFilingSupDetailBlameInfo>> getBlames(String uid, String projectId, String mType, String count, String page, String pageSize) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId", uid);
        pramaras.put("projectId", projectId);
        pramaras.put("mType" , mType);
        pramaras.put("count" ,count);
        pramaras.put("page" ,page);
        pramaras.put("pageSize" ,pageSize);

        return   Api.getGbkApiserver().getFilingSupDetailBlameInfo(Constant.HTTP_GET_FILING_SUP_DETAIL_BLAME, pramaras);
    }
}
