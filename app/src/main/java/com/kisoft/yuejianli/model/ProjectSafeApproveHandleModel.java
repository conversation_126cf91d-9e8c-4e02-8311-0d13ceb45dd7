package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.MessageTodo;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

import com.kisoft.yuejianli.contract.ProjectSafeApproveHandleContract;
import com.kisoft.yuejianli.entity.ProjectSafeInspection;
import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/5/7.
 */

public class ProjectSafeApproveHandleModel extends BaseModel implements
        ProjectSafeApproveHandleContract.ProjectSafeApproveHandleModelContract {


    public ProjectSafeApproveHandleModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<ProjectSafeInspection>> getSafeInspection(String uid, String projectId, String id) {
        Map<String,Object> pramras = new HashMap<>();
        pramras.put("userId" ,uid );
        pramras.put("projectId" ,projectId);
        pramras.put("inspectIds" ,id);
        return Api.getGbkApiserver().getSceneSafeInspect(Constant.HTTP_GET_SCENE_SAFE_REPORT, pramras);
    }

    @Override
    public Call<NetworkResponse<Boolean>> addTodoMessage(String uid, String projectId, MessageTodo todo, String messageType) {
        Map<String,Object> pramras = new HashMap<>();
        pramras.put("userId" ,uid );
        pramras.put("projectId" ,projectId);
        pramras.put("messageType", messageType);
        pramras.put("data" , StringUtil.objectToJson(todo));
        return Api.getGbkApiserver().addMessageTodo(Constant.HTTP_ADD_MESSAGE_TODO, pramras);
    }
}
