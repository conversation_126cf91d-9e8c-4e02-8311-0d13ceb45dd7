package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectQualityCountMonthContract;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

import com.kisoft.yuejianli.entity.AccidentInfos;

/**
 * Created by tudou on 2018/4/27.
 */

public class ProjectQualityCountMonthModel extends BaseModel implements
        ProjectQualityCountMonthContract.ProjectQualityCountMonthModelContract {

    public ProjectQualityCountMonthModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<AccidentInfos>> getAllAccidents(String uid, String projectId, String month, String count, String pageSize, String page) {
        Map<String ,Object> pramaras = new HashMap<>();
        pramaras.put("userId" ,uid);
        pramaras.put("projectId" ,projectId);
        pramaras.put("month" ,month);
        pramaras.put("count" ,count);
        pramaras.put("pageSize", pageSize);
        pramaras.put("page", page);
        return Api.getGbkApiserver().getQualityAllAccidents(Constant.HTTP_GET_ALL_QUALITY_ACCIDENT, pramaras);
    }

    @Override
    public Call<NetworkResponse<String>> getHandelCount(String uid, String projectId, String month) {
        Map<String ,Object> pramaras = new HashMap<>();
        pramaras.put("userId" ,uid);
        pramaras.put("projectId" ,projectId);
        pramaras.put("month" ,month);
        return Api.getGbkApiserver().getMonthHandelAccidentCount(Constant.HTTP_GET_MONTH_HANDLE_ACCIDENT, pramaras);
    }
}
