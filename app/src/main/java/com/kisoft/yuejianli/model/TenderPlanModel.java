package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.TenderPlanContract;
import com.kisoft.yuejianli.entity.TenderPlan;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by tudou on 2018/6/27.
 */

public class TenderPlanModel extends BaseModel implements TenderPlanContract.TenderPlanModelContract {

    public TenderPlanModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<TenderPlan>>> getTenderPlan(String uid, String month) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId", uid);
        pramaras.put("month", month);
        return Api.getGbkApiserver().getTenderPlan(Constant.HTTP_GET_TENDER_PLAN, pramaras);
    }
}
