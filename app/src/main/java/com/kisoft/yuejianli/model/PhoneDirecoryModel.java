package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.PhoneDirectoryContract;
import com.kisoft.yuejianli.entity.Department;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

import com.kisoft.yuejianli.entity.Communication;

/**
 * Created by tudou on 2018/3/27.
 */

public class PhoneDirecoryModel extends BaseModel implements PhoneDirectoryContract.PhoneDirecrotyModelContract {

    public PhoneDirecoryModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<List<Communication>>> getPhoneInfo(String projectId) {
        Map<String, Object> paramars = new HashMap<>();
        paramars.put("projectid" , projectId );

        return Api.getGbkApiserver().getUserLinkman(Constant.HTTP_GET_PROJECT_USERINFO, paramars);
    }

    @Override
    public Call<NetworkResponse<List<Department>>> getProjectTree(String projectId) {
        Map<String, Object> paramars = new HashMap<>();
        paramars.put("projectid" , projectId );

        return Api.getGbkApiserver().getProjectTree(Constant.HTTP_GET_PROJECT_TREE,paramars);
    }
}
