package com.kisoft.yuejianli.model;

import android.content.Context;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseModel;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ExamOnlineContract;
import com.kisoft.yuejianli.contract.ExamRecordContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ExamRecord;
import com.kisoft.yuejianli.entity.ExamUserInfo;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;


public class ExamRecordModel extends BaseModel implements ExamRecordContract.Model {

    public ExamRecordModel(Context context) {
        super(context);
    }

    @Override
    public Call<NetworkResponse<BaseList<ExamRecord>>> getExamRecordList(String onlineState, int count, int page, int pageSize) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("userId", SettingManager.getInstance().getUserId());
        parameters.put("onlineState" ,onlineState);//0=在线考试 1=模拟考试
        parameters.put("count" ,String.valueOf(count));
        parameters.put("page" ,String.valueOf(page));
        parameters.put("pageSize" ,String.valueOf(pageSize));
        return Api.getGbkApiserver().getExamRecordList(Constant.HTTP_GET_EXAM_RECORD_LIST ,parameters);
    }
}
