package com.kisoft.yuejianli.hdmh;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.views.ProjectChoseActivity;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * Created by tudou on 2018/6/6.
 */
public class HdmhProjectInfoActivity extends BaseActivity {

    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.tool_bar)
    Toolbar toolBar;
    @BindView(R.id.mRecyclerView)
    RecyclerView mRecyclerView;
    private HdmhMenuInfo mMenuInfo;

    public static void launch(Activity activity, HdmhMenuInfo item) {
        Intent intent = new Intent(activity, HdmhProjectInfoActivity.class);
        intent.putExtra("HdmhMenuInfo", item);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getIntent() != null) {
            mMenuInfo = (HdmhMenuInfo) getIntent().getSerializableExtra("HdmhMenuInfo");
        }
        init();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_permission_control;
    }

    private void init() {
        tvTitle.setText("项目基本信息");
        ProjectInfo projectInfo = SettingManager.getInstance().getProject();
        List<Map<String,Object>> hdmhMenuInfos = new ArrayList() {{
            add(new HashMap() {{ put("title", "项目名称:");put("content", projectInfo.getProjectName());}});
            add(new HashMap() {{ put("title", "项目代码:");put("content", projectInfo.getProjectCode());}});
            add(new HashMap() {{ put("title", "项目负责人:");put("content", projectInfo.getProjectEngName());}});
            add(new HashMap() {{ put("title", "监理范围对应概算额:");put("content", projectInfo.getApproximateSum());}});
            add(new HashMap() {{ put("title", "监理合同签约价:");put("content", projectInfo.getSupConFee());}});
            add(new HashMap() {{ put("title", "建设单位:");put("content", projectInfo.getOwnerName());}});
            add(new HashMap() {{ put("title", "业务分类:");put("content", projectInfo.getPjType());}});
            add(new HashMap() {{ put("title", "飞行区指标:");put("content", projectInfo.getFlyCode());}});
            add(new HashMap() {{ put("title", "项目建设内容:");put("content", projectInfo.getProjectOrientation());}});
        }};
//        @{@"项目名称：":[NSString stringWithFormat:@"%@",projectInfo[@"projectName"]]},
//        @{@"项目代码：":[NSString stringWithFormat:@"%@",projectInfo[@"projectCode"]]},
//        @{@"项目负责人：":[NSString stringWithFormat:@"%@",projectInfo[@"projectEngName"]]},
//        @{@"监理范围对应概算额：":[NSString stringWithFormat:@"%@",projectInfo[@"approximateSum"]]},
//        @{@"监理合同签约价（万元）：":[NSString stringWithFormat:@"%@",projectInfo[@"supConFee"]]},
//        @{@"建设单位：":[NSString stringWithFormat:@"%@",projectInfo[@"ownerName"]]},
//        @{@"业务分类：":[NSString stringWithFormat:@"%@",projectInfo[@"pjType"]]},
//        @{@"飞行区指标：":[NSString stringWithFormat:@"%@",projectInfo[@"flyCode"]]},
//        @{@"项目建设内容：":[NSString stringWithFormat:@"%@",projectInfo[@"projectOrientation"]]}


        if (mRecyclerView.getLayoutManager() == null) {
//            GridLayoutManager layoutManager = new GridLayoutManager(this, 3);
//            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
//            mRecyclerView.setLayoutManager(layoutManager);
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            mRecyclerView.setLayoutManager(manager);
        }
        HdmhProjectInfoAdapter permissionTypeAdapter = new HdmhProjectInfoAdapter(R.layout.cell_label_mult, hdmhMenuInfos);
        mRecyclerView.setAdapter(permissionTypeAdapter);
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @OnClick(R.id.iv_action)
    public void rightClick() {
        Intent intent = new Intent();
        intent.setClass(this, ProjectChoseActivity.class);
        startActivityForResult(intent, Constant.REQUEST_CODE_CHANGE_PEOJECT);
    }

    public class HdmhProjectInfoAdapter extends BaseQuickAdapter<Map<String,Object>, BaseViewHolder> {

        public HdmhProjectInfoAdapter(int layoutResId, @Nullable @org.jetbrains.annotations.Nullable List<Map<String,
                Object>> data) {
            super(layoutResId, data);
        }

        @Override
        protected void convert(@NonNull @NotNull BaseViewHolder helper, Map<String,Object> item) {
            TextView tvTitle = helper.itemView.findViewById(R.id.tv_label_title);
            TextView tvContent = helper.itemView.findViewById(R.id.tv_view_content_mult);
            tvTitle.setText(String.format("%s", item.get("title")));
            tvContent.setText(String.format("%s", item.get("content")));
        }

        @Override
        public void onBindViewHolder(BaseViewHolder holder, int position) {
            super.onBindViewHolder(holder, position);
            ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
            layoutParams.height = LinearLayout.LayoutParams.WRAP_CONTENT;

        }
    }
}
