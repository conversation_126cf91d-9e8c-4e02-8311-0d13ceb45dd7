package com.kisoft.yuejianli.hdmh;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.api.NetworkResponse3;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.entity.httpresult.ProcessHttpResult;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.GsonUtil;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.views.ApplyActivity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/5/28.
 */
public class HdmhPublicApplyFormViewListActivity extends BaseActivity implements BaseRefreshListener {

    //    @BindView(R.id.iv_back)
//    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    //    @BindView(R.id.tool_bar)
//    Toolbar toolBar;
    @BindView(R.id.mRecyclerView)
    RecyclerView mRecyclerView;
    @BindView(R.id.prLayout)
    PullToRefreshLayout prLayout;

//    private boolean mXmkn = false;
//    private String selectType;

    private View empotyView;
    private ApplyFormViewListAdapter mAdapter;
    private List<ProcessListBean> mList = new ArrayList<>();
    HdmhMenuInfo mPermission;
    private List<Map<String, Object>> mData = new ArrayList<>();

    private int page = 1;
    private int pageSize = 20;
    private int count = 0;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    private String mListUrl;

    public static void launch(Activity activity, HdmhMenuInfo item) {
        Intent intent = new Intent(activity, HdmhPublicApplyFormViewListActivity.class);
        intent.putExtra("HdmhMenuInfo", item);
        activity.startActivity(intent);
    }


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getIntent() != null) {
            mPermission = (HdmhMenuInfo) getIntent().getSerializableExtra("HdmhMenuInfo");
        }
        initData();
        initView();
    }


    private void initView() {
        tvSubmit.setText("新增");
        tvSubmit.setVisibility(View.VISIBLE);
        // 周报
//        if (StringUtil.isEqual(mPermission.getButtonHtmlContent(),"T_PROD_WEEKLY_REPORT")){
//            tvSubmit.setVisibility(View.GONE);
//        }
//        // 项目季度、年终考评
//        if (StringUtil.isEqual(mPermission.getButtonHtmlContent(),"T_PROJ_ASSESSMENT_SCORE")){
//            tvSubmit.setVisibility(View.GONE);
//        }

        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        if (mRecyclerView.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            mRecyclerView.setLayoutManager(manager);
        }
        mAdapter = new ApplyFormViewListAdapter(mList);
        mAdapter.setEmptyView(empotyView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                goDetail(mList.get(position), false);
            }
        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                mRecyclerView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        page++;
//                        if (mList.size() >= pageSize) {
//                            mAdapter.loadMoreEnd();
//                        } else {
//                        }
                        getData2();
                    }
                }, 1000);
            }
        }, mRecyclerView);
        mRecyclerView.setAdapter(mAdapter);
        prLayout.setRefreshListener(this);
        refresh();
    }


    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        mListUrl =
                HdmhPageConfigData.getPageData(mPermission.getBusinessType()).get("listUrl").toString();
        if (StringUtil.isEmpty(mListUrl)){
            mListUrl = HdmhPageConfigData.getPageData(mPermission.getBusinessType(), mPermission.getTitle()).get(
                    "listUrl").toString();
        }
        tvTitle.setText(mPermission.getTitle());
    }


    @Override
    public int getLayoutId() {
        return R.layout.activity_project_scene_list;
    }


    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }


    @OnClick(R.id.tv_submit)
    public void add() {
        Intent intent = new Intent(mContext, ApplyActivity.class);
        intent.putExtra("businessType", mPermission.getBusinessType());
        intent.putExtra("title", mPermission.getTitle());
        startActivity(intent);
//        goDetail(null, true);
    }


    private void goDetail(ProcessListBean item, boolean isAdd) {
        Intent intent = new Intent(mContext, ApplyActivity.class);
        intent.putExtra("isApply", false);
        intent.putExtra("title", mPermission.getTitle());
        intent.putExtra("bean", item);
        intent.putExtra("workType", "3");
        intent.putExtra("businessType", item.getBusinessType());
        intent.putExtra("flowStateName", item.getFlowStateName());
        startActivity(intent);
    }


    @Override
    public void refresh() {
        page = 1;
        count = 0;
        getData2();
    }


    @Override
    public void loadMore() {
        page++;
        getData2();
    }


    private void getData1() {
        String p = Integer.toString(page);
        String z = Integer.toString(pageSize);
        //workType类型（1=待办、2=已办、3=我发起的）
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("workType", "3");
//        pramaras.put("wfName", wfName);
        pramaras.put("userId", userInfo.getId());
        pramaras.put("projectId", projectInfo.getProjectId());

        pramaras.put("businessType", mPermission.getBusinessType());
        pramaras.put("count", 0);
        pramaras.put("page", p);
        pramaras.put("pageSize", z);
        Api.getGbkApiserver().getProcessList(Constant.HTTP_GET_PROCESS_LIST1, pramaras).enqueue(new Callback<NetworkResponse<ProcessHttpResult>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ProcessHttpResult>> call,
                                   Response<NetworkResponse<ProcessHttpResult>> response) {
                if (page == 1){
                    mList.clear();
                }
                count = response.body().getData().getCount();
                mList.addAll(response.body().getData().getList());
                if (response.body().getData().getList().size() < pageSize) {
                    mAdapter.loadMoreEnd();
                }
                prLayout.finishRefresh();
                prLayout.finishLoadMore();
                mAdapter.notifyDataSetChanged();
                System.out.println(response);
            }

            @Override
            public void onFailure(Call<NetworkResponse<ProcessHttpResult>> call, Throwable throwable) {

            }
        });
    }


    private void getData2() {
        String p = Integer.toString(page);
        String z = Integer.toString(pageSize);
        Map<String, Object> pram = new HashMap<>();
        pram.put("projectId", projectInfo.getProjectId());
        pram.put("page", p);
        pram.put("limit", z);
        pram.put("deptId", userInfo.getFather());
        pram.put("hasFlow", "true");
        String url = SettingManager.getInstance().getBaseUrl() + mListUrl;
//        String url = SettingManager.getInstance().getBaseUrl() + "/roa/OaQualityEvaluationAction.do?method=getOaQualityEvaluationList";
//http://192.168.2.101:8089/kaiyue/pds/SafetySupervisionNoticeAction.do?method=getOaQualityEvaluationList
        OkHttpRequestManager.getInstance().post(url, pram, new IRequestCallback() {
            @Override
            public void onSuccess(String response) {
                prLayout.finishRefresh();
                prLayout.finishLoadMore();
                Log.i("TAG", "onSuccess: " + response);
                try {
                    NetworkResponse3 response2 = GsonUtil.GsonToBean(response, NetworkResponse3.class);
                    NetworkResponse3.NetworkResponse3Data data = response2.getData();
                    if (page == 1){
                        mList.clear();
                    }
                    count = data.getTotal();
                    mList.addAll(data.getData());
                    if (data.getData().size() < pageSize) {
                        mAdapter.loadMoreEnd();
                    }
                    prLayout.finishRefresh();
                    prLayout.finishLoadMore();
                    mAdapter.notifyDataSetChanged();
                    System.out.println(response);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(Throwable throwable) {
                throwable.printStackTrace();
                prLayout.finishRefresh();
                prLayout.finishLoadMore();
            }
        });
    }


    // 信息管理
    public class ApplyFormViewListAdapter extends BaseQuickAdapter<ProcessListBean, BaseViewHolder> {
        public ApplyFormViewListAdapter(List<ProcessListBean> data) {
            super(R.layout.item_project_sup_plan, data);
        }

        @Override
        protected void convert(BaseViewHolder helper, ProcessListBean item) {
            TextView tvPlanTitle = helper.itemView.findViewById(R.id.tv_plan_title);
            TextView tvCharger = helper.itemView.findViewById(R.id.tv_charger);
            try {
                tvPlanTitle.setText(item.getName());
                tvCharger.setText(item.getDraftMan());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}