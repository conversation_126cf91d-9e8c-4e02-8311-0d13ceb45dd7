package com.kisoft.yuejianli.hdmh;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.AssetManager;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.GsonUtil;
import com.kisoft.yuejianli.utils.PhoneUtil;
import com.kisoft.yuejianli.views.ProjectChoseActivity;

import org.jetbrains.annotations.NotNull;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 权限控制显示菜单
 */
public class HdmhSupervisionControlActivity extends BaseActivity implements BaseQuickAdapter.OnItemClickListener {

    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.tool_bar)
    Toolbar toolBar;
    @BindView(R.id.mRecyclerView)
    RecyclerView mRecyclerView;
    private String mType;

    public static void launch(Activity activity) {
        Intent intent = new Intent(activity, HdmhSupervisionControlActivity.class);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        init();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_permission_control;
    }

    private void init() {

        String jsonData = getJson(this, "hdmh.json");
        //        解决 泛型<T>不能强转为List
        List<Map<String, Object>> maps = GsonUtil.GsonToListMaps(jsonData);
        List<HdmhMenuInfo> hdmhMenuInfos = GsonUtil.jsonToList(jsonData, HdmhMenuInfo.class);

        if (mRecyclerView.getLayoutManager() == null) {
            GridLayoutManager layoutManager = new GridLayoutManager(this, 3);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            mRecyclerView.setLayoutManager(layoutManager);
        }
        tvTitle.setPadding(0, 0, PhoneUtil.dpTopx(this, 50), 0);
        if (SettingManager.getInstance().getProject() != null) {
            tvTitle.setText(SettingManager.getInstance().getProject().getProjectName());
        }
        ivAction.setVisibility(View.VISIBLE);
        ivAction.setImageResource(R.drawable.ic_project_list);

        HdmhMenuInfoAdapter permissionTypeAdapter = new HdmhMenuInfoAdapter(R.layout.item_project_content,
                hdmhMenuInfos);
        mRecyclerView.setAdapter(permissionTypeAdapter);
        permissionTypeAdapter.setOnItemClickListener(this);
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @OnClick(R.id.iv_action)
    public void rightClick() {
        Intent intent = new Intent();
        intent.setClass(this, ProjectChoseActivity.class);
        startActivityForResult(intent, Constant.REQUEST_CODE_CHANGE_PEOJECT);
    }

    @Override
    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
        Object object = adapter.getData().get(position);
        if (object instanceof HdmhMenuInfo) {
            HdmhMenuInfo info = (HdmhMenuInfo) object;
            List<HdmhMenuInfo> childList = info.getChildList();
            HdmhSupervisionMenuListActivity.launch(HdmhSupervisionControlActivity.this,childList,info.getTitle());

//            AppUtils.launchActivity(this, appPermission.getMenuHrefAndroid(),
//                    appPermission.getButtonHtmlContentAndroid());
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Constant.REQUEST_CODE_CHANGE_PEOJECT) {
            tvTitle.setText(SettingManager.getInstance().getProject().getProjectName());
        }
    }


    private String getJson(Context context, String fileName) {
        StringBuffer stringBuffer = new StringBuffer();
        try {
            AssetManager assetsManager = context.getAssets();
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(assetsManager.open(fileName)));
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuffer.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return stringBuffer.toString();
    }




    public class HdmhMenuInfoAdapter extends BaseQuickAdapter<HdmhMenuInfo, BaseViewHolder> {

        public HdmhMenuInfoAdapter(int layoutResId,
                                   @Nullable @org.jetbrains.annotations.Nullable List<HdmhMenuInfo> data) {
            super(layoutResId, data);
        }

        @Override
        protected void convert(@NonNull @NotNull BaseViewHolder helper, HdmhMenuInfo item) {
            TextView tvContent = helper.itemView.findViewById(R.id.tv_content);
            Drawable contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.ic_customer_manager);
            tvContent.setText(item.getTitle());
            contentIc.setBounds(0, 0, contentIc.getMinimumWidth(), contentIc.getMinimumHeight());
            tvContent.setCompoundDrawables(null, contentIc, null, null);
        }
    }
}
