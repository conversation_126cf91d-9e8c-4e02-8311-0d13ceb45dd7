package com.kisoft.yuejianli.hdmh;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse2;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.GsonUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * Created by tudou on 2018/5/28.
 */
public class HdmhPublicViewListActivity extends BaseActivity implements BaseRefreshListener {

    //    @BindView(R.id.iv_back)
//    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    //    @BindView(R.id.tool_bar)
//    Toolbar toolBar;
    @BindView(R.id.mRecyclerView)
    RecyclerView mRecyclerView;
    @BindView(R.id.prLayout)
    PullToRefreshLayout prLayout;

//    private boolean mXmkn = false;
//    private String selectType;

    private View empotyView;
    private HdmhPublicViewListActivity.ProjectSceneListAdapter mAdapter;
    private List<Map<String, Object>> mList = new ArrayList<>();
    HdmhMenuInfo mPermission;
    private List<Map<String, Object>> mData = new ArrayList<>();

    private int page = 1;
    private int pageSize = 20;
    private int total = 0;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    private String mListUrl;

    public static void launch(Activity activity, HdmhMenuInfo item) {
        Intent intent = new Intent(activity, HdmhPublicViewListActivity.class);
        intent.putExtra("HdmhMenuInfo", item);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getIntent() != null) {
            mPermission = (HdmhMenuInfo) getIntent().getSerializableExtra("HdmhMenuInfo");
        }
        initData();
        initView();
    }

    private void initView() {
        tvSubmit.setText("新增");
        tvSubmit.setVisibility(View.VISIBLE);
        // 周报
//        if (StringUtil.isEqual(mPermission.getButtonHtmlContent(),"T_PROD_WEEKLY_REPORT")){
//            tvSubmit.setVisibility(View.GONE);
//        }
//        // 项目季度、年终考评
//        if (StringUtil.isEqual(mPermission.getButtonHtmlContent(),"T_PROJ_ASSESSMENT_SCORE")){
//            tvSubmit.setVisibility(View.GONE);
//        }

        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        if (mRecyclerView.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            mRecyclerView.setLayoutManager(manager);
        }
        mAdapter = new ProjectSceneListAdapter(mList);
        mAdapter.setEmptyView(empotyView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                goDetail(mList.get(position), false);
            }
        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                mRecyclerView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        page++;
                        if (mList.size() >= total) {
                            mAdapter.loadMoreEnd();
                        } else {
                            getData();
                        }
                    }
                }, 1000);
            }
        }, mRecyclerView);
        mRecyclerView.setAdapter(mAdapter);
        prLayout.setRefreshListener(this);
        refresh();
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        mListUrl =
                HdmhPageConfigData.getPageData(mPermission.getBusinessType()).get("listUrl").toString();
        tvTitle.setText(mPermission.getTitle());
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_project_scene_list;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @OnClick(R.id.tv_submit)
    public void add() {
        goDetail(null, true);
    }

    private void goDetail(Map<String, Object> map, boolean isAdd) {

        boolean file2 = (boolean) HdmhPageConfigData.getPageData(mPermission.getBusinessType()).get("file2");
        boolean isApply = (boolean) HdmhPageConfigData.getPageData(mPermission.getBusinessType()).get("isApply");
        if (isApply) {
            HdmhPublicApplyFormViewListActivity.launch(this,mPermission);
        }else if (file2) {
            Intent intent = new Intent();
            intent.setClass(this, HdmhPublicViewDetailFile2Activity.class);
            intent.putExtra("isAdd", isAdd);
            intent.putExtra("title", mPermission.getTitle());
            intent.putExtra("btype", mPermission.getBusinessType());
            if (!isAdd) {
                intent.putExtra("HdmhViewDetailActivityData", (Serializable) map);
                intent.putExtra("bId", map.get("id").toString());
            }
            startActivity(intent);
        } else {
            Intent intent = new Intent();
            intent.setClass(this, HdmhPublicViewDetailActivity.class);
            intent.putExtra("isAdd", isAdd);
            intent.putExtra("btype", mPermission.getBusinessType());
            intent.putExtra("title", mPermission.getTitle());
            if (!isAdd) {
                intent.putExtra("HdmhViewDetailActivityData", (Serializable) map);
                intent.putExtra("bId", map.get("id").toString());
            }
            startActivity(intent);
        }
    }

    @Override
    public void refresh() {
        page = 1;
        getData();
    }

    @Override
    public void loadMore() {
        page++;
        getData();
    }

    private void getData() {
        String p = Integer.toString(page);
        String z = Integer.toString(pageSize);
        Map<String, Object> pram = new HashMap<>();
        pram.put("projectId", projectInfo.getProjectId());
        pram.put("page", p);
        pram.put("limit", z);
        pram.put("deptId", userInfo.getFather());
        String url = SettingManager.getInstance().getBaseUrl() + mListUrl;
//http://192.168.2.101:8089/kaiyue/pds/SafetySupervisionNoticeAction.do?method=getSafetySupervisionNoticeList
        OkHttpRequestManager.getInstance().post(url, pram, new IRequestCallback() {
            @Override
            public void onSuccess(String response) {
                prLayout.finishRefresh();
                prLayout.finishLoadMore();
                Log.i("TAG", "onSuccess: " + response);
//                Map<String, Object> map = GsonUtil.GsonToMaps(response);
//                Map<String, Object> map1 = (Map<String, Object>) map.get("data");
//                mList = (List<Map<String, Object>>) map1.get("data");
                try {
                    NetworkResponse2 response2 = GsonUtil.GsonToBean(response, NetworkResponse2.class);
                    NetworkResponse2.NetworkResponse2Data data = response2.getData();
                    total = data.getTotal();
                    if (page == 1) {
                        mList = data.getData();
                    } else {
                        mList.addAll(data.getData());
                    }
                    mAdapter.setNewData(mList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(Throwable throwable) {
                throwable.printStackTrace();
                prLayout.finishRefresh();
                prLayout.finishLoadMore();
            }
        });
    }


    // 信息管理
    public class ProjectSceneListAdapter extends BaseQuickAdapter<Map<String, Object>, BaseViewHolder> {
        public ProjectSceneListAdapter(List<Map<String, Object>> data) {
            super(R.layout.item_project_sup_plan, data);
        }

        @Override
        protected void convert(BaseViewHolder helper, Map item) {

            TextView tvPlanTitle = helper.itemView.findViewById(R.id.tv_plan_title);
            TextView tvCharger = helper.itemView.findViewById(R.id.tv_charger);
            try {
                tvPlanTitle.setText(mPermission.getTitle());
                tvCharger.setText(item.get("createName").toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}