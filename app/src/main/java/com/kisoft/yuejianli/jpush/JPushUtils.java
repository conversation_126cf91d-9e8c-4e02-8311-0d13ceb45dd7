package com.kisoft.yuejianli.jpush;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.widget.Toast;

import com.kisoft.yuejianli.YueApplacation;

import java.util.LinkedHashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.jpush.android.api.JPushInterface;
import cn.jpush.android.api.TagAliasCallback;

/**
 * Created by bhd on 17/5/2.
 * QQ:602394773
 */
public class JPushUtils implements Handler.Callback {
    public static final String TAG = "JPushUtils";
    private Handler handler = new Handler(this);
    private static JPushUtils instance = null;
    public static final int MSG_SET_TAGS = 1;
    public static final int MSG_SET_ALIAS = 2;

    private JPushUtils() {
    }

    public static JPushUtils getInstance() {
        synchronized (JPushUtils.class) {
            if (null == instance) {
                instance = new JPushUtils();
            }
        }
        return instance;
    }

    public void setTag(Context mContent, String tag) {
        // 检查 tag 的有效性
        if (isNullOrEmpty(tag)) {
            Toast.makeText(mContent, "tag不能为空", Toast.LENGTH_SHORT).show();
            return;
        }

        // ","隔开的多个 转换成 Set
        String[] sArray = tag.split(",");
        Set<String> tagSet = new LinkedHashSet<String>();
        for (String sTagItme : sArray) {
            if (!isValidTagAndAlias(sTagItme)) {
                Toast.makeText(mContent, "格式不对", Toast.LENGTH_SHORT).show();
                return;
            }
            tagSet.add(sTagItme);
        }

        //调用JPush API设置Tag
        handler.sendMessage(handler.obtainMessage(MSG_SET_TAGS, tagSet));

    }

    public void setAlias(String alias){

        //调用JPush API设置alias
        handler.sendMessage(handler.obtainMessage(MSG_SET_ALIAS, alias));
    }

    public Set<String> getTag() {
        String tag = "user";

        // ","隔开的多个 转换成 Set
        String[] sArray = tag.split(",");
        Set<String> tagSet = new LinkedHashSet<String>();
        for (String sTagItme : sArray) {
            tagSet.add(sTagItme);
        }
        return tagSet;
    }

    private final TagAliasCallback mTagsCallback = new TagAliasCallback() {

        @Override
        public void gotResult(int code, String alias, Set<String> tags) {
            String logs;
            switch (code) {
                case 0:
                    logs = "Set tag and alias success";
                    Log.i(TAG, logs);
                    break;

                case 6002:
                    logs = "Failed to set alias and tags due to timeout. Try again after 60s.";
                    Log.i(TAG, logs);
                    if (isConnected(YueApplacation.getInstance())) {
                        handler.sendMessageDelayed(handler.obtainMessage(MSG_SET_TAGS, tags), 1000 * 60);
                    } else {
                        Log.i(TAG, "No network");
                    }
                    break;

                default:
                    logs = "Failed with errorCode = " + code;
                    Log.e(TAG, logs);
            }
        }

    };

    /**
     * 方法名：clearTag()
     * 功　能：取消之前设置的alias和tag值
     * 参　数：无
     * 返回值：无
     */
    public void clearAliasAndTags(Context mContent){
        Set<String> strs = new LinkedHashSet<>();
        strs.clear();
        JPushInterface.setAliasAndTags(mContent.getApplicationContext(), "", strs, mTagsCallback);
    }

    @Override
    public boolean handleMessage(Message msg) {
        switch (msg.what) {
            case MSG_SET_TAGS:
                Log.d(TAG, "Set tags in handler.");
                JPushInterface.setAliasAndTags(YueApplacation.getInstance(), null, (Set<String>) msg.obj, mTagsCallback);
                break;
            case MSG_SET_ALIAS:
                JPushInterface.setAliasAndTags(YueApplacation.getInstance(), (String) msg.obj, getTag(), mTagsCallback);
                break;
        }
        return false;
    }

    public boolean isNullOrEmpty(String s){
        if (null == s || "".equals(s) || "null".equals(s))
            return true;
        return false;
    }

    // 校验Tag Alias 只能是数字,英文字母和中文
    public boolean isValidTagAndAlias(String s) {
        Pattern p = Pattern.compile("^[\u4E00-\u9FA50-9a-zA-Z_!@#$&*+=.|]+$");
        Matcher m = p.matcher(s);
        return m.matches();
    }

    public boolean isConnected(Context context) {
        ConnectivityManager conn = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo info = conn.getActiveNetworkInfo();
        return (info != null && info.isConnected());
    }
}
