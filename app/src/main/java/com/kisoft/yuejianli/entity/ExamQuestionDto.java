package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * 单个题内容
 */
public class ExamQuestionDto extends Base {
    private String eqId;        //主键
    private String eqbId;       //所属题库Id
    private String qustName;        //题目名称
    private String diffDegree;      //难易程度 0=简单、1=一般、2=难、3=较难、4=困难
    private String qustType;        //试题类型 0=单选题、1=多选题、2=判断、3=填空、4=简答
    private String answer;      //标准答案 答案用@隔开(@)
    private String points;      //知识点
    private String answerAnalsis;       //答案解析
    private String scoreSet;    //分值设置
    private String taskAnswer;   //考试答案
    private String eqbName;   //所属题库名称

    public String getEqId() {
        return eqId;
    }

    public void setEqId(String eqId) {
        this.eqId = eqId;
    }

    public String getEqbId() {
        return eqbId;
    }

    public void setEqbId(String eqbId) {
        this.eqbId = eqbId;
    }

    public String getQustName() {
        return qustName;
    }

    public void setQustName(String qustName) {
        this.qustName = qustName;
    }

    public String getDiffDegree() {
        return diffDegree;
    }

    public void setDiffDegree(String diffDegree) {
        this.diffDegree = diffDegree;
    }

    public String getQustType() {
        return qustType;
    }

    public void setQustType(String qustType) {
        this.qustType = qustType;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public String getPoints() {
        return points;
    }

    public void setPoints(String points) {
        this.points = points;
    }

    public String getAnswerAnalsis() {
        return answerAnalsis;
    }

    public void setAnswerAnalsis(String answerAnalsis) {
        this.answerAnalsis = answerAnalsis;
    }

    public String getScoreSet() {
        return scoreSet;
    }

    public void setScoreSet(String scoreSet) {
        this.scoreSet = scoreSet;
    }

    public String getTaskAnswer() {
        return taskAnswer;
    }

    public void setTaskAnswer(String taskAnswer) {
        this.taskAnswer = taskAnswer;
    }

    public String getEqbName() {
        return eqbName;
    }

    public void setEqbName(String eqbName) {
        this.eqbName = eqbName;
    }
}
