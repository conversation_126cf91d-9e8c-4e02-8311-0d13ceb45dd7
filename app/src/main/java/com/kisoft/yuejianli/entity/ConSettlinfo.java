package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * 查询子合同列表(用于子合同结算选择)
 */
public class ConSettlinfo extends Base {
    private String csiId;         //主键
    private String ciId;          //所属主合同Id
    private String conName;       //所属主合同名称
    private String projectId;     //关联项目Id
    private String projectName;   //关联项目名称
    private String partyA;        //甲方
    private String partyB;        //乙方
    private String partyC;        //丙方
    private String subConName;    //子合同名称
    private String subConNo;      //子合同编号
    private String deptId;        //负责实施部门Id
    private String deptName;      //负责实施部门名称
    private String conType;       //子合同类型
    private String conAmount;     //合同金额
    private String conPeriod;     //合同工期
    private String bondFee;       //质保费率(%)
    private String perfBond;      //履约保证金
    private String moneyFlow;     //资金流向  0=流出、1=流入
    private String isSub;         //是否是专业分包  0=是、1=否
    private String isCross;       //是否是跨项目子合同  0=是、1=否
    private String leaseType;     //租赁类型
    private String beginDate;     //合同开始日期
    private String endDate;       //合同结束日期
    private String signDate;      //合同签订日期
    private String conState;      //合同状态  0=未开始、1=执行中、2=已结束
    private String conContent;    //合同内容
    private String conAbstract;   //摘要
    private String payRemark;     //付款约定
    private String otherTerms;    //其他条款
    private String remark;        //备注
    private String createId;      //创建人Id
    private String createName;    //创建人姓名
    private String createTime;    //创建时间
    private String wfId;          //流程Id
    private String wfStatus;      //流程状态  1=起草、2=审批中、4=已审批、9=已终止//满足查询之用
    private String signDate1;       //签约日期
    private String changetpye;      //选择属性
    private String cpmAmountTotal;  //累计收款项
    private String decAmountTotal;  //累计扣款项
    private String ticketAmountTotal; //成本票累计缴纳金额
    private String balanceAmount;//合同余额
    private String invoiceAmount;//开票金额
    private String postProjectAmount;//结项金额
    private String sum;//合同数量
    private String workFlowType;
    private String wfTaskId;
    private String wfTaskState;
    private String isSendBack;

    public String getCsiId() {
        return csiId;
    }

    public void setCsiId(String csiId) {
        this.csiId = csiId;
    }

    public String getCiId() {
        return ciId;
    }

    public void setCiId(String ciId) {
        this.ciId = ciId;
    }

    public String getConName() {
        return conName;
    }

    public void setConName(String conName) {
        this.conName = conName;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getPartyA() {
        return partyA;
    }

    public void setPartyA(String partyA) {
        this.partyA = partyA;
    }

    public String getPartyB() {
        return partyB;
    }

    public void setPartyB(String partyB) {
        this.partyB = partyB;
    }

    public String getPartyC() {
        return partyC;
    }

    public void setPartyC(String partyC) {
        this.partyC = partyC;
    }

    public String getSubConName() {
        return subConName;
    }

    public void setSubConName(String subConName) {
        this.subConName = subConName;
    }

    public String getSubConNo() {
        return subConNo;
    }

    public void setSubConNo(String subConNo) {
        this.subConNo = subConNo;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getConType() {
        return conType;
    }

    public void setConType(String conType) {
        this.conType = conType;
    }

    public String getConAmount() {
        return conAmount;
    }

    public void setConAmount(String conAmount) {
        this.conAmount = conAmount;
    }

    public String getConPeriod() {
        return conPeriod;
    }

    public void setConPeriod(String conPeriod) {
        this.conPeriod = conPeriod;
    }

    public String getBondFee() {
        return bondFee;
    }

    public void setBondFee(String bondFee) {
        this.bondFee = bondFee;
    }

    public String getPerfBond() {
        return perfBond;
    }

    public void setPerfBond(String perfBond) {
        this.perfBond = perfBond;
    }

    public String getMoneyFlow() {
        return moneyFlow;
    }

    public void setMoneyFlow(String moneyFlow) {
        this.moneyFlow = moneyFlow;
    }

    public String getIsSub() {
        return isSub;
    }

    public void setIsSub(String isSub) {
        this.isSub = isSub;
    }

    public String getIsCross() {
        return isCross;
    }

    public void setIsCross(String isCross) {
        this.isCross = isCross;
    }

    public String getLeaseType() {
        return leaseType;
    }

    public void setLeaseType(String leaseType) {
        this.leaseType = leaseType;
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getSignDate() {
        return signDate;
    }

    public void setSignDate(String signDate) {
        this.signDate = signDate;
    }

    public String getConState() {
        return conState;
    }

    public void setConState(String conState) {
        this.conState = conState;
    }

    public String getConContent() {
        return conContent;
    }

    public void setConContent(String conContent) {
        this.conContent = conContent;
    }

    public String getConAbstract() {
        return conAbstract;
    }

    public void setConAbstract(String conAbstract) {
        this.conAbstract = conAbstract;
    }

    public String getPayRemark() {
        return payRemark;
    }

    public void setPayRemark(String payRemark) {
        this.payRemark = payRemark;
    }

    public String getOtherTerms() {
        return otherTerms;
    }

    public void setOtherTerms(String otherTerms) {
        this.otherTerms = otherTerms;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getWfId() {
        return wfId;
    }

    public void setWfId(String wfId) {
        this.wfId = wfId;
    }

    public String getWfStatus() {
        return wfStatus;
    }

    public void setWfStatus(String wfStatus) {
        this.wfStatus = wfStatus;
    }

    public String getSignDate1() {
        return signDate1;
    }

    public void setSignDate1(String signDate1) {
        this.signDate1 = signDate1;
    }

    public String getChangetpye() {
        return changetpye;
    }

    public void setChangetpye(String changetpye) {
        this.changetpye = changetpye;
    }

    public String getCpmAmountTotal() {
        return cpmAmountTotal;
    }

    public void setCpmAmountTotal(String cpmAmountTotal) {
        this.cpmAmountTotal = cpmAmountTotal;
    }

    public String getDecAmountTotal() {
        return decAmountTotal;
    }

    public void setDecAmountTotal(String decAmountTotal) {
        this.decAmountTotal = decAmountTotal;
    }

    public String getTicketAmountTotal() {
        return ticketAmountTotal;
    }

    public void setTicketAmountTotal(String ticketAmountTotal) {
        this.ticketAmountTotal = ticketAmountTotal;
    }

    public String getBalanceAmount() {
        return balanceAmount;
    }

    public void setBalanceAmount(String balanceAmount) {
        this.balanceAmount = balanceAmount;
    }

    public String getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(String invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public String getPostProjectAmount() {
        return postProjectAmount;
    }

    public void setPostProjectAmount(String postProjectAmount) {
        this.postProjectAmount = postProjectAmount;
    }

    public String getSum() {
        return sum;
    }

    public void setSum(String sum) {
        this.sum = sum;
    }

    public String getWorkFlowType() {
        return workFlowType;
    }

    public void setWorkFlowType(String workFlowType) {
        this.workFlowType = workFlowType;
    }

    public String getWfTaskId() {
        return wfTaskId;
    }

    public void setWfTaskId(String wfTaskId) {
        this.wfTaskId = wfTaskId;
    }

    public String getWfTaskState() {
        return wfTaskState;
    }

    public void setWfTaskState(String wfTaskState) {
        this.wfTaskState = wfTaskState;
    }

    public String getIsSendBack() {
        return isSendBack;
    }

    public void setIsSendBack(String isSendBack) {
        this.isSendBack = isSendBack;
    }
}
