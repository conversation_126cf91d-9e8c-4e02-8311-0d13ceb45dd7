package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by Administrator on 2019/4/15 0015.
 */

public class ApplyLeaveInfo extends Base {
    private String leavetitle;
    private String leavetype;
    private String start;
    private String end;
    private String cause;
    private String userId;
    private String username;
    private String leaveGuid;


    private String startTime;
    private String endTime;
    private String leaveDays;
    private String applyDate;


    private String   startTimeHour;
    private String   startTimeMin;
    private String   endTimeHour;
    private String   endTimeMin;

    public String getStartTimeHour() {
        return startTimeHour;
    }

    public void setStartTimeHour(String startTimeHour) {
        this.startTimeHour = startTimeHour;
    }

    public String getStartTimeMin() {
        return startTimeMin;
    }

    public void setStartTimeMin(String startTimeMin) {
        this.startTimeMin = startTimeMin;
    }

    public String getEndTimeHour() {
        return endTimeHour;
    }

    public void setEndTimeHour(String endTimeHour) {
        this.endTimeHour = endTimeHour;
    }

    public String getEndTimeMin() {
        return endTimeMin;
    }

    public void setEndTimeMin(String endTimeMin) {
        this.endTimeMin = endTimeMin;
    }

    public String getLeaveDays() {
        return leaveDays;
    }

    public void setLeaveDays(String leaveDays) {
        this.leaveDays = leaveDays;
    }

    public String getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(String applyDate) {
        this.applyDate = applyDate;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getLeavetitle() {
        return leavetitle;
    }

    public void setLeavetitle(String leavetitle) {
        this.leavetitle = leavetitle;
    }

    public String getLeavetype() {
        return leavetype;
    }

    public void setLeavetype(String leavetype) {
        this.leavetype = leavetype;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }

    public String getCause() {
        return cause;
    }

    public void setCause(String cause) {
        this.cause = cause;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getLeaveGuid() {
        return leaveGuid;
    }

    public void setLeaveGuid(String leaveGuid) {
        this.leaveGuid = leaveGuid;
    }

    public Map<String, Object> getParameters() {
        Map<String, Object> parameters = new HashMap();
        if (!StringUtil.isEmpty(leavetitle))
            parameters.put("leavetitle", leavetitle);
        if (!StringUtil.isEmpty(leavetype))
            parameters.put("leavetype", leavetype);

        if (!StringUtil.isEmpty(leaveGuid))
            parameters.put("leaveGuid", leaveGuid);
//开始日期
        if (!StringUtil.isEmpty(start))
            parameters.put("start", start);
//结束日期
        if (!StringUtil.isEmpty(end))
            parameters.put("end", end);


        if (!StringUtil.isEmpty(cause))
            parameters.put("cause", cause);
        if (!StringUtil.isEmpty(userId))
            parameters.put("userId", userId);
        if (!StringUtil.isEmpty(username))
            parameters.put("username", username);


        if (!StringUtil.isEmpty(startTime))
            parameters.put("startdate", startTime);

        if (!StringUtil.isEmpty(endTime))
            parameters.put("enddate", endTime);

        if (!StringUtil.isEmpty(leaveDays))
            parameters.put("leaveDays", leaveDays);
        if (!StringUtil.isEmpty(applyDate))
            parameters.put("applyDate", applyDate);

        if (!StringUtil.isEmpty(startTimeHour))
            parameters.put("startTimeHour", startTimeHour);
        if (!StringUtil.isEmpty(startTimeMin))
            parameters.put("startTimeMin", startTimeMin);
        if (!StringUtil.isEmpty(endTimeHour))
            parameters.put("endTimeHour", endTimeHour);
        if (!StringUtil.isEmpty(endTimeMin))
            parameters.put("endTimeMin", endTimeMin);


        return parameters;
    }
}
