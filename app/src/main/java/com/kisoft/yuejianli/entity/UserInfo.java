package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by tudo<PERSON> on 2018/3/12.
 */

public class UserInfo extends Base {


    /*
    {
    "code": 200,
    "message": "登录成功！",
    "data": {
        "faceStyle": "1",
        "companyId": "14110614345981208173d2ed01bac092",
        "dutyStartMin": "",
        "endTimeHou": "",
        "deptShortName": "",
        "objectId": "180714101327975c921eb0d133a8f1c9",
        "objectType": "p",
        "attendAddress": "锦业时代",
        "id": "141108105107950c9a6829c55192b626",
        "userOrgRelation": "180708184746954db1ba8eb1497291ed,141108105107950c9a6829c55192b626,1411061440351687889a58c11a8556ff,14110614345981208173d2ed01bac092,0,1807081849118445be8eee9c133a72d1",
        "thisOperationObject": "",
        "companyShortName": "公司总部机关",
        "fieldState": "0",
        "scheduleWay": "0",
        "indexStyle": "0",
        "effectRange": "500",
        "name": "赵纪锋",
        "workDay": "0,1,2,3,4,5,Z",
        "beginTimeMin": "",
        "objectName": "上海浦江机场扩建一期项目",
        "dutyStartStr": "20:00",
        "account": "zhaojifeng",
        "workFlexTimes": "15",
        "arName": "西安公司员工考勤A组",
        "workDayStr": "",
        "sysTitle": "是的范德萨",
        "endTimeStr": "19:00",
        "lateTimes": "25",
        "beginTimeHou": "",
        "father": "1411061440351687889a58c11a8556ff",
        "latAndLong": "108.877977,34.192921",
        "companyName": "公司总部机关",
        "arId": "181203111441232eb091c169b95272ff",
        "manageId": "110602113756546aa7cfa4f9374df22a",
        "dutyStartHou": "",
        "projectId": "1503091650451230a4dc972986e4c1c3",
        "userTitle": "",
        "absentTimes": "30",
        "artificialPerson": "",
        "endTimeMin": "",
        "manageName": "测试员02",
        "fatherName": "项目管理部",
        "beginTimeStr": "08:30"
    }
}

     */

    private String arId;
    private String password;
    private String avatar;


    private String id;
    private String account;             // 账号
    private String name;                // 姓名
    private String father;              // 上级领导id
    private String companyId;           // 公司id
    private String faceStyle;
    private String userOrgRelation;     // 用户机关关系
    private String projectId;
    private String fatherName;          // 上级领导名称
    private String companyName;         // 公司名称
    private String companyShortName;    // 公司缩写
    private String deptShortName;       // 部门id
    private String deptShortId;       // 部门id
    private String criticalTime;       // 缺勤时间限制
    private String artificialPerson;
    private String sysTitle = "";
    private String userTitle = "";      // 用户职位
    private String indexStyle;
    private String objectId;            // 项目id
    private String objectType;          // 项目类型
    private String objectName;          // 项目名称
    private String thisOperationObject;  // 是否是运营中的项目

    private String latAndLong; //经纬度
    private String effectRange; //有效范围
    private String beginTimeStr;  //上班时间
    private String endTimeStr;   //下班时间
    private String workFlexTimes; //上班弹性时间
    private String lateTimes; //严重迟到
    private String absentTimes; //旷工
    private String fieldState;   //允许外勤打卡

    private String scheduleWay;        //排班方式   // 0:固定四次班制 1:自由打卡 2：固定两次班制
    private String workDay;         //工作日
    private String userTel;         //手机端联系电话
    private String userPotoPath;    //用户图像路径
    private String beginTimeHou;    //上午上班时间小时
    private String beginTimeMin;    //上午上班时间分钟
    private String endTimeHou;    //上午下班时间小时
    private String endTimeMin;    //上午下班时间分钟
    private String beginTimeHou1;    //下午上班时间小时
    private String beginTimeMin1;    //下午上班时间分钟
    private String endTimeHou1;    //下午下班时间小时
    private String endTimeMin1;    //下午下班时间分钟

    private String postName;        // 岗位名称
    private String faceId;        // 人脸信息
    private boolean faceLogin;// 人脸登录
    private String meId;
    private String tenantId; // 租户id


    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public UserInfo() {

    }

    public String getArId() {
        return arId;
    }

    public void setArId(String arId) {
        this.arId = arId;
    }

    public String getFaceId() {
        return faceId;
    }

    public void setFaceId(String faceId) {
        this.faceId = faceId;
    }

    public boolean isFaceLogin() {
        return faceLogin;
    }

    public void setFaceLogin(boolean faceLogin) {
        this.faceLogin = faceLogin;
    }

    public String getMeId() {
        return meId;
    }

    public void setMeId(String meId) {
        this.meId = meId;
    }

    public String getBeginTimeHou() {
        return beginTimeHou;
    }

    public void setBeginTimeHou(String beginTimeHou) {
        this.beginTimeHou = beginTimeHou;
    }

    public String getBeginTimeMin() {
        return beginTimeMin;
    }

    public void setBeginTimeMin(String beginTimeMin) {
        this.beginTimeMin = beginTimeMin;
    }

    public String getEndTimeHou() {
        return endTimeHou;
    }

    public void setEndTimeHou(String endTimeHou) {
        this.endTimeHou = endTimeHou;
    }

    public String getEndTimeMin() {
        return endTimeMin;
    }

    public void setEndTimeMin(String endTimeMin) {
        this.endTimeMin = endTimeMin;
    }

    public String getBeginTimeHou1() {
        return beginTimeHou1;
    }

    public void setBeginTimeHou1(String beginTimeHou1) {
        this.beginTimeHou1 = beginTimeHou1;
    }

    public String getBeginTimeMin1() {
        return beginTimeMin1;
    }

    public void setBeginTimeMin1(String beginTimeMin1) {
        this.beginTimeMin1 = beginTimeMin1;
    }

    public String getEndTimeHou1() {
        return endTimeHou1;
    }

    public void setEndTimeHou1(String endTimeHou1) {
        this.endTimeHou1 = endTimeHou1;
    }

    public String getEndTimeMin1() {
        return endTimeMin1;
    }

    public void setEndTimeMin1(String endTimeMin1) {
        this.endTimeMin1 = endTimeMin1;
    }

    public String getScheduleWay() {
        return scheduleWay;
    }

    public void setScheduleWay(String scheduleWay) {
        this.scheduleWay = scheduleWay;
    }

    public String getCriticalTime() {
        return criticalTime;
    }

    public void setCriticalTime(String criticalTime) {
        this.criticalTime = criticalTime;
    }

    public String getWorkDay() {
        return workDay;
    }

    public void setWorkDay(String workDay) {
        this.workDay = workDay;
    }

    public String getFieldState() {
        return fieldState;
    }

    public void setFieldState(String fieldState) {
        this.fieldState = fieldState;
    }

    public String getBeginTimeStr() {
        return beginTimeStr;
    }

    public void setBeginTimeStr(String beginTimeStr) {
        this.beginTimeStr = beginTimeStr;
    }

    public String getEndTimeStr() {
        return endTimeStr;
    }

    public void setEndTimeStr(String endTimeStr) {
        this.endTimeStr = endTimeStr;
    }

    public String getWorkFlexTimes() {
        return workFlexTimes;
    }

    public void setWorkFlexTimes(String workFlexTimes) {
        this.workFlexTimes = workFlexTimes;
    }

    public String getLateTimes() {
        return lateTimes;
    }

    public void setLateTimes(String lateTimes) {
        this.lateTimes = lateTimes;
    }

    public String getAbsentTimes() {
        return absentTimes;
    }

    public void setAbsentTimes(String absentTimes) {
        this.absentTimes = absentTimes;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFather() {
        return father;
    }

    public void setFather(String father) {
        this.father = father;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getFaceStyle() {
        return faceStyle;
    }

    public void setFaceStyle(String faceStyle) {
        this.faceStyle = faceStyle;
    }

    public String getUserOrgRelation() {
        return userOrgRelation;
    }

    public void setUserOrgRelation(String userOrgRelation) {
        this.userOrgRelation = userOrgRelation;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getFatherName() {
        return fatherName;
    }

    public void setFatherName(String fatherName) {
        this.fatherName = fatherName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyShortName() {
        return companyShortName;
    }

    public void setCompanyShortName(String companyShortName) {
        this.companyShortName = companyShortName;
    }

    public String getDeptShortName() {
        return deptShortName;
    }

    public void setDeptShortName(String deptShortName) {
        this.deptShortName = deptShortName;
    }

    public String getArtificialPerson() {
        return artificialPerson;
    }

    public void setArtificialPerson(String artificialPerson) {
        this.artificialPerson = artificialPerson;
    }

    public String getSysTitle() {
        return sysTitle;
    }

    public void setSysTitle(String sysTitle) {
        this.sysTitle = sysTitle;
    }

    public String getUserTitle() {
        return userTitle;
    }

    public void setUserTitle(String userTitle) {
        this.userTitle = userTitle;
    }

    public String getIndexStyle() {
        return indexStyle;
    }

    public void setIndexStyle(String indexStyle) {
        this.indexStyle = indexStyle;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getObjectType() {
        return objectType;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType;
    }

    public String getObjectName() {
        return objectName;
    }

    public void setObjectName(String objectName) {
        this.objectName = objectName;
    }

    public String getThisOperationObject() {
        return thisOperationObject;
    }

    public void setThisOperationObject(String thisOperationObject) {
        this.thisOperationObject = thisOperationObject;
    }


    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getLatAndLong() {
        return latAndLong;
    }

    public void setLatAndLong(String latAndLong) {
        this.latAndLong = latAndLong;
    }

    public String getEffectRange() {
        return effectRange;
    }

    public void setEffectRange(String effectRange) {
        this.effectRange = effectRange;
    }

    public String getUserTel() {
        return userTel;
    }

    public void setUserTel(String userTel) {
        this.userTel = userTel;
    }

    public String getUserPotoPath() {
        return userPotoPath;
    }

    public void setUserPotoPath(String userPotoPath) {
        this.userPotoPath = userPotoPath;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getDeptShortId() {
        return deptShortId;
    }

    public void setDeptShortId(String deptShortId) {
        this.deptShortId = deptShortId;
    }
}
