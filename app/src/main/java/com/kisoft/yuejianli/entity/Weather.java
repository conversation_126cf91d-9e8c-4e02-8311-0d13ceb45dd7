package com.kisoft.yuejianli.entity;

import java.io.Serializable;
import java.util.List;

/**
 * Created by Administrator on 2019/4/15 0015.
 */

public class Weather implements Serializable {
    private List<Future> future;

    public List<Future> getFuture() {
        return future;
    }

    public void setFuture(List<Future> future) {
        this.future = future;
    }

    public static class Future implements Serializable{
        private String weather;
        private String direct;
        private String temperature;

        public String getTemperature() {
            return temperature;
        }

        public void setTemperature(String temperature) {
            this.temperature = temperature;
        }

        public String getWeather() {
            return weather;
        }

        public void setWeather(String weather) {
            this.weather = weather;
        }

        public String getDirect() {
            return direct;
        }

        public void setDirect(String direct) {
            this.direct = direct;
        }
    }
}
