package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by t<PERSON><PERSON> on 2018/6/22.
 * 监理规划
 */
public class ProjectFilingSupPlan extends Base {

    public static final String PLAN_TYPE_A = "a";    // 监理规划
    public static final String PLAN_TYPE_B = "b";    // 安全监理规划
    public static final String PLAN_TYPE_C = "c";    // 监理旁站方案
    public static final String PLAN_TYPE_D = "d";    // 见证取样方案

    public static final String PLAN_TYPE_A_NAME = "监理规划";
    public static final String PLAN_TYPE_B_NAME = "安全监理规划";
    public static final String PLAN_TYPE_C_NAME = "监理旁站方案";
    public static final String PLAN_TYPE_D_NAME = "见证取样方案";

    private String id;

    private String projectId;
    private String projectName;
    private String spSubject;                        // 主题（专项， 工序）
    private String spContent;                        // 内容
    private String supDirectorId;                    // 负责人id
    private String supDirectorName;                  // 负责人name
    private String verifierId;                       // 审核人id
    private String verifierName;                     // 审核人name
    private String verifyState;                      // 审核状态
    private String wfStatus;                         // 审核状态
    private String type;                             // 类型（a,b,c,d）
    private String createId;
    private String createName;
    private String remark;
    private String wfId;

    public ProjectFilingSupPlan() {
    }

    public String getWfId() {
        return wfId;
    }

    public void setWfId(String wfId) {
        this.wfId = wfId;
    }

    public String getWfStatus() {
        return wfStatus;
    }

    public void setWfStatus(String wfStatus) {
        this.wfStatus = wfStatus;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getSpSubject() {
        return spSubject;
    }

    public void setSpSubject(String spSubject) {
        this.spSubject = spSubject;
    }

    public String getSpContent() {
        return spContent;
    }

    public void setSpContent(String spContent) {
        this.spContent = spContent;
    }

    public String getSupDirectorId() {
        return supDirectorId;
    }

    public void setSupDirectorId(String supDirectorId) {
        this.supDirectorId = supDirectorId;
    }

    public String getSupDirectorName() {
        return supDirectorName;
    }

    public void setSupDirectorName(String supDirectorName) {
        this.supDirectorName = supDirectorName;
    }

    public String getVerifierId() {
        return verifierId;
    }

    public void setVerifierId(String verifierId) {
        this.verifierId = verifierId;
    }

    public String getVerifierName() {
        return verifierName;
    }

    public void setVerifierName(String verifierName) {
        this.verifierName = verifierName;
    }

    public String getVerifyState() {
        return verifyState;
    }

    public void setVerifyState(String verifyState) {
        this.verifyState = verifyState;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
