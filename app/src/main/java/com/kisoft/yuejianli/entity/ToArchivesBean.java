package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.List;

/**
 * Description: 公文起草-基本信息
 * Author     : yanlu
 * Date       : 2019/1/12 16:16
 */

public class ToArchivesBean extends Base {


    /**
     * archivesDto : {"csList":[],"arcBody":"","cfield11":"","cfield10":"","assign":0,"arcNo":"综[2019]5号","annexid":"","createdate":"2019-1-12","cfield19":"","cfield18":"","cfield17":"","cfield16":"","cfield15":"","cfield14":"","cfield13":"","templetUrl":"","cfield12":"","kid":"","wftRemark":"","file":null,"insertUserId":"","role":"","temId":"141126141925475ba7e5722bc17a07cc","businessId":"","option":"","wftId":"1411251357288961a6225b7a4a819fd3","cfield20":"","securityList":[{"value":"1","label":""},{"value":"2","label":"绝密"},{"value":"3","label":"机密"},{"value":"4","label":"秘密"},{"value":"5","label":"一般"}],"pageCount":"","urgentList":[{"value":"1","label":""},{"value":"2","label":"急件"},{"value":"3","label":"特急"},{"value":"4","label":"一般"}],"fmoduleId":"","fvtName":"","filePath":"d:/krmis/roa/archives/templet/18100916565883058c25576a4af57858_附件1：2019年度西安市科技计划项目申报指南.doc","dfield1":"","businessType":"","wfState":"","dfield5":"","dfield4":"","dfield3":"","dfield2":"","wftName":"发函流程","creatorid":"","hzIds":[],"firstReadTime":"","idea":"","nfield1":0,"nfield3":0,"wfId":"","nfield2":0,"nfield5":0,"nfield4":0,"lastReadTime":"","readerIdea":"","isImport":"","title":"","templetFileName":"","readerName":"","num":"","fcreatedate":"","callBackName":"","arcNum":"","jdId":"","hzNameList":[],"remark":"","cfield9":"","cfield7":"","fvtGuid":"","cfield8":"","deptid":"","zsIds":[],"zgId":"","userId":"","backNodeId":"","index":"","insertUserName":"","wfTaskId":"","headList":[],"cfield4":"","arcId":"190112161422494bb785295883383f93","cfield3":"","cfield6":"","cfield5":"","reader":"","cfield2":"","cfield1":"","fileName":"附件1：2019年度西安市科技计划项目申报指南.doc","ursige":"","sesige":"","fsize":"","security":"","appRemark":"","archives3":"","archives2":"","contNumber":"","archives1":"","zsIdList":[],"urgent":"","drafterArchives":"","creatorName":"","zsList":[],"hzIdList":[],"factfilename":"","filename":"","annexList":[],"wfName":""}
     * currYear : 2019
     * dtoList : [{"businessTitle":"正文","businessUrl":"javascript:add('1');"}]
     */

    private ArchivesDtoBean   archivesDto;
    private String            currYear;
    private List<DtoListBean> dtoList;

    public ArchivesDtoBean getArchivesDto() {
        return archivesDto;
    }

    public void setArchivesDto(ArchivesDtoBean archivesDto) {
        this.archivesDto = archivesDto;
    }

    public String getCurrYear() {
        return currYear;
    }

    public void setCurrYear(String currYear) {
        this.currYear = currYear;
    }

    public List<DtoListBean> getDtoList() {
        return dtoList;
    }

    public void setDtoList(List<DtoListBean> dtoList) {
        this.dtoList = dtoList;
    }

    public static class ArchivesDtoBean {
        /**
         * csList : []
         * arcBody :
         * cfield11 :
         * cfield10 :
         * assign : 0
         * arcNo : 综[2019]5号
         * annexid :
         * createdate : 2019-1-12
         * cfield19 :
         * cfield18 :
         * cfield17 :
         * cfield16 :
         * cfield15 :
         * cfield14 :
         * cfield13 :
         * templetUrl :
         * cfield12 :
         * kid :
         * wftRemark :
         * file : null
         * insertUserId :
         * role :
         * temId : 141126141925475ba7e5722bc17a07cc
         * businessId :
         * option :
         * wftId : 1411251357288961a6225b7a4a819fd3
         * cfield20 :
         * securityList : [{"value":"1","label":""},{"value":"2","label":"绝密"},{"value":"3","label":"机密"},{"value":"4","label":"秘密"},{"value":"5","label":"一般"}]
         * pageCount :
         * urgentList : [{"value":"1","label":""},{"value":"2","label":"急件"},{"value":"3","label":"特急"},{"value":"4","label":"一般"}]
         * fmoduleId :
         * fvtName :
         * filePath : d:/krmis/roa/archives/templet/18100916565883058c25576a4af57858_附件1：2019年度西安市科技计划项目申报指南.doc
         * dfield1 :
         * businessType :
         * wfState :
         * dfield5 :
         * dfield4 :
         * dfield3 :
         * dfield2 :
         * wftName : 发函流程
         * creatorid :
         * hzIds : []
         * firstReadTime :
         * idea :
         * nfield1 : 0
         * nfield3 : 0
         * wfId :
         * nfield2 : 0
         * nfield5 : 0
         * nfield4 : 0
         * lastReadTime :
         * readerIdea :
         * isImport :
         * title :
         * templetFileName :
         * readerName :
         * num :
         * fcreatedate :
         * callBackName :
         * arcNum :
         * jdId :
         * hzNameList : []
         * remark :
         * cfield9 :
         * cfield7 :
         * fvtGuid :
         * cfield8 :
         * deptid :
         * zsIds : []
         * zgId :
         * userId :
         * backNodeId :
         * index :
         * insertUserName :
         * wfTaskId :
         * headList : []
         * cfield4 :
         * arcId : 190112161422494bb785295883383f93
         * cfield3 :
         * cfield6 :
         * cfield5 :
         * reader :
         * cfield2 :
         * cfield1 :
         * fileName : 附件1：2019年度西安市科技计划项目申报指南.doc
         * ursige :
         * sesige :
         * fsize :
         * security :
         * appRemark :
         * archives3 :
         * archives2 :
         * contNumber :
         * archives1 :
         * zsIdList : []
         * urgent :
         * drafterArchives :
         * creatorName :
         * zsList : []
         * hzIdList : []
         * factfilename :
         * filename :
         * annexList : []
         * wfName :
         */

        private String                 arcBody;
        private String                 cfield11;
        private String                 cfield10;
        private int                    assign;
        private String                 arcNo;
        private String                 annexid;
        private String                 createdate;
        private String                 cfield19;
        private String                 cfield18;
        private String                 cfield17;
        private String                 cfield16;
        private String                 cfield15;
        private String                 cfield14;
        private String                 cfield13;
        private String                 templetUrl;
        private String                 cfield12;
        private String                 kid;
        private String                 wftRemark;
        private Object                 file;
        private String                 insertUserId;
        private String                 role;
        private String                 temId;
        private String                 businessId;
        private String                 option;
        private String                 wftId;
        private String                 cfield20;
        private String                 pageCount;
        private String                 fmoduleId;
        private String                 fvtName;
        private String                 filePath;
        private String                 dfield1;
        private String                 businessType;
        private String                 wfState;
        private String                 dfield5;
        private String                 dfield4;
        private String                 dfield3;
        private String                 dfield2;
        private String                 wftName;
        private String                 creatorid;
        private String                 firstReadTime;
        private String                 idea;
        private int                    nfield1;
        private int                    nfield3;
        private String                 wfId;
        private int                    nfield2;
        private int                    nfield5;
        private int                    nfield4;
        private String                 lastReadTime;
        private String                 readerIdea;
        private String                 isImport;
        private String                 title;
        private String                 templetFileName;
        private String                 readerName;
        private String                 num;
        private String                 fcreatedate;
        private String                 callBackName;
        private String                 arcNum;
        private String                 jdId;
        private String                 remark;
        private String                 cfield9;
        private String                 cfield7;
        private String                 fvtGuid;
        private String                 cfield8;
        private String                 deptid;
        private String                 zgId;
        private String                 userId;
        private String                 backNodeId;
        private String                 index;
        private String                 insertUserName;
        private String                 wfTaskId;
        private String                 cfield4;
        private String                 arcId;
        private String                 cfield3;
        private String                 cfield6;
        private String                 cfield5;
        private String                 reader;
        private String                 cfield2;
        private String                 cfield1;
        private String                 fileName;
        private String                 ursige;
        private String                 sesige;
        private String                 fsize;
        private String                 security;
        private String                 appRemark;
        private String                 archives3;
        private String                 archives2;
        private String                 contNumber;
        private String                 archives1;
        private String                 urgent;
        private String                 drafterArchives;
        private String                 creatorName;
        private String                 factfilename;
        private String                 filename;
        private String                 wfName;
        private List<?>                csList;
        private List<SecurityListBean> securityList;
        private List<UrgentListBean>   urgentList;
        private List<?>                hzIds;
        private List<?>                hzNameList;
        private List<?>                zsIds;
        private List<?>                headList;
        private List<?>                zsIdList;
        private List<?>                zsList;
        private List<?>                hzIdList;
        private List<?>                annexList;

        public String getArcBody() {
            return arcBody;
        }

        public void setArcBody(String arcBody) {
            this.arcBody = arcBody;
        }

        public String getCfield11() {
            return cfield11;
        }

        public void setCfield11(String cfield11) {
            this.cfield11 = cfield11;
        }

        public String getCfield10() {
            return cfield10;
        }

        public void setCfield10(String cfield10) {
            this.cfield10 = cfield10;
        }

        public int getAssign() {
            return assign;
        }

        public void setAssign(int assign) {
            this.assign = assign;
        }

        public String getArcNo() {
            return arcNo;
        }

        public void setArcNo(String arcNo) {
            this.arcNo = arcNo;
        }

        public String getAnnexid() {
            return annexid;
        }

        public void setAnnexid(String annexid) {
            this.annexid = annexid;
        }

        public String getCreatedate() {
            return createdate;
        }

        public void setCreatedate(String createdate) {
            this.createdate = createdate;
        }

        public String getCfield19() {
            return cfield19;
        }

        public void setCfield19(String cfield19) {
            this.cfield19 = cfield19;
        }

        public String getCfield18() {
            return cfield18;
        }

        public void setCfield18(String cfield18) {
            this.cfield18 = cfield18;
        }

        public String getCfield17() {
            return cfield17;
        }

        public void setCfield17(String cfield17) {
            this.cfield17 = cfield17;
        }

        public String getCfield16() {
            return cfield16;
        }

        public void setCfield16(String cfield16) {
            this.cfield16 = cfield16;
        }

        public String getCfield15() {
            return cfield15;
        }

        public void setCfield15(String cfield15) {
            this.cfield15 = cfield15;
        }

        public String getCfield14() {
            return cfield14;
        }

        public void setCfield14(String cfield14) {
            this.cfield14 = cfield14;
        }

        public String getCfield13() {
            return cfield13;
        }

        public void setCfield13(String cfield13) {
            this.cfield13 = cfield13;
        }

        public String getTempletUrl() {
            return templetUrl;
        }

        public void setTempletUrl(String templetUrl) {
            this.templetUrl = templetUrl;
        }

        public String getCfield12() {
            return cfield12;
        }

        public void setCfield12(String cfield12) {
            this.cfield12 = cfield12;
        }

        public String getKid() {
            return kid;
        }

        public void setKid(String kid) {
            this.kid = kid;
        }

        public String getWftRemark() {
            return wftRemark;
        }

        public void setWftRemark(String wftRemark) {
            this.wftRemark = wftRemark;
        }

        public Object getFile() {
            return file;
        }

        public void setFile(Object file) {
            this.file = file;
        }

        public String getInsertUserId() {
            return insertUserId;
        }

        public void setInsertUserId(String insertUserId) {
            this.insertUserId = insertUserId;
        }

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public String getTemId() {
            return temId;
        }

        public void setTemId(String temId) {
            this.temId = temId;
        }

        public String getBusinessId() {
            return businessId;
        }

        public void setBusinessId(String businessId) {
            this.businessId = businessId;
        }

        public String getOption() {
            return option;
        }

        public void setOption(String option) {
            this.option = option;
        }

        public String getWftId() {
            return wftId;
        }

        public void setWftId(String wftId) {
            this.wftId = wftId;
        }

        public String getCfield20() {
            return cfield20;
        }

        public void setCfield20(String cfield20) {
            this.cfield20 = cfield20;
        }

        public String getPageCount() {
            return pageCount;
        }

        public void setPageCount(String pageCount) {
            this.pageCount = pageCount;
        }

        public String getFmoduleId() {
            return fmoduleId;
        }

        public void setFmoduleId(String fmoduleId) {
            this.fmoduleId = fmoduleId;
        }

        public String getFvtName() {
            return fvtName;
        }

        public void setFvtName(String fvtName) {
            this.fvtName = fvtName;
        }

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public String getDfield1() {
            return dfield1;
        }

        public void setDfield1(String dfield1) {
            this.dfield1 = dfield1;
        }

        public String getBusinessType() {
            return businessType;
        }

        public void setBusinessType(String businessType) {
            this.businessType = businessType;
        }

        public String getWfState() {
            return wfState;
        }

        public void setWfState(String wfState) {
            this.wfState = wfState;
        }

        public String getDfield5() {
            return dfield5;
        }

        public void setDfield5(String dfield5) {
            this.dfield5 = dfield5;
        }

        public String getDfield4() {
            return dfield4;
        }

        public void setDfield4(String dfield4) {
            this.dfield4 = dfield4;
        }

        public String getDfield3() {
            return dfield3;
        }

        public void setDfield3(String dfield3) {
            this.dfield3 = dfield3;
        }

        public String getDfield2() {
            return dfield2;
        }

        public void setDfield2(String dfield2) {
            this.dfield2 = dfield2;
        }

        public String getWftName() {
            return wftName;
        }

        public void setWftName(String wftName) {
            this.wftName = wftName;
        }

        public String getCreatorid() {
            return creatorid;
        }

        public void setCreatorid(String creatorid) {
            this.creatorid = creatorid;
        }

        public String getFirstReadTime() {
            return firstReadTime;
        }

        public void setFirstReadTime(String firstReadTime) {
            this.firstReadTime = firstReadTime;
        }

        public String getIdea() {
            return idea;
        }

        public void setIdea(String idea) {
            this.idea = idea;
        }

        public int getNfield1() {
            return nfield1;
        }

        public void setNfield1(int nfield1) {
            this.nfield1 = nfield1;
        }

        public int getNfield3() {
            return nfield3;
        }

        public void setNfield3(int nfield3) {
            this.nfield3 = nfield3;
        }

        public String getWfId() {
            return wfId;
        }

        public void setWfId(String wfId) {
            this.wfId = wfId;
        }

        public int getNfield2() {
            return nfield2;
        }

        public void setNfield2(int nfield2) {
            this.nfield2 = nfield2;
        }

        public int getNfield5() {
            return nfield5;
        }

        public void setNfield5(int nfield5) {
            this.nfield5 = nfield5;
        }

        public int getNfield4() {
            return nfield4;
        }

        public void setNfield4(int nfield4) {
            this.nfield4 = nfield4;
        }

        public String getLastReadTime() {
            return lastReadTime;
        }

        public void setLastReadTime(String lastReadTime) {
            this.lastReadTime = lastReadTime;
        }

        public String getReaderIdea() {
            return readerIdea;
        }

        public void setReaderIdea(String readerIdea) {
            this.readerIdea = readerIdea;
        }

        public String getIsImport() {
            return isImport;
        }

        public void setIsImport(String isImport) {
            this.isImport = isImport;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getTempletFileName() {
            return templetFileName;
        }

        public void setTempletFileName(String templetFileName) {
            this.templetFileName = templetFileName;
        }

        public String getReaderName() {
            return readerName;
        }

        public void setReaderName(String readerName) {
            this.readerName = readerName;
        }

        public String getNum() {
            return num;
        }

        public void setNum(String num) {
            this.num = num;
        }

        public String getFcreatedate() {
            return fcreatedate;
        }

        public void setFcreatedate(String fcreatedate) {
            this.fcreatedate = fcreatedate;
        }

        public String getCallBackName() {
            return callBackName;
        }

        public void setCallBackName(String callBackName) {
            this.callBackName = callBackName;
        }

        public String getArcNum() {
            return arcNum;
        }

        public void setArcNum(String arcNum) {
            this.arcNum = arcNum;
        }

        public String getJdId() {
            return jdId;
        }

        public void setJdId(String jdId) {
            this.jdId = jdId;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getCfield9() {
            return cfield9;
        }

        public void setCfield9(String cfield9) {
            this.cfield9 = cfield9;
        }

        public String getCfield7() {
            return cfield7;
        }

        public void setCfield7(String cfield7) {
            this.cfield7 = cfield7;
        }

        public String getFvtGuid() {
            return fvtGuid;
        }

        public void setFvtGuid(String fvtGuid) {
            this.fvtGuid = fvtGuid;
        }

        public String getCfield8() {
            return cfield8;
        }

        public void setCfield8(String cfield8) {
            this.cfield8 = cfield8;
        }

        public String getDeptid() {
            return deptid;
        }

        public void setDeptid(String deptid) {
            this.deptid = deptid;
        }

        public String getZgId() {
            return zgId;
        }

        public void setZgId(String zgId) {
            this.zgId = zgId;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getBackNodeId() {
            return backNodeId;
        }

        public void setBackNodeId(String backNodeId) {
            this.backNodeId = backNodeId;
        }

        public String getIndex() {
            return index;
        }

        public void setIndex(String index) {
            this.index = index;
        }

        public String getInsertUserName() {
            return insertUserName;
        }

        public void setInsertUserName(String insertUserName) {
            this.insertUserName = insertUserName;
        }

        public String getWfTaskId() {
            return wfTaskId;
        }

        public void setWfTaskId(String wfTaskId) {
            this.wfTaskId = wfTaskId;
        }

        public String getCfield4() {
            return cfield4;
        }

        public void setCfield4(String cfield4) {
            this.cfield4 = cfield4;
        }

        public String getArcId() {
            return arcId;
        }

        public void setArcId(String arcId) {
            this.arcId = arcId;
        }

        public String getCfield3() {
            return cfield3;
        }

        public void setCfield3(String cfield3) {
            this.cfield3 = cfield3;
        }

        public String getCfield6() {
            return cfield6;
        }

        public void setCfield6(String cfield6) {
            this.cfield6 = cfield6;
        }

        public String getCfield5() {
            return cfield5;
        }

        public void setCfield5(String cfield5) {
            this.cfield5 = cfield5;
        }

        public String getReader() {
            return reader;
        }

        public void setReader(String reader) {
            this.reader = reader;
        }

        public String getCfield2() {
            return cfield2;
        }

        public void setCfield2(String cfield2) {
            this.cfield2 = cfield2;
        }

        public String getCfield1() {
            return cfield1;
        }

        public void setCfield1(String cfield1) {
            this.cfield1 = cfield1;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getUrsige() {
            return ursige;
        }

        public void setUrsige(String ursige) {
            this.ursige = ursige;
        }

        public String getSesige() {
            return sesige;
        }

        public void setSesige(String sesige) {
            this.sesige = sesige;
        }

        public String getFsize() {
            return fsize;
        }

        public void setFsize(String fsize) {
            this.fsize = fsize;
        }

        public String getSecurity() {
            return security;
        }

        public void setSecurity(String security) {
            this.security = security;
        }

        public String getAppRemark() {
            return appRemark;
        }

        public void setAppRemark(String appRemark) {
            this.appRemark = appRemark;
        }

        public String getArchives3() {
            return archives3;
        }

        public void setArchives3(String archives3) {
            this.archives3 = archives3;
        }

        public String getArchives2() {
            return archives2;
        }

        public void setArchives2(String archives2) {
            this.archives2 = archives2;
        }

        public String getContNumber() {
            return contNumber;
        }

        public void setContNumber(String contNumber) {
            this.contNumber = contNumber;
        }

        public String getArchives1() {
            return archives1;
        }

        public void setArchives1(String archives1) {
            this.archives1 = archives1;
        }

        public String getUrgent() {
            return urgent;
        }

        public void setUrgent(String urgent) {
            this.urgent = urgent;
        }

        public String getDrafterArchives() {
            return drafterArchives;
        }

        public void setDrafterArchives(String drafterArchives) {
            this.drafterArchives = drafterArchives;
        }

        public String getCreatorName() {
            return creatorName;
        }

        public void setCreatorName(String creatorName) {
            this.creatorName = creatorName;
        }

        public String getFactfilename() {
            return factfilename;
        }

        public void setFactfilename(String factfilename) {
            this.factfilename = factfilename;
        }

        public String getFilename() {
            return filename;
        }

        public void setFilename(String filename) {
            this.filename = filename;
        }

        public String getWfName() {
            return wfName;
        }

        public void setWfName(String wfName) {
            this.wfName = wfName;
        }

        public List<?> getCsList() {
            return csList;
        }

        public void setCsList(List<?> csList) {
            this.csList = csList;
        }

        public List<SecurityListBean> getSecurityList() {
            return securityList;
        }

        public void setSecurityList(List<SecurityListBean> securityList) {
            this.securityList = securityList;
        }

        public List<UrgentListBean> getUrgentList() {
            return urgentList;
        }

        public void setUrgentList(List<UrgentListBean> urgentList) {
            this.urgentList = urgentList;
        }

        public List<?> getHzIds() {
            return hzIds;
        }

        public void setHzIds(List<?> hzIds) {
            this.hzIds = hzIds;
        }

        public List<?> getHzNameList() {
            return hzNameList;
        }

        public void setHzNameList(List<?> hzNameList) {
            this.hzNameList = hzNameList;
        }

        public List<?> getZsIds() {
            return zsIds;
        }

        public void setZsIds(List<?> zsIds) {
            this.zsIds = zsIds;
        }

        public List<?> getHeadList() {
            return headList;
        }

        public void setHeadList(List<?> headList) {
            this.headList = headList;
        }

        public List<?> getZsIdList() {
            return zsIdList;
        }

        public void setZsIdList(List<?> zsIdList) {
            this.zsIdList = zsIdList;
        }

        public List<?> getZsList() {
            return zsList;
        }

        public void setZsList(List<?> zsList) {
            this.zsList = zsList;
        }

        public List<?> getHzIdList() {
            return hzIdList;
        }

        public void setHzIdList(List<?> hzIdList) {
            this.hzIdList = hzIdList;
        }

        public List<?> getAnnexList() {
            return annexList;
        }

        public void setAnnexList(List<?> annexList) {
            this.annexList = annexList;
        }
    }

}
