package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

public class ItemArrivalInfo extends Base {
    private String tvName;
    private String tvText;
    private String tvImageUrl;

    public ItemArrivalInfo() {
    }

    public String getTvName() {
        return tvName;
    }

    public void setTvName(String tvName) {
        this.tvName = tvName;
    }

    public String getTvText() {
        return tvText;
    }

    public void setTvText(String tvText) {
        this.tvText = tvText;
    }

    public String getTvImageUrl() {
        return tvImageUrl;
    }

    public void setTvImageUrl(String tvImageUrl) {
        this.tvImageUrl = tvImageUrl;
    }

    @Override
    public String toString() {
        return "ItemArrivalInfo{" +
                "tvName='" + tvName + '\'' +
                ", tvText='" + tvText + '\'' +
                ", tvImageUrl='" + tvImageUrl + '\'' +
                '}';
    }
}
