package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by tudo<PERSON> on 2018/6/19.
 */

public class CustomerVisit extends Base {


    public static final String IS_DEGREE_YES = "1";
    public static final String IS_DEGREE_NO = "0";

    private String crpGuid;                     // 计划id
    private String craGuid;                     // 申请id
    private String projectId;                   // 项目id
    private String projectName;                 // 项目名称
    private String cusGuid;                     // 客户id
    private String cusName;                     // 客户名称
    private String retbackMan;                  // 回访人
    private String planDate;                    // 计划回访时间
    private String actualDate;                  // 实际回访时间
    private String statisDegree;                // 是否完成满意度调查
    private String statisDegreeName;
    private String retbackObject;               // 回访对象 （业主单位内某人）
    private String dealInfo;                    // 详情
    private String createId;
    private String createName;
    private String createTime;


    public CustomerVisit() {
    }

    public String getCrpGuid() {
        return crpGuid;
    }

    public void setCrpGuid(String crpGuid) {
        this.crpGuid = crpGuid;
    }

    public String getCraGuid() {
        return craGuid;
    }

    public void setCraGuid(String craGuid) {
        this.craGuid = craGuid;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getCusGuid() {
        return cusGuid;
    }

    public void setCusGuid(String cusGuid) {
        this.cusGuid = cusGuid;
    }

    public String getCusName() {
        return cusName;
    }

    public void setCusName(String cusName) {
        this.cusName = cusName;
    }

    public String getRetbackMan() {
        return retbackMan;
    }

    public void setRetbackMan(String retbackMan) {
        this.retbackMan = retbackMan;
    }

    public String getPlanDate() {
        return planDate;
    }

    public void setPlanDate(String planDate) {
        this.planDate = planDate;
    }

    public String getActualDate() {
        return actualDate;
    }

    public void setActualDate(String actualDate) {
        this.actualDate = actualDate;
    }

    public String getStatisDegree() {
        return statisDegree;
    }

    public void setStatisDegree(String statisDegree) {
        this.statisDegree = statisDegree;
    }

    public String getStatisDegreeName() {
        return statisDegreeName;
    }

    public void setStatisDegreeName(String statisDegreeName) {
        this.statisDegreeName = statisDegreeName;
    }

    public String getRetbackObject() {
        return retbackObject;
    }

    public void setRetbackObject(String retbackObject) {
        this.retbackObject = retbackObject;
    }

    public String getDealInfo() {
        return dealInfo;
    }

    public void setDealInfo(String dealInfo) {
        this.dealInfo = dealInfo;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
}
