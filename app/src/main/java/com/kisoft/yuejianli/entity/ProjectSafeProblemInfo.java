package com.kisoft.yuejianli.entity;

import java.util.List;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by tudo<PERSON> on 2018/5/23.
 */

public class ProjectSafeProblemInfo extends Base{

    private int count;

    private List<ProjectSafeInspection> list;


    public ProjectSafeProblemInfo() {
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public List<ProjectSafeInspection> getList() {
        return list;
    }

    public void setList(List<ProjectSafeInspection> list) {
        this.list = list;
    }
}
