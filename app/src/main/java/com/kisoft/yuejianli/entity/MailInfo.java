package com.kisoft.yuejianli.entity;

import java.util.List;

/**
 * 邮箱
 */
public class MailInfo {


    /**
     * freversionId :
     * fbody :
     * fcreateDate : 2019-08-04 17:26
     * fsubject : 关于公司考勤制度的说明
     * ffileNameName :
     * index :
     * freceiverName : 王茜
     * fccmanId :
     * fccmanName :
     * fccmanNames : []
     * fcreateName :
     * sumSize :
     * fcreateId :
     * freceiverNames : []
     * fsendWay :
     * state :
     * fmailId : 1908041726148230b8d6956def9cc0e8
     * fflag :
     */

    private String freversionId;
    private String fbody;//正文
    private String fcreateDate;//日期
    private String fsubject;//主题
    private String ffileNameName;
    private String index;
    private String freceiverName;
    private String fccmanId;
    private String fccmanName;
    private String fcreateName;
    private String sumSize;
    private String fcreateId;
    private String fsendWay;
    private String state;//邮件状态(App)
    private String fmailId;
    private String fflag;
    private List<?> fccmanNames;
    private List<?> freceiverNames;

    public String getFreversionId() {
        return freversionId;
    }

    public void setFreversionId(String freversionId) {
        this.freversionId = freversionId;
    }

    public String getFbody() {
        return fbody;
    }

    public void setFbody(String fbody) {
        this.fbody = fbody;
    }

    public String getFcreateDate() {
        return fcreateDate;
    }

    public void setFcreateDate(String fcreateDate) {
        this.fcreateDate = fcreateDate;
    }

    public String getFsubject() {
        return fsubject;
    }

    public void setFsubject(String fsubject) {
        this.fsubject = fsubject;
    }

    public String getFfileNameName() {
        return ffileNameName;
    }

    public void setFfileNameName(String ffileNameName) {
        this.ffileNameName = ffileNameName;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getFreceiverName() {
        return freceiverName;
    }

    public void setFreceiverName(String freceiverName) {
        this.freceiverName = freceiverName;
    }

    public String getFccmanId() {
        return fccmanId;
    }

    public void setFccmanId(String fccmanId) {
        this.fccmanId = fccmanId;
    }

    public String getFccmanName() {
        return fccmanName;
    }

    public void setFccmanName(String fccmanName) {
        this.fccmanName = fccmanName;
    }

    public String getFcreateName() {
        return fcreateName;
    }

    public void setFcreateName(String fcreateName) {
        this.fcreateName = fcreateName;
    }

    public String getSumSize() {
        return sumSize;
    }

    public void setSumSize(String sumSize) {
        this.sumSize = sumSize;
    }

    public String getFcreateId() {
        return fcreateId;
    }

    public void setFcreateId(String fcreateId) {
        this.fcreateId = fcreateId;
    }

    public String getFsendWay() {
        return fsendWay;
    }

    public void setFsendWay(String fsendWay) {
        this.fsendWay = fsendWay;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getFmailId() {
        return fmailId;
    }

    public void setFmailId(String fmailId) {
        this.fmailId = fmailId;
    }

    public String getFflag() {
        return fflag;
    }

    public void setFflag(String fflag) {
        this.fflag = fflag;
    }

    public List<?> getFccmanNames() {
        return fccmanNames;
    }

    public void setFccmanNames(List<?> fccmanNames) {
        this.fccmanNames = fccmanNames;
    }

    public List<?> getFreceiverNames() {
        return freceiverNames;
    }

    public void setFreceiverNames(List<?> freceiverNames) {
        this.freceiverNames = freceiverNames;
    }
}
