package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.Date;
import java.util.List;

/**
 * 危大工程巡检 list
 */
public class WitnessSamplesInspectList extends Base {
    private int count;
    private List<WitnessSamplesInspectInfo> list;

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public List<WitnessSamplesInspectInfo> getList() {
        return list;
    }

    public void setList(List<WitnessSamplesInspectInfo> list) {
        this.list = list;
    }

}
