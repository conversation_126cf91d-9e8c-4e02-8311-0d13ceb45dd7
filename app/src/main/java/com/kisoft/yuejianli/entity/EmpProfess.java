package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.text.ParseException;

/**
 * 证件库
 */
public class EmpProfess  extends Base {
    private String professguid = "";   //主键
    private String empguid = "";     //员工Id
    private String protype = "";     //证书类型
    private String ename = "";     //证件名称
    private String grade = "";      //
    private String professnum = "";  //证件编号
    private String appdate = "";     //取得日期
    private String corp = "";       //发证机关
    private String remark = "";    //备注
    private String spcciality;     //专业
    private String expdate;      //有效日期
    private String state; //1:使用中，0：在库
    private String empState; //新增员工状态 yangjianjun add 2019-1-14
    private String filePath; //证书附件路径
    private String fileName; //附件文件名称 用附件标识标注
    private String proclass; //证书类别  0=职称信息、1=职（执）业资格
    //新增  cj
    private String name;//员工名字

    private boolean isSelect;

    public String getProfessguid() {
        return professguid;
    }

    public void setProfessguid(String professguid) {
        this.professguid = professguid;
    }

    public String getEmpguid() {
        return empguid;
    }

    public void setEmpguid(String empguid) {
        this.empguid = empguid;
    }

    public String getProtype() {
        return protype;
    }

    public void setProtype(String protype) {
        this.protype = protype;
    }

    public String getEname() {
        return ename;
    }

    public void setEname(String ename) {
        this.ename = ename;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getProfessnum() {
        return professnum;
    }

    public void setProfessnum(String professnum) {
        this.professnum = professnum;
    }

    public String getAppdate() {
        return appdate;
    }

    public void setAppdate(String appdate) {
        this.appdate = appdate;
    }

    public String getCorp() {
        return corp;
    }

    public void setCorp(String corp) {
        this.corp = corp;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSpcciality() {
        return spcciality;
    }

    public void setSpcciality(String spcciality) {
        this.spcciality = spcciality;
    }

    public String getExpdate() {
        return expdate;
    }

    public void setExpdate(String expdate) {
        this.expdate = expdate;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getEmpState() {
        return empState;
    }

    public void setEmpState(String empState) {
        this.empState = empState;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getProclass() {
        return proclass;
    }

    public void setProclass(String proclass) {
        this.proclass = proclass;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isSelect() {
        return isSelect;
    }

    public void setSelect(boolean select) {
        isSelect = select;
    }

    //是否过期
    public boolean isInvalid(){
        if(StringUtil.isEmpty(expdate)){
            return false;
        }
        try {
            return System.currentTimeMillis()> DateUtil.stringToLong(empState, DateUtil.YMD);
        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }
    }

}
