package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.List;

/**
 * Created by tudou on 2018/7/25.
 */

public class WorkFlowTaskInfo extends Base {

    private int count;

    private List<WorkFlowTask> list;

    public WorkFlowTaskInfo() {
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public List<WorkFlowTask> getList() {
        return list;
    }

    public void setList(List<WorkFlowTask> list) {
        this.list = list;
    }
}
