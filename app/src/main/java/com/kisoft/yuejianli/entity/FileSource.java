package com.kisoft.yuejianli.entity;

/**
 * 考试资料
 */
public class FileSource {

    /**
     * readRight :
     * fileRemark :
     * fileGuid :
     * folderGuid :
     * admin :
     * keyId :
     * remark :
     * type : 文件夹
     * operator :
     * styleGuid :
     * administrator :
     * datetime : 2021-04-19 09:05
     * id : 2104190905458591800a1028af29aa72
     * createDate : null
     * creator : 杨南辉
     * writeRight :
     * userName :
     * userId :
     * version :
     * pathname :
     * fileCounts : 0
     * fristVerGuid :
     * filename :
     * fsize :
     * parentGuid :
     * name : 测试视频目录
     * viewurl :
     * style :
     * folderRemark :
     */

    private String readRight;
    private String fileRemark;
    private String fileGuid;
    private String folderGuid;
    private String admin;
    private String keyId;
    private String remark;
    private String type;
    private String operator;
    private String styleGuid;
    private String administrator;
    private String datetime;
    private String id;
    private String createDate;
    private String creator;
    private String writeRight;
    private String userName;
    private String userId;
    private String version;
    private String pathname;
    private String fileCounts;
    private String fristVerGuid;
    private String filename;
    private String fsize;
    private String parentGuid;
    private String name;
    private String viewurl;
    private String style;
    private String folderRemark;
    private String isStudy;//是否已学(0:未学；1：已学）

    public String getIsStudy() {
        return isStudy;
    }

    public void setIsStudy(String isStudy) {
        this.isStudy = isStudy;
    }

    public String getReadRight() {
        return readRight;
    }

    public void setReadRight(String readRight) {
        this.readRight = readRight;
    }

    public String getFileRemark() {
        return fileRemark;
    }

    public void setFileRemark(String fileRemark) {
        this.fileRemark = fileRemark;
    }

    public String getFileGuid() {
        return fileGuid;
    }

    public void setFileGuid(String fileGuid) {
        this.fileGuid = fileGuid;
    }

    public String getFolderGuid() {
        return folderGuid;
    }

    public void setFolderGuid(String folderGuid) {
        this.folderGuid = folderGuid;
    }

    public String getAdmin() {
        return admin;
    }

    public void setAdmin(String admin) {
        this.admin = admin;
    }

    public String getKeyId() {
        return keyId;
    }

    public void setKeyId(String keyId) {
        this.keyId = keyId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getStyleGuid() {
        return styleGuid;
    }

    public void setStyleGuid(String styleGuid) {
        this.styleGuid = styleGuid;
    }

    public String getAdministrator() {
        return administrator;
    }

    public void setAdministrator(String administrator) {
        this.administrator = administrator;
    }

    public String getDatetime() {
        return datetime;
    }

    public void setDatetime(String datetime) {
        this.datetime = datetime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getWriteRight() {
        return writeRight;
    }

    public void setWriteRight(String writeRight) {
        this.writeRight = writeRight;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getPathname() {
        return pathname;
    }

    public void setPathname(String pathname) {
        this.pathname = pathname;
    }

    public String getFileCounts() {
        return fileCounts;
    }

    public void setFileCounts(String fileCounts) {
        this.fileCounts = fileCounts;
    }

    public String getFristVerGuid() {
        return fristVerGuid;
    }

    public void setFristVerGuid(String fristVerGuid) {
        this.fristVerGuid = fristVerGuid;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getFsize() {
        return fsize;
    }

    public void setFsize(String fsize) {
        this.fsize = fsize;
    }

    public String getParentGuid() {
        return parentGuid;
    }

    public void setParentGuid(String parentGuid) {
        this.parentGuid = parentGuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getViewurl() {
        return viewurl;
    }

    public void setViewurl(String viewurl) {
        this.viewurl = viewurl;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public String getFolderRemark() {
        return folderRemark;
    }

    public void setFolderRemark(String folderRemark) {
        this.folderRemark = folderRemark;
    }
}
