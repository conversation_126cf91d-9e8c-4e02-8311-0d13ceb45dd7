package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * 借用记录
 */
public class BorrowRecord extends Base {
    /**
     * "planName": "西安棚户区改造项目002-投标计划",
     *                 "professguid": "19071810475397738cec35b2dc498f45",
     *                 "borrType": "1",
     *                 "idbaId": "200929175338506187ee2898335b0181",
     *                 "userId": "",
     *                 "ename": "112",
     *                 "idbarId": "200929175338510ca693e3ecb45d9f9f",
     *                 "state": "0",
     *                 "userName": "",
     *                 "ipId": "200615162827484ca10541a20c2a0623"
     */

    private String planName;
    private String professguid;
    private String borrType;
    private String idbaId;
    private String userId;
    private String ename;
    private String state;
    private String userName;
    private String ipId;

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getProfessguid() {
        return professguid;
    }

    public void setProfessguid(String professguid) {
        this.professguid = professguid;
    }

    public String getBorrType() {
        return borrType;
    }

    public void setBorrType(String borrType) {
        this.borrType = borrType;
    }

    public String getIdbaId() {
        return idbaId;
    }

    public void setIdbaId(String idbaId) {
        this.idbaId = idbaId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getEname() {
        return ename;
    }

    public void setEname(String ename) {
        this.ename = ename;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getIpId() {
        return ipId;
    }

    public void setIpId(String ipId) {
        this.ipId = ipId;
    }
}