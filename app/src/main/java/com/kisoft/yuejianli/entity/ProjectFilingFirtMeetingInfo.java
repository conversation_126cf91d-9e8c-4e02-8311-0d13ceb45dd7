package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.List;

/**
 * Created by tudo<PERSON> on 2018/6/22.
 */

public class ProjectFilingFirtMeetingInfo extends Base {

    private int count;

    private List<ProjectFilingFirtMeeting> list;


    public ProjectFilingFirtMeetingInfo() {
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public List<ProjectFilingFirtMeeting> getList() {
        return list;
    }

    public void setList(List<ProjectFilingFirtMeeting> list) {
        this.list = list;
    }
}
