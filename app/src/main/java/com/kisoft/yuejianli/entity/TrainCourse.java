package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by tudo<PERSON> on 2018/6/20.
 * 培训课程
 */

public class TrainCourse extends Base {

    private String tcoGuid;             // 课程id
    private String ccGuid;              // 课程类别
    private String courseName;          // 课程名
    private String courseCode;          // 课程编码
    private String courseContent;       // 课程内容
    private String coursePurpose;       // 课程目的
    private Double needClass;           // 需要课时
    private Double fee;                 // 培训费用
    private String beginTimeStr;             // 开始时间
    private String endTimeStr;               // 结束时间
    private String orgId;               // 组织id
    private String orgName;             // 组织名称
    private Long minCounts;             // 最小开班人数
    private String isPublic;            // 是否公开
    private String ishaveCertificate;   // 是否有证书
    private String ishaveContract;      // 是否有合同
    private String courseNatrue;        // 课程性质（）
    private String createId;
    private String createName;


    public TrainCourse() {
    }

    public String getTcoGuid() {
        return tcoGuid;
    }

    public void setTcoGuid(String tcoGuid) {
        this.tcoGuid = tcoGuid;
    }

    public String getCcGuid() {
        return ccGuid;
    }

    public void setCcGuid(String ccGuid) {
        this.ccGuid = ccGuid;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getCourseCode() {
        return courseCode;
    }

    public void setCourseCode(String courseCode) {
        this.courseCode = courseCode;
    }

    public String getCourseContent() {
        return courseContent;
    }

    public void setCourseContent(String courseContent) {
        this.courseContent = courseContent;
    }

    public String getCoursePurpose() {
        return coursePurpose;
    }

    public void setCoursePurpose(String coursePurpose) {
        this.coursePurpose = coursePurpose;
    }

    public Double getNeedClass() {
        return needClass;
    }

    public void setNeedClass(Double needClass) {
        this.needClass = needClass;
    }

    public Double getFee() {
        return fee;
    }

    public void setFee(Double fee) {
        this.fee = fee;
    }

    public String getBeginTimeStr() {
        return beginTimeStr;
    }

    public void setBeginTimeStr(String beginTimeStr) {
        this.beginTimeStr = beginTimeStr;
    }

    public String getEndTimeStr() {
        return endTimeStr;
    }

    public void setEndTimeStr(String endTimeStr) {
        this.endTimeStr = endTimeStr;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Long getMinCounts() {
        return minCounts;
    }

    public void setMinCounts(Long minCounts) {
        this.minCounts = minCounts;
    }

    public String getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(String isPublic) {
        this.isPublic = isPublic;
    }

    public String getIshaveCertificate() {
        return ishaveCertificate;
    }

    public void setIshaveCertificate(String ishaveCertificate) {
        this.ishaveCertificate = ishaveCertificate;
    }

    public String getIshaveContract() {
        return ishaveContract;
    }

    public void setIshaveContract(String ishaveContract) {
        this.ishaveContract = ishaveContract;
    }

    public String getCourseNatrue() {
        return courseNatrue;
    }

    public void setCourseNatrue(String courseNatrue) {
        this.courseNatrue = courseNatrue;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
}
