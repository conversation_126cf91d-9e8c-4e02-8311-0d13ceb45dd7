package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by t<PERSON><PERSON> on 2018/7/25.
 */

public class WorkFlowNode extends Base {

    private String nodeId;                       // 节点id
    private String wfId;                         // 工作流id
    private String wftNodeId;                    // 工作流节点模版id
    private String nodeName;                     // 节点名称  （前， 后）
    private long nodeType;                       // 1~9  1(开始) 9（结束）
    private String actor;                        // 节点人员
    private String actorType;                    // 人员类型（C， D， P）
    private long postRule;
    private long state;
    private String creator;                      // 创建人id
    private String fromNode;                     // 上一节点id
    private String dependNode;                   // 依赖节点id
    private String dependPara;

    public WorkFlowNode() {
    }


    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getWfId() {
        return wfId;
    }

    public void setWfId(String wfId) {
        this.wfId = wfId;
    }

    public String getWftNodeId() {
        return wftNodeId;
    }

    public void setWftNodeId(String wftNodeId) {
        this.wftNodeId = wftNodeId;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public long getNodeType() {
        return nodeType;
    }

    public void setNodeType(long nodeType) {
        this.nodeType = nodeType;
    }

    public String getActor() {
        return actor;
    }

    public void setActor(String actor) {
        this.actor = actor;
    }

    public String getActorType() {
        return actorType;
    }

    public void setActorType(String actorType) {
        this.actorType = actorType;
    }

    public long getPostRule() {
        return postRule;
    }

    public void setPostRule(long postRule) {
        this.postRule = postRule;
    }

    public long getState() {
        return state;
    }

    public void setState(long state) {
        this.state = state;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getFromNode() {
        return fromNode;
    }

    public void setFromNode(String fromNode) {
        this.fromNode = fromNode;
    }

    public String getDependNode() {
        return dependNode;
    }

    public void setDependNode(String dependNode) {
        this.dependNode = dependNode;
    }

    public String getDependPara() {
        return dependPara;
    }

    public void setDependPara(String dependPara) {
        this.dependPara = dependPara;
    }
}
