package com.kisoft.yuejianli.entity.event;

import com.kisoft.yuejianli.base.Base;

/**
 * 人脸识别
 */
public class FaceImageEvent extends Base {

    private String bestImageBase64;
    private String path;

    public FaceImageEvent(String bestImageBase64) {
        this.bestImageBase64 = bestImageBase64;
    }

    public FaceImageEvent(String bestImageBase64, String path) {
        this.bestImageBase64 = bestImageBase64;
        this.path = path;
    }

    public String getBestImageBase64() {
        return bestImageBase64;
    }

    public void setBestImageBase64(String bestImageBase64) {
        this.bestImageBase64 = bestImageBase64;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}
