package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.List;

/**
 * 试题详情
 */
public class ExamQuestionInfo extends Base{

    public static final String FAVORITE_TRUE="0";
    public static final String FAVORITE_FALSE="1";
    public static final String Q_TYPE_SINGLE_SELECT="0"; //单选
    public static final String Q_TYPE_MORE_SELECT="1";  //多选
    public static final String Q_TYPE_JUDGE="2";    //判断
    public static final String Q_TYPE_GAP_FILLING="3";  //填空
    public static final String Q_TYPE_SHORT_ANSWER="4"; //简答

    public static final String ANSWER_PROGRESS_NONE="2";
    public static final String ANSWER_PROGRESS_COMPLETE="0";
    public static final String ANSWER_PROGRESS_ERROR="1";
    /**
     * answerContent : É¶½²µÀÀíÈø¿Æ¼¼´øÀ´¿­Èö½«´øÀ´¿¨Èø½¨µµÁ¢¿¨@É¶½²µÀÀíÈø¿Æ¼¼´øÀ´¿­Èö½«´øÀ´¿¨Èø½¨µµÁ¢¿¨@a´ð°¸@°´Ê±@
     * answerList : [{"answerContent":"啥讲道理萨科技带来凯撒将带来卡萨建档立卡","eqbId":"200620182219831da36dfc0d2af08159","eaId":"200620182400088bad04f6f1c342216c","eqId":"20062018240002419339b09072a14999"},{"answerContent":"按时","eqbId":"200620182219831da36dfc0d2af08159","eaId":"20062018240021458dd2714191ca3de6","eqId":"20062018240002419339b09072a14999"},{"answerContent":"啥讲道理萨科技带来凯撒将带来卡萨建档立卡","eqbId":"200620182219831da36dfc0d2af08159","eaId":"200620182400025bb88c85cb71b08ad1","eqId":"20062018240002419339b09072a14999"},{"answerContent":"a答案","eqbId":"200620182219831da36dfc0d2af08159","eaId":"200620182400148e965852f1fa5a4479","eqId":"20062018240002419339b09072a14999"}]
     * createTime : 2020-06-20
     * diffDegree : 0
     * eqbId : 200620182219831da36dfc0d2af08159
     * scoreSet : 12
     * remark :
     * answer : A B C D
     * qustName : 候占国监理考试
     * createName : 候占国
     * eqId : 20062018240002419339b09072a14999
     * diffDegreeName : 一般
     * qustType : 1
     * points : 按时大
     * qustTypeName :
     * answerAnalsis : 啥讲道理萨科技带来凯撒将带来卡萨建档立卡
     * createId : 20060815132615719b585fe24fd3a821
     */

    private String eqId;        //主键
    private String eqbId;       //所属题库Id
    private String eqbName;       //所属题库
    private String qustName;        //题目名称
    private String diffDegree;      //难易程度 0=简单、1=一般、2=难、3=较难、4=困难
    private String qustType;        //试题类型 0=单选题、1=多选题、2=判断、3=填空、4=简答
    private String answer;      //标准答案 答案用逗号隔开(##)
    private String points;      //知识点
    private String answerAnalsis;       //答案解析
    private String remark;      //备注
    private String createId;        //创建人Id    系统当前人员Id
    private String createName;      //创建人姓名    系统当前人员姓名
    private String createTime;      //创建时间 系统当前时间
    private String answerContent;   //选项答案(备用)

    private String scoreSet;    //分值设置
    private String qustTypeName;    //试题类型名称
    private String diffDegreeName;  //难易程度名称

    //答题进度
    private String isRight=ANSWER_PROGRESS_NONE;//是否正确   0:正确;1:错误;2:未答
    private String isFavorite;   //是否已收藏 //是否已收藏 0:已收藏 1:已移除

    //是否回答题
    private boolean hasAnswer;
    private boolean isSelect;
    private boolean showAnalysis;

    private List<AnswerListBean> answerList;

    private String taskAnswer;   //考试答案
    private String answerOption;

    public String getIsFavorite() {
        return isFavorite;
    }

    public void setIsFavorite(String isFavorite) {
        this.isFavorite = isFavorite;
    }

    public String getEqbName() {
        return eqbName;
    }

    public void setEqbName(String eqbName) {
        this.eqbName = eqbName;
    }

    public String getAnswerOption() {
        return answerOption;
    }

    public void setAnswerOption(String answerOption) {
        this.answerOption = answerOption;
    }

    public String getTaskAnswer() {
        return taskAnswer;
    }

    public void setTaskAnswer(String taskAnswer) {
        this.taskAnswer = taskAnswer;
    }

    public boolean isShowAnalysis() {
        return showAnalysis;
    }

    public void setShowAnalysis(boolean showAnalysis) {
        this.showAnalysis = showAnalysis;
    }

    public boolean isSelect() {
        return isSelect;
    }

    public void setSelect(boolean select) {
        isSelect = select;
    }

    public boolean isHasAnswer() {
        return hasAnswer;
    }

    public void setHasAnswer(boolean hasAnswer) {
        this.hasAnswer = hasAnswer;
    }

    public String getIsRight() {
        return isRight;
    }

    public void setIsRight(String isRight) {
        this.isRight = isRight;
    }

    public String getAnswerContent() {
        return answerContent;
    }

    public void setAnswerContent(String answerContent) {
        this.answerContent = answerContent;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getDiffDegree() {
        return diffDegree;
    }

    public void setDiffDegree(String diffDegree) {
        this.diffDegree = diffDegree;
    }

    public String getEqbId() {
        return eqbId;
    }

    public void setEqbId(String eqbId) {
        this.eqbId = eqbId;
    }

    public String getScoreSet() {
        return scoreSet;
    }

    public void setScoreSet(String scoreSet) {
        this.scoreSet = scoreSet;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public String getQustName() {
        return qustName;
    }

    public void setQustName(String qustName) {
        this.qustName = qustName;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getEqId() {
        return eqId;
    }

    public void setEqId(String eqId) {
        this.eqId = eqId;
    }

    public String getDiffDegreeName() {
        return diffDegreeName;
    }

    public void setDiffDegreeName(String diffDegreeName) {
        this.diffDegreeName = diffDegreeName;
    }

    public String getQustType() {
        return qustType;
    }

    public void setQustType(String qustType) {
        this.qustType = qustType;
    }

    public String getPoints() {
        return points;
    }

    public void setPoints(String points) {
        this.points = points;
    }

    public String getQustTypeName() {
        return qustTypeName;
    }

    public void setQustTypeName(String qustTypeName) {
        this.qustTypeName = qustTypeName;
    }

    public String getAnswerAnalsis() {
        return answerAnalsis;
    }

    public void setAnswerAnalsis(String answerAnalsis) {
        this.answerAnalsis = answerAnalsis;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public List<AnswerListBean> getAnswerList() {
        return answerList;
    }

    public void setAnswerList(List<AnswerListBean> answerList) {
        this.answerList = answerList;
    }

    public static class AnswerListBean extends Base {
        /**
         * answerContent : 啥讲道理萨科技带来凯撒将带来卡萨建档立卡
         * eqbId : 200620182219831da36dfc0d2af08159
         * eaId : 200620182400088bad04f6f1c342216c
         * eqId : 20062018240002419339b09072a14999
         */

        private String answerContent;
        private String eqbId;
        private String eaId;
        private String eqId;
        private String scoreSet;
        private String taskAnswer;
        private String answer;
        private String qustName;
        private String answerOption;
        //是否选中
        private boolean isSelect;

        public AnswerListBean() {
        }

        public String getScoreSet() {
            return scoreSet;
        }

        public void setScoreSet(String scoreSet) {
            this.scoreSet = scoreSet;
        }

        public String getTaskAnswer() {
            return taskAnswer;
        }

        public void setTaskAnswer(String taskAnswer) {
            this.taskAnswer = taskAnswer;
        }

        public String getAnswer() {
            return answer;
        }

        public void setAnswer(String answer) {
            this.answer = answer;
        }

        public String getQustName() {
            return qustName;
        }

        public void setQustName(String qustName) {
            this.qustName = qustName;
        }

        public String getAnswerOption() {
            return answerOption;
        }

        public void setAnswerOption(String answerOption) {
            this.answerOption = answerOption;
        }

        public AnswerListBean(String answerContent) {
            this.answerContent = answerContent;
        }

        public boolean isSelect() {
            return isSelect;
        }

        public void setSelect(boolean select) {
            isSelect = select;
        }

        public String getAnswerContent() {
            return answerContent;
        }

        public void setAnswerContent(String answerContent) {
            this.answerContent = answerContent;
        }

        public String getEqbId() {
            return eqbId;
        }

        public void setEqbId(String eqbId) {
            this.eqbId = eqbId;
        }

        public String getEaId() {
            return eaId;
        }

        public void setEaId(String eaId) {
            this.eaId = eaId;
        }

        public String getEqId() {
            return eqId;
        }

        public void setEqId(String eqId) {
            this.eqId = eqId;
        }
    }
}
