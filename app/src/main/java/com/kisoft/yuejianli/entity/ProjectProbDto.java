package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by Administrator on 2019/7/1 0001.
 */

public class ProjectProbDto  extends Base {
    private String ppId;                // 主键
    private String ppNum;               // 表单编号
    private String projectId;           // 项目 Id
    private String projectName;         // 项目名称
    private String unitEngineer;        // 单位工程
    private String probPart;            // 问题部位
    private String probType;            // 问题类型 0=从问题参数库中选择(质量问题、安全问题、人员问题、工艺 问题、环保问题、程序问题、其他问题)（关联参数设 置）
    private String company;             // 施工单位
    private String trackUserId;         // 跟踪人 Id
    private String trackUserName;       // 跟踪人姓名新增问题记录 悦监理-APP-v2.0 接口文档 2019-1-18 西安凯悦软件有限责任公司 版权所有 翻版必究
    private String compDate;            // 限期完成时间
    private String probClass;           // 问题类别 0=巡检、1=旁站、2=质量、3=进度、4=安全、5=治污减霾、6=其他
    private String hazardSource;        // 涉及重大危险源从重大危险源库中关联
    private String supervisionType;     // 下发监理通知单类型 0,1,2,3,4,5
    private String measTypeId;          // 措施类型Id,措施类型对应的如：通知单 Id、会议 Id 等
    private String measType;            // 措施类型，0=口头、1=通知单、2=会议、3=其他
    private String remark;              // 备注
    private String probClassId;         // 问题类别 Id,与问题类别对应，关联巡检、旁站、质量、进度、安全、治污减霾、其他等的主键 Id
    private String snEnclosure;         // 附件
    private String state;               // 状态，0=未提交、1=整改中、2=已完成、3=已复查
    private String createId;            // 创建人 Id
    private String createName;          // 创建人姓名
    private String createTime;          // 创建时间
    private String probName;            // 问题名称
    private String probLevel;           // 问题等级 0=一般、1=严重、2=重大
    //满足综合查询条件之用
    private String beginTimeStr;        // 开始时间
    private String endTimeStr;          // 结束时间
    private String beginCompDate;       // 限期完成时间-开始
    private String endCompDate;         // 限期完成时间-结束
    //已复查记录查询条件及问题综合查询
    private String beginReviewDate;     // 开始-复查日期
    private String endReviewDate;       // 结束-复查日期
    private String reviewResults;       // 复查结果
    private String rectDate;            // 整改日期
    private String reviewDate;          // 复查日期
    private String beginRectDate;       // 开始整改日期
    private String endRectDate;         // 结束整改日期

    public String getPpId() {
        return ppId;
    }

    public void setPpId(String ppId) {
        this.ppId = ppId;
    }

    public String getPpNum() {
        return ppNum;
    }

    public void setPpNum(String ppNum) {
        this.ppNum = ppNum;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getUnitEngineer() {
        return unitEngineer;
    }

    public void setUnitEngineer(String unitEngineer) {
        this.unitEngineer = unitEngineer;
    }

    public String getProbPart() {
        return probPart;
    }

    public void setProbPart(String probPart) {
        this.probPart = probPart;
    }

    public String getProbType() {
        return probType;
    }

    public void setProbType(String probType) {
        this.probType = probType;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getTrackUserId() {
        return trackUserId;
    }

    public void setTrackUserId(String trackUserId) {
        this.trackUserId = trackUserId;
    }

    public String getTrackUserName() {
        return trackUserName;
    }

    public void setTrackUserName(String trackUserName) {
        this.trackUserName = trackUserName;
    }

    public String getCompDate() {
        return compDate;
    }

    public void setCompDate(String compDate) {
        this.compDate = compDate;
    }

    public String getProbClass() {
        return probClass;
    }

    public void setProbClass(String probClass) {
        this.probClass = probClass;
    }

    public String getHazardSource() {
        return hazardSource;
    }

    public void setHazardSource(String hazardSource) {
        this.hazardSource = hazardSource;
    }

    public String getSupervisionType() {
        return supervisionType;
    }

    public void setSupervisionType(String supervisionType) {
        this.supervisionType = supervisionType;
    }

    public String getMeasTypeId() {
        return measTypeId;
    }

    public void setMeasTypeId(String measTypeId) {
        this.measTypeId = measTypeId;
    }

    public String getMeasType() {
        return measType;
    }

    public void setMeasType(String measType) {
        this.measType = measType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getProbClassId() {
        return probClassId;
    }

    public void setProbClassId(String probClassId) {
        this.probClassId = probClassId;
    }

    public String getSnEnclosure() {
        return snEnclosure;
    }

    public void setSnEnclosure(String snEnclosure) {
        this.snEnclosure = snEnclosure;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getProbName() {
        return probName;
    }

    public void setProbName(String probName) {
        this.probName = probName;
    }

    public String getProbLevel() {
        return probLevel;
    }

    public void setProbLevel(String probLevel) {
        this.probLevel = probLevel;
    }

    public String getBeginTimeStr() {
        return beginTimeStr;
    }

    public void setBeginTimeStr(String beginTimeStr) {
        this.beginTimeStr = beginTimeStr;
    }

    public String getEndTimeStr() {
        return endTimeStr;
    }

    public void setEndTimeStr(String endTimeStr) {
        this.endTimeStr = endTimeStr;
    }

    public String getBeginCompDate() {
        return beginCompDate;
    }

    public void setBeginCompDate(String beginCompDate) {
        this.beginCompDate = beginCompDate;
    }

    public String getEndCompDate() {
        return endCompDate;
    }

    public void setEndCompDate(String endCompDate) {
        this.endCompDate = endCompDate;
    }

    public String getBeginReviewDate() {
        return beginReviewDate;
    }

    public void setBeginReviewDate(String beginReviewDate) {
        this.beginReviewDate = beginReviewDate;
    }

    public String getEndReviewDate() {
        return endReviewDate;
    }

    public void setEndReviewDate(String endReviewDate) {
        this.endReviewDate = endReviewDate;
    }

    public String getReviewResults() {
        return reviewResults;
    }

    public void setReviewResults(String reviewResults) {
        this.reviewResults = reviewResults;
    }

    public String getRectDate() {
        return rectDate;
    }

    public void setRectDate(String rectDate) {
        this.rectDate = rectDate;
    }

    public String getReviewDate() {
        return reviewDate;
    }

    public void setReviewDate(String reviewDate) {
        this.reviewDate = reviewDate;
    }

    public String getBeginRectDate() {
        return beginRectDate;
    }

    public void setBeginRectDate(String beginRectDate) {
        this.beginRectDate = beginRectDate;
    }

    public String getEndRectDate() {
        return endRectDate;
    }

    public void setEndRectDate(String endRectDate) {
        this.endRectDate = endRectDate;
    }
}
