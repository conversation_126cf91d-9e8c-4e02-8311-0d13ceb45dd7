package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.List;

/**
 * 考核自评
 */
public class PeEvaluateDetail extends Base {

    private String peGuid;//主键
    private String ptGuid;//所属考核任务ID
    private String pdGuid;//所属考核指标明细ID
    private String user1Guid;//考核人id
    private String user1Name;
    private String user2Guid;//被考核人员ID
    private String user2Name;
    private String grade;//考核得分
    private String createTime;
    private String type;//考评类型
    private String remark;//备注
    private String weight1;//自评权重
    private String peState;
    private String ptName;
    private String grade1;
    private String itemName;
    private String selfEvalState;
    private String stateName;
    private String peType;
    private String end;
    private String state;
    private String lowGrade;
    private String peStateName;
    private String start;
    private String weight;
    private String userGuid;
    private String userName;
    private String piName;
    private String selfEvalStateName;
    private String lowhight;
    private String hightGrade;
    private List<TPeGradeDTO> peGrades;//考核统计

    public String getPeState() {
        return peState;
    }

    public void setPeState(String peState) {
        this.peState = peState;
    }

    public String getPtName() {
        return ptName;
    }

    public void setPtName(String ptName) {
        this.ptName = ptName;
    }

    public String getGrade1() {
        return grade1;
    }

    public void setGrade1(String grade1) {
        this.grade1 = grade1;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getSelfEvalState() {
        return selfEvalState;
    }

    public void setSelfEvalState(String selfEvalState) {
        this.selfEvalState = selfEvalState;
    }

    public String getStateName() {
        return stateName;
    }

    public void setStateName(String stateName) {
        this.stateName = stateName;
    }

    public String getPeType() {
        return peType;
    }

    public void setPeType(String peType) {
        this.peType = peType;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getLowGrade() {
        return lowGrade;
    }

    public void setLowGrade(String lowGrade) {
        this.lowGrade = lowGrade;
    }

    public String getPeStateName() {
        return peStateName;
    }

    public void setPeStateName(String peStateName) {
        this.peStateName = peStateName;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPiName() {
        return piName;
    }

    public void setPiName(String piName) {
        this.piName = piName;
    }

    public String getSelfEvalStateName() {
        return selfEvalStateName;
    }

    public void setSelfEvalStateName(String selfEvalStateName) {
        this.selfEvalStateName = selfEvalStateName;
    }

    public String getLowhight() {
        return lowhight;
    }

    public void setLowhight(String lowhight) {
        this.lowhight = lowhight;
    }

    public String getHightGrade() {
        return hightGrade;
    }

    public void setHightGrade(String hightGrade) {
        this.hightGrade = hightGrade;
    }

    public String getPeGuid() {
        return peGuid;
    }

    public void setPeGuid(String peGuid) {
        this.peGuid = peGuid;
    }

    public String getPtGuid() {
        return ptGuid;
    }

    public void setPtGuid(String ptGuid) {
        this.ptGuid = ptGuid;
    }

    public String getPdGuid() {
        return pdGuid;
    }

    public void setPdGuid(String pdGuid) {
        this.pdGuid = pdGuid;
    }

    public String getUser1Guid() {
        return user1Guid;
    }

    public void setUser1Guid(String user1Guid) {
        this.user1Guid = user1Guid;
    }

    public String getUser1Name() {
        return user1Name;
    }

    public void setUser1Name(String user1Name) {
        this.user1Name = user1Name;
    }

    public String getUser2Guid() {
        return user2Guid;
    }

    public void setUser2Guid(String user2Guid) {
        this.user2Guid = user2Guid;
    }

    public String getUser2Name() {
        return user2Name;
    }

    public void setUser2Name(String user2Name) {
        this.user2Name = user2Name;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getWeight1() {
        return weight1;
    }

    public void setWeight1(String weight1) {
        this.weight1 = weight1;
    }

    public List<TPeGradeDTO> getPeGrades() {
        return peGrades;
    }

    public void setPeGrades(List<TPeGradeDTO> peGrades) {
        this.peGrades = peGrades;
    }

    public static class TPeGradeDTO extends Base{
        private String pgGuid;//主键
        private String ptGuid;//所属考核任务ID
        private String userGuid;//被考核人ID
        private String userName;//被考核人姓名
        private String totalgrade;//得分
        private String createTime;//创建时间
        private String describe;//描述(备注)

        public TPeGradeDTO() {
        }

        public TPeGradeDTO(String pgGuid, String ptGuid, String userGuid, String userName) {
            this.pgGuid = pgGuid;
            this.ptGuid = ptGuid;
            this.userGuid = userGuid;
            this.userName = userName;
        }

        public String getPgGuid() {
            return pgGuid;
        }

        public void setPgGuid(String pgGuid) {
            this.pgGuid = pgGuid;
        }

        public String getPtGuid() {
            return ptGuid;
        }

        public void setPtGuid(String ptGuid) {
            this.ptGuid = ptGuid;
        }

        public String getUserGuid() {
            return userGuid;
        }

        public void setUserGuid(String userGuid) {
            this.userGuid = userGuid;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getTotalgrade() {
            return totalgrade;
        }

        public void setTotalgrade(String totalgrade) {
            this.totalgrade = totalgrade;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getDescribe() {
            return describe;
        }

        public void setDescribe(String describe) {
            this.describe = describe;
        }
    }

}
