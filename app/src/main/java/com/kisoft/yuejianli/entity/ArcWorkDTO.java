package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Description: 公文信息
 * Author     : yanlu
 * Date       : 2019/1/3 16:27
 */

public class ArcWorkDTO extends Base{

    /**
     * "index": "",
     "flowTaskState": 3,
     "draftDate": {
     "month": 1,
     "day": 5,
     "year": 119,
     "nanos": 0,
     "seconds": 54,
     "time": 1550222394000,
     "date": 15,
     "timezoneOffset": -480,
     "hours": 17,
     "minutes": 19
     },
     "flowId": "190215152645727eb21d12b295b183b6",
     "draftMan": "",
     "toLevelId": 0,
     "providerId": "",
     "businessId": "190215152645707ea1e5bed9c4114319",
     "typeId": "",
     "businessType": "t_doc_archives",
     "formLevelId": 0,
     "wfType": "1",
     "flowTaskId": "19021517195433539b8a0db342ac5ddb",
     "flowState": "3",
     "name": "2019-2-15-流程测试",
     "flowStateName": "未办理"
     */
    //新增字段
    private String index ;
    private String toLevelId ;
    private String providerId ;
    private String typeId ;
    private String businessType ;
    private String formLevelId ;
    private String wfType ;
    private String flowTaskId ;
    private String flowState ;
    private String flowStateName ;
    private DraftDate draftDate;

    private String mtdId;
    private String content;
    private String createTimeStr;


    private String businessId ;  //公文主键
    private String flowId;     //流程Id
    private String flowTaskState; //流程状态
    private String name;   //标题
    private String draftMan; //起草人

    public String getMtdId() {
        return mtdId;
    }

    public void setMtdId(String mtdId) {
        this.mtdId = mtdId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getToLevelId() {
        return toLevelId;
    }

    public void setToLevelId(String toLevelId) {
        this.toLevelId = toLevelId;
    }

    public String getProviderId() {
        return providerId;
    }

    public void setProviderId(String providerId) {
        this.providerId = providerId;
    }

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getFormLevelId() {
        return formLevelId;
    }

    public void setFormLevelId(String formLevelId) {
        this.formLevelId = formLevelId;
    }

    public String getWfType() {
        return wfType;
    }

    public void setWfType(String wfType) {
        this.wfType = wfType;
    }

    public String getFlowTaskId() {
        return flowTaskId;
    }

    public void setFlowTaskId(String flowTaskId) {
        this.flowTaskId = flowTaskId;
    }

    public String getFlowState() {
        return flowState;
    }

    public void setFlowState(String flowState) {
        this.flowState = flowState;
    }

    public String getFlowStateName() {
        return flowStateName;
    }

    public void setFlowStateName(String flowStateName) {
        this.flowStateName = flowStateName;
    }

    public void setDraftDate(DraftDate draftDate) {
        this.draftDate = draftDate;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getFlowId() {
        return flowId;
    }

    public void setFlowId(String flowId) {
        this.flowId = flowId;
    }

    public String getFlowTaskState() {
        return flowTaskState;
    }

    public void setFlowTaskState(String flowTaskState) {
        this.flowTaskState = flowTaskState;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDraftMan() {
        return draftMan;
    }

    public DraftDate getDraftDate() {
        return draftDate;
    }

    public void setDraftMan(String draftMan) {
        this.draftMan = draftMan;
    }

    public static class DraftDate extends Base {
        private String month;
        private String day;
        private String year;
        private String nanos;
        private String seconds;
        private String time;
        private String date;
        private String timezoneOffset;
        private String hours;
        private String minutes;

        public String getMonth() {
            return month;
        }

        public void setMonth(String month) {
            this.month = month;
        }

        public String getDay() {
            return day;
        }

        public void setDay(String day) {
            this.day = day;
        }

        public String getYear() {
            return year;
        }

        public void setYear(String year) {
            this.year = year;
        }

        public String getNanos() {
            return nanos;
        }

        public void setNanos(String nanos) {
            this.nanos = nanos;
        }

        public String getSeconds() {
            return seconds;
        }

        public void setSeconds(String seconds) {
            this.seconds = seconds;
        }

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public String getTimezoneOffset() {
            return timezoneOffset;
        }

        public void setTimezoneOffset(String timezoneOffset) {
            this.timezoneOffset = timezoneOffset;
        }

        public String getHours() {
            return hours;
        }

        public void setHours(String hours) {
            this.hours = hours;
        }

        public String getMinutes() {
            return minutes;
        }

        public void setMinutes(String minutes) {
            this.minutes = minutes;
        }
    }
}
