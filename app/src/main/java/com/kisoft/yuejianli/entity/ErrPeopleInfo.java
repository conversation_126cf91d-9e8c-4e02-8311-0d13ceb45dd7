package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.List;

public class ErrPeopleInfo extends Base {
        /**
         * count : 1
         * list : [{"createTime":"","state":"外勤","remarks":"","endDate":"","projectId":"","beginDate":"","orgName":"","workHours":"","attendType":"","bwAllDates":"","punchPoint1":"","orgId":"","punchTime2":"","punchPoint3":"","punchPoint2":"","punchTime1":"","punchPoint4":"","asId":"2004291507400732b9cb7c7461affa7c","userId":"1411061510426418964b4caed722b3b7","userName":"王茜","overTimeHours":"","projectName":"","punchTime4":"","punchTime3":""}]
         */

        private int count;
        private List<ListBean> list;

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public List<ListBean> getList() {
            return list;
        }

        public void setList(List<ListBean> list) {
            this.list = list;
        }

        public static class ListBean {
            /**
             * createTime :
             * state : 外勤
             * remarks :
             * endDate :
             * projectId :
             * beginDate :
             * orgName :
             * workHours :
             * attendType :
             * bwAllDates :
             * punchPoint1 :
             * orgId :
             * punchTime2 :
             * punchPoint3 :
             * punchPoint2 :
             * punchTime1 :
             * punchPoint4 :
             * asId : 2004291507400732b9cb7c7461affa7c
             * userId : 1411061510426418964b4caed722b3b7
             * userName : 王茜
             * overTimeHours :
             * projectName :
             * punchTime4 :
             * punchTime3 :
             */

            private String createTime;
            private String state;
            private String remarks;
            private String endDate;
            private String projectId;
            private String beginDate;
            private String orgName;
            private String workHours;
            private String attendType;
            private String bwAllDates;
            private String punchPoint1;
            private String orgId;
            private String punchTime2;
            private String punchPoint3;
            private String punchPoint2;
            private String punchTime1;
            private String punchPoint4;
            private String asId;
            private String userId;
            private String userName;
            private String overTimeHours;
            private String projectName;
            private String punchTime4;
            private String punchTime3;

            public String getCreateTime() {
                return createTime;
            }

            public void setCreateTime(String createTime) {
                this.createTime = createTime;
            }

            public String getState() {
                return state;
            }

            public void setState(String state) {
                this.state = state;
            }

            public String getRemarks() {
                return remarks;
            }

            public void setRemarks(String remarks) {
                this.remarks = remarks;
            }

            public String getEndDate() {
                return endDate;
            }

            public void setEndDate(String endDate) {
                this.endDate = endDate;
            }

            public String getProjectId() {
                return projectId;
            }

            public void setProjectId(String projectId) {
                this.projectId = projectId;
            }

            public String getBeginDate() {
                return beginDate;
            }

            public void setBeginDate(String beginDate) {
                this.beginDate = beginDate;
            }

            public String getOrgName() {
                return orgName;
            }

            public void setOrgName(String orgName) {
                this.orgName = orgName;
            }

            public String getWorkHours() {
                return workHours;
            }

            public void setWorkHours(String workHours) {
                this.workHours = workHours;
            }

            public String getAttendType() {
                return attendType;
            }

            public void setAttendType(String attendType) {
                this.attendType = attendType;
            }

            public String getBwAllDates() {
                return bwAllDates;
            }

            public void setBwAllDates(String bwAllDates) {
                this.bwAllDates = bwAllDates;
            }

            public String getPunchPoint1() {
                return punchPoint1;
            }

            public void setPunchPoint1(String punchPoint1) {
                this.punchPoint1 = punchPoint1;
            }

            public String getOrgId() {
                return orgId;
            }

            public void setOrgId(String orgId) {
                this.orgId = orgId;
            }

            public String getPunchTime2() {
                return punchTime2;
            }

            public void setPunchTime2(String punchTime2) {
                this.punchTime2 = punchTime2;
            }

            public String getPunchPoint3() {
                return punchPoint3;
            }

            public void setPunchPoint3(String punchPoint3) {
                this.punchPoint3 = punchPoint3;
            }

            public String getPunchPoint2() {
                return punchPoint2;
            }

            public void setPunchPoint2(String punchPoint2) {
                this.punchPoint2 = punchPoint2;
            }

            public String getPunchTime1() {
                return punchTime1;
            }

            public void setPunchTime1(String punchTime1) {
                this.punchTime1 = punchTime1;
            }

            public String getPunchPoint4() {
                return punchPoint4;
            }

            public void setPunchPoint4(String punchPoint4) {
                this.punchPoint4 = punchPoint4;
            }

            public String getAsId() {
                return asId;
            }

            public void setAsId(String asId) {
                this.asId = asId;
            }

            public String getUserId() {
                return userId;
            }

            public void setUserId(String userId) {
                this.userId = userId;
            }

            public String getUserName() {
                return userName;
            }

            public void setUserName(String userName) {
                this.userName = userName;
            }

            public String getOverTimeHours() {
                return overTimeHours;
            }

            public void setOverTimeHours(String overTimeHours) {
                this.overTimeHours = overTimeHours;
            }

            public String getProjectName() {
                return projectName;
            }

            public void setProjectName(String projectName) {
                this.projectName = projectName;
            }

            public String getPunchTime4() {
                return punchTime4;
            }

            public void setPunchTime4(String punchTime4) {
                this.punchTime4 = punchTime4;
            }

            public String getPunchTime3() {
                return punchTime3;
            }

            public void setPunchTime3(String punchTime3) {
                this.punchTime3 = punchTime3;
            }
        }
}
