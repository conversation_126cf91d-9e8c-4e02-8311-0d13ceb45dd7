package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by Administrator on 2019/4/15 0015.
 */

public class ApplyJKInfo extends Base {
    private String oelNum;      //借款单据
    private String projectId;   //项目 Id
    private String projectName; //项目名称
    private String remark;      //借款事由
    private String userId;      //借款人 ID
    private String userName;    //借款人姓名
    private String loanDate;    //借款时间
    private String loanAmount;  //借款金额
    private String wfId;
    private String oelId;

    public String getWfId() {
        return wfId;
    }

    public void setWfId(String wfId) {
        this.wfId = wfId;
    }

    public String getOelNum() {
        return oelNum;
    }

    public void setOelNum(String oelNum) {
        this.oelNum = oelNum;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getLoanDate() {
        return loanDate;
    }

    public void setLoanDate(String loanDate) {
        this.loanDate = loanDate;
    }

    public String getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(String loanAmount) {
        this.loanAmount = loanAmount;
    }

    public String getOelId() {
        return oelId;
    }

    public void setOelId(String oelId) {
        this.oelId = oelId;
    }

    public Map<String, Object> getParameters(){

        Map<String, Object> parameters = new HashMap();
        parameters.put("oelNum", oelNum);
        parameters.put("projectId", projectId);
        parameters.put("projectName", projectName);
        parameters.put("remark", remark);
        parameters.put("userId", userId);
        parameters.put("userName", userName);
        parameters.put("loanDate", loanDate);
        parameters.put("loanAmount", loanAmount);
        parameters.put("oelId", oelId);

        return parameters;
    }
}
