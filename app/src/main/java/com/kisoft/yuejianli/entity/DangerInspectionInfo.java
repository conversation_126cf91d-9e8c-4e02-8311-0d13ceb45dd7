package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.Date;

/**
 * 危大工程
 */
public class DangerInspectionInfo extends Base {
    private String diId;        //主键
    private String projectId;       //项目id
    private String projectName;     //项目名称
    private String diNumber;        //编号
    private String builder;     //施工单位
    private String reviewPosition;      //检查部位
    private String imageProgress;       //形象进度
    private String reviewMode;      //检查方式
    private String reviewContent;       //检查内容
    private String problem;     //存在问题
    private String opinion;     //处理意见
    private String enclosure;       //照片,附件
    private String remark;      //备注
    private String createId;        //创建人Id
    private String createName;      //创建人姓名
    private String createTime;       //创建时间
    private String processResult;       ////处理结果 0--合格  1-不合格

    private String unitProjectId;
    private String criticalType; //危大工程类型
    private String criticalProject; //危大工程

    public String getDiId() {
        return diId;
    }

    public void setDiId(String diId) {
        this.diId = diId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getDiNumber() {
        return diNumber;
    }

    public void setDiNumber(String diNumber) {
        this.diNumber = diNumber;
    }

    public String getBuilder() {
        return builder;
    }

    public void setBuilder(String builder) {
        this.builder = builder;
    }

    public String getReviewPosition() {
        return reviewPosition;
    }

    public void setReviewPosition(String reviewPosition) {
        this.reviewPosition = reviewPosition;
    }

    public String getImageProgress() {
        return imageProgress;
    }

    public void setImageProgress(String imageProgress) {
        this.imageProgress = imageProgress;
    }

    public String getReviewMode() {
        return reviewMode;
    }

    public void setReviewMode(String reviewMode) {
        this.reviewMode = reviewMode;
    }

    public String getReviewContent() {
        return reviewContent;
    }

    public void setReviewContent(String reviewContent) {
        this.reviewContent = reviewContent;
    }

    public String getProblem() {
        return problem;
    }

    public void setProblem(String problem) {
        this.problem = problem;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public String getEnclosure() {
        return enclosure;
    }

    public void setEnclosure(String enclosure) {
        this.enclosure = enclosure;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getProcessResult() {
        return processResult;
    }

    public void setProcessResult(String processResult) {
        this.processResult = processResult;
    }

    public String getUnitProjectId() {
        return unitProjectId;
    }

    public void setUnitProjectId(String unitProjectId) {
        this.unitProjectId = unitProjectId;
    }

    public String getCriticalType() {
        return criticalType;
    }

    public void setCriticalType(String criticalType) {
        this.criticalType = criticalType;
    }

    public String getCriticalProject() {
        return criticalProject;
    }

    public void setCriticalProject(String criticalProject) {
        this.criticalProject = criticalProject;
    }
}
