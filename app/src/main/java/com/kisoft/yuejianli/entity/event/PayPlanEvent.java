package com.kisoft.yuejianli.entity.event;

import com.kisoft.yuejianli.entity.ConInfo;

/**
 * 支付计划
 */
public class PayPlanEvent {

    private ConInfo.ConPlanPaymentDto planPayment;
    private int pos;
    private boolean isDelete;

    public PayPlanEvent(ConInfo.ConPlanPaymentDto planPayment, int pos, boolean isDelete) {
        this.planPayment = planPayment;
        this.pos = pos;
        this.isDelete = isDelete;
    }

    public boolean isDelete() {
        return isDelete;
    }

    public void setDelete(boolean delete) {
        isDelete = delete;
    }

    public ConInfo.ConPlanPaymentDto getPlanPayment() {
        return planPayment;
    }

    public void setPlanPayment(ConInfo.ConPlanPaymentDto planPayment) {
        this.planPayment = planPayment;
    }

    public int getPos() {
        return pos;
    }

    public void setPos(int pos) {
        this.pos = pos;
    }
}
