package com.kisoft.yuejianli.entity;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;

/**
 * Created by Administrator on 2019/10/29 0029.
 */

public class AddressPointTypeAdapter extends TypeAdapter<AddressPoint> {
    @Override
    public void write(JsonWriter out, AddressPoint value) throws IOException {
        out.beginObject();
        //按自定义顺序输出字段信息
        out.name("longitude").value(value.getLongitude());
        out.name("latitude").value(value.getLatitude());
        out.endObject();
    }

    @Override
    public AddressPoint read(JsonReader in) throws IOException {
        return null;
    }
}
