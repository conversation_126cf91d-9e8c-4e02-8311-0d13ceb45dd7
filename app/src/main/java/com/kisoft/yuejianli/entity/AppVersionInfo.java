package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by tudo<PERSON> on 2018/7/27.
 */

public class AppVersionInfo extends Base {

    private String id;			            // 主键
    private int code;                       // 版本号
    private String system;	                // 手机系统
    private String state;		            // 状态(是否开启更新)
    private String url;                     // 下载地址
    private String details;                 // 更新详情


    public AppVersionInfo() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getVersionCode() {
        return code;
    }

    public void setVersionCode(int versionCode) {
        this.code = versionCode;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }
}
