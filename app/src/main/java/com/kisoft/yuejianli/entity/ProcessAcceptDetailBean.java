package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.List;

public class ProcessAcceptDetailBean {

    private String no;
    private String resultDate;
    private List<AddRowFiledDataBean> addRowFiled;
    private String designPerson;
    private String adminEngineer;
    private String designCompany;
    private String adminCompanyIdea;
    private String unitId;
    private String dateThree;
    private String id;
    private String workCompanyIdea;
    private String dateOne;
    private String dateFour;
    private String proLeader;
    private String unitName;
    private String keyProName;
    private String proPerson;
    private String buildCompany;
    private String createTime;
    private String createId;
    private String dateTwo;
    private String proTechName;
    private String projectName;
    private String projectId;
    private String createName;

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public String getResultDate() {
        return resultDate;
    }

    public void setResultDate(String resultDate) {
        this.resultDate = resultDate;
    }

    public List<AddRowFiledDataBean> getAddRowFiled() {
        return addRowFiled;
    }

    public void setAddRowFiled(List<AddRowFiledDataBean> addRowFiled) {
        this.addRowFiled = addRowFiled;
    }

    public String getDesignPerson() {
        return designPerson;
    }

    public void setDesignPerson(String designPerson) {
        this.designPerson = designPerson;
    }

    public String getAdminEngineer() {
        return adminEngineer;
    }

    public void setAdminEngineer(String adminEngineer) {
        this.adminEngineer = adminEngineer;
    }

    public String getDesignCompany() {
        return designCompany;
    }

    public void setDesignCompany(String designCompany) {
        this.designCompany = designCompany;
    }

    public String getAdminCompanyIdea() {
        return adminCompanyIdea;
    }

    public void setAdminCompanyIdea(String adminCompanyIdea) {
        this.adminCompanyIdea = adminCompanyIdea;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getDateThree() {
        return dateThree;
    }

    public void setDateThree(String dateThree) {
        this.dateThree = dateThree;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWorkCompanyIdea() {
        return workCompanyIdea;
    }

    public void setWorkCompanyIdea(String workCompanyIdea) {
        this.workCompanyIdea = workCompanyIdea;
    }

    public String getDateOne() {
        return dateOne;
    }

    public void setDateOne(String dateOne) {
        this.dateOne = dateOne;
    }

    public String getDateFour() {
        return dateFour;
    }

    public void setDateFour(String dateFour) {
        this.dateFour = dateFour;
    }

    public String getProLeader() {
        return proLeader;
    }

    public void setProLeader(String proLeader) {
        this.proLeader = proLeader;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getKeyProName() {
        return keyProName;
    }

    public void setKeyProName(String keyProName) {
        this.keyProName = keyProName;
    }

    public String getProPerson() {
        return proPerson;
    }

    public void setProPerson(String proPerson) {
        this.proPerson = proPerson;
    }

    public String getBuildCompany() {
        return buildCompany;
    }

    public void setBuildCompany(String buildCompany) {
        this.buildCompany = buildCompany;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getDateTwo() {
        return dateTwo;
    }

    public void setDateTwo(String dateTwo) {
        this.dateTwo = dateTwo;
    }

    public String getProTechName() {
        return proTechName;
    }

    public void setProTechName(String proTechName) {
        this.proTechName = proTechName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public static class AddRowFiledDataBean extends Base {
        private String checkProject;
        private String keyProName;
        private String subName;
        private String range;
        private String checkDetail;

        public String getCheckProject() {
            return checkProject;
        }

        public void setCheckProject(String checkProject) {
            this.checkProject = checkProject;
        }

        public String getKeyProName() {
            return keyProName;
        }

        public void setKeyProName(String keyProName) {
            this.keyProName = keyProName;
        }

        public String getSubName() {
            return subName;
        }

        public void setSubName(String subName) {
            this.subName = subName;
        }

        public String getRange() {
            return range;
        }

        public void setRange(String range) {
            this.range = range;
        }

        public String getCheckDetail() {
            return checkDetail;
        }

        public void setCheckDetail(String checkDetail) {
            this.checkDetail = checkDetail;
        }
    }
}
