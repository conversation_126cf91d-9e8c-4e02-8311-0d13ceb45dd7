package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by tudo<PERSON> on 2018/5/3.
 */

public class ProjectSafeInspection extends Base {


    public final static String PDS_QUALITY_INSPECT_DZG_DESC = "待整改";

    public final static String PDS_QUALITY_INSPECT_DYZ_DESC = "待验证";

    public final static String PDS_QUALITY_INSPECT_YYZ_DESC = "已验证";

    public final static String PDS_QUALITY_INSPECT_DZG = "dzg";// 待整改

    public final static String PDS_QUALITY_INSPECT_DYZ = "dyz";// 待验证

    public final static String PDS_QUALITY_INSPECT_YYZ = "yyz";// 已验证

    private String inspectId;           // 检查id
    private String projectId;           // 项目id
    private String taskId;              // 任务id
    private String moduleId;            // 模块id
    private Double score;               // 评分
    private String inspector;           // 检查人
    private String requisitionType;     // 申请类型
    private String requisitionId;       // 申请编号
    private String feedbackId;          // 反馈id
    private String validateId;          // 确认id
    private String creatorId;           // 创建人id
    private String creatorName;         // 创建人姓名
    private String modifier;            // 修改人
    private String remark;              // 备注
    private String taskName;            // 任务名称
    private String wbsCode;

    private String inspectTimeStr;
    private String createTimeStr;
    private String modifyTimeStr;

    private String projectWsinfo;       //  检查工点信息
    private String inspectContent;      //  检查部位
    private String constrCompany;       //  施工单位
    private String constrCharge;        //  施工负责人
    private String Attachment;          //  附件
    private String problemPhoto;        //  问题图片
    private String problemStatement;    //  问题描述
    private String inspectStatus;       //  状态
    private String unitProjectId;


    public ProjectSafeInspection() {
    }

    public String getInspectId() {
        return inspectId;
    }

    public void setInspectId(String inspectId) {
        this.inspectId = inspectId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public String getInspector() {
        return inspector;
    }

    public void setInspector(String inspector) {
        this.inspector = inspector;
    }

    public String getRequisitionType() {
        return requisitionType;
    }

    public void setRequisitionType(String requisitionType) {
        this.requisitionType = requisitionType;
    }

    public String getRequisitionId() {
        return requisitionId;
    }

    public void setRequisitionId(String requisitionId) {
        this.requisitionId = requisitionId;
    }

    public String getFeedbackId() {
        return feedbackId;
    }

    public void setFeedbackId(String feedbackId) {
        this.feedbackId = feedbackId;
    }

    public String getValidateId() {
        return validateId;
    }

    public void setValidateId(String validateId) {
        this.validateId = validateId;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getWbsCode() {
        return wbsCode;
    }

    public void setWbsCode(String wbsCode) {
        this.wbsCode = wbsCode;
    }

    public String getInspectTimeStr() {
        return inspectTimeStr;
    }

    public void setInspectTimeStr(String inspectTimeStr) {
        this.inspectTimeStr = inspectTimeStr;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getModifyTimeStr() {
        return modifyTimeStr;
    }

    public void setModifyTimeStr(String modifyTimeStr) {
        this.modifyTimeStr = modifyTimeStr;
    }


    public String getProjectWsinfo() {
        return projectWsinfo;
    }

    public void setProjectWsinfo(String projectWsinfo) {
        this.projectWsinfo = projectWsinfo;
    }

    public String getInspectContent() {
        return inspectContent;
    }

    public void setInspectContent(String inspectContent) {
        this.inspectContent = inspectContent;
    }

    public String getConstrCompany() {
        return constrCompany;
    }

    public void setConstrCompany(String constrCompany) {
        this.constrCompany = constrCompany;
    }

    public String getConstrCharge() {
        return constrCharge;
    }

    public void setConstrCharge(String constrCharge) {
        this.constrCharge = constrCharge;
    }

    public String getAttachment() {
        return Attachment;
    }

    public void setAttachment(String attachment) {
        Attachment = attachment;
    }

    public String getProblemPhoto() {
        return problemPhoto;
    }

    public void setProblemPhoto(String problemPhoto) {
        this.problemPhoto = problemPhoto;
    }

    public String getProblemStatement() {
        return problemStatement;
    }

    public void setProblemStatement(String problemStatement) {
        this.problemStatement = problemStatement;
    }

    public String getInspectStatus() {
        return inspectStatus;
    }

    public void setInspectStatus(String inspectStatus) {
        this.inspectStatus = inspectStatus;
    }

    public String getUnitProjectId() {
        return unitProjectId;
    }

    public void setUnitProjectId(String unitProjectId) {
        this.unitProjectId = unitProjectId;
    }
}
