package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

public class SubContractListDataInfo extends Base {

    private String acquiWay;
    private String adminRmb;
    private String airportClass;
    private String amountDate;
    private String amountOpened;
    private String amountToTheAccount;
    private String archivetime;
    private String area;
    private String arrivalSituation;
    private String auditName;
    private String balance;
    private String bidDirId;
    private String bidDirName;
    private String bondType;
    private String bondWarrDate;
    private String budgetAmount;
    private String buildArea;
    private String busiNature;
    private String chargeDirId;
    private String chargeDirName;
    private String chargeEngId;
    private String chargeEngName;
    private String chargeOrgId;
    private String chargeOrgName;
    private String ciId;
    private String city;
    private String clientCode;
    private String conAddress;
    private Double conAmount;
    private String conClass;
    private String conDate;
    private String conEndDate;
    private String conName;
    private String conNo;
    private String conOldId;
    private String conSign;
    private String conStartDate;
    private String conType;
    private String constructionPeriod;
    private String contractContent;
    private String contractRmb;
    private String contractSign;
    private String contractType;
    private String createId;
    private String createName;
    private String createTime;
    private String defectPeriod;
    private String endTime;
    private String entrustParty;
    private String flylEvel;
    private String ifUnit;
    private String implOrgId;
    private String implOrgName;
    private String industryNature;
    private String investor;
    private String invoice;
    private String invoiceDate;
    private String isSendBack;
    private String issueDate;
    private String linkTel;
    private String lvyueRmb;
    private String majorType;
    private String opinionNum;
    private String ownName;
    private String ownerAddress;
    private String ownerConNumber;
    private String partnerCompany;
    private String partyAUnit;
    private String partyBName;
    private String partyBUnit;
    private String payRemark;
    private String perFeeWarrDate;
    private String periodRemark;
    private String person;
    private String planEndDate;
    private String planStartDate;
    private String projectArea;
    private String projectId;
    private String projectName;
    private String projectNo;
    private String province;
    private String reFulxDate;
    private String reFulxEndDate;
    private String reFulxStartDate;
    private String relationPerson;
    private String remark;
    private String serveContent;
    private String situation;
    private String sourceFonds;
    private String startTime;
    private String state;
    private String supConNo;
    private String taskNum;
    private String techServerGroup;
    private String tentativeDeposit;
    private String unopenedRemainingAmount;
    private String verifierId;
    private String verifierName;
    private String verifyContent;
    private String verifyState;
    private String wfId;
    private String wfState;
    private String wfTaskId;
    private String wfTaskState;
    private String workFlowType;
    private String zhibaoRmb;

    public String getAcquiWay() {
        return acquiWay;
    }

    public void setAcquiWay(String acquiWay) {
        this.acquiWay = acquiWay;
    }

    public String getAdminRmb() {
        return adminRmb;
    }

    public void setAdminRmb(String adminRmb) {
        this.adminRmb = adminRmb;
    }

    public String getAirportClass() {
        return airportClass;
    }

    public void setAirportClass(String airportClass) {
        this.airportClass = airportClass;
    }

    public String getAmountDate() {
        return amountDate;
    }

    public void setAmountDate(String amountDate) {
        this.amountDate = amountDate;
    }

    public String getAmountOpened() {
        return amountOpened;
    }

    public void setAmountOpened(String amountOpened) {
        this.amountOpened = amountOpened;
    }

    public String getAmountToTheAccount() {
        return amountToTheAccount;
    }

    public void setAmountToTheAccount(String amountToTheAccount) {
        this.amountToTheAccount = amountToTheAccount;
    }

    public String getArchivetime() {
        return archivetime;
    }

    public void setArchivetime(String archivetime) {
        this.archivetime = archivetime;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getArrivalSituation() {
        return arrivalSituation;
    }

    public void setArrivalSituation(String arrivalSituation) {
        this.arrivalSituation = arrivalSituation;
    }

    public String getAuditName() {
        return auditName;
    }

    public void setAuditName(String auditName) {
        this.auditName = auditName;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }

    public String getBidDirId() {
        return bidDirId;
    }

    public void setBidDirId(String bidDirId) {
        this.bidDirId = bidDirId;
    }

    public String getBidDirName() {
        return bidDirName;
    }

    public void setBidDirName(String bidDirName) {
        this.bidDirName = bidDirName;
    }

    public String getBondType() {
        return bondType;
    }

    public void setBondType(String bondType) {
        this.bondType = bondType;
    }

    public String getBondWarrDate() {
        return bondWarrDate;
    }

    public void setBondWarrDate(String bondWarrDate) {
        this.bondWarrDate = bondWarrDate;
    }

    public String getBudgetAmount() {
        return budgetAmount;
    }

    public void setBudgetAmount(String budgetAmount) {
        this.budgetAmount = budgetAmount;
    }

    public String getBuildArea() {
        return buildArea;
    }

    public void setBuildArea(String buildArea) {
        this.buildArea = buildArea;
    }

    public String getBusiNature() {
        return busiNature;
    }

    public void setBusiNature(String busiNature) {
        this.busiNature = busiNature;
    }

    public String getChargeDirId() {
        return chargeDirId;
    }

    public void setChargeDirId(String chargeDirId) {
        this.chargeDirId = chargeDirId;
    }

    public String getChargeDirName() {
        return chargeDirName;
    }

    public void setChargeDirName(String chargeDirName) {
        this.chargeDirName = chargeDirName;
    }

    public String getChargeEngId() {
        return chargeEngId;
    }

    public void setChargeEngId(String chargeEngId) {
        this.chargeEngId = chargeEngId;
    }

    public String getChargeEngName() {
        return chargeEngName;
    }

    public void setChargeEngName(String chargeEngName) {
        this.chargeEngName = chargeEngName;
    }

    public String getChargeOrgId() {
        return chargeOrgId;
    }

    public void setChargeOrgId(String chargeOrgId) {
        this.chargeOrgId = chargeOrgId;
    }

    public String getChargeOrgName() {
        return chargeOrgName;
    }

    public void setChargeOrgName(String chargeOrgName) {
        this.chargeOrgName = chargeOrgName;
    }

    public String getCiId() {
        return ciId;
    }

    public void setCiId(String ciId) {
        this.ciId = ciId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getConAddress() {
        return conAddress;
    }

    public void setConAddress(String conAddress) {
        this.conAddress = conAddress;
    }

    public Double getConAmount() {
        return conAmount;
    }

    public void setConAmount(Double conAmount) {
        this.conAmount = conAmount;
    }

    public String getConClass() {
        return conClass;
    }

    public void setConClass(String conClass) {
        this.conClass = conClass;
    }

    public String getConDate() {
        return conDate;
    }

    public void setConDate(String conDate) {
        this.conDate = conDate;
    }

    public String getConEndDate() {
        return conEndDate;
    }

    public void setConEndDate(String conEndDate) {
        this.conEndDate = conEndDate;
    }

    public String getConName() {
        return conName;
    }

    public void setConName(String conName) {
        this.conName = conName;
    }

    public String getConNo() {
        return conNo;
    }

    public void setConNo(String conNo) {
        this.conNo = conNo;
    }

    public String getConOldId() {
        return conOldId;
    }

    public void setConOldId(String conOldId) {
        this.conOldId = conOldId;
    }

    public String getConSign() {
        return conSign;
    }

    public void setConSign(String conSign) {
        this.conSign = conSign;
    }

    public String getConStartDate() {
        return conStartDate;
    }

    public void setConStartDate(String conStartDate) {
        this.conStartDate = conStartDate;
    }

    public String getConType() {
        return conType;
    }

    public void setConType(String conType) {
        this.conType = conType;
    }

    public String getConstructionPeriod() {
        return constructionPeriod;
    }

    public void setConstructionPeriod(String constructionPeriod) {
        this.constructionPeriod = constructionPeriod;
    }

    public String getContractContent() {
        return contractContent;
    }

    public void setContractContent(String contractContent) {
        this.contractContent = contractContent;
    }

    public String getContractRmb() {
        return contractRmb;
    }

    public void setContractRmb(String contractRmb) {
        this.contractRmb = contractRmb;
    }

    public String getContractSign() {
        return contractSign;
    }

    public void setContractSign(String contractSign) {
        this.contractSign = contractSign;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getDefectPeriod() {
        return defectPeriod;
    }

    public void setDefectPeriod(String defectPeriod) {
        this.defectPeriod = defectPeriod;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getEntrustParty() {
        return entrustParty;
    }

    public void setEntrustParty(String entrustParty) {
        this.entrustParty = entrustParty;
    }

    public String getFlylEvel() {
        return flylEvel;
    }

    public void setFlylEvel(String flylEvel) {
        this.flylEvel = flylEvel;
    }

    public String getIfUnit() {
        return ifUnit;
    }

    public void setIfUnit(String ifUnit) {
        this.ifUnit = ifUnit;
    }

    public String getImplOrgId() {
        return implOrgId;
    }

    public void setImplOrgId(String implOrgId) {
        this.implOrgId = implOrgId;
    }

    public String getImplOrgName() {
        return implOrgName;
    }

    public void setImplOrgName(String implOrgName) {
        this.implOrgName = implOrgName;
    }

    public String getIndustryNature() {
        return industryNature;
    }

    public void setIndustryNature(String industryNature) {
        this.industryNature = industryNature;
    }

    public String getInvestor() {
        return investor;
    }

    public void setInvestor(String investor) {
        this.investor = investor;
    }

    public String getInvoice() {
        return invoice;
    }

    public void setInvoice(String invoice) {
        this.invoice = invoice;
    }

    public String getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(String invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public String getIsSendBack() {
        return isSendBack;
    }

    public void setIsSendBack(String isSendBack) {
        this.isSendBack = isSendBack;
    }

    public String getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(String issueDate) {
        this.issueDate = issueDate;
    }

    public String getLinkTel() {
        return linkTel;
    }

    public void setLinkTel(String linkTel) {
        this.linkTel = linkTel;
    }

    public String getLvyueRmb() {
        return lvyueRmb;
    }

    public void setLvyueRmb(String lvyueRmb) {
        this.lvyueRmb = lvyueRmb;
    }

    public String getMajorType() {
        return majorType;
    }

    public void setMajorType(String majorType) {
        this.majorType = majorType;
    }

    public String getOpinionNum() {
        return opinionNum;
    }

    public void setOpinionNum(String opinionNum) {
        this.opinionNum = opinionNum;
    }

    public String getOwnName() {
        return ownName;
    }

    public void setOwnName(String ownName) {
        this.ownName = ownName;
    }

    public String getOwnerAddress() {
        return ownerAddress;
    }

    public void setOwnerAddress(String ownerAddress) {
        this.ownerAddress = ownerAddress;
    }

    public String getOwnerConNumber() {
        return ownerConNumber;
    }

    public void setOwnerConNumber(String ownerConNumber) {
        this.ownerConNumber = ownerConNumber;
    }

    public String getPartnerCompany() {
        return partnerCompany;
    }

    public void setPartnerCompany(String partnerCompany) {
        this.partnerCompany = partnerCompany;
    }

    public String getPartyAUnit() {
        return partyAUnit;
    }

    public void setPartyAUnit(String partyAUnit) {
        this.partyAUnit = partyAUnit;
    }

    public String getPartyBName() {
        return partyBName;
    }

    public void setPartyBName(String partyBName) {
        this.partyBName = partyBName;
    }

    public String getPartyBUnit() {
        return partyBUnit;
    }

    public void setPartyBUnit(String partyBUnit) {
        this.partyBUnit = partyBUnit;
    }

    public String getPayRemark() {
        return payRemark;
    }

    public void setPayRemark(String payRemark) {
        this.payRemark = payRemark;
    }

    public String getPerFeeWarrDate() {
        return perFeeWarrDate;
    }

    public void setPerFeeWarrDate(String perFeeWarrDate) {
        this.perFeeWarrDate = perFeeWarrDate;
    }

    public String getPeriodRemark() {
        return periodRemark;
    }

    public void setPeriodRemark(String periodRemark) {
        this.periodRemark = periodRemark;
    }

    public String getPerson() {
        return person;
    }

    public void setPerson(String person) {
        this.person = person;
    }

    public String getPlanEndDate() {
        return planEndDate;
    }

    public void setPlanEndDate(String planEndDate) {
        this.planEndDate = planEndDate;
    }

    public String getPlanStartDate() {
        return planStartDate;
    }

    public void setPlanStartDate(String planStartDate) {
        this.planStartDate = planStartDate;
    }

    public String getProjectArea() {
        return projectArea;
    }

    public void setProjectArea(String projectArea) {
        this.projectArea = projectArea;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getReFulxDate() {
        return reFulxDate;
    }

    public void setReFulxDate(String reFulxDate) {
        this.reFulxDate = reFulxDate;
    }

    public String getReFulxEndDate() {
        return reFulxEndDate;
    }

    public void setReFulxEndDate(String reFulxEndDate) {
        this.reFulxEndDate = reFulxEndDate;
    }

    public String getReFulxStartDate() {
        return reFulxStartDate;
    }

    public void setReFulxStartDate(String reFulxStartDate) {
        this.reFulxStartDate = reFulxStartDate;
    }

    public String getRelationPerson() {
        return relationPerson;
    }

    public void setRelationPerson(String relationPerson) {
        this.relationPerson = relationPerson;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getServeContent() {
        return serveContent;
    }

    public void setServeContent(String serveContent) {
        this.serveContent = serveContent;
    }

    public String getSituation() {
        return situation;
    }

    public void setSituation(String situation) {
        this.situation = situation;
    }

    public String getSourceFonds() {
        return sourceFonds;
    }

    public void setSourceFonds(String sourceFonds) {
        this.sourceFonds = sourceFonds;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getSupConNo() {
        return supConNo;
    }

    public void setSupConNo(String supConNo) {
        this.supConNo = supConNo;
    }

    public String getTaskNum() {
        return taskNum;
    }

    public void setTaskNum(String taskNum) {
        this.taskNum = taskNum;
    }

    public String getTechServerGroup() {
        return techServerGroup;
    }

    public void setTechServerGroup(String techServerGroup) {
        this.techServerGroup = techServerGroup;
    }

    public String getTentativeDeposit() {
        return tentativeDeposit;
    }

    public void setTentativeDeposit(String tentativeDeposit) {
        this.tentativeDeposit = tentativeDeposit;
    }

    public String getUnopenedRemainingAmount() {
        return unopenedRemainingAmount;
    }

    public void setUnopenedRemainingAmount(String unopenedRemainingAmount) {
        this.unopenedRemainingAmount = unopenedRemainingAmount;
    }

    public String getVerifierId() {
        return verifierId;
    }

    public void setVerifierId(String verifierId) {
        this.verifierId = verifierId;
    }

    public String getVerifierName() {
        return verifierName;
    }

    public void setVerifierName(String verifierName) {
        this.verifierName = verifierName;
    }

    public String getVerifyContent() {
        return verifyContent;
    }

    public void setVerifyContent(String verifyContent) {
        this.verifyContent = verifyContent;
    }

    public String getVerifyState() {
        return verifyState;
    }

    public void setVerifyState(String verifyState) {
        this.verifyState = verifyState;
    }

    public String getWfId() {
        return wfId;
    }

    public void setWfId(String wfId) {
        this.wfId = wfId;
    }

    public String getWfState() {
        return wfState;
    }

    public void setWfState(String wfState) {
        this.wfState = wfState;
    }

    public String getWfTaskId() {
        return wfTaskId;
    }

    public void setWfTaskId(String wfTaskId) {
        this.wfTaskId = wfTaskId;
    }

    public String getWfTaskState() {
        return wfTaskState;
    }

    public void setWfTaskState(String wfTaskState) {
        this.wfTaskState = wfTaskState;
    }

    public String getWorkFlowType() {
        return workFlowType;
    }

    public void setWorkFlowType(String workFlowType) {
        this.workFlowType = workFlowType;
    }

    public String getZhibaoRmb() {
        return zhibaoRmb;
    }

    public void setZhibaoRmb(String zhibaoRmb) {
        this.zhibaoRmb = zhibaoRmb;
    }
}
