package com.kisoft.yuejianli.entity;

/**
 * 形象进度
 */
public class ImageProgress {

    /**
     * pwiName : 安装装修11
     * createTime : 2020-02-18
     * snEnclosure :
     * remark : 444
     * isLandMark : 0
     * planStartDate : 2019-10-25
     * state : 1
     * endDate :
     * projectId : 1503091650451230a4dc972986e4c1c3
     * totalProgRatio :
     * beginDate :
     * planEndDate : 2019-12-01
     * createName : 杨南辉
     * currMeasures :
     * pdId :
     * deviFactors :
     * deviAnalysis :
     * progState : null
     * progRatio :
     * pddId : 20021813002878199313eedd793cd58a
     * pwiId : 1910250922230196a8fe9d99a1ec2b13
     * stateName :
     * projectName : 新建西安至成都铁路西安至江油段（陕西境内）工程监理（XCJL-1标段）
     * createId : 150121165340445289b7a9fe868ce32d
     */

    private String pwiName;
    private String createTime;
    private String snEnclosure;
    private String remark;
    private String isLandMark;
    private String planStartDate;
    private String state;
    private String endDate;
    private String projectId;
    private String totalProgRatio;
    private String beginDate;
    private String planEndDate;
    private String createName;
    private String currMeasures;
    private String pdId;
    private String deviFactors;
    private String deviAnalysis;
    private String progState;
    private String progRatio;
    private String pddId;
    private String pwiId;
    private String stateName;
    private String projectName;
    private String createId;

    public String getPwiName() {
        return pwiName;
    }

    public void setPwiName(String pwiName) {
        this.pwiName = pwiName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getSnEnclosure() {
        return snEnclosure;
    }

    public void setSnEnclosure(String snEnclosure) {
        this.snEnclosure = snEnclosure;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIsLandMark() {
        return isLandMark;
    }

    public void setIsLandMark(String isLandMark) {
        this.isLandMark = isLandMark;
    }

    public String getPlanStartDate() {
        return planStartDate;
    }

    public void setPlanStartDate(String planStartDate) {
        this.planStartDate = planStartDate;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getTotalProgRatio() {
        return totalProgRatio;
    }

    public void setTotalProgRatio(String totalProgRatio) {
        this.totalProgRatio = totalProgRatio;
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getPlanEndDate() {
        return planEndDate;
    }

    public void setPlanEndDate(String planEndDate) {
        this.planEndDate = planEndDate;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCurrMeasures() {
        return currMeasures;
    }

    public void setCurrMeasures(String currMeasures) {
        this.currMeasures = currMeasures;
    }

    public String getPdId() {
        return pdId;
    }

    public void setPdId(String pdId) {
        this.pdId = pdId;
    }

    public String getDeviFactors() {
        return deviFactors;
    }

    public void setDeviFactors(String deviFactors) {
        this.deviFactors = deviFactors;
    }

    public String getDeviAnalysis() {
        return deviAnalysis;
    }

    public void setDeviAnalysis(String deviAnalysis) {
        this.deviAnalysis = deviAnalysis;
    }

    public String getProgState() {
        return progState;
    }

    public void setProgState(String progState) {
        this.progState = progState;
    }

    public String getProgRatio() {
        return progRatio;
    }

    public void setProgRatio(String progRatio) {
        this.progRatio = progRatio;
    }

    public String getPddId() {
        return pddId;
    }

    public void setPddId(String pddId) {
        this.pddId = pddId;
    }

    public String getPwiId() {
        return pwiId;
    }

    public void setPwiId(String pwiId) {
        this.pwiId = pwiId;
    }

    public String getStateName() {
        return stateName;
    }

    public void setStateName(String stateName) {
        this.stateName = stateName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }
}
