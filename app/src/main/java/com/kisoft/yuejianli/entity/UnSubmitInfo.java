package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;

/**
 * Created by tudou on 2018/6/23.
 */

@Entity
public class UnSubmitInfo extends Base{

    @Id(autoincrement = true)
    private Long id ;
    private String type;
    private String typeName;
    private String uid;
    private String projectId;
    private String projectName;
    private String time;
    private String data;
    private int status = 0;             // (0,未提交   1，已提交)

    public UnSubmitInfo() {
    }

    public UnSubmitInfo(String type, String typeName, String uid, String projectId, String projectName, String time, String data) {
        this.type = type;
        this.typeName = typeName;
        this.uid = uid;
        this.projectId = projectId;
        this.projectName = projectName;
        this.time = time;
        this.data = data;
    }

    @Generated(hash = 2096643486)
    public UnSubmitInfo(Long id, String type, String typeName, String uid, String projectId, String projectName, String time,
            String data, int status) {
        this.id = id;
        this.type = type;
        this.typeName = typeName;
        this.uid = uid;
        this.projectId = projectId;
        this.projectName = projectName;
        this.time = time;
        this.data = data;
        this.status = status;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
}
