package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by tudo<PERSON> on 2018/3/12.
 */

public class ProjectContent extends Base {

    /** 工程监理  */
    public static final int PROJECT_INFO = 0;               // 项目概况
    public static final int PROJECT_QUALITY = 1;            // 质量控制
    public static final int PROJECT_SAFETY = 2;             // 安全管理
    public static final int PROJECT_PROGRESS = 3;           // 进度控制
    public static final int PROJECT_CONTRACT = 4;           // 合同管理
    public static final int PROJECT_INVESTMENT = 5;         // 投资控制
    public static final int PROJECT_INFO_CONTROL = 6;       // 信息管理
    public static final int PROJECT_END_SERVICE = 7;        // 后期服务

    public static final int PROJECT_PREPARE_INFO = 20;      // 准备阶段
    public static final int PROJECT_ORGANIZATION_COORDINATE = 21;       // 组织协调
    public static final int PROJECT_NOTICE_SERVICE = 22;     // 通知单
    //public static final int PROJECT_ABSTRACT_CONFERENCE_SERVICE = 23;   // 会议纪要
    public static final int PROJECT_QUESTION_MANAGE_SERVICE = 24;       // 问题管理
    public static final int PROJECT_KNOWLEDGE_SERVICE = 25;             // 知识库
    public static final int PROJECT_CHECK_AND_ACCEPT=26;                //竣工验收

    //项目看板
    public static final int PROJECT_KANBAN=32;   //项目看板
    //监理指令
    public static final int PROJECT_INSTRUCTION=33;   //监理指令


    /** 常用功能列表   */
    public static final int PROJECT_SCENE = 8;                          // 现场巡检
    public static final int PROJECT_LIBRARY = 9;                                // 文库
    public static final int PROJECT_USER_TREE = 10;                             // 通讯录
    public static final int PROJECT_ATTENDANCE_RECORD = 11;                     // 考勤记录
    public static final int PROJECT_ATTENDANCE_SGIN_IN = 12;                    // 考勤打卡
    public static final int PROJECT_COMPANY_COMFILES = 199;                      // 公共资源
    public static final int PROJECT_COMPANY_TRAINING = 13;                      // 企业培训

    public static final int PROJECT_HUMAN_ASSESSMENT = 14;    // 绩效考核

    /** 经营管理  */
    public static final int PROJECT_TENDER_PLAN = 15;           // 投标计划
    public static final int PROJECT_TENDER_MONEY = 16;        // 保函、保证金
    public static final int PROJECT_TENDER_FILE = 17;           // 投资文件
    public static final int PROJECT_TENDER_START = 18;          // 投资开始
    public static final int PROJECT_CUSTOMER_MANAGEMENT = 19;   // 客户管理
    public static final int PROJECT_RESOURCE_MANAGEMENT = 120;   // 客户管理

    /*竣工验收*/
    public  static final  int PROJECT_ACCEPT_PRE=27;        //预验收
    public  static final  int PROJECT_ACCEPT_CHECK=28;      //竣工验收
    public  static final  int PROJECT_ACCEPT_REPORT=29;     //质量评估报告
    public  static final  int PROJECT_ACCEPT_ARCHIVES=30;   //竣工档案移交
    public  static final  int PROJECT_ACCEPT_MEANS=31;      //外部资料移交




    /** 常用功能名称   */
    public static final String PROJECT_INFO_NAME = "项目概况";
    public static final String PROJECT_QUALITY_NAME = "质量控制";
    public static final String PROJECT_SAFETY_NAME = "安全管理";
    public static final String PROJECT_PROGRESS_NAME = "进度控制";
    public static final String PROJECT_CONTRACT_NAME = "合同管理";
    public static final String PROJECT_INVESTMENT_NAME = "投资控制";
    public static final String PROJECT_INFO_CONTROL_NAME = "信息管理";
    public static final String PROJECT_END_SERVICE_NAME = "后期服务";
    public static final String PROJECT_NOTICE_NAME = "通知单";
    public static final String PROJECT_SCENE_NAME = "现场巡检";
    public static final String PROJECT_LIBRARY_NAME = "知识库";
    public static final String PROJECT_USER_TREE_NAME = "通讯录";
    public static final String PROJECT_ATTENDANCE_RECORD_NAME = "考勤记录";
    public static final String PROJECT_ATTENDANCE_SGIN_IN_NAME = "考勤打卡";
 //   public static final String PROJECT_COMPANY_TRAINING_NAME = "企业培训";
    public static final String PROJECT_HUMAN_ASSESSMENT_NAME = "绩效考核";
    public static final String PROJECT_TENDER_PLAN_NAME = "投标计划";
    public static final String PROJECT_TENDER_MONEY_NAME = "保函、保证金";
    public static final String PROJECT_TENDER_FILE_NAME = "投标文件";
    public static final String PROJECT_TENDER_START_NAME = "开标";
    public static final String PROJECT_CUSTOMER_MANAGEMENT_NAME = "客户管理";
    public static final String PROJECT_RESOURCE_MANAGEMENT_NAME = "资源管理";
    public static final String PROJECT_PREPARE_INFO_NAME = "准备阶段";
    public static final String PROJECT_ORGANIZATION_COORDINATE_NAME = "组织协调";
   // public static final String PROJECT_ABSTRACT_CONFERENCE_NAME = "会议";
    public static final String PROJECT_QUESTION_MANAGE_NAME = "问题管理";
    public static final String PROJECT_KNOWLEDGE_NAME = "知识库";
    public static final String PROJECT_CHECK_AND_ACCEPT_NAME="竣工验收";
    public static final String PROJECT_KANBAN_NAME="项目看板";
    public static final String PROJECT_INSTRUCTION_NAME="监理指令";
    public static final String PROJECT_CHECK_NAME ="工程检查记录";
    public static final String PROJECT_PE_EVALUATE_NAME ="考核自评";
    public static final String PROJECT_RESOURCE_CONTRACT_NAME="合同业绩";
    public static final String PROJECT_RESOURCE_SUP_NAME="总监业绩";
    public static final String PROJECT_RESOURCE_PERSON_NAME="人员业绩";
    public static final String PROJECT_RESOURCE_EMP_NAME="证件库";
    public static final String PROJECT_RESOURCE_CERTIFICATE_NAME="证书库";
    public static final String PROJECT_COMPANY_COMFILES_NAME="公共资源";

/*
* 竣工验收
**/
    public static final String PROJECT_ACCEPT_PRE_NAME="预验收";
    public static final String PROJECT_ACCEPT_CHECK_NAME="竣工验收";
    public static final String PROJECT_ACCEPT_REPORT_NAME="质量评估报告";
    public static final String PROJECT_ACCEPT_ARCHIVES_NAME="竣工档案移交";
    public static final String PROJECT_ACCEPT_MEANS_NAME="外部资料移交";

    /** 资源管理  */
    public static final int PROJECT_RESOURCE_CONTRACT = 121;           // 合同业绩
    public static final int PROJECT_RESOURCE_SUP = 122;        // 总监业绩
    public static final int PROJECT_RESOURCE_PERSON = 123;           // 人员业绩
    public static final int PROJECT_RESOURCE_EMP = 124;          // 证件库
    public static final int PROJECT_RESOURCE_CERTIFICATE = 125;   // 证书库

    private int id;
    private String name;
    private boolean isOpen = false;

    public ProjectContent() {
    }

    public ProjectContent(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public ProjectContent(int id, String name, boolean isOpen) {
        this.id = id;
        this.name = name;
        this.isOpen = isOpen;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isOpen() {

        return isOpen;
    }

    public void setOpen(boolean open) {
        isOpen = open;
    }
}
