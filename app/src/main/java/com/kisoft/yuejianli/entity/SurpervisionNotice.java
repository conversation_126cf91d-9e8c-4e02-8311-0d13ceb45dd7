package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by tudo<PERSON> on 2018/5/7.
 */

public class SurpervisionNotice extends Base {

    public static final String IS_ANSWER_YES = "1";
    public static final String IS_ANSWER_NO = "0";

    private String snId;// 主键
    private String projectId;// 项目id
    private String projectName;// 项目名称
    private String snNumber;// 编号
    private String company;// 施工单位
    private String renson;// 事由
    private String content;// 内容
    private String snStatus;// 状态
    private String snEnclosure;// 附件
    private String createId;// 创建人id
    private String createTime;// 创建时间
    private String superUnit;//监理单位
    private String createName;//创建人名字

    public SurpervisionNotice() {
    }

    public String getSnId() {
        return snId;
    }

    public void setSnId(String snId) {
        this.snId = snId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getSnNumber() {
        return snNumber;
    }

    public void setSnNumber(String snNumber) {
        this.snNumber = snNumber;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getRenson() {
        return renson;
    }

    public void setRenson(String renson) {
        this.renson = renson;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSnStatus() {
        return snStatus;
    }

    public void setSnStatus(String snStatus) {
        this.snStatus = snStatus;
    }

    public String getSnEnclosure() {
        return snEnclosure;
    }

    public void setSnEnclosure(String snEnclosure) {
        this.snEnclosure = snEnclosure;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getSuperUnit() {
        return superUnit;
    }

    public void setSuperUnit(String superUnit) {
        this.superUnit = superUnit;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
}
