package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * 抽检记录
 */
public class SpotCheckInfo extends Base {
    private String id;  //主键
    private String checkNumber; //编号
    private String constructionUnit;    //施工单位
    private String conSzj;  //合同段
    private String checkUserId; //抽检人id
    private String checkUserName;   //抽检人姓名
    private String checkTime; //抽检时间
    private String engineeringParts;    //工程部位
    private String checkItem;   //抽检项目
    private String projectId;   //项目id
    private String projectName; //项目名称
    private String result;  //检查结果
    private String conclusion;  //检查结论
    private String opinion; //处理意见
    private String auditId; //审核人id
    private String auditName;   //审核人姓名
    private String auditTime; //审核日期
    private String createId;    //创建人Id    系统当前人员I
    private String createName;  //创建人姓名    系统当前人员姓
    private String createTime;    //创建时间 系统当前时

    private String unitProjectId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCheckNumber() {
        return checkNumber;
    }

    public void setCheckNumber(String checkNumber) {
        this.checkNumber = checkNumber;
    }

    public String getConstructionUnit() {
        return constructionUnit;
    }

    public void setConstructionUnit(String constructionUnit) {
        this.constructionUnit = constructionUnit;
    }

    public String getConSzj() {
        return conSzj;
    }

    public void setConSzj(String conSzj) {
        this.conSzj = conSzj;
    }

    public String getCheckUserId() {
        return checkUserId;
    }

    public void setCheckUserId(String checkUserId) {
        this.checkUserId = checkUserId;
    }

    public String getCheckUserName() {
        return checkUserName;
    }

    public void setCheckUserName(String checkUserName) {
        this.checkUserName = checkUserName;
    }

    public String getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(String checkTime) {
        this.checkTime = checkTime;
    }

    public String getEngineeringParts() {
        return engineeringParts;
    }

    public void setEngineeringParts(String engineeringParts) {
        this.engineeringParts = engineeringParts;
    }

    public String getCheckItem() {
        return checkItem;
    }

    public void setCheckItem(String checkItem) {
        this.checkItem = checkItem;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getConclusion() {
        return conclusion;
    }

    public void setConclusion(String conclusion) {
        this.conclusion = conclusion;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public String getAuditId() {
        return auditId;
    }

    public void setAuditId(String auditId) {
        this.auditId = auditId;
    }

    public String getAuditName() {
        return auditName;
    }

    public void setAuditName(String auditName) {
        this.auditName = auditName;
    }

    public String getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(String auditTime) {
        this.auditTime = auditTime;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUnitProjectId() {
        return unitProjectId;
    }

    public void setUnitProjectId(String unitProjectId) {
        this.unitProjectId = unitProjectId;
    }
}
