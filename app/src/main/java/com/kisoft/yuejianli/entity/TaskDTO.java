package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Description: 任务信息
 * Author     : yanlu
 * String       : 2018/12/28 17:30
 */
public class TaskDTO extends Base{

    private String tskGuid;  //主键
    private String ttGuid;  //任务类型Id
    private String ttName;
    private String topic;//主题
    private String tsklevel;              //与level相同
    private String beginDate;//开始时间
    private String endDate;//结束时间
    private String content; //内容
    private String creator;//编写人Id
    private String createId;
    private String replace;          //发空
    private String createname;//编写人姓名
    private String createname0;//编写人       //使用createname
    private String createdate;//创建日期
    private String target;//质量目标          //发空
    private String progress;//进度
    private String finishDate;//完成时间            //不管
    //知会人ids
    private String inforGuid;    //知会人id
    private String inforGuidNameShort;              //发空
    private String inforGuidName;//知会人            //逗号间隔
    //负责人ids
    private String masterGuid;  //负责人Id
    private String masterGuidNameShort;             //发空
    private String masterGuidName;//负责人名称
    //参与人ids
    private String minorGuid;
    private String minorGuidNameShort;              //发空
    private String minorGuidName;//参与人
    //角色
    private String userRoles;
    //级别
    private String level;

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getTtName() {
        return ttName;
    }

    public void setTtName(String ttName) {
        this.ttName = ttName;
    }

    public String getInforGuid() {
        return inforGuid;
    }

    public void setInforGuid(String inforGuid) {
        this.inforGuid = inforGuid;
    }

    public String getTskGuid() {
        return tskGuid;
    }

    public void setTskGuid(String tskGuid) {
        this.tskGuid = tskGuid;
    }

    public String getTtGuid() {
        return ttGuid;
    }

    public void setTtGuid(String ttGuid) {
        this.ttGuid = ttGuid;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getTsklevel() {
        return tsklevel;
    }

    public void setTsklevel(String tsklevel) {
        this.tsklevel = tsklevel;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getReplace() {
        return replace;
    }

    public void setReplace(String replace) {
        this.replace = replace;
    }

    public String getCreatename() {
        return createname;
    }

    public void setCreatename(String createname) {
        this.createname = createname;
    }

    public String getCreatename0() {
        return createname0;
    }

    public void setCreatename0(String createname0) {
        this.createname0 = createname0;
    }

    public String getCreatedate() {
        return createdate;
    }

    public void setCreatedate(String createdate) {
        this.createdate = createdate;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }


    public String getInforGuidNameShort() {
        return inforGuidNameShort;
    }

    public void setInforGuidNameShort(String inforGuidNameShort) {
        this.inforGuidNameShort = inforGuidNameShort;
    }

    public String getInforGuidName() {
        return inforGuidName;
    }

    public void setInforGuidName(String inforGuidName) {
        this.inforGuidName = inforGuidName;
    }

    public String getMasterGuid() {
        return masterGuid;
    }

    public void setMasterGuid(String masterGuid) {
        this.masterGuid = masterGuid;
    }

    public String getMasterGuidNameShort() {
        return masterGuidNameShort;
    }

    public void setMasterGuidNameShort(String masterGuidNameShort) {
        this.masterGuidNameShort = masterGuidNameShort;
    }

    public String getMasterGuidName() {
        return masterGuidName;
    }

    public void setMasterGuidName(String masterGuidName) {
        this.masterGuidName = masterGuidName;
    }

    public String getMinorGuid() {
        return minorGuid;
    }

    public void setMinorGuid(String minorGuid) {
        this.minorGuid = minorGuid;
    }

    public String getMinorGuidNameShort() {
        return minorGuidNameShort;
    }

    public void setMinorGuidNameShort(String minorGuidNameShort) {
        this.minorGuidNameShort = minorGuidNameShort;
    }

    public String getMinorGuidName() {
        return minorGuidName;
    }

    public void setMinorGuidName(String minorGuidName) {
        this.minorGuidName = minorGuidName;
    }

    public String getUserRoles() {
        return userRoles;
    }

    public void setUserRoles(String userRoles) {
        this.userRoles = userRoles;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getFinishDate() {
        return finishDate;
    }

    public void setFinishDate(String finishDate) {
        this.finishDate = finishDate;
    }

    public String getProgress() {
        return progress;
    }

    public void setProgress(String progress) {
        this.progress = progress;
    }
}
