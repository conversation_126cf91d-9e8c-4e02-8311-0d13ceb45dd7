package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by Administrator on 2019/5/14 0014.
 */
public class ChangeDto extends Base {
    String changeId;            //主键
    String projectId;           //项目名称
    String projectName;         //项目名称
    String createId;            //创建人Id
    String createName;          //创建姓名
    String creatName;           //创建姓名
    String projectDate;         //创建时间
    String createTime;          //创建时间(bhd加)
    String changeNumber;        //工程变更单编号
    String changeTime;          //工程变更日期
    String changeContent;       //工程变更内容
    String auditStatus;         //审核状态
    String auditReason;         //审核原因
    String changeUnit;          //工程变更
    String supervisionUnit;     //监理单位
    String reason;              //原因
    String changeNotice;        //工程变更通知
    String presentationUnit;    //提出单位（业主单位提出变更、设计单位提出变更、监理单位提出变更、施工单位提出变更 手机端需要）
    String representative;      //代表人
    String today;               //日期
    String agreement;           //一致意见
    String buildSign;           //建设单位签字
    String designSign;          //设计单位签字
    String supSign;             //监理机构签字
    String remark;              //备注
    //2019-05-09 add yangjianjun（新增字段）
    String changePrice;         //变更价款
    String changeType;         //变更类型 (工程变更=1、设计变更=2、现场签证=3)
    String changeParts;         //变更部位
    String enclosure;           //附件
    String contractId;          //合同主键
    String contractName;        //合同名称

    public String getCreatName() {
        return creatName;
    }

    public void setCreatName(String creatName) {
        this.creatName = creatName;
    }

    public String getChangeContent() {
        return changeContent;
    }

    public void setChangeContent(String changeContent) {
        this.changeContent = changeContent;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getChangeId() {
        return changeId;
    }

    public void setChangeId(String changeId) {
        this.changeId = changeId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getProjectDate() {
        return projectDate;
    }

    public void setProjectDate(String projectDate) {
        this.projectDate = projectDate;
    }

    public String getChangeNumber() {
        return changeNumber;
    }

    public void setChangeNumber(String changeNumber) {
        this.changeNumber = changeNumber;
    }

    public String getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(String changeTime) {
        this.changeTime = changeTime;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getAuditReason() {
        return auditReason;
    }

    public void setAuditReason(String auditReason) {
        this.auditReason = auditReason;
    }

    public String getChangeUnit() {
        return changeUnit;
    }

    public void setChangeUnit(String changeUnit) {
        this.changeUnit = changeUnit;
    }

    public String getSupervisionUnit() {
        return supervisionUnit;
    }

    public void setSupervisionUnit(String supervisionUnit) {
        this.supervisionUnit = supervisionUnit;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getChangeNotice() {
        return changeNotice;
    }

    public void setChangeNotice(String changeNotice) {
        this.changeNotice = changeNotice;
    }

    public String getPresentationUnit() {
        return presentationUnit;
    }

    public void setPresentationUnit(String presentationUnit) {
        this.presentationUnit = presentationUnit;
    }

    public String getRepresentative() {
        return representative;
    }

    public void setRepresentative(String representative) {
        this.representative = representative;
    }

    public String getToday() {
        return today;
    }

    public void setToday(String today) {
        this.today = today;
    }

    public String getAgreement() {
        return agreement;
    }

    public void setAgreement(String agreement) {
        this.agreement = agreement;
    }

    public String getBuildSign() {
        return buildSign;
    }

    public void setBuildSign(String buildSign) {
        this.buildSign = buildSign;
    }

    public String getDesignSign() {
        return designSign;
    }

    public void setDesignSign(String designSign) {
        this.designSign = designSign;
    }

    public String getSupSign() {
        return supSign;
    }

    public void setSupSign(String supSign) {
        this.supSign = supSign;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getChangePrice() {
        return changePrice;
    }

    public void setChangePrice(String changePrice) {
        this.changePrice = changePrice;
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getChangeParts() {
        return changeParts;
    }

    public void setChangeParts(String changeParts) {
        this.changeParts = changeParts;
    }

    public String getEnclosure() {
        return enclosure;
    }

    public void setEnclosure(String enclosure) {
        this.enclosure = enclosure;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }
}
