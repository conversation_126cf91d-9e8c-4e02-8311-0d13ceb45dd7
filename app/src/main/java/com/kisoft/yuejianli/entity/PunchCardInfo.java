package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.utils.DateUtil;

import java.text.ParseException;

/**
 * Created by tudo<PERSON> on 2018/3/20.
 */

public class PunchCardInfo {

    public static final String SIGN_OK = "1";               // 正常
    public static final String SIGN_LATE = "2";             // 迟到
    public static final String SIGN_AHEAD= "3";             // 早退
    public static final String SIGN_NOT = "4";              // 矿工
    public static final String SIGN_LEAVE = "5";            // 请假
    public static final String SIGN_FIL_WORK = "6";         // 外勤


    private String asId;// 主键id
    private String userId;// 用户id
    private String userName;// 用户姓名
    private String punchTime1;// 打卡时间1
    private String punchPoint1;// 打卡地点1   ,(地点的，封装经纬度处理)
    private String punchTime2;// 打卡时间2
    private String punchPoint2;// 打卡地点2     （地点）
    private String punchTime3;// 打卡时间3
    private String punchPoint3;// 打卡地点3     （地点）
    private String punchTime4;// 打卡时间4
    private String punchPoint4;// 打卡地点4     （地点）
    private String remark1;
    private String remark2;
    private String remark3;
    private String remark4;
    private String state;// 状态
    private String remarks;// 备注
    private String createTime;//
    private String projectId;//项目ID
   private String projectName;//项目名称

    public PunchCardInfo() {
    }


    public String getAsId() {
        return asId;
    }

    public void setAsId(String asId) {
        this.asId = asId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPunchTime1() {
        return punchTime1;
    }

    public void setPunchTime1(String punchTime1) {
        this.punchTime1 = punchTime1;
    }

    public String getPunchPoint1() {
        return punchPoint1;
    }

    public void setPunchPoint1(String punchPoint1) {
        this.punchPoint1 = punchPoint1;
    }

    public String getPunchTime2() {
        return punchTime2;
    }

    public void setPunchTime2(String punchTime2) {
        this.punchTime2 = punchTime2;
    }

    public String getPunchPoint2() {
        return punchPoint2;
    }

    public void setPunchPoint2(String punchPoint2) {
        this.punchPoint2 = punchPoint2;
    }

    public String getPunchTime3() {
        return punchTime3;
    }

    public void setPunchTime3(String punchTime3) {
        this.punchTime3 = punchTime3;
    }

    public String getPunchPoint3() {
        return punchPoint3;
    }

    public void setPunchPoint3(String punchPoint3) {
        this.punchPoint3 = punchPoint3;
    }

    public String getPunchTime4() {
        return punchTime4;
    }

    public void setPunchTime4(String punchTime4) {
        this.punchTime4 = punchTime4;
    }

    public String getPunchPoint4() {
        return punchPoint4;
    }

    public void setPunchPoint4(String punchPoint4) {
        this.punchPoint4 = punchPoint4;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }


    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }

    public String getRemark2() {
        return remark2;
    }

    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }

    public String getRemark3() {
        return remark3;
    }

    public void setRemark3(String remark3) {
        this.remark3 = remark3;
    }

    public String getRemark4() {
        return remark4;
    }

    public void setRemark4(String remark4) {
        this.remark4 = remark4;
    }

    public String getYear(){
        return getCreateTime().substring(0,4);
    }

    public String getMonth(){
        String month = "";
        try {
            month = DateUtil.getMonthForDate(DateUtil.stringToDate(getPunchTime1(),DateUtil.YMD_HMS));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return month;
    }

    public String getDay(){
        String day = "";
        try {
            day = DateUtil.getDayForDate(DateUtil.stringToDate(getPunchTime1(),DateUtil.YMD_HMS));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return day;
    }

    @Override
    public String toString() {
        return "PunchCardInfo{" +
                "asId='" + asId + '\'' +
                ", userId='" + userId + '\'' +
                ", userName='" + userName + '\'' +
                ", punchTime1='" + punchTime1 + '\'' +
                ", punchPoint1='" + punchPoint1 + '\'' +
                ", punchTime2='" + punchTime2 + '\'' +
                ", punchPoint2='" + punchPoint2 + '\'' +
                ", state='" + state + '\'' +
                ", remarks='" + remarks + '\'' +
                ", createTime='" + createTime + '\'' +
                ", projectId='" + projectId + '\'' +
                ", projectName='" + projectName + '\'' +
                '}';
    }
}
