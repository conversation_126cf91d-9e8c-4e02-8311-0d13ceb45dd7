package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by t<PERSON><PERSON> on 2018/6/20.
 * 意见征集人（对象）
 */

public class TrainInvitationrRight extends Base {

    public static final String STATUS_0 = "0";          // 未处理
    public static final String STATUS_1 = "1";          // 同意
    public static final String STATUS_2 = "2";          // 不同意


    private String tcrGuid;             // 主键
    private String tiGuid;              // 培训征集id
    private String userGuid;            // 征集对象id
    private String userName;            // 征集人名称
    private String state;               // 状态（意见结果）

    public TrainInvitationrRight() {
    }

    public String getTcrGuid() {
        return tcrGuid;
    }

    public void setTcrGuid(String tcrGuid) {
        this.tcrGuid = tcrGuid;
    }

    public String getTiGuid() {
        return tiGuid;
    }

    public void setTiGuid(String tiGuid) {
        this.tiGuid = tiGuid;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
