package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.List;

public class TodayLedgerInfo extends Base {
        /**
         * count : 13
         * list : [{"userId":"1411061510426418964b4caed722b3b7","userName":"王茜","todayTempList":"1-0-0-0-0-0"},{"userId":"150604165411428fb0c26aabb1e8bcfa","userName":"肖龙","todayTempList":"0-0-0-0-0-0"},{"userId":"1506041654114291a4d3bf31ad0fbb7a","userName":"范杰","todayTempList":"0-0-0-0-0-0"},{"userId":"1506041654114295bb0c7a3e5337c70e","userName":"孟轲","todayTempList":"0-0-0-0-0-0"},{"userId":"1506121955224705b052b786542f9799","userName":"岳保亮","todayTempList":"0-0-0-0-0-0"},{"userId":"1506121955224707902fcdac5868aa00","userName":"刘泉","todayTempList":"0-0-0-0-0-0"},{"userId":"150612195522470a9171e05faf0cc3af","userName":"张忠诚","todayTempList":"0-0-0-0-0-0"},{"userId":"150612195522470bac575b0d9881415d","userName":"张永辉","todayTempList":"0-0-0-0-0-0"},{"userId":"150612195522470da177da18acdb90f4","userName":"柯海军","todayTempList":"0-0-0-0-0-0"},{"userId":"150612195522470fa72facbd94db5f1c","userName":"党展","todayTempList":"0-0-0-0-0-0"}]
         */

        private int count;
        private List<ListBean> list;

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public List<ListBean> getList() {
            return list;
        }

        public void setList(List<ListBean> list) {
            this.list = list;
        }

        public static class ListBean {
            /**
             * userId : 1411061510426418964b4caed722b3b7
             * userName : 王茜
             * todayTempList : 1-0-0-0-0-0
             */

            private String userId;
            private String userName;
            private String todayTempList;

            public String getUserId() {
                return userId;
            }

            public void setUserId(String userId) {
                this.userId = userId;
            }

            public String getUserName() {
                return userName;
            }

            public void setUserName(String userName) {
                this.userName = userName;
            }

            public String getTodayTempList() {
                return todayTempList;
            }

            public void setTodayTempList(String todayTempList) {
                this.todayTempList = todayTempList;
            }
        }
}
