package com.kisoft.yuejianli.entity;

/**
 * 资源管理搜索内容
 */
public class ResourceManagementSearch {

    private String projectName; //项目名称
    private String startDate; //开始时间
    private String endDate; //结束时间
    private String projectAmountMIn; //项目投资范围(最小值)
    private String projectAmountMax; //项目投资范围(最大值)
    private String contractNo; //合同编号
    private String userName; //姓名
    private String eName; //职称
    private String deptName; //部门
    private String empname;   //员工姓名
    private String protype; //证件类型
    private String state; //证件状态
    private String empState;  //员工状态
    private String fcNameNew;   //证书名称
    private String fcNumberNew;  //证书编号
    private String fcStyleNew; //证书类别

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getProjectAmountMIn() {
        return projectAmountMIn;
    }

    public void setProjectAmountMIn(String projectAmountMIn) {
        this.projectAmountMIn = projectAmountMIn;
    }

    public String getProjectAmountMax() {
        return projectAmountMax;
    }

    public void setProjectAmountMax(String projectAmountMax) {
        this.projectAmountMax = projectAmountMax;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String geteName() {
        return eName;
    }

    public void seteName(String eName) {
        this.eName = eName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getEmpname() {
        return empname;
    }

    public void setEmpname(String empname) {
        this.empname = empname;
    }

    public String getProtype() {
        return protype;
    }

    public void setProtype(String protype) {
        this.protype = protype;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getEmpState() {
        return empState;
    }

    public void setEmpState(String empState) {
        this.empState = empState;
    }

    public String getFcNameNew() {
        return fcNameNew;
    }

    public void setFcNameNew(String fcNameNew) {
        this.fcNameNew = fcNameNew;
    }

    public String getFcNumberNew() {
        return fcNumberNew;
    }

    public void setFcNumberNew(String fcNumberNew) {
        this.fcNumberNew = fcNumberNew;
    }

    public String getFcStyleNew() {
        return fcStyleNew;
    }

    public void setFcStyleNew(String fcStyleNew) {
        this.fcStyleNew = fcStyleNew;
    }
}
