package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.List;

/**
 * Created by tudou on 2018/6/19.
 */

public class CustomerVisitInfo extends Base{


    private int count;
    private List<CustomerVisit> list;

    public CustomerVisitInfo() {
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public List<CustomerVisit> getList() {
        return list;
    }

    public void setList(List<CustomerVisit> list) {
        this.list = list;
    }
}
