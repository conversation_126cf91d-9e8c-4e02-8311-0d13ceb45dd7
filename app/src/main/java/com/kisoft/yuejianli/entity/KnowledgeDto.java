package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by Administrator on 2019/5/20 0020.
 */

public class KnowledgeDto extends Base {
    private String kbEnclosure;
    private String kbId;
    private String createTime;
    private String maxPage;
    private String page;
    private String kbTitle;
    private String sizePage;
    private String kbRemarks;
    private String createName;
    private String createId;

    public String getKbEnclosure() {
        return kbEnclosure;
    }

    public void setKbEnclosure(String kbEnclosure) {
        this.kbEnclosure = kbEnclosure;
    }

    public String getKbId() {
        return kbId;
    }

    public void setKbId(String kbId) {
        this.kbId = kbId;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getMaxPage() {
        return maxPage;
    }

    public void setMaxPage(String maxPage) {
        this.maxPage = maxPage;
    }

    public String getPage() {
        return page;
    }

    public void setPage(String page) {
        this.page = page;
    }

    public String getKbTitle() {
        return kbTitle;
    }

    public void setKbTitle(String kbTitle) {
        this.kbTitle = kbTitle;
    }

    public String getSizePage() {
        return sizePage;
    }

    public void setSizePage(String sizePage) {
        this.sizePage = sizePage;
    }

    public String getKbRemarks() {
        return kbRemarks;
    }

    public void setKbRemarks(String kbRemarks) {
        this.kbRemarks = kbRemarks;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }
}
