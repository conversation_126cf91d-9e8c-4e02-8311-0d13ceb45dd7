package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.math.BigDecimal;

/**
 * Created by tudo<PERSON> on 2018/6/10.
 */

public class ProConstructInvestment extends Base {

    private String id;
    private String projectId;
    private String projectName;
    private String company;                 // 施工单位
    private String moneyUppercase;          // 合同金额（大写）
    private BigDecimal moneyLowercase;      // 金额 （小写，做计算）
    private String createId;
    private String creatName;
    private String createTime;
    private String remark;

    public ProConstructInvestment() {

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getMoneyUppercase() {
        return moneyUppercase;
    }

    public void setMoneyUppercase(String moneyUppercase) {
        this.moneyUppercase = moneyUppercase;
    }

    public BigDecimal getMoneyLowercase() {
        return moneyLowercase;
    }

    public void setMoneyLowercase(BigDecimal moneyLowercase) {
        this.moneyLowercase = moneyLowercase;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreatName() {
        return creatName;
    }

    public void setCreatName(String creatName) {
        this.creatName = creatName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
