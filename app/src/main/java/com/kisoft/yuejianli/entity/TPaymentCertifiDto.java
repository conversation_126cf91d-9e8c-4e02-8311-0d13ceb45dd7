package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by Administrator on 2019/5/14 0014.
 */
public class TPaymentCertifiDto extends Base {
    String pcId;                // 主键id
    String projectId;           // 项目工程
    String projectName;         // 项目名称
    String pcNumber;            // 编号
    String company;             // 施工单位
    String typeId;              // 暂时关联外键表的主键ID
    String coNumber;            // 工程款支付申请表编号
    String moneyUppercase;      // 金额大写
    String moneyLowercase;      // 金额小写
    String declareMoney;        // 施工单位申报款为：
    String deservedMoney;       // 经审核施工单位应得款为：
    String deductibleMoney;     // 本期应扣款为：
    String payableMoney;        // 本期应付款为：
    String pcStatus;            // 状态
    String pcEnclosure;         // 附件
    String createId;            // 创建人
    String creatName;           // 创建名字
    String createTime;          // 创建时间
    //2019-05-10 add
    String contractId;          //合同Id
    String contractName;        //合同名称
    String payType;             //款项类别（1=合同内付款；2=变更合同付款）

    public String getPcId() {
        return pcId;
    }

    public void setPcId(String pcId) {
        this.pcId = pcId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getPcNumber() {
        return pcNumber;
    }

    public void setPcNumber(String pcNumber) {
        this.pcNumber = pcNumber;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getCoNumber() {
        return coNumber;
    }

    public void setCoNumber(String coNumber) {
        this.coNumber = coNumber;
    }

    public String getMoneyUppercase() {
        return moneyUppercase;
    }

    public void setMoneyUppercase(String moneyUppercase) {
        this.moneyUppercase = moneyUppercase;
    }

    public String getMoneyLowercase() {
        return moneyLowercase;
    }

    public void setMoneyLowercase(String moneyLowercase) {
        this.moneyLowercase = moneyLowercase;
    }

    public String getDeclareMoney() {
        return declareMoney;
    }

    public void setDeclareMoney(String declareMoney) {
        this.declareMoney = declareMoney;
    }

    public String getDeservedMoney() {
        return deservedMoney;
    }

    public void setDeservedMoney(String deservedMoney) {
        this.deservedMoney = deservedMoney;
    }

    public String getDeductibleMoney() {
        return deductibleMoney;
    }

    public void setDeductibleMoney(String deductibleMoney) {
        this.deductibleMoney = deductibleMoney;
    }

    public String getPayableMoney() {
        return payableMoney;
    }

    public void setPayableMoney(String payableMoney) {
        this.payableMoney = payableMoney;
    }

    public String getPcStatus() {
        return pcStatus;
    }

    public void setPcStatus(String pcStatus) {
        this.pcStatus = pcStatus;
    }

    public String getPcEnclosure() {
        return pcEnclosure;
    }

    public void setPcEnclosure(String pcEnclosure) {
        this.pcEnclosure = pcEnclosure;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreatName() {
        return creatName;
    }

    public void setCreatName(String creatName) {
        this.creatName = creatName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }
}
