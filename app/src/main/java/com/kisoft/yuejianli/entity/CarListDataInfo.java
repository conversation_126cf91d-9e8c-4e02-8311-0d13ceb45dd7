package com.kisoft.yuejianli.entity;

import com.google.gson.annotations.SerializedName;
import com.kisoft.yuejianli.base.Base;

public class CarListDataInfo extends Base {

    private String buyPrice;
    private String firstCheckDate;
    private String cartype;
    private String cfGuid;
    private String machineCode;
    private String carName;
    private String carcode;
    private String lastCheckDate;
    private String FDeptId;
    private String buyDate;
    private String sign;
    private String carGuid;
    private String remark;
    private String spec;
    private String carProperty;
    private String inTime;
    private String carFrame;
    private String FDeptName;
    private String driver;
    private String carOwner;
    private String imgPath;
    private String cfName;
    private String outTime;

    public String getBuyPrice() {
        return buyPrice;
    }

    public void setBuyPrice(String buyPrice) {
        this.buyPrice = buyPrice;
    }

    public String getFirstCheckDate() {
        return firstCheckDate;
    }

    public void setFirstCheckDate(String firstCheckDate) {
        this.firstCheckDate = firstCheckDate;
    }

    public String getCartype() {
        return cartype;
    }

    public void setCartype(String cartype) {
        this.cartype = cartype;
    }

    public String getCfGuid() {
        return cfGuid;
    }

    public void setCfGuid(String cfGuid) {
        this.cfGuid = cfGuid;
    }

    public String getMachineCode() {
        return machineCode;
    }

    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }

    public String getCarName() {
        return carName;
    }

    public void setCarName(String carName) {
        this.carName = carName;
    }

    public String getCarcode() {
        return carcode;
    }

    public void setCarcode(String carcode) {
        this.carcode = carcode;
    }

    public String getLastCheckDate() {
        return lastCheckDate;
    }

    public void setLastCheckDate(String lastCheckDate) {
        this.lastCheckDate = lastCheckDate;
    }

    public String getFDeptId() {
        return FDeptId;
    }

    public void setFDeptId(String FDeptId) {
        this.FDeptId = FDeptId;
    }

    public String getFDeptName() {
        return FDeptName;
    }

    public void setFDeptName(String FDeptName) {
        this.FDeptName = FDeptName;
    }

    public String getBuyDate() {
        return buyDate;
    }

    public void setBuyDate(String buyDate) {
        this.buyDate = buyDate;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getCarGuid() {
        return carGuid;
    }

    public void setCarGuid(String carGuid) {
        this.carGuid = carGuid;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getCarProperty() {
        return carProperty;
    }

    public void setCarProperty(String carProperty) {
        this.carProperty = carProperty;
    }

    public String getInTime() {
        return inTime;
    }

    public void setInTime(String inTime) {
        this.inTime = inTime;
    }

    public String getCarFrame() {
        return carFrame;
    }

    public void setCarFrame(String carFrame) {
        this.carFrame = carFrame;
    }


    public String getDriver() {
        return driver;
    }

    public void setDriver(String driver) {
        this.driver = driver;
    }

    public String getCarOwner() {
        return carOwner;
    }

    public void setCarOwner(String carOwner) {
        this.carOwner = carOwner;
    }

    public String getImgPath() {
        return imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public String getCfName() {
        return cfName;
    }

    public void setCfName(String cfName) {
        this.cfName = cfName;
    }

    public String getOutTime() {
        return outTime;
    }

    public void setOutTime(String outTime) {
        this.outTime = outTime;
    }
}
