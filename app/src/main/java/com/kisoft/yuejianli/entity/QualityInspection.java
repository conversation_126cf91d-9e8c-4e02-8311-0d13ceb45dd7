package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by tudo<PERSON> on 2018/4/8.
 * 现场巡检
 */

public class QualityInspection extends Base {

    public static final String PROGRESS_TYPE_COMPLETE = "0";
    public static final String PROGRESS_TYPE_DELAY ="1";
    public static final String PROGRESS_TYPE_AHEAD = "2";
    public static final String PROGRESS_TYPE_NORMAL = "3";

    public static final String QUALITY_TYPE_OK = "0";
    public static final String QUALITY_TYPE_WAR = "1";
    public static final String QUALITY_TYPE_NO = "2";

    private String qiId;                    // 主键id
    private String projectId;               // 项目id
    private String qiStatus = "1";          // 质量 状态
    private String qiContent;               // 内容
    private String opinion;               // 发现的问题及处理情况
    private String remark;               // 备注/质量安全环保等情况
    private String qiEnclosure;             // 附件
    private String createId;                // 创建人
    private String createName;                // 创建人

    //新增
    private String insPosition;             // 检测部位
    private String constUnit;               // 施工单位
    private String proStatus = "1";         // 进度状态
    private String safeStatus= "1";         // 安全状态
    private String constContent;            // 施工内容
    private String createTime;
    private boolean isStatus;

    private String unitProjectId;

    private String qiNumber;    //编号
    private String projectScale;//工程规模
    private String conSzj;   //合同段
    private String weather;	//天气
    private String hot;	//温度

    private String inspector;   //巡视人员
    private String dirName; //总监
    private String agent;   //总代
    private String supervision; //专监
    private String supervisor;  //监理员



    public QualityInspection() {
    }

    public boolean isStatus() {
        return isStatus;
    }

    public void setStatus(boolean status) {
        isStatus = status;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getQiId() {
        return qiId;
    }

    public void setQiId(String qiId) {
        this.qiId = qiId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getQiStatus() {
        return qiStatus;
    }

    public void setQiStatus(String qiStatus) {
        this.qiStatus = qiStatus;
    }

    public String getQiContent() {
        return qiContent;
    }

    public void setQiContent(String qiContent) {
        this.qiContent = qiContent;
    }

    public String getQiEnclosure() {
        return qiEnclosure;
    }

    public void setQiEnclosure(String qiEnclosure) {
        this.qiEnclosure = qiEnclosure;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getInsPosition() {
        return insPosition;
    }

    public void setInsPosition(String insPosition) {
        this.insPosition = insPosition;
    }

    public String getConstUnit() {
        return constUnit;
    }

    public void setConstUnit(String constUnit) {
        this.constUnit = constUnit;
    }

    public String getProStatus() {
        return proStatus;
    }

    public void setProStatus(String proStatus) {
        this.proStatus = proStatus;
    }

    public String getSafeStatus() {
        return safeStatus;
    }

    public void setSafeStatus(String safeStatus) {
        this.safeStatus = safeStatus;
    }

    public String getConstContent() {
        return constContent;
    }

    public void setConstContent(String constContent) {
        this.constContent = constContent;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUnitProjectId() {
        return unitProjectId;
    }

    public void setUnitProjectId(String unitProjectId) {
        this.unitProjectId = unitProjectId;
    }

    public String getQiNumber() {
        return qiNumber;
    }

    public void setQiNumber(String qiNumber) {
        this.qiNumber = qiNumber;
    }

    public String getProjectScale() {
        return projectScale;
    }

    public void setProjectScale(String projectScale) {
        this.projectScale = projectScale;
    }

    public String getConSzj() {
        return conSzj;
    }

    public void setConSzj(String conSzj) {
        this.conSzj = conSzj;
    }

    public String getWeather() {
        return weather;
    }

    public void setWeather(String weather) {
        this.weather = weather;
    }

    public String getHot() {
        return hot;
    }

    public void setHot(String hot) {
        this.hot = hot;
    }

    public String getInspector() {
        return inspector;
    }

    public void setInspector(String inspector) {
        this.inspector = inspector;
    }

    public String getDirName() {
        return dirName;
    }

    public void setDirName(String dirName) {
        this.dirName = dirName;
    }

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    public String getSupervision() {
        return supervision;
    }

    public void setSupervision(String supervision) {
        this.supervision = supervision;
    }

    public String getSupervisor() {
        return supervisor;
    }

    public void setSupervisor(String supervisor) {
        this.supervisor = supervisor;
    }
}
