package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.List;

/**
 * Created by tudou on 2018/4/26.
 */

public class AccidentInfos extends Base {

    private int count;

    private List<QualityAccident> list;

    public AccidentInfos() {
    }

    public List<QualityAccident> getList() {
        return list;
    }

    public void setList(List<QualityAccident> list) {
        this.list = list;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
