package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Description: 审批人
 * Author     : 闫璐
 * Date       : 2018/12/23 20:56
 */

public class ProjectApproverInfo extends Base{

        /**
         * credentials : 
         * userId : 150612195522479da71de5865f04a401
         * pwiId : 
         * enterDate : null
         * grade : 
         * leaveDateStr : 
         * leaveDate : null
         * sex : 
         * poiName : 
         * age : 
         * idCard : 
         * createTime : 
         * expdate : null
         * poiId : 
         * professnum : 
         * linkTel : 
         * corp : 
         * enterDateStr : 
         * postGuid : 
         * userName : 祁劲栋
         * createName : 
         * appdate : null
         * spcciality : 
         * createId : 
         * ename : 
         * remark : 
         * postName : 
         * constUnit : 
         * projectId : 
         * pwiName : 
         * state : 
         * puiId : 
         * projectName : 
         */

        private String credentials;
        private String userId;
        private String pwiId;
        private String enterDate;
        private String grade;
        private String leaveDateStr;
        private String leaveDate;
        private String sex;
        private String poiName;
        private String age;
        private String idCard;
        private String createTime;
        private String expdate;
        private String poiId;
        private String professnum;
        private String linkTel;
        private String corp;
        private String enterDateStr;
        private String postGuid;
        private String userName;
        private String createName;
        private String appdate;
        private String spcciality;
        private String createId;
        private String ename;
        private String remark;
        private String postName;
        private String constUnit;
        private String projectId;
        private String pwiName;
        private String state;
        private String puiId;
        private String projectName;

        public String getCredentials() {
            return credentials;
        }

        public void setCredentials(String credentials) {
            this.credentials = credentials;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getPwiId() {
            return pwiId;
        }

        public void setPwiId(String pwiId) {
            this.pwiId = pwiId;
        }

        public String getEnterDate() {
            return enterDate;
        }

        public void setEnterDate(String enterDate) {
            this.enterDate = enterDate;
        }

        public String getGrade() {
            return grade;
        }

        public void setGrade(String grade) {
            this.grade = grade;
        }

        public String getLeaveDateStr() {
            return leaveDateStr;
        }

        public void setLeaveDateStr(String leaveDateStr) {
            this.leaveDateStr = leaveDateStr;
        }

        public String getLeaveDate() {
            return leaveDate;
        }

        public void setLeaveDate(String leaveDate) {
            this.leaveDate = leaveDate;
        }

        public String getSex() {
            return sex;
        }

        public void setSex(String sex) {
            this.sex = sex;
        }

        public String getPoiName() {
            return poiName;
        }

        public void setPoiName(String poiName) {
            this.poiName = poiName;
        }

        public String getAge() {
            return age;
        }

        public void setAge(String age) {
            this.age = age;
        }

        public String getIdCard() {
            return idCard;
        }

        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getExpdate() {
            return expdate;
        }

        public void setExpdate(String expdate) {
            this.expdate = expdate;
        }

        public String getPoiId() {
            return poiId;
        }

        public void setPoiId(String poiId) {
            this.poiId = poiId;
        }

        public String getProfessnum() {
            return professnum;
        }

        public void setProfessnum(String professnum) {
            this.professnum = professnum;
        }

        public String getLinkTel() {
            return linkTel;
        }

        public void setLinkTel(String linkTel) {
            this.linkTel = linkTel;
        }

        public String getCorp() {
            return corp;
        }

        public void setCorp(String corp) {
            this.corp = corp;
        }

        public String getEnterDateStr() {
            return enterDateStr;
        }

        public void setEnterDateStr(String enterDateStr) {
            this.enterDateStr = enterDateStr;
        }

        public String getPostGuid() {
            return postGuid;
        }

        public void setPostGuid(String postGuid) {
            this.postGuid = postGuid;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getCreateName() {
            return createName;
        }

        public void setCreateName(String createName) {
            this.createName = createName;
        }

        public String getAppdate() {
            return appdate;
        }

        public void setAppdate(String appdate) {
            this.appdate = appdate;
        }

        public String getSpcciality() {
            return spcciality;
        }

        public void setSpcciality(String spcciality) {
            this.spcciality = spcciality;
        }

        public String getCreateId() {
            return createId;
        }

        public void setCreateId(String createId) {
            this.createId = createId;
        }

        public String getEname() {
            return ename;
        }

        public void setEname(String ename) {
            this.ename = ename;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getPostName() {
            return postName;
        }

        public void setPostName(String postName) {
            this.postName = postName;
        }

        public String getConstUnit() {
            return constUnit;
        }

        public void setConstUnit(String constUnit) {
            this.constUnit = constUnit;
        }

        public String getProjectId() {
            return projectId;
        }

        public void setProjectId(String projectId) {
            this.projectId = projectId;
        }

        public String getPwiName() {
            return pwiName;
        }

        public void setPwiName(String pwiName) {
            this.pwiName = pwiName;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getPuiId() {
            return puiId;
        }

        public void setPuiId(String puiId) {
            this.puiId = puiId;
        }

        public String getProjectName() {
            return projectName;
        }

        public void setProjectName(String projectName) {
            this.projectName = projectName;
        }

}
