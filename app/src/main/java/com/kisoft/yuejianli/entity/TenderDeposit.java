package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.List;

/**
 * Created by tudo<PERSON> on 2018/6/27.
 * 投标保函保证金
 */

public class TenderDeposit extends Base {

    private int count;
    private List<DepositAccept> list;

    public TenderDeposit() {
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public List<DepositAccept> getList() {
        return list;
    }

    public void setList(List<DepositAccept> list) {
        this.list = list;
    }

    /**
     * 保函、保证金缴纳情况
     */
    public static class DepositAccept extends Base{

        private String ibaId;
        private String ipId;            // 计划id
        private String planName;        // 计划名称
        private String invCompany;      // 投标公司
        private String accptDate;       // 付款时间
        private String userName;        // 付款人
        private String accptAmount;     // 付款金额
        private String remark;
        private String createId;
        private String createName;
        private String createTime;
        private boolean reTurn;       // 是否已返回


        public DepositAccept() {
        }

        public String getIbaId() {
            return ibaId;
        }

        public void setIbaId(String ibaId) {
            this.ibaId = ibaId;
        }

        public String getIpId() {
            return ipId;
        }

        public void setIpId(String ipId) {
            this.ipId = ipId;
        }

        public String getPlanName() {
            return planName;
        }

        public void setPlanName(String planName) {
            this.planName = planName;
        }

        public String getInvCompany() {
            return invCompany;
        }

        public void setInvCompany(String invCompany) {
            this.invCompany = invCompany;
        }

        public String getAccptDate() {
            return accptDate;
        }

        public void setAccptDate(String accptDate) {
            this.accptDate = accptDate;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getAccptAmount() {
            return accptAmount;
        }

        public void setAccptAmount(String accptAmount) {
            this.accptAmount = accptAmount;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getCreateId() {
            return createId;
        }

        public void setCreateId(String createId) {
            this.createId = createId;
        }

        public String getCreateName() {
            return createName;
        }

        public void setCreateName(String createName) {
            this.createName = createName;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public boolean isReTurn() {
            return reTurn;
        }

        public void setReTurn(boolean reTurn) {
            this.reTurn = reTurn;
        }
    }


    /**
     * 保函、保证金退回情况
     */
    public static class DepositReturn extends Base{
        private String ibrId;
        private String ipId;
        private String planName;
        private String invCompany;
        private String userName;
        private String returnDate;
        private String returnAmount;
        private String remark;
        private String createId;
        private String createName;
        private String createTime;

        public DepositReturn() {
        }

        public String getIbrId() {
            return ibrId;
        }

        public void setIbrId(String ibrId) {
            this.ibrId = ibrId;
        }

        public String getIpId() {
            return ipId;
        }

        public void setIpId(String ipId) {
            this.ipId = ipId;
        }

        public String getPlanName() {
            return planName;
        }

        public void setPlanName(String planName) {
            this.planName = planName;
        }

        public String getInvCompany() {
            return invCompany;
        }

        public void setInvCompany(String invCompany) {
            this.invCompany = invCompany;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getReturnDate() {
            return returnDate;
        }

        public void setReturnDate(String returnDate) {
            this.returnDate = returnDate;
        }

        public String getReturnAmount() {
            return returnAmount;
        }

        public void setReturnAmount(String returnAmount) {
            this.returnAmount = returnAmount;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getCreateId() {
            return createId;
        }

        public void setCreateId(String createId) {
            this.createId = createId;
        }

        public String getCreateName() {
            return createName;
        }

        public void setCreateName(String createName) {
            this.createName = createName;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }


    }


}
