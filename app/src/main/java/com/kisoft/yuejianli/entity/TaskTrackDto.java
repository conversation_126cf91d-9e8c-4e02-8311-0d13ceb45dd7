package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Description: 任务跟踪
 * Author     : yanlu
 * Date       : 2018/12/29 16:38
 */
public class TaskTrackDto extends Base{

    private String tsktckGuid;//主键
    private String tskGuid;//任务Id
    private String userGuid;//跟踪人Id
    private String report; //内容
    private Long ttpercent; //完成百分比
    private String progress;//进度（0=未开始、1=进行中、2=已完成、3=已推迟）
    private String createdate;  //创建时间
    private String reporttype;//记录类型（0=负责人答复、1=参与人答复、2=发起人答复）
    private String username;//跟踪人姓名
    private String status;//工作任务整体进度

    public String getTsktckGuid() {
        return tsktckGuid;
    }

    public void setTsktckGuid(String tsktckGuid) {
        this.tsktckGuid = tsktckGuid;
    }

    public String getTskGuid() {
        return tskGuid;
    }

    public void setTskGuid(String tskGuid) {
        this.tskGuid = tskGuid;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getReport() {
        return report;
    }

    public void setReport(String report) {
        this.report = report;
    }

    public Long getTtpercent() {
        return ttpercent;
    }

    public void setTtpercent(Long ttpercent) {
        this.ttpercent = ttpercent;
    }

    public String getProgress() {
        return progress;
    }

    public void setProgress(String progress) {
        this.progress = progress;
    }

    public String getCreatedate() {
        return createdate;
    }

    public void setCreatedate(String createdate) {
        this.createdate = createdate;
    }

    public String getReporttype() {
        return reporttype;
    }

    public void setReporttype(String reporttype) {
        this.reporttype = reporttype;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
