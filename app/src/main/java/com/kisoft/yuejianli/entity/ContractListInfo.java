package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.List;

/**
 * Created by Administrator on 2019/5/6 0006.
 */

public class ContractListInfo extends Base{
    private int count;
    private List<ContractBean> list;

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public List<ContractBean> getList() {
        return list;
    }

    public void setList(List<ContractBean> list) {
        this.list = list;
    }

    public static class ContractBean extends Base {
        private String conAmount1;
        private String maxPage;
        private String investor;
        private String conName;
        private String payRemark;
        private String ciId;
        private String wfId;
        private String conPeriod;
        private String conAddress;
        private String changetpye;
        private String chargeDirId;
        private String supConNo;
        private String chargeEngId;
        private String pjTotalInv;
        private String startEnd;
        private String decAmountTotal;
        private String pjAddress;
        private String conClass;
        private String chargeEngName;
        private String createTime;
        private String ownName;
        private String conType;
        private String wfState;
        private String conNo;
        private String linkTel;
        private String pjDirId;
        private String createName;
        private String pjContent;
        private String conDate;
        private String createId;
        private String projectArea;
        private String remark;
        private String cpmAmountTotal;
        private String chargeDirName;
        private String conDate1;
        private String pjDirName;
        private String pjSjdw;
        private String projectId;
        private String changesInfo;
        private String page;
        private String conAmount;
        private String kfztdw;
        private String projectName;
        private String sizePage;

        private String invAmountSum;
        private String cpmAmountSum;

        public String getCpmAmountSum() {
            return cpmAmountSum;
        }

        public void setCpmAmountSum(String cpmAmountSum) {
            this.cpmAmountSum = cpmAmountSum;
        }

        public String getInvAmountSum() {
            return invAmountSum;
        }

        public void setInvAmountSum(String invAmountSum) {
            this.invAmountSum = invAmountSum;
        }

        public String getConAmount1() {
            return conAmount1;
        }

        public void setConAmount1(String conAmount1) {
            this.conAmount1 = conAmount1;
        }

        public String getMaxPage() {
            return maxPage;
        }

        public void setMaxPage(String maxPage) {
            this.maxPage = maxPage;
        }

        public String getInvestor() {
            return investor;
        }

        public void setInvestor(String investor) {
            this.investor = investor;
        }

        public String getConName() {
            return conName;
        }

        public void setConName(String conName) {
            this.conName = conName;
        }

        public String getPayRemark() {
            return payRemark;
        }

        public void setPayRemark(String payRemark) {
            this.payRemark = payRemark;
        }

        public String getCiId() {
            return ciId;
        }

        public void setCiId(String ciId) {
            this.ciId = ciId;
        }

        public String getWfId() {
            return wfId;
        }

        public void setWfId(String wfId) {
            this.wfId = wfId;
        }

        public String getConPeriod() {
            return conPeriod;
        }

        public void setConPeriod(String conPeriod) {
            this.conPeriod = conPeriod;
        }

        public String getConAddress() {
            return conAddress;
        }

        public void setConAddress(String conAddress) {
            this.conAddress = conAddress;
        }

        public String getChangetpye() {
            return changetpye;
        }

        public void setChangetpye(String changetpye) {
            this.changetpye = changetpye;
        }

        public String getChargeDirId() {
            return chargeDirId;
        }

        public void setChargeDirId(String chargeDirId) {
            this.chargeDirId = chargeDirId;
        }

        public String getSupConNo() {
            return supConNo;
        }

        public void setSupConNo(String supConNo) {
            this.supConNo = supConNo;
        }

        public String getChargeEngId() {
            return chargeEngId;
        }

        public void setChargeEngId(String chargeEngId) {
            this.chargeEngId = chargeEngId;
        }

        public String getPjTotalInv() {
            return pjTotalInv;
        }

        public void setPjTotalInv(String pjTotalInv) {
            this.pjTotalInv = pjTotalInv;
        }

        public String getStartEnd() {
            return startEnd;
        }

        public void setStartEnd(String startEnd) {
            this.startEnd = startEnd;
        }

        public String getDecAmountTotal() {
            return decAmountTotal;
        }

        public void setDecAmountTotal(String decAmountTotal) {
            this.decAmountTotal = decAmountTotal;
        }

        public String getPjAddress() {
            return pjAddress;
        }

        public void setPjAddress(String pjAddress) {
            this.pjAddress = pjAddress;
        }

        public String getConClass() {
            return conClass;
        }

        public void setConClass(String conClass) {
            this.conClass = conClass;
        }

        public String getChargeEngName() {
            return chargeEngName;
        }

        public void setChargeEngName(String chargeEngName) {
            this.chargeEngName = chargeEngName;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getOwnName() {
            return ownName;
        }

        public void setOwnName(String ownName) {
            this.ownName = ownName;
        }

        public String getConType() {
            return conType;
        }

        public void setConType(String conType) {
            this.conType = conType;
        }

        public String getWfState() {
            return wfState;
        }

        public void setWfState(String wfState) {
            this.wfState = wfState;
        }

        public String getConNo() {
            return conNo;
        }

        public void setConNo(String conNo) {
            this.conNo = conNo;
        }

        public String getLinkTel() {
            return linkTel;
        }

        public void setLinkTel(String linkTel) {
            this.linkTel = linkTel;
        }

        public String getPjDirId() {
            return pjDirId;
        }

        public void setPjDirId(String pjDirId) {
            this.pjDirId = pjDirId;
        }

        public String getCreateName() {
            return createName;
        }

        public void setCreateName(String createName) {
            this.createName = createName;
        }

        public String getPjContent() {
            return pjContent;
        }

        public void setPjContent(String pjContent) {
            this.pjContent = pjContent;
        }

        public String getConDate() {
            return conDate;
        }

        public void setConDate(String conDate) {
            this.conDate = conDate;
        }

        public String getCreateId() {
            return createId;
        }

        public void setCreateId(String createId) {
            this.createId = createId;
        }

        public String getProjectArea() {
            return projectArea;
        }

        public void setProjectArea(String projectArea) {
            this.projectArea = projectArea;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getCpmAmountTotal() {
            return cpmAmountTotal;
        }

        public void setCpmAmountTotal(String cpmAmountTotal) {
            this.cpmAmountTotal = cpmAmountTotal;
        }

        public String getChargeDirName() {
            return chargeDirName;
        }

        public void setChargeDirName(String chargeDirName) {
            this.chargeDirName = chargeDirName;
        }

        public String getConDate1() {
            return conDate1;
        }

        public void setConDate1(String conDate1) {
            this.conDate1 = conDate1;
        }

        public String getPjDirName() {
            return pjDirName;
        }

        public void setPjDirName(String pjDirName) {
            this.pjDirName = pjDirName;
        }

        public String getPjSjdw() {
            return pjSjdw;
        }

        public void setPjSjdw(String pjSjdw) {
            this.pjSjdw = pjSjdw;
        }

        public String getProjectId() {
            return projectId;
        }

        public void setProjectId(String projectId) {
            this.projectId = projectId;
        }

        public String getChangesInfo() {
            return changesInfo;
        }

        public void setChangesInfo(String changesInfo) {
            this.changesInfo = changesInfo;
        }

        public String getPage() {
            return page;
        }

        public void setPage(String page) {
            this.page = page;
        }

        public String getConAmount() {
            return conAmount;
        }

        public void setConAmount(String conAmount) {
            this.conAmount = conAmount;
        }

        public String getKfztdw() {
            return kfztdw;
        }

        public void setKfztdw(String kfztdw) {
            this.kfztdw = kfztdw;
        }

        public String getProjectName() {
            return projectName;
        }

        public void setProjectName(String projectName) {
            this.projectName = projectName;
        }

        public String getSizePage() {
            return sizePage;
        }

        public void setSizePage(String sizePage) {
            this.sizePage = sizePage;
        }
    }
}
