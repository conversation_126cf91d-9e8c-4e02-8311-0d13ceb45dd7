package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

public class ConBeanInfo extends Base {
        /**
         * supConNoFlag :
         * perFeeWarrDate : 2019-10-10
         * conType : 141210130904177a809b3356acec4dcb
         * linkTel : 13463980960
         * wfTaskState : 3
         * remark :
         * pjSjdw :
         * pjDirName :
         * implOrgId :
         * planStartDate : 2019-10-11
         * ciId : 200317180304512a91df09024b1198e1
         * workFlowType : 1
         * maxPage : 0
         * page : 0
         * chargeEngId :
         * conNo : 123654
         * busiNatureName : 招标代理
         * refulxDate : 2019-08-27
         * kfztdw :
         * cpmAmountTotal :
         * createId : 1411061510426418964b4caed722b3b7
         * conPerfFee : 11111
         * invAmountSum :
         * acquiWay : 190715171459214bb026751ccbceb82f
         * conBond : 1234
         * chargeDirName :
         * wfTaskId : 20031718050460958dde44105f00dea1
         * conDate : 2019-04-10
         * changesInfo :
         * wfState : 2
         * bondType :
         * createName : 王茜
         * chargeDirId :
         * conClass : 1901151812096884b9f57e23bbfafd05
         * chargeOrgId :
         * chargeOrgName :
         * conName : 666
         * refulxDate1 :
         * cpmAmountSum :
         * projectName : 投标项目
         * conAmount1 :
         * pjContent :
         * decAmountTotal :
         * bidDirId : 150121165325774895f6fe02d482ccd3
         * createTime : 2020-3-17 18:03:04
         * conClassName : 合作
         * periodRemark :
         * wfId : 200317180502479097603de9e213c02e
         * conDate1 :
         * sourceFonds : 自筹资金
         * investor :
         * pjTotalInv : 100000
         * projectId : 1903211045275978aba15c8281236e6d
         * projectArea : 0
         * bondTypeName :
         * conAddress : 231
         * supConNo :
         * bondWarrDate : 2019-10-31
         * ownName : 河北浩智房地产开发有限责任公司
         * conPeriod : 1350
         * sourceFondsName :
         * acquiWayName : 竞争性磋商
         * conAmount : 1234567890
         * payRemark :
         * pjAddress :
         * issueDate : 2019-08-21
         * chargeEngName :
         * startEnd :
         * planEndDate : 2019-10-11
         * pjDirId :
         * bidDirName : 周虹
         * isSendBack : 0
         * conTypeName : 公路
         * implOrgName :
         * changetpye :
         * sizePage : 0
         * busiNature : 19071517113696768c559e0be8826730
         */

        private String supConNoFlag;
        private String perFeeWarrDate;
        private String conType;
        private String linkTel;
        private String wfTaskState;
        private String remark;
        private String pjSjdw;
        private String pjDirName;
        private String implOrgId;
        private String planStartDate;
        private String ciId;
        private String workFlowType;
        private int maxPage;
        private int page;
        private String chargeEngId;
        private String conNo;
        private String busiNatureName;
        private String refulxDate;
        private String kfztdw;
        private String cpmAmountTotal;
        private String createId;
        private String conPerfFee;
        private String invAmountSum;
        private String acquiWay;
        private String conBond;
        private String chargeDirName;
        private String wfTaskId;
        private String conDate;
        private String changesInfo;
        private String wfState;
        private String bondType;
        private String createName;
        private String chargeDirId;
        private String conClass;
        private String chargeOrgId;
        private String chargeOrgName;
        private String conName;
        private String refulxDate1;
        private String cpmAmountSum;
        private String projectName;
        private String conAmount1;
        private String pjContent;
        private String decAmountTotal;
        private String bidDirId;
        private String createTime;
        private String conClassName;
        private String periodRemark;
        private String wfId;
        private String conDate1;
        private String sourceFonds;
        private String investor;
        private String pjTotalInv;
        private String projectId;
        private String projectArea;
        private String bondTypeName;
        private String conAddress;
        private String supConNo;
        private String bondWarrDate;
        private String ownName;
        private String conPeriod;
        private String sourceFondsName;
        private String acquiWayName;
        private String conAmount;
        private String payRemark;
        private String pjAddress;
        private String issueDate;
        private String chargeEngName;
        private String startEnd;
        private String planEndDate;
        private String pjDirId;
        private String bidDirName;
        private String isSendBack;
        private String conTypeName;
        private String implOrgName;
        private String changetpye;
        private int sizePage;
        private String busiNature;

        public String getSupConNoFlag() {
            return supConNoFlag;
        }

        public void setSupConNoFlag(String supConNoFlag) {
            this.supConNoFlag = supConNoFlag;
        }

        public String getPerFeeWarrDate() {
            return perFeeWarrDate;
        }

        public void setPerFeeWarrDate(String perFeeWarrDate) {
            this.perFeeWarrDate = perFeeWarrDate;
        }

        public String getConType() {
            return conType;
        }

        public void setConType(String conType) {
            this.conType = conType;
        }

        public String getLinkTel() {
            return linkTel;
        }

        public void setLinkTel(String linkTel) {
            this.linkTel = linkTel;
        }

        public String getWfTaskState() {
            return wfTaskState;
        }

        public void setWfTaskState(String wfTaskState) {
            this.wfTaskState = wfTaskState;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getPjSjdw() {
            return pjSjdw;
        }

        public void setPjSjdw(String pjSjdw) {
            this.pjSjdw = pjSjdw;
        }

        public String getPjDirName() {
            return pjDirName;
        }

        public void setPjDirName(String pjDirName) {
            this.pjDirName = pjDirName;
        }

        public String getImplOrgId() {
            return implOrgId;
        }

        public void setImplOrgId(String implOrgId) {
            this.implOrgId = implOrgId;
        }

        public String getPlanStartDate() {
            return planStartDate;
        }

        public void setPlanStartDate(String planStartDate) {
            this.planStartDate = planStartDate;
        }

        public String getCiId() {
            return ciId;
        }

        public void setCiId(String ciId) {
            this.ciId = ciId;
        }

        public String getWorkFlowType() {
            return workFlowType;
        }

        public void setWorkFlowType(String workFlowType) {
            this.workFlowType = workFlowType;
        }

        public int getMaxPage() {
            return maxPage;
        }

        public void setMaxPage(int maxPage) {
            this.maxPage = maxPage;
        }

        public int getPage() {
            return page;
        }

        public void setPage(int page) {
            this.page = page;
        }

        public String getChargeEngId() {
            return chargeEngId;
        }

        public void setChargeEngId(String chargeEngId) {
            this.chargeEngId = chargeEngId;
        }

        public String getConNo() {
            return conNo;
        }

        public void setConNo(String conNo) {
            this.conNo = conNo;
        }

        public String getBusiNatureName() {
            return busiNatureName;
        }

        public void setBusiNatureName(String busiNatureName) {
            this.busiNatureName = busiNatureName;
        }

        public String getRefulxDate() {
            return refulxDate;
        }

        public void setRefulxDate(String refulxDate) {
            this.refulxDate = refulxDate;
        }

        public String getKfztdw() {
            return kfztdw;
        }

        public void setKfztdw(String kfztdw) {
            this.kfztdw = kfztdw;
        }

        public String getCpmAmountTotal() {
            return cpmAmountTotal;
        }

        public void setCpmAmountTotal(String cpmAmountTotal) {
            this.cpmAmountTotal = cpmAmountTotal;
        }

        public String getCreateId() {
            return createId;
        }

        public void setCreateId(String createId) {
            this.createId = createId;
        }

        public String getConPerfFee() {
            return conPerfFee;
        }

        public void setConPerfFee(String conPerfFee) {
            this.conPerfFee = conPerfFee;
        }

        public String getInvAmountSum() {
            return invAmountSum;
        }

        public void setInvAmountSum(String invAmountSum) {
            this.invAmountSum = invAmountSum;
        }

        public String getAcquiWay() {
            return acquiWay;
        }

        public void setAcquiWay(String acquiWay) {
            this.acquiWay = acquiWay;
        }

        public String getConBond() {
            return conBond;
        }

        public void setConBond(String conBond) {
            this.conBond = conBond;
        }

        public String getChargeDirName() {
            return chargeDirName;
        }

        public void setChargeDirName(String chargeDirName) {
            this.chargeDirName = chargeDirName;
        }

        public String getWfTaskId() {
            return wfTaskId;
        }

        public void setWfTaskId(String wfTaskId) {
            this.wfTaskId = wfTaskId;
        }

        public String getConDate() {
            return conDate;
        }

        public void setConDate(String conDate) {
            this.conDate = conDate;
        }

        public String getChangesInfo() {
            return changesInfo;
        }

        public void setChangesInfo(String changesInfo) {
            this.changesInfo = changesInfo;
        }

        public String getWfState() {
            return wfState;
        }

        public void setWfState(String wfState) {
            this.wfState = wfState;
        }

        public String getBondType() {
            return bondType;
        }

        public void setBondType(String bondType) {
            this.bondType = bondType;
        }

        public String getCreateName() {
            return createName;
        }

        public void setCreateName(String createName) {
            this.createName = createName;
        }

        public String getChargeDirId() {
            return chargeDirId;
        }

        public void setChargeDirId(String chargeDirId) {
            this.chargeDirId = chargeDirId;
        }

        public String getConClass() {
            return conClass;
        }

        public void setConClass(String conClass) {
            this.conClass = conClass;
        }

        public String getChargeOrgId() {
            return chargeOrgId;
        }

        public void setChargeOrgId(String chargeOrgId) {
            this.chargeOrgId = chargeOrgId;
        }

        public String getChargeOrgName() {
            return chargeOrgName;
        }

        public void setChargeOrgName(String chargeOrgName) {
            this.chargeOrgName = chargeOrgName;
        }

        public String getConName() {
            return conName;
        }

        public void setConName(String conName) {
            this.conName = conName;
        }

        public String getRefulxDate1() {
            return refulxDate1;
        }

        public void setRefulxDate1(String refulxDate1) {
            this.refulxDate1 = refulxDate1;
        }

        public String getCpmAmountSum() {
            return cpmAmountSum;
        }

        public void setCpmAmountSum(String cpmAmountSum) {
            this.cpmAmountSum = cpmAmountSum;
        }

        public String getProjectName() {
            return projectName;
        }

        public void setProjectName(String projectName) {
            this.projectName = projectName;
        }

        public String getConAmount1() {
            return conAmount1;
        }

        public void setConAmount1(String conAmount1) {
            this.conAmount1 = conAmount1;
        }

        public String getPjContent() {
            return pjContent;
        }

        public void setPjContent(String pjContent) {
            this.pjContent = pjContent;
        }

        public String getDecAmountTotal() {
            return decAmountTotal;
        }

        public void setDecAmountTotal(String decAmountTotal) {
            this.decAmountTotal = decAmountTotal;
        }

        public String getBidDirId() {
            return bidDirId;
        }

        public void setBidDirId(String bidDirId) {
            this.bidDirId = bidDirId;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getConClassName() {
            return conClassName;
        }

        public void setConClassName(String conClassName) {
            this.conClassName = conClassName;
        }

        public String getPeriodRemark() {
            return periodRemark;
        }

        public void setPeriodRemark(String periodRemark) {
            this.periodRemark = periodRemark;
        }

        public String getWfId() {
            return wfId;
        }

        public void setWfId(String wfId) {
            this.wfId = wfId;
        }

        public String getConDate1() {
            return conDate1;
        }

        public void setConDate1(String conDate1) {
            this.conDate1 = conDate1;
        }

        public String getSourceFonds() {
            return sourceFonds;
        }

        public void setSourceFonds(String sourceFonds) {
            this.sourceFonds = sourceFonds;
        }

        public String getInvestor() {
            return investor;
        }

        public void setInvestor(String investor) {
            this.investor = investor;
        }

        public String getPjTotalInv() {
            return pjTotalInv;
        }

        public void setPjTotalInv(String pjTotalInv) {
            this.pjTotalInv = pjTotalInv;
        }

        public String getProjectId() {
            return projectId;
        }

        public void setProjectId(String projectId) {
            this.projectId = projectId;
        }

        public String getProjectArea() {
            return projectArea;
        }

        public void setProjectArea(String projectArea) {
            this.projectArea = projectArea;
        }

        public String getBondTypeName() {
            return bondTypeName;
        }

        public void setBondTypeName(String bondTypeName) {
            this.bondTypeName = bondTypeName;
        }

        public String getConAddress() {
            return conAddress;
        }

        public void setConAddress(String conAddress) {
            this.conAddress = conAddress;
        }

        public String getSupConNo() {
            return supConNo;
        }

        public void setSupConNo(String supConNo) {
            this.supConNo = supConNo;
        }

        public String getBondWarrDate() {
            return bondWarrDate;
        }

        public void setBondWarrDate(String bondWarrDate) {
            this.bondWarrDate = bondWarrDate;
        }

        public String getOwnName() {
            return ownName;
        }

        public void setOwnName(String ownName) {
            this.ownName = ownName;
        }

        public String getConPeriod() {
            return conPeriod;
        }

        public void setConPeriod(String conPeriod) {
            this.conPeriod = conPeriod;
        }

        public String getSourceFondsName() {
            return sourceFondsName;
        }

        public void setSourceFondsName(String sourceFondsName) {
            this.sourceFondsName = sourceFondsName;
        }

        public String getAcquiWayName() {
            return acquiWayName;
        }

        public void setAcquiWayName(String acquiWayName) {
            this.acquiWayName = acquiWayName;
        }

        public String getConAmount() {
            return conAmount;
        }

        public void setConAmount(String conAmount) {
            this.conAmount = conAmount;
        }

        public String getPayRemark() {
            return payRemark;
        }

        public void setPayRemark(String payRemark) {
            this.payRemark = payRemark;
        }

        public String getPjAddress() {
            return pjAddress;
        }

        public void setPjAddress(String pjAddress) {
            this.pjAddress = pjAddress;
        }

        public String getIssueDate() {
            return issueDate;
        }

        public void setIssueDate(String issueDate) {
            this.issueDate = issueDate;
        }

        public String getChargeEngName() {
            return chargeEngName;
        }

        public void setChargeEngName(String chargeEngName) {
            this.chargeEngName = chargeEngName;
        }

        public String getStartEnd() {
            return startEnd;
        }

        public void setStartEnd(String startEnd) {
            this.startEnd = startEnd;
        }

        public String getPlanEndDate() {
            return planEndDate;
        }

        public void setPlanEndDate(String planEndDate) {
            this.planEndDate = planEndDate;
        }

        public String getPjDirId() {
            return pjDirId;
        }

        public void setPjDirId(String pjDirId) {
            this.pjDirId = pjDirId;
        }

        public String getBidDirName() {
            return bidDirName;
        }

        public void setBidDirName(String bidDirName) {
            this.bidDirName = bidDirName;
        }

        public String getIsSendBack() {
            return isSendBack;
        }

        public void setIsSendBack(String isSendBack) {
            this.isSendBack = isSendBack;
        }

        public String getConTypeName() {
            return conTypeName;
        }

        public void setConTypeName(String conTypeName) {
            this.conTypeName = conTypeName;
        }

        public String getImplOrgName() {
            return implOrgName;
        }

        public void setImplOrgName(String implOrgName) {
            this.implOrgName = implOrgName;
        }

        public String getChangetpye() {
            return changetpye;
        }

        public void setChangetpye(String changetpye) {
            this.changetpye = changetpye;
        }

        public int getSizePage() {
            return sizePage;
        }

        public void setSizePage(int sizePage) {
            this.sizePage = sizePage;
        }

        public String getBusiNature() {
            return busiNature;
        }

        public void setBusiNature(String busiNature) {
            this.busiNature = busiNature;
        }
}
