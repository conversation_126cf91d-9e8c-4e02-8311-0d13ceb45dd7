package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by t<PERSON><PERSON> on 2018/4/14.
 */

public class ProjectEmployer extends Base {

    private String id;
    private String name;


    public ProjectEmployer() {
    }


    public ProjectEmployer(Communication communication){
        this.id = communication.getUserId();
        this.name = communication.getUserName();
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
