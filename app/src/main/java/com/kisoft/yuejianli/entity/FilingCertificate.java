package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.Date;

/**
 * 公司证书
 */
public class FilingCertificate extends Base {
    private String fcName; //证书名称
    private String fcNumber; //证书编号
    private String fcStyle;     //证书类型
    private String ciId;        //公司Id
    private String companyName;  //公司名称
    private String credentials;  //证件状态
    //2019-07-16 add
    private String issueDate;       //发证日期
    private Long copyNums;        //可借阅份数
    private String validDate;       //有效期
    private String reviewDate;     //年审日期
    private String reviewOrgName;//年审办理部门
    private String awardAgenName;  //颁奖机构
    private String isMerit;     //是否获得奖状
    private String id;          //主键
    private String createId; //创建人Id
    private String createName;  //创建人姓名
    private String createTime;    //创建时间
    private String remark;       //备注
    private String projectId;   //项目Id
    private String projectName; //项目名称
    //2019-07-16 add 满足查询之用
    private String issueDateStr;
    //发证日期（字符型）
    private String validDateStr;
    //有效期(字符型)
    private String reviewDateStr;
    //年审日期（字符型）
    private String createTimeStr;//创建时间（字符型）

    //是否选中
    private boolean isSelect;

    public boolean isSelect() {
        return isSelect;
    }

    public void setSelect(boolean select) {
        isSelect = select;
    }

    public String getFcStyle() {
        return fcStyle;
    }

    public void setFcStyle(String fcStyle) {
        this.fcStyle = fcStyle;
    }

    public Long getCopyNums() {
        return copyNums;
    }

    public void setCopyNums(Long copyNums) {
        this.copyNums = copyNums;
    }

    public String getReviewOrgName() {
        return reviewOrgName;
    }

    public void setReviewOrgName(String reviewOrgName) {
        this.reviewOrgName = reviewOrgName;
    }

    public String getFcName() {
        return fcName;
    }

    public void setFcName(String fcName) {
        this.fcName = fcName;
    }

    public String getFcNumber() {
        return fcNumber;
    }

    public void setFcNumber(String fcNumber) {
        this.fcNumber = fcNumber;
    }

    public String getCiId() {
        return ciId;
    }

    public void setCiId(String ciId) {
        this.ciId = ciId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCredentials() {
        return credentials;
    }

    public void setCredentials(String credentials) {
        this.credentials = credentials;
    }

    public String getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(String issueDate) {
        this.issueDate = issueDate;
    }

    public String getValidDate() {
        return validDate;
    }

    public void setValidDate(String validDate) {
        this.validDate = validDate;
    }

    public String getReviewDate() {
        return reviewDate;
    }

    public void setReviewDate(String reviewDate) {
        this.reviewDate = reviewDate;
    }

    public String getAwardAgenName() {
        return awardAgenName;
    }

    public void setAwardAgenName(String awardAgenName) {
        this.awardAgenName = awardAgenName;
    }

    public String getIsMerit() {
        return isMerit;
    }

    public void setIsMerit(String isMerit) {
        this.isMerit = isMerit;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getIssueDateStr() {
        return issueDateStr;
    }

    public void setIssueDateStr(String issueDateStr) {
        this.issueDateStr = issueDateStr;
    }

    public String getValidDateStr() {
        return validDateStr;
    }

    public void setValidDateStr(String validDateStr) {
        this.validDateStr = validDateStr;
    }

    public String getReviewDateStr() {
        return reviewDateStr;
    }

    public void setReviewDateStr(String reviewDateStr) {
        this.reviewDateStr = reviewDateStr;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }
}
