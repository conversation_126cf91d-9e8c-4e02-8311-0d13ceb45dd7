package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

import java.util.List;

/**
 * Created by Administrator on 2019/5/9 0009.
 */

public class SafeInspectListDto extends Base {
    private List<ProjectSafeInspection> list;
    private int count;

    public List<ProjectSafeInspection> getList() {
        return list;
    }

    public void setList(List<ProjectSafeInspection> list) {
        this.list = list;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
