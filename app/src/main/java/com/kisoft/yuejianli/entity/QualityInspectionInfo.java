package com.kisoft.yuejianli.entity;

import java.util.List;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by tudou on 2018/5/28.
 */

public class QualityInspectionInfo extends Base {

    private int count;

    private List<QualityInspection> list;

    public QualityInspectionInfo() {
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public List<QualityInspection> getList() {
        return list;
    }

    public void setList(List<QualityInspection> list) {
        this.list = list;
    }
}
