package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

public class ShouWenInfo extends Base {

    private String securityTime;
    private String verifierId;
    private String fileNumber;
    private String attachTitle;
    private String skNo;
    private String userid;
    private String ffIds;
    private String rfName;
    private String ffAttr;
    private String keyword;
    private String raId;
    private String endDateNew;
    private String titleNew;
    private String orgName;
    private String cbdept;
    private String deptId;
    private String index;
    private String ffNames;
    private String contType;
    private String verifyState;
    private String nameNew;
    private String name;
    private String regId;
    private String curYear;
    private String createName;
    private String bussinessId;
    private String cbMenId;
    private String remark;
    private String title;
    private String verifierName;
    private String isSendBack;
    private String security;
    private String cbMenName;
    private String verifyContent;
    private String fileNo;
    private String wfState;
    private String company;
    private String rfId;
    private String wfTaskState;
    private String createDate;
    private String wfTaskId;
    private String raNo;
    private String workFlowType;
    private String fileDate;
    private String userName;
    private String attachNumber;
    private String beginDateNew;
    private String rfIdNew;
    private String cbDeptId;
    private String wfId;
    private String isRegister;
    private String createId;
    private String projectId;
    private String projectName;


    public String getSecurityTime() {
        return securityTime;
    }

    public void setSecurityTime(String securityTime) {
        this.securityTime = securityTime;
    }

    public String getVerifierId() {
        return verifierId;
    }

    public void setVerifierId(String verifierId) {
        this.verifierId = verifierId;
    }

    public String getFileNumber() {
        return fileNumber;
    }

    public void setFileNumber(String fileNumber) {
        this.fileNumber = fileNumber;
    }

    public String getAttachTitle() {
        return attachTitle;
    }

    public void setAttachTitle(String attachTitle) {
        this.attachTitle = attachTitle;
    }

    public String getSkNo() {
        return skNo;
    }

    public void setSkNo(String skNo) {
        this.skNo = skNo;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getFfIds() {
        return ffIds;
    }

    public void setFfIds(String ffIds) {
        this.ffIds = ffIds;
    }

    public String getRfName() {
        return rfName;
    }

    public void setRfName(String rfName) {
        this.rfName = rfName;
    }

    public String getFfAttr() {
        return ffAttr;
    }

    public void setFfAttr(String ffAttr) {
        this.ffAttr = ffAttr;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getRaId() {
        return raId;
    }

    public void setRaId(String raId) {
        this.raId = raId;
    }

    public String getEndDateNew() {
        return endDateNew;
    }

    public void setEndDateNew(String endDateNew) {
        this.endDateNew = endDateNew;
    }

    public String getTitleNew() {
        return titleNew;
    }

    public void setTitleNew(String titleNew) {
        this.titleNew = titleNew;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getCbdept() {
        return cbdept;
    }

    public void setCbdept(String cbdept) {
        this.cbdept = cbdept;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getFfNames() {
        return ffNames;
    }

    public void setFfNames(String ffNames) {
        this.ffNames = ffNames;
    }

    public String getContType() {
        return contType;
    }

    public void setContType(String contType) {
        this.contType = contType;
    }

    public String getVerifyState() {
        return verifyState;
    }

    public void setVerifyState(String verifyState) {
        this.verifyState = verifyState;
    }

    public String getNameNew() {
        return nameNew;
    }

    public void setNameNew(String nameNew) {
        this.nameNew = nameNew;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRegId() {
        return regId;
    }

    public void setRegId(String regId) {
        this.regId = regId;
    }

    public String getCurYear() {
        return curYear;
    }

    public void setCurYear(String curYear) {
        this.curYear = curYear;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getBussinessId() {
        return bussinessId;
    }

    public void setBussinessId(String bussinessId) {
        this.bussinessId = bussinessId;
    }

    public String getCbMenId() {
        return cbMenId;
    }

    public void setCbMenId(String cbMenId) {
        this.cbMenId = cbMenId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getVerifierName() {
        return verifierName;
    }

    public void setVerifierName(String verifierName) {
        this.verifierName = verifierName;
    }

    public String getIsSendBack() {
        return isSendBack;
    }

    public void setIsSendBack(String isSendBack) {
        this.isSendBack = isSendBack;
    }

    public String getSecurity() {
        return security;
    }

    public void setSecurity(String security) {
        this.security = security;
    }

    public String getCbMenName() {
        return cbMenName;
    }

    public void setCbMenName(String cbMenName) {
        this.cbMenName = cbMenName;
    }

    public String getVerifyContent() {
        return verifyContent;
    }

    public void setVerifyContent(String verifyContent) {
        this.verifyContent = verifyContent;
    }

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public String getWfState() {
        return wfState;
    }

    public void setWfState(String wfState) {
        this.wfState = wfState;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getRfId() {
        return rfId;
    }

    public void setRfId(String rfId) {
        this.rfId = rfId;
    }

    public String getWfTaskState() {
        return wfTaskState;
    }

    public void setWfTaskState(String wfTaskState) {
        this.wfTaskState = wfTaskState;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getWfTaskId() {
        return wfTaskId;
    }

    public void setWfTaskId(String wfTaskId) {
        this.wfTaskId = wfTaskId;
    }

    public String getRaNo() {
        return raNo;
    }

    public void setRaNo(String raNo) {
        this.raNo = raNo;
    }

    public String getWorkFlowType() {
        return workFlowType;
    }

    public void setWorkFlowType(String workFlowType) {
        this.workFlowType = workFlowType;
    }

    public String getFileDate() {
        return fileDate;
    }

    public void setFileDate(String fileDate) {
        this.fileDate = fileDate;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAttachNumber() {
        return attachNumber;
    }

    public void setAttachNumber(String attachNumber) {
        this.attachNumber = attachNumber;
    }

    public String getBeginDateNew() {
        return beginDateNew;
    }

    public void setBeginDateNew(String beginDateNew) {
        this.beginDateNew = beginDateNew;
    }

    public String getRfIdNew() {
        return rfIdNew;
    }

    public void setRfIdNew(String rfIdNew) {
        this.rfIdNew = rfIdNew;
    }

    public String getCbDeptId() {
        return cbDeptId;
    }

    public void setCbDeptId(String cbDeptId) {
        this.cbDeptId = cbDeptId;
    }

    public String getWfId() {
        return wfId;
    }

    public void setWfId(String wfId) {
        this.wfId = wfId;
    }

    public String getIsRegister() {
        return isRegister;
    }

    public void setIsRegister(String isRegister) {
        this.isRegister = isRegister;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }


    public Map<String, Object> getParameters(){

        Map<String, Object> parameters = new HashMap();
        parameters.put("userName", createName);
        parameters.put("userId", createId);
        parameters.put("data", StringUtil.objectToJson(this));
        return parameters;
    }
}
