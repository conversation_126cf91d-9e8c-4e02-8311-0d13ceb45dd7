package com.kisoft.yuejianli.entity;
import com.kisoft.yuejianli.base.Base;

/**
 * 功能权限
 */
public class FunctionPermission extends Base {

    private boolean hasOCR;
    private boolean hasScan;
    private boolean hasFace;
    private boolean hasAudio;
    private boolean hasIM;
    private boolean hasOffice;
    private boolean hasWBS;
    private boolean hasCerNotice;
    private boolean hasHomePortal;
    private boolean hasProcessGroup;
    private boolean hasHomeToDo;
    private boolean hasProcessMenu;
    private boolean hasHomeStatement;
    private boolean hasUpdateFunction;
    private boolean hasMoreAttendancePointsFunction;
    private boolean hasHomeWeekReportApprovalFunction;

    public boolean isHasOCR() {
        return hasOCR;
    }

    public void setHasOCR(boolean hasOCR) {
        this.hasOCR = hasOCR;
    }

    public boolean isHasScan() {
        return hasScan;
    }

    public void setHasScan(boolean hasScan) {
        this.hasScan = hasScan;
    }

    public boolean isHasFace() {
        return hasFace;
    }

    public void setHasFace(boolean hasFace) {
        this.hasFace = hasFace;
    }

    public boolean isHasAudio() {
        return hasAudio;
    }

    public void setHasAudio(boolean hasAudio) {
        this.hasAudio = hasAudio;
    }

    public boolean isHasIM() {
        return hasIM;
    }

    public void setHasIM(boolean hasIM) {
        this.hasIM = hasIM;
    }

    public boolean isHasOffice() {
        return hasOffice;
    }

    public void setHasOffice(boolean hasOffice) {
        this.hasOffice = hasOffice;
    }

    public boolean isHasWBS() {
        return hasWBS;
    }

    public void setHasWBS(boolean hasWBS) {
        this.hasWBS = hasWBS;
    }

    public boolean isHasCerNotice() {
        return hasCerNotice;
    }

    public void setHasCerNotice(boolean hasCerNotice) {
        this.hasCerNotice = hasCerNotice;
    }

    public boolean isHasHomePortal() {
        return hasHomePortal;
    }

    public void setHasHomePortal(boolean hasHomePortal) {
        this.hasHomePortal = hasHomePortal;
    }

    public boolean isHasProcessGroup() {
        return hasProcessGroup;
    }

    public void setHasProcessGroup(boolean hasProcessGroup) {
        this.hasProcessGroup = hasProcessGroup;
    }

    public boolean isHasHomeToDo() {
        return hasHomeToDo;
    }

    public void setHasHomeToDo(boolean hasHomeToDo) {
        this.hasHomeToDo = hasHomeToDo;
    }

    public boolean isHasProcessMenu() {
        return hasProcessMenu;
    }

    public void setHasProcessMenu(boolean hasProcessMenu) {
        this.hasProcessMenu = hasProcessMenu;
    }

    public boolean isHasHomeStatement() {
        return hasHomeStatement;
    }

    public void setHasHomeStatement(boolean hasHomeStatement) {
        this.hasHomeStatement = hasHomeStatement;
    }

    public boolean isHasUpdateFunction() {
        return hasUpdateFunction;
    }

    public void setHasUpdateFunction(boolean hasUpdateFunction) {
        this.hasUpdateFunction = hasUpdateFunction;
    }

    public boolean isHasMoreAttendancePointsFunction() {
        return hasMoreAttendancePointsFunction;
    }

    public void setHasMoreAttendancePointsFunction(boolean hasMoreAttendancePointsFunction) {
        this.hasMoreAttendancePointsFunction = hasMoreAttendancePointsFunction;
    }

    public boolean isHasHomeWeekReportApprovalFunction() {
        return hasHomeWeekReportApprovalFunction;
    }

    public void setHasHomeWeekReportApprovalFunction(boolean hasHomeWeekReportApprovalFunction) {
        this.hasHomeWeekReportApprovalFunction = hasHomeWeekReportApprovalFunction;
    }
}