package com.kisoft.yuejianli.entity;

import com.kisoft.yuejianli.base.Base;

/**
 * Created by tudo<PERSON> on 2018/5/8.
 */

public class MessageTodo extends Base {


    public static final long MESSAGE_TODO_TYPE_SAFE_INSPECT = 1;                 // 安全检查
    public static final long MESSAGE_TODO_TYPE_QUALITY_INSPECT = 2;              // 质量检查
    public static final long MESSAGE_TODO_TYPE_SAFE_REFORM_INSPECT = 3;          // 安全整改验收
    public static final long MESSAGE_TODO_TYPE_QUALITY_REFORM_INSPECT = 4;       // 质量整改验收

    public static final String MESSAGE_TODO_NAME_SAFE_INSPECT = "现场安全检查";
    public static final String MESSAGE_TODO_NAME_QUALITY_INSPECT = "现场质量检查";
    public static final String MESSAGE_TODO_NAME_SAFE_REFORM_INSPECT = "安全整改验收";
    public static final String MESSAGE_TODO_NAME_SAFE_QUALITY_REFORM_INSPECT = "质量整改验收";


    private String mtdId;               // 待办id
    private String fromUserId;          // 下发者id
    private String subject;             // 主题
    private String content;             // 内容
    private long messageType;           // 消息类型
    private String dynamicRemindTime;   // 到期时间
    private long aheadTime;             // 提前提醒时间
    private long period;                // 周期
    private long remindWay;             // 提醒方式      默认为1
    private String creator;             // 创建者id
    private String createTimeStr;       // 创建时间
    private String url;                 // 内含待办事件的id

    private String acceptors;
    private String creatorName;         // 创建者 name
    private String fromUserName;        // 下发者  name
    private String acceptorId;          // 接受者 id 集合
    private String acceptorName;        // 接受者 name 集合



    public MessageTodo() {
    }

    public String getMtdId() {
        return mtdId;
    }

    public void setMtdId(String mtdId) {
        this.mtdId = mtdId;
    }

    public String getFromUserId() {
        return fromUserId;
    }

    public void setFromUserId(String fromUserId) {
        this.fromUserId = fromUserId;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public long getMessageType() {
        return messageType;
    }

    public void setMessageType(long messageType) {
        this.messageType = messageType;
    }

    public String getDynamicRemindTime() {
        return dynamicRemindTime;
    }

    public void setDynamicRemindTime(String dynamicRemindTime) {
        this.dynamicRemindTime = dynamicRemindTime;
    }

    public long getAheadTime() {
        return aheadTime;
    }

    public void setAheadTime(long aheadTime) {
        this.aheadTime = aheadTime;
    }

    public long getPeriod() {
        return period;
    }

    public void setPeriod(long period) {
        this.period = period;
    }

    public long getRemindWay() {
        return remindWay;
    }

    public void setRemindWay(long remindWay) {
        this.remindWay = remindWay;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getAcceptors() {
        return acceptors;
    }

    public void setAcceptors(String acceptors) {
        this.acceptors = acceptors;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getFromUserName() {
        return fromUserName;
    }

    public void setFromUserName(String fromUserName) {
        this.fromUserName = fromUserName;
    }

    public String getAcceptorId() {
        return acceptorId;
    }

    public void setAcceptorId(String acceptorId) {
        this.acceptorId = acceptorId;
    }

    public String getAcceptorName() {
        return acceptorName;
    }

    public void setAcceptorName(String acceptorName) {
        this.acceptorName = acceptorName;
    }
}
