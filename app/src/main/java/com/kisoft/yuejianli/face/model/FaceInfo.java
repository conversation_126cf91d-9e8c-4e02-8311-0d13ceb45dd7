package com.kisoft.yuejianli.face.model;

import java.util.List;

public class FaceInfo {


    private List<FacesBean> faces;

    public List<FacesBean> getFaces() {
        return faces;
    }

    public void setFaces(List<FacesBean> faces) {
        this.faces = faces;
    }

    public static class FacesBean {
        /**
         * bouding_box : {"height":"174","top_left_x":"22","top_left_y":"37","width":"174"}
         * attributes : {"age":"35","dress":{"glass":"none","hat":"none"},"gender":"male","headpose":{"pitch_angle":"-3.8939126","roll_angle":"-3.988193","yaw_angle":"-1.0292832"},"smile":"yes"}
         * landmark : {"eyebrow_contour":{"point":[{"x":"158.78517","y":"90.02418"},{"x":"150.86162","y":"82.432076"},{"x":"131.3591","y":"80.78191"},{"x":"121.39423","y":"87.62841"},{"x":"132.25839","y":"88.44499"},{"x":"149.63844","y":"89.3718"},{"x":"59.65225","y":"99.64757"},{"x":"65.96038","y":"88.92635"},{"x":"85.72037","y":"85.07116"},{"x":"97.09234","y":"89.34221"},{"x":"86.07851","y":"92.297516"},{"x":"67.51544","y":"95.58905"}]},"eyes_contour":{"point":[{"x":"69.22003","y":"105.52183"},{"x":"81.599174","y":"98.70017"},{"x":"94.22879","y":"103.18641"},{"x":"82.32345","y":"107.02255"},{"x":"150.97876","y":"99.29232"},{"x":"137.91818","y":"93.49849"},{"x":"125.78866","y":"100.8499"},{"x":"138.81555","y":"102.29028"},{"x":"75.16096","y":"100.01521"},{"x":"75.6541","y":"107.08471"},{"x":"89.18221","y":"106.11414"},{"x":"88.87559","y":"98.87617"},{"x":"130.7761","y":"95.49036"},{"x":"131.64914","y":"102.47154"},{"x":"145.64833","y":"101.04822"},{"x":"145.52222","y":"94.39164"}]},"face_contour":{"point":[{"x":"50.049454","y":"104.4499"},{"x":"52.502007","y":"123.68245"},{"x":"55.028637","y":"143.12273"},{"x":"59.972454","y":"163.16571"},{"x":"69.73235","y":"180.53296"},{"x":"81.76993","y":"195.74933"},{"x":"96.15602","y":"210.08699"},{"x":"115.32445","y":"215.14973"},{"x":"134.04883","y":"209.08778"},{"x":"147.96034","y":"194.2003"},{"x":"158.3112","y":"178.27126"},{"x":"165.62479","y":"159.71454"},{"x":"168.95554","y":"139.12373"},{"x":"169.59795","y":"119.938416"},{"x":"169.9221","y":"99.658844"}]},"mouth_contour":{"point":[{"x":"82.56091","y":"160.6078"},{"x":"91.546906","y":"154.97893"},{"x":"101.45366","y":"152.51962"},{"x":"111.45223","y":"153.75433"},{"x":"122.52581","y":"152.07089"},{"x":"133.3687","y":"152.31891"},{"x":"142.48122","y":"154.72552"},{"x":"136.62144","y":"167.81747"},{"x":"126.79148","y":"175.79927"},{"x":"113.959526","y":"179.32193"},{"x":"99.90567","y":"178.14986"},{"x":"88.95302","y":"172.11215"},{"x":"95.30528","y":"168.83522"},{"x":"112.094025","y":"170.73698"},{"x":"129.00156","y":"166.50134"},{"x":"128.46986","y":"156.3436"},{"x":"111.70824","y":"158.2964"},{"x":"96.44703","y":"158.49106"},{"x":"111.8587","y":"164.61029"}]},"nose_contour":{"point":[{"x":"100.12734","y":"103.06376"},{"x":"99.64418","y":"121.0322"},{"x":"92.39153","y":"134.87288"},{"x":"95.70406","y":"140.70796"},{"x":"113.04284","y":"143.04053"},{"x":"129.63658","y":"138.64207"},{"x":"131.6913","y":"131.54257"},{"x":"122.73644","y":"119.520386"},{"x":"119.106","y":"102.450005"},{"x":"102.1847","y":"138.2676"},{"x":"122.21693","y":"137.64107"},{"x":"112.65759","y":"131.8112"}]}}
         */

        private BoudingBoxBean bouding_box;
        private AttributesBean attributes;
        private LandmarkBean landmark;

        public BoudingBoxBean getBouding_box() {
            return bouding_box;
        }

        public void setBouding_box(BoudingBoxBean bouding_box) {
            this.bouding_box = bouding_box;
        }

        public AttributesBean getAttributes() {
            return attributes;
        }

        public void setAttributes(AttributesBean attributes) {
            this.attributes = attributes;
        }

        public LandmarkBean getLandmark() {
            return landmark;
        }

        public void setLandmark(LandmarkBean landmark) {
            this.landmark = landmark;
        }

        public static class BoudingBoxBean {
            /**
             * height : 174
             * top_left_x : 22
             * top_left_y : 37
             * width : 174
             */

            private String height;
            private String top_left_x;
            private String top_left_y;
            private String width;

            public String getHeight() {
                return height;
            }

            public void setHeight(String height) {
                this.height = height;
            }

            public String getTop_left_x() {
                return top_left_x;
            }

            public void setTop_left_x(String top_left_x) {
                this.top_left_x = top_left_x;
            }

            public String getTop_left_y() {
                return top_left_y;
            }

            public void setTop_left_y(String top_left_y) {
                this.top_left_y = top_left_y;
            }

            public String getWidth() {
                return width;
            }

            public void setWidth(String width) {
                this.width = width;
            }
        }

        public static class AttributesBean {
            /**
             * age : 35
             * dress : {"glass":"none","hat":"none"}
             * gender : male
             * headpose : {"pitch_angle":"-3.8939126","roll_angle":"-3.988193","yaw_angle":"-1.0292832"}
             * smile : yes
             */

            private String age;
            private DressBean dress;
            private String gender;
            private HeadposeBean headpose;
            private String smile;

            public String getAge() {
                return age;
            }

            public void setAge(String age) {
                this.age = age;
            }

            public DressBean getDress() {
                return dress;
            }

            public void setDress(DressBean dress) {
                this.dress = dress;
            }

            public String getGender() {
                return gender;
            }

            public void setGender(String gender) {
                this.gender = gender;
            }

            public HeadposeBean getHeadpose() {
                return headpose;
            }

            public void setHeadpose(HeadposeBean headpose) {
                this.headpose = headpose;
            }

            public String getSmile() {
                return smile;
            }

            public void setSmile(String smile) {
                this.smile = smile;
            }

            public static class DressBean {
                /**
                 * glass : none
                 * hat : none
                 */

                private String glass;
                private String hat;

                public String getGlass() {
                    return glass;
                }

                public void setGlass(String glass) {
                    this.glass = glass;
                }

                public String getHat() {
                    return hat;
                }

                public void setHat(String hat) {
                    this.hat = hat;
                }
            }

            public static class HeadposeBean {
                /**
                 * pitch_angle : -3.8939126
                 * roll_angle : -3.988193
                 * yaw_angle : -1.0292832
                 */

                private String pitch_angle;
                private String roll_angle;
                private String yaw_angle;

                public String getPitch_angle() {
                    return pitch_angle;
                }

                public void setPitch_angle(String pitch_angle) {
                    this.pitch_angle = pitch_angle;
                }

                public String getRoll_angle() {
                    return roll_angle;
                }

                public void setRoll_angle(String roll_angle) {
                    this.roll_angle = roll_angle;
                }

                public String getYaw_angle() {
                    return yaw_angle;
                }

                public void setYaw_angle(String yaw_angle) {
                    this.yaw_angle = yaw_angle;
                }
            }
        }

        public static class LandmarkBean {
            /**
             * eyebrow_contour : {"point":[{"x":"158.78517","y":"90.02418"},{"x":"150.86162","y":"82.432076"},{"x":"131.3591","y":"80.78191"},{"x":"121.39423","y":"87.62841"},{"x":"132.25839","y":"88.44499"},{"x":"149.63844","y":"89.3718"},{"x":"59.65225","y":"99.64757"},{"x":"65.96038","y":"88.92635"},{"x":"85.72037","y":"85.07116"},{"x":"97.09234","y":"89.34221"},{"x":"86.07851","y":"92.297516"},{"x":"67.51544","y":"95.58905"}]}
             * eyes_contour : {"point":[{"x":"69.22003","y":"105.52183"},{"x":"81.599174","y":"98.70017"},{"x":"94.22879","y":"103.18641"},{"x":"82.32345","y":"107.02255"},{"x":"150.97876","y":"99.29232"},{"x":"137.91818","y":"93.49849"},{"x":"125.78866","y":"100.8499"},{"x":"138.81555","y":"102.29028"},{"x":"75.16096","y":"100.01521"},{"x":"75.6541","y":"107.08471"},{"x":"89.18221","y":"106.11414"},{"x":"88.87559","y":"98.87617"},{"x":"130.7761","y":"95.49036"},{"x":"131.64914","y":"102.47154"},{"x":"145.64833","y":"101.04822"},{"x":"145.52222","y":"94.39164"}]}
             * face_contour : {"point":[{"x":"50.049454","y":"104.4499"},{"x":"52.502007","y":"123.68245"},{"x":"55.028637","y":"143.12273"},{"x":"59.972454","y":"163.16571"},{"x":"69.73235","y":"180.53296"},{"x":"81.76993","y":"195.74933"},{"x":"96.15602","y":"210.08699"},{"x":"115.32445","y":"215.14973"},{"x":"134.04883","y":"209.08778"},{"x":"147.96034","y":"194.2003"},{"x":"158.3112","y":"178.27126"},{"x":"165.62479","y":"159.71454"},{"x":"168.95554","y":"139.12373"},{"x":"169.59795","y":"119.938416"},{"x":"169.9221","y":"99.658844"}]}
             * mouth_contour : {"point":[{"x":"82.56091","y":"160.6078"},{"x":"91.546906","y":"154.97893"},{"x":"101.45366","y":"152.51962"},{"x":"111.45223","y":"153.75433"},{"x":"122.52581","y":"152.07089"},{"x":"133.3687","y":"152.31891"},{"x":"142.48122","y":"154.72552"},{"x":"136.62144","y":"167.81747"},{"x":"126.79148","y":"175.79927"},{"x":"113.959526","y":"179.32193"},{"x":"99.90567","y":"178.14986"},{"x":"88.95302","y":"172.11215"},{"x":"95.30528","y":"168.83522"},{"x":"112.094025","y":"170.73698"},{"x":"129.00156","y":"166.50134"},{"x":"128.46986","y":"156.3436"},{"x":"111.70824","y":"158.2964"},{"x":"96.44703","y":"158.49106"},{"x":"111.8587","y":"164.61029"}]}
             * nose_contour : {"point":[{"x":"100.12734","y":"103.06376"},{"x":"99.64418","y":"121.0322"},{"x":"92.39153","y":"134.87288"},{"x":"95.70406","y":"140.70796"},{"x":"113.04284","y":"143.04053"},{"x":"129.63658","y":"138.64207"},{"x":"131.6913","y":"131.54257"},{"x":"122.73644","y":"119.520386"},{"x":"119.106","y":"102.450005"},{"x":"102.1847","y":"138.2676"},{"x":"122.21693","y":"137.64107"},{"x":"112.65759","y":"131.8112"}]}
             */

            private EyebrowContourBean eyebrow_contour;
            private EyesContourBean eyes_contour;
            private FaceContourBean face_contour;
            private MouthContourBean mouth_contour;
            private NoseContourBean nose_contour;

            public EyebrowContourBean getEyebrow_contour() {
                return eyebrow_contour;
            }

            public void setEyebrow_contour(EyebrowContourBean eyebrow_contour) {
                this.eyebrow_contour = eyebrow_contour;
            }

            public EyesContourBean getEyes_contour() {
                return eyes_contour;
            }

            public void setEyes_contour(EyesContourBean eyes_contour) {
                this.eyes_contour = eyes_contour;
            }

            public FaceContourBean getFace_contour() {
                return face_contour;
            }

            public void setFace_contour(FaceContourBean face_contour) {
                this.face_contour = face_contour;
            }

            public MouthContourBean getMouth_contour() {
                return mouth_contour;
            }

            public void setMouth_contour(MouthContourBean mouth_contour) {
                this.mouth_contour = mouth_contour;
            }

            public NoseContourBean getNose_contour() {
                return nose_contour;
            }

            public void setNose_contour(NoseContourBean nose_contour) {
                this.nose_contour = nose_contour;
            }

            public static class EyebrowContourBean {
                private List<PointBean> point;

                public List<PointBean> getPoint() {
                    return point;
                }

                public void setPoint(List<PointBean> point) {
                    this.point = point;
                }

                public static class PointBean {
                    /**
                     * x : 158.78517
                     * y : 90.02418
                     */

                    private String x;
                    private String y;

                    public String getX() {
                        return x;
                    }

                    public void setX(String x) {
                        this.x = x;
                    }

                    public String getY() {
                        return y;
                    }

                    public void setY(String y) {
                        this.y = y;
                    }
                }
            }

            public static class EyesContourBean {
                private List<PointBeanX> point;

                public List<PointBeanX> getPoint() {
                    return point;
                }

                public void setPoint(List<PointBeanX> point) {
                    this.point = point;
                }

                public static class PointBeanX {
                    /**
                     * x : 69.22003
                     * y : 105.52183
                     */

                    private String x;
                    private String y;

                    public String getX() {
                        return x;
                    }

                    public void setX(String x) {
                        this.x = x;
                    }

                    public String getY() {
                        return y;
                    }

                    public void setY(String y) {
                        this.y = y;
                    }
                }
            }

            public static class FaceContourBean {
                private List<PointBeanXX> point;

                public List<PointBeanXX> getPoint() {
                    return point;
                }

                public void setPoint(List<PointBeanXX> point) {
                    this.point = point;
                }

                public static class PointBeanXX {
                    /**
                     * x : 50.049454
                     * y : 104.4499
                     */

                    private String x;
                    private String y;

                    public String getX() {
                        return x;
                    }

                    public void setX(String x) {
                        this.x = x;
                    }

                    public String getY() {
                        return y;
                    }

                    public void setY(String y) {
                        this.y = y;
                    }
                }
            }

            public static class MouthContourBean {
                private List<PointBeanXXX> point;

                public List<PointBeanXXX> getPoint() {
                    return point;
                }

                public void setPoint(List<PointBeanXXX> point) {
                    this.point = point;
                }

                public static class PointBeanXXX {
                    /**
                     * x : 82.56091
                     * y : 160.6078
                     */

                    private String x;
                    private String y;

                    public String getX() {
                        return x;
                    }

                    public void setX(String x) {
                        this.x = x;
                    }

                    public String getY() {
                        return y;
                    }

                    public void setY(String y) {
                        this.y = y;
                    }
                }
            }

            public static class NoseContourBean {
                private List<PointBeanXXXX> point;

                public List<PointBeanXXXX> getPoint() {
                    return point;
                }

                public void setPoint(List<PointBeanXXXX> point) {
                    this.point = point;
                }

                public static class PointBeanXXXX {
                    /**
                     * x : 100.12734
                     * y : 103.06376
                     */

                    private String x;
                    private String y;

                    public String getX() {
                        return x;
                    }

                    public void setX(String x) {
                        this.x = x;
                    }

                    public String getY() {
                        return y;
                    }

                    public void setY(String y) {
                        this.y = y;
                    }
                }
            }
        }
    }
}
