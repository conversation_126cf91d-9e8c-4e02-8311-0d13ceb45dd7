/**
 * Copyright (C) 2017 Baidu Inc. All rights reserved.
 */
package com.kisoft.yuejianli.face.decode;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.text.TextUtils;

import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.api.HuaweiService;
import com.kisoft.yuejianli.face.FaceStatusEnum;
import com.kisoft.yuejianli.face.IDetect;
import com.kisoft.yuejianli.face.model.FaceDetect;
import com.kisoft.yuejianli.face.model.FaceExtInfo;
import com.kisoft.yuejianli.face.model.FaceInfo;
import com.kisoft.yuejianli.face.model.FaceModel;
import com.kisoft.yuejianli.face.utils.Base64Utils;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.PhotoUtil;

import org.xutils.common.util.LogUtil;

import java.io.ByteArrayOutputStream;
import java.io.File;

import top.zibin.luban.CompressionPredicate;
import top.zibin.luban.Luban;
import top.zibin.luban.OnCompressListener;

/**
 * 人脸跟踪,活体检测功能类
 */
public class FaceModule implements IDetect{

    private static final String TAG = FaceModule.class.getSimpleName();
    // 图像宽度，高度
    private int mImageWidth = 0;
    private int mImageHeight = 0;
    // 检测图像
    private String mSaveFaceArgbData = null;
    // 检测状态
    private int mErrCode = 0;
    // 活体检测
    private boolean mLivenessFlag = false;

    private FaceModel mFaceModel=new FaceModel();

    private int mDegree = 90;
    private boolean doDetect =true;
    private long startTime;
    private IModel iModel;

    public FaceModule() {

    }

    public void detect(byte[] imageData, int imageWidth, int imageHeight,IModel iModel) {
        this.iModel=iModel;
        if (imageData != null&&imageData.length>0 && imageWidth > 0 && imageHeight > 0) {
            if(doDetect&&System.currentTimeMillis()-startTime>500){
                LogUtil.e("FaceModule imageData "+imageData.length);
                doDetect =false;
                startTime=System.currentTimeMillis();
                compressDetect(imageData, imageWidth, imageHeight);

//                Api.getFaceUrlApiServer().doFaceDetectByBase64(parameters).enqueue(new Callback<NetworkResponse<FaceExtInfo>>() {
//                    @Override
//                    public void onResponse(Call<NetworkResponse<FaceExtInfo>> call, Response<NetworkResponse<FaceExtInfo>> response) {
//                        FaceModel model = new FaceModel();
////            }
//
////                        FaceInfo[] faceInfos = faceTrackerDecode(imageData, imageWidth, imageHeight);
//                        model.setArgbImage(mArgbData);
//                        model.setFaceInfos(getExtInfo(faceInfos));
//                        model.setFaceModuleState(getModuleState(mErrCode));
//                        model.setFrameTime(System.currentTimeMillis());
//                    }
//
//                    @Override
//                    public void onFailure(Call<NetworkResponse<FaceExtInfo>> call, Throwable t) {
//
//                    }
//                });
            }
        }
    }

    //压缩图片 并检测
    private void compressDetect(byte[] imageData, int imageWidth, int imageHeight) {
        YuvImage yuvimage=new YuvImage(imageData, ImageFormat.NV21, imageWidth,imageHeight, null);//20、20分别是图的宽度与高度
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        yuvimage.compressToJpeg(new Rect(0, 0,imageWidth, imageHeight), 80, baos);//80--JPG图片的质量[0-100],100最高
        byte[] jdata = baos.toByteArray();
        Bitmap bmp = PhotoUtil.rotateBitmap(BitmapFactory.decodeByteArray(jdata, 0, jdata.length),-90);
        LogUtil.e("FaceModule bitmap "+bmp.getByteCount());
        File file = new File(YueApplacation.getContext().getCacheDir() + "temp.jpg");
        PhotoUtil.saveBitmap(bmp, file);

        FileUtil.deleteAllFile(YueApplacation.getContext().getExternalCacheDir().getAbsolutePath());
        Luban.with(YueApplacation.getContext()).load(file)
                .ignoreBy(200)
                .setTargetDir(YueApplacation.getContext().getExternalCacheDir().getAbsolutePath())
                .filter(new CompressionPredicate() {
                    @Override
                    public boolean apply(String path) {
                        return !(TextUtils.isEmpty(path) || path.toLowerCase().endsWith(".gif"));
                    }
                })
                .setCompressListener(new OnCompressListener() {
                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onSuccess(File file) {
                        LogUtil.e("FaceModule detect "+file.length()+" "+file.getAbsolutePath());
                        String encodeImage = PhotoUtil.imageToBase64(file.getAbsolutePath());
//                                String encodeImage = BitmapUtils.bitmapToJpegBase64(PhotoUtil.loadBitmap(file.getPath()), 100);
//                                writeTxtToFile(encodeImage,YueApplacation.getContext().getExternalCacheDir().getPath(),"test");
                        startDetect(encodeImage);
                    }

                    @Override
                    public void onError(Throwable e) {
                        String encodeImage = Base64Utils.encodeToString(imageData, Base64Utils.NO_WRAP);
//                                if (encodeImage.length() > 0) {
//                                    encodeImage = encodeImage.replace("\\/", "/");
//                                }
                        startDetect(encodeImage);
                    }
                }).launch();
    }

    //开始检测
    private void startDetect(String encodeImage) {
        HuaweiService.getInstance().faceDetect(encodeImage, new HuaweiService.FaceDetectListener() {
            @Override
            public void onSuccess(FaceDetect faceDetect) {
                if(faceDetect!=null){
                    mFaceModel.setFaceDetect(faceDetect);
                    mFaceModel.setFaceStatus(getModuleState(faceDetect));
                    if(faceDetect.getResult()!=null){
                        if(faceDetect.getResult().isAlive()){
                            LogUtil.e("FaceModule onSuccess");
                            mSaveFaceArgbData=faceDetect.getResult().getPicture();
                        }else {
                            doDetect =true;
                        }
                    }else {
                        doDetect =true;
                    }
                    if(iModel!=null){
                        iModel.getModel(mFaceModel);
                    }
                }else {
                    doDetect =true;
                }
            }

            @Override
            public void onFailure(Throwable throwable) {
                LogUtil.e("FaceModule onFailure");
                doDetect =true;
            }
        });
    }

    private FaceStatusEnum getModuleState(FaceDetect faceDetect) {
        FaceStatusEnum status = FaceStatusEnum.Detect_NoFace;
        if(faceDetect!=null){
            if(faceDetect.getResult()==null){
                status = FaceStatusEnum.Detect_NoFace;
            }else if(faceDetect.getResult().isAlive()){
                status = FaceStatusEnum.OK;
            }else {
                status=FaceStatusEnum.Detect_ImageBlured;
            }
        }
        return status;
    }

    @Override
    public String getBestFaceImage() {
        return mSaveFaceArgbData;
    }

    @Override
    public void reset() {

    }

    public void setPreviewDegree(int degree) {
        if (degree >= 0 && degree <= 360) {
            mDegree = degree;
        }
    }

}
