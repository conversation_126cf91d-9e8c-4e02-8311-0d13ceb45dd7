/**
 * Copyright (C) 2017 Baidu Inc. All rights reserved.
 */
package com.kisoft.yuejianli.face.strategy;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.text.TextUtils;
import android.util.Log;


import com.kisoft.yuejianli.face.FaceConfig;
import com.kisoft.yuejianli.face.FaceEnvironment;
import com.kisoft.yuejianli.face.FaceStatusEnum;
import com.kisoft.yuejianli.face.IDetect;
import com.kisoft.yuejianli.face.IDetectStrategy;
import com.kisoft.yuejianli.face.IDetectStrategyCallback;
import com.kisoft.yuejianli.face.decode.FaceModule;
import com.kisoft.yuejianli.face.model.FaceDetect;
import com.kisoft.yuejianli.face.model.FaceExtInfo;
import com.kisoft.yuejianli.face.model.FaceModel;
import com.kisoft.yuejianli.face.utils.BitmapUtils;
import com.kisoft.yuejianli.face.utils.SoundPoolHelper;

import org.xutils.common.util.LogUtil;

import java.util.HashMap;

import static com.kisoft.yuejianli.face.FaceEnvironment.TIME_DETECT_MODULE;
import static com.kisoft.yuejianli.face.FaceEnvironment.TIME_MODULE;

/**
 * 人脸跟踪策略控制类
 */
@Deprecated
public final class FaceDetectStrategyModule extends FaceStrategyModule implements IDetectStrategy {

    private static final String TAG = FaceDetectStrategyModule.class.getSimpleName();
    private final FaceModule mFaceModule;
    private Context mContext;
    private Rect mPreviewRect;
    private Rect mDetectRect;
    private DetectStrategy mDetectStrategy;
    private SoundPoolHelper mSoundPlayHelper = null;
    private boolean mIsFirstTipsed = false;
    private volatile boolean mIsEnableSound = true;
    protected HashMap<String, String> mBase64ImageMap = new HashMap<String, String>();
    protected HashMap<FaceStatusEnum, String> mTipsMap = new HashMap<FaceStatusEnum, String>();
    private IDetectStrategyCallback mIDetectStrategyCallback;

    public FaceDetectStrategyModule(Context context) {

        mContext = context;
        mDetectStrategy = new DetectStrategy();
        mSoundPlayHelper = new SoundPoolHelper(context);
        mFaceModule=new FaceModule();
        mLaunchTime = System.currentTimeMillis();
    }

    public void setConfigValue(FaceConfig config) {
        if (config != null && mDetectStrategy != null) {
            mDetectStrategy.setHeadAngle(
                    config.getHeadPitchValue(),
                    config.getHeadYawValue(),
                    config.getHeadRollValue());
        }
    }

    @Override
    public void setDetectStrategyConfig(Rect previewRect, Rect detectRect, IDetectStrategyCallback callback) {
        mPreviewRect = previewRect;
        mDetectRect = detectRect;
        mIDetectStrategyCallback = callback;
    }

    @Override
    public void setDetectStrategySoundEnable(boolean flag) {
        mIsEnableSound = flag;
    }

    @Override
    public void setPreviewDegree(int degree) {
    }

    @Override
    public String getBestFaceImage() {
        String encodeImage = "";
        if (mFaceModule != null
                && mFaceModule.getBestFaceImage() != null
                && mFaceModule.getBestFaceImage().length() > 0) {
            encodeImage=mFaceModule.getBestFaceImage();
        }
        return encodeImage;
    }

    @Override
    public void detectStrategy(byte[] imageData) {
        if (!mIsFirstTipsed) {
            mIsFirstTipsed = true;
            processUITips(FaceStatusEnum.Detect_NoFace);
        }
        if (mIsProcessing) {
            process(imageData);
        }
    }

    @Override
    protected void processStrategy(byte[] imageData) {
        mFaceModule.detect(imageData, mPreviewRect.height(), mPreviewRect.width(), new IDetect.IModel() {
            @Override
            public void getModel(FaceModel model) {
                processUIStrategy(new UIDetectResultRunnable(model));
            }
        });
    }

    private void processUIResult(FaceModel model) {

        LogUtil.e("processUIResult");
        if (!mIsProcessing) {
            return;
        }

        if (System.currentTimeMillis() - mLaunchTime > TIME_MODULE && TIME_MODULE != 0) {
            LogUtil.e("processUIResult 活体验证超时");
            // 活体验证超时
            mIsProcessing = false;
            processUICallback(FaceStatusEnum.Error_Timeout);
            return;
        }

//        if (System.currentTimeMillis() - mLaunchTime < 1600) {
//            LogUtil.e("processUIResult mLaunchTime");
//            return;
//        }

        FaceDetect faceInfo;
        if (model != null
                && model.getFaceDetect() != null) {
            faceInfo = model.getFaceDetect();

        } else {
            faceInfo = null;
            if (mDetectStrategy != null) {
                mDetectStrategy.reset();
            }
        }
        FaceStatusEnum detectStatus = FaceStatusEnum.Detect_NoFace;
        if(faceInfo!=null){
            LogUtil.e("processUIResult faceInfo");
            detectStatus=model.getFaceStatus();
            if (detectStatus == FaceStatusEnum.OK) {
                processUICompletion(faceInfo, FaceStatusEnum.OK);
            } else {
                if (detectStatus == FaceStatusEnum.Detect_NoFace) {
                    mDetectStrategy.reset();
                }
                if (mDetectStrategy.isTimeout()) {
                    mIsProcessing = false;
                    processUICallback(FaceStatusEnum.Error_DetectTimeout);
                    return;
                }
                processUITips(detectStatus);
            }
        }else {
            if (detectStatus == FaceStatusEnum.Detect_NoFace) {
                mDetectStrategy.reset();
                if (mNoFaceTime == 0) {
                    mNoFaceTime = System.currentTimeMillis();
                } else if (System.currentTimeMillis() - mNoFaceTime > TIME_DETECT_MODULE) {
                    mIsProcessing = false;
                    processUICallback(FaceStatusEnum.Error_DetectTimeout);
                    return;
                }
            } else {
                mNoFaceTime = 0;
            }
            if (mDetectStrategy.isTimeout()) {
                mIsProcessing = false;
                processUICallback(FaceStatusEnum.Error_DetectTimeout);
                return;
            }
            processUITips(detectStatus);
        }
    }

    private boolean processUITips(FaceStatusEnum status) {
        boolean flag = false;
        if (status != null) {
            mSoundPlayHelper.setEnableSound(mIsEnableSound);
            flag = mSoundPlayHelper.playSound(status);
            if (flag) {
                processUICallback(status);
            }
        }
        return flag;
    }

    private void processUICallback(FaceStatusEnum status) {

        if (status == FaceStatusEnum.Error_DetectTimeout
                || status == FaceStatusEnum.Error_LivenessTimeout
                || status == FaceStatusEnum.Error_Timeout) {

        }

        if (mIDetectStrategyCallback != null) {
            mIDetectStrategyCallback.onDetectCompletion(status, getStatusTextResId(status), null);
        }
    }

    private void processUICompletion(FaceDetect faceInfo, FaceStatusEnum status) {
        mIsProcessing = false;
        mIsCompletion = true;

        if (mIDetectStrategyCallback != null) {
            String imageEncode = faceInfo.getResult().getPicture();
            mBase64ImageMap.put(IDetectStrategyCallback.IMAGE_KEY_BEST_IMAGE, imageEncode);
            processUIStrategyDelay(new Runnable() {
                @Override
                public void run() {
                    processUITips(FaceStatusEnum.Liveness_Completion);
                }
            }, 500);
            mIDetectStrategyCallback.onDetectCompletion(status, getStatusTextResId(status), mBase64ImageMap);
        }
    }

    private String getStatusTextResId(FaceStatusEnum status) {
        String tips = "";
        if (mTipsMap.containsKey(status)) {
            tips = mTipsMap.get(status);
        } else {
            int resId = FaceEnvironment.getTipsId(status);
            if (resId > 0) {
                tips = mContext.getResources().getString(resId);
                mTipsMap.put(status, tips);
            }
        }
        return tips;
    }

    private class UIDetectResultRunnable implements Runnable {
        private final FaceModel mModel;

        public UIDetectResultRunnable(FaceModel model) {
            mModel = model;
        }

        @Override
        public void run() {
            processUIResult(mModel);
        }
    }
}
