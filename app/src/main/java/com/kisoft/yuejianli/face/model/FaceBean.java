package com.kisoft.yuejianli.face.model;

/**
 * 人脸库人脸
 */
public class FaceBean {
    /**
     * bounding_box : {"height":170,"top_left_x":20,"top_left_y":37,"width":170}
     * external_fields : {"id":"home","timestamp":12}
     * external_image_id : 123
     * face_id : 6KLB1Ktu
     * similarity : 0.996146
     */

    private BoundingBoxBean bounding_box;
    private ExternalFieldsBean external_fields;
    private String external_image_id;
    private String face_id;
    private double similarity;

    public BoundingBoxBean getBounding_box() {
        return bounding_box;
    }

    public void setBounding_box(BoundingBoxBean bounding_box) {
        this.bounding_box = bounding_box;
    }

    public ExternalFieldsBean getExternal_fields() {
        return external_fields;
    }

    public void setExternal_fields(ExternalFieldsBean external_fields) {
        this.external_fields = external_fields;
    }

    public String getExternal_image_id() {
        return external_image_id;
    }

    public void setExternal_image_id(String external_image_id) {
        this.external_image_id = external_image_id;
    }

    public String getFace_id() {
        return face_id;
    }

    public void setFace_id(String face_id) {
        this.face_id = face_id;
    }

    public double getSimilarity() {
        return similarity;
    }

    public void setSimilarity(double similarity) {
        this.similarity = similarity;
    }

    public static class BoundingBoxBean {
        /**
         * height : 170
         * top_left_x : 20
         * top_left_y : 37
         * width : 170
         */

        private int height;
        private int top_left_x;
        private int top_left_y;
        private int width;

        public int getHeight() {
            return height;
        }

        public void setHeight(int height) {
            this.height = height;
        }

        public int getTop_left_x() {
            return top_left_x;
        }

        public void setTop_left_x(int top_left_x) {
            this.top_left_x = top_left_x;
        }

        public int getTop_left_y() {
            return top_left_y;
        }

        public void setTop_left_y(int top_left_y) {
            this.top_left_y = top_left_y;
        }

        public int getWidth() {
            return width;
        }

        public void setWidth(int width) {
            this.width = width;
        }
    }

    public static class ExternalFieldsBean {
        /**
         * id : home
         * timestamp : 12
         */

        private String id;
        private int timestamp;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public int getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(int timestamp) {
            this.timestamp = timestamp;
        }
    }
}
