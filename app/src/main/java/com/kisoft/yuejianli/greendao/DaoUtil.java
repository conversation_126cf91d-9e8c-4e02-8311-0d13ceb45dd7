package com.kisoft.yuejianli.greendao;

import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.entity.UnSubmitInfo;

import java.util.List;

/**
 * Created by tudo<PERSON> on 2018/6/23.
 */

public class DaoUtil {

    public static void addUnSubmitInfo(UnSubmitInfo info){
        YueApplacation.getInstance().getDaoSession().getUnSubmitInfoDao().insert(info);
    }

    public static void deleteSubmitInfo(UnSubmitInfo info){
        YueApplacation.getInstance().getDaoSession().getUnSubmitInfoDao().delete(info);
    }

    public static List<UnSubmitInfo> getUnSubmitInfo(){
       return YueApplacation.getInstance().getDaoSession().getUnSubmitInfoDao().queryBuilder().list();
    }

    public static UnSubmitInfo getWorkById(Long workId){
        return YueApplacation.getInstance().getDaoSession().getUnSubmitInfoDao().load(workId);
    }

}
