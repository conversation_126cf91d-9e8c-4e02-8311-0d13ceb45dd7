package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.widget.TextView;

import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.VisitPlan;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/16.
 */

public class EndServiceVisitPlanDetailActivity extends BaseActivity {


    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.tv_project_name)
    TextView tvProjectName;

    @BindView(R.id.tv_visit_unit)
    TextView tvVisitunit;

    @BindView(R.id.tv_visit_type)
    TextView tvVisitType;

    @BindView(R.id.tv_visiter)
    TextView tvVisiter;

    @BindView(R.id.tv_visit_purpose)
    TextView tvVisitPurpose;

    @BindView(R.id.tv_formulater)
    TextView tvFormulater;

    @BindView(R.id.tv_formulate_time)
    TextView tvFormulateTime;

    @BindView(R.id.tv_plan_time)
    TextView tvPlanTime;

    @BindView(R.id.tv_content)
    TextView tvContent;


    private VisitPlan plan;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initData();
        initView();
    }

    private void initData() {
        Intent intent = getIntent();
        if (intent != null) {
            plan = (VisitPlan) intent.getSerializableExtra(Constant.INTENT_KEY_PROJECT_VISIT_PLAN);
        }
    }


    private void initView() {
        tvTitle.setText("回访计划");
        if (plan != null) {
            tvProjectName.setText(plan.getProjectName());
            tvVisitunit.setText(plan.getVisitUnit());
            tvVisitType.setText(plan.getVisitType());
            tvVisiter.setText(plan.getVisiteRname());
            tvVisitPurpose.setText(plan.getVisitPurpose());
            tvFormulater.setText(plan.getFormulaterName());
            tvFormulateTime.setText(plan.getFormulateTime());
            tvPlanTime.setText(plan.getVisitTime());
            tvContent.setText(plan.getVisitContent());
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_end_service_visit_plan_detail;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }
}
