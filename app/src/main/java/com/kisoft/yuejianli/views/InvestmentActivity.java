package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.adpter.ConstructPayAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.InvestmentContract;
import com.kisoft.yuejianli.entity.ConstructPay;
import com.kisoft.yuejianli.entity.ProConstructInvestment;
import com.kisoft.yuejianli.entity.ProjectConstructPayCertificate;
import com.kisoft.yuejianli.entity.ProjectConstructSafePayCertificate;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.InvestmentModel;
import com.kisoft.yuejianli.presenter.InvestmentPresenter;
import com.kisoft.yuejianli.ui.RingView;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/10.
 */
public class InvestmentActivity extends BaseActivity<InvestmentModel, InvestmentPresenter> implements InvestmentContract.InvestmentViewContract {

    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    // 头部图片
    View headView;
    RingView mProgress;
    private List<Integer> valueList = new ArrayList<>();
    TextView tvAllMoney;
    private List<ConstructPay> pays = new ArrayList<>();
    private ConstructPayAdapter mAdapter;

    private ProConstructInvestment investment;
    private BigDecimal realPayMoney = new BigDecimal("0");
    private List<ProjectConstructPayCertificate> certificates = new ArrayList<>();
    private List<ProjectConstructSafePayCertificate> safePayCertificates = new ArrayList<>();

    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private InvestmentModel model;
    private InvestmentPresenter presenter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new InvestmentModel(this);
        presenter = new InvestmentPresenter(this, model);
        initMVP(model, presenter);
        initView();
        initData();
    }


    private void initView(){
        tvTitle.setText("投资控制");
        headView = getLayoutInflater().inflate(R.layout.header_invsetment, null);
        mProgress = headView.findViewById(R.id.rv_progress);
        tvAllMoney = headView.findViewById(R.id.tv_all_money);
        if(valueList .size() == 0){
            setPoint();
        }
        mProgress.setValueList(valueList);
        mProgress.setPointer(false);
        if(rvContent.getLayoutManager() == null){
            LinearLayoutManager ma = new LinearLayoutManager(this);
            ma.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(ma);
        }
        mAdapter = new ConstructPayAdapter(R.layout.item_construct_pay, pays);
        mAdapter.setHeaderView(headView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                // 查看支付详情
                if(ConstructPay.PAY_TYPE_CONTRUCTION == pays.get(position).getPayType()){
                    goCertificateDetail((ProjectConstructPayCertificate) pays.get(position).getPayInfo());
                }else if(ConstructPay.PAY_TYPE_SAFE == pays.get(position).getPayType()){
                    goSafeCertificateDetail((ProjectConstructSafePayCertificate) pays.get(position).getPayInfo());
                }
            }
        });
        rvContent.setAdapter(mAdapter);

    }

    private void initData(){
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        if(projectInfo != null){
            mPresenter.getProjectInvsetmentInfo(userInfo.getId(), projectInfo.getProjectId());
        }
    }

    private void setPoint(){
        valueList.add(0);
        valueList.add(10);
        valueList.add(20);
        valueList.add(30);
        valueList.add(40);
        valueList.add(50);
        valueList.add(60);
        valueList.add(70);
        valueList.add(80);
        valueList.add(90);
        valueList.add(100);
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_invsetment;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    @Override
    public void showInvsetmentInfo(ProConstructInvestment investment) {
        this.investment = investment;
        mPresenter.getPayCertificate(userInfo.getId(), projectInfo.getProjectId());
    }

    @Override
    public void showPayCertificate(List<ProjectConstructPayCertificate> certificates) {
        this.certificates.clear();
        this.certificates.addAll(certificates);
        mPresenter.getSafePayCertificate(userInfo.getId(), projectInfo.getProjectId());
        // 计算价格
    }

    @Override
    public void showSafePayCertificate(List<ProjectConstructSafePayCertificate> certificates) {
        this.safePayCertificates.clear();
        this.safePayCertificates.addAll(certificates);
        calculateInvestment();
    }

    /**
     * 计算项目支付情况
     */
    private void calculateInvestment(){
        pays.clear();
        for(ProjectConstructPayCertificate certificate: certificates){
            pays.add(new ConstructPay(ConstructPay.PAY_TYPE_CONTRUCTION, ConstructPay.PAY_TYPE_CONTRUCTION_NAME, certificate));
            realPayMoney = realPayMoney.add(certificate.getPayableMoney());
        }

        for (ProjectConstructSafePayCertificate safePayCertificate: safePayCertificates){
            pays.add(new ConstructPay(ConstructPay.PAY_TYPE_SAFE, ConstructPay.PAY_TYPE_SAFE_NAME, safePayCertificate));
            realPayMoney = realPayMoney.add(safePayCertificate.getMoneyLowercase());
        }
        tvAllMoney.setText(realPayMoney+" 元");
        initProgress();

    }

    private void initProgress(){
        float progress = 0;
        if(investment != null  && investment.getMoneyLowercase()!= null){
            float all = investment.getMoneyLowercase().floatValue();
            float pay = realPayMoney.floatValue();
            progress = pay/all;

            Log.i("000" ,  "-------------"+progress);
        }

        mProgress.setValue((int) (progress*100), new RingView.OnProgerssChange() {
            @Override
            public void OnProgerssChange(float interpolatedTime) {
                mAdapter.notifyDataSetChanged();
            }
        },999 );
    }


    /**
     * 工程支付款详情
     */
    private void goCertificateDetail(ProjectConstructPayCertificate certificate){
        Intent intent = new Intent();
        intent.setClass(this, ContructPayCertificateDetailActivity.class);
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constant.INTENT_KEY_PAY_CERTIFICATE , certificate);
        intent.putExtras(bundle);
        startActivity(intent);
    }


    /**
     * 安全措施详情
     */
    private void goSafeCertificateDetail(ProjectConstructSafePayCertificate safePayCertificate){
        Intent intent = new Intent();
        intent.setClass(this,ContructSafePayCertificateDetailActivity.class );
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constant.INTENT_KEY_SAFE_PAY_CERTIFICATE, safePayCertificate);
        intent.putExtras(bundle);
        startActivity(intent);
    }

}
