package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.haibin.calendarview.Calendar;
import com.haibin.calendarview.CalendarLayout;
import com.haibin.calendarview.CalendarView;
import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.adpter.TodoInfoAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.contract.TodoContract;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.TaskDTO;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.TodoModel;
import com.kisoft.yuejianli.presenter.TodoPresenter;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/4/20.
 */

public class TodoActivity extends BaseActivity<TodoModel, TodoPresenter> implements TodoContract.TodoViewContract,
        CalendarView.OnDateSelectedListener, CalendarView.OnYearChangeListener, BaseRefreshListener {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_month_day)
    TextView tvMonthDay;
    @BindView(R.id.tv_year)
    TextView tvYear;
    @BindView(R.id.tv_lunar)
    TextView tvLunar;
    @BindView(R.id.tv_current_day)
    TextView tvCurrentDay;

    @BindView(R.id.calendarLayout)
    CalendarLayout      calendarLayout;
    @BindView(R.id.calendarView)
    CalendarView        mCalendarView;
    @BindView(R.id.rv_todo_content)
    RecyclerView        rvContent;
    @BindView(R.id.ptrl_todo_list)
    PullToRefreshLayout ptrlTodoList;

    private int count    = 0;
    private int page     = 1;
    private int pageSize = 15;

    private int         year;
    private String      time;
    private UserInfo    userInfo;
    private ProjectInfo projectInfo;

    //private List<MessageTodo> todoInfos = new ArrayList();
    private List<TaskDTO> todoInfos   = new ArrayList();
    private List<TaskDTO> selectInfos = new ArrayList<>();
    private List<String>  ids         = new ArrayList<>();
    private TodoInfoAdapter mAdapter;

    private TodoModel     model;
    private TodoPresenter presenter;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        model = new TodoModel(this);
        presenter = new TodoPresenter(this, model);
        initMVP(model, presenter);
        initView();
        initData();
    }

    private void initView() {

        tvTitle.setText("待办事件");
        mCalendarView.setOnDateSelectedListener(this);
        mCalendarView.setOnYearChangeListener(this);
        tvYear.setText(String.valueOf(mCalendarView.getCurYear()));
        year = mCalendarView.getCurYear();
        tvMonthDay.setText(mCalendarView.getCurMonth() + "月" + mCalendarView.getCurDay() + "日");
        tvLunar.setText("今日");
        tvCurrentDay.setText(String.valueOf(mCalendarView.getCurDay()).trim());
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(this);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(layoutManager);
        }
        mAdapter = new TodoInfoAdapter(selectInfos);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                Intent intent = new Intent(TodoActivity.this, TaskActivity.class);
                intent.putExtra("data", selectInfos.get(position));
                intent.putExtra("type", 1);
                startActivity(intent);
            }
        });
        rvContent.setAdapter(mAdapter);

        ptrlTodoList.autoRefresh();
        ptrlTodoList.setRefreshListener(this);

    }

    private void initData() {
        time = DateUtil.getTodayDate();
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        /*原来

        Intent intent = getIntent();
        if(intent != null){
            ArrayList<String> toIds = intent.getStringArrayListExtra(Constant.INTENT_KEY_IDS);
            if(toIds!= null && toIds.size() >0){
                for(String s:toIds){
                    ids.add(s);
                }
            }
            if(ids.size() > 0){
                mPresenter.getTodoMessage(userInfo.getId(), ids);
            }
        }*/

    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_todo;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @OnClick(R.id.fl_current)
    public void onClickCurrent() {
        mCalendarView.scrollToCurrent();
    }

    @Override
    public void onDateSelected(Calendar calendar, boolean isClick) {
        tvMonthDay.setText(calendar.getMonth() + "月" + calendar.getDay() + "日");
        tvYear.setText(String.valueOf(calendar.getYear()));
        tvLunar.setText(calendar.getLunar());
        year = calendar.getYear();
        time = DateUtil.getToday(calendar.getYear(), calendar.getMonth(), calendar.getDay());
        tvCurrentDay.setText(calendar.getDay() + "  ");
        if (mAdapter != null) {
            initCaladarData();
        }
    }

    @Override
    public void onYearChange(int year) {

    }

    private Calendar getSchemeCalendar(int year, int month, int day, int color, String text) {
        Calendar calendar = new Calendar();
        calendar.setYear(year);
        calendar.setMonth(month);
        calendar.setDay(day);
        calendar.setSchemeColor(color);//如果单独标记颜色、则会使用这个颜色
        calendar.setScheme(text);
        calendar.addScheme(new Calendar.Scheme());
        calendar.addScheme(0xFF008800, "假");
        calendar.addScheme(0xFF008800, "节");
        return calendar;
    }

    /*@Override
    public void showMessageTodo(List<MessageTodo> info) {
        if(info != null && info.size() > 0){
            todoInfos.clear();
            todoInfos.addAll(info);
        }
        initScheme();
        initCaladarData();

    }*/

    @Override
    public void showTaskToDoByUserId(List<TaskDTO> dtoList) {
        if (dtoList != null && dtoList.size() > 0) {
            todoInfos.addAll(dtoList);
        } else {
            showToast("没有更多");
        }
        ptrlTodoList.finishRefresh();
        ptrlTodoList.finishLoadMore();
        initScheme();
        initCaladarData();
    }

    private void initCaladarData() {
        selectInfos.clear();
        for (TaskDTO todo : todoInfos) {
            if (DateUtil.timeIsEq(todo.getBeginDate(), time)) {
                selectInfos.add(todo);
            }
        }
        mAdapter.notifyDataSetChanged();
    }

    private void initScheme() {
        /*List<Calendar> schemes = new ArrayList<>();
        for(MessageTodo todo:todoInfos){
            int year = Integer.valueOf(DateUtil.getTimeStrByTime(todo.getDynamicRemindTime(),DateUtil.YMD_HM,1));
            int month = Integer.valueOf(DateUtil.getTimeStrByTime(todo.getDynamicRemindTime(),DateUtil.YMD_HM,2));
            int day = Integer.valueOf(DateUtil.getTimeStrByTime(todo.getDynamicRemindTime(),DateUtil.YMD_HM,3));
            schemes.add(getSchemeCalendar(year,month,day,0xFFedc56d,"办"));

        }
        mCalendarView.setSchemeDate(schemes);*/

        List<Calendar> schemes = new ArrayList<>();
        for (TaskDTO todo : todoInfos) {
            int year = Integer.valueOf(DateUtil.getTimeStrByTime(todo.getBeginDate(), DateUtil.YMD, 1));
            int month = Integer.valueOf(DateUtil.getTimeStrByTime(todo.getBeginDate(), DateUtil.YMD, 2));
            int day = Integer.valueOf(DateUtil.getTimeStrByTime(todo.getBeginDate(), DateUtil.YMD, 3));
            schemes.add(getSchemeCalendar(year, month, day, 0xFFedc56d, "办"));

        }
        mCalendarView.setSchemeDate(schemes);

    }


    @Override
    public void refresh() {
        mPresenter.getTaskToDoByUserId(DateUtil.getMonthDate(), userInfo.getUserOrgRelation(), pageSize + "", page + "", count + "");
        todoInfos.clear();
    }

    @Override
    public void loadMore() {
        page++;
        mPresenter.getTaskToDoByUserId(DateUtil.getMonthDate(), userInfo.getUserOrgRelation(), pageSize + "", page + "", count + "");
    }
}
