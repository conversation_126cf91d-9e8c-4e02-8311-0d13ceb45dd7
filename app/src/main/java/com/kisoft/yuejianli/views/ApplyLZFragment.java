package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import android.os.Environment;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ApplyLZContract;
import com.kisoft.yuejianli.entity.ApplyLZInfo;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ApplyLZModel;
import com.kisoft.yuejianli.presenter.ApplyLZPresenter;
import com.kisoft.yuejianli.ui.YFileListView;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.io.File;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.views.watermark.ImageUtil;

/**
 * Created by Administrator on 2019/4/10 0010.
 */

public class ApplyLZFragment extends BaseFragment<ApplyLZModel, ApplyLZPresenter> implements ApplyLZContract.ApplyLZViewContract {

    public View mRootView;
    Unbinder unbinder;

    @BindView(R.id.tv_name)
    TextView tvName;
    @BindView(R.id.et_company_name)
    EditText etCompanyName;
    @BindView(R.id.et_post)
    EditText etPost;
    @BindView(R.id.tv_quit_date)
    TextView tvQuitDate;
    @BindView(R.id.et_quit_direct)
    EditText etQuitDirect;
    @BindView(R.id.et_remark)
    EditText etRemark;
//    @BindView(R.id.sp_reasons)
//    Spinner spReasons;


    @BindView(R.id.tv_reasons)
    EditText tvReasons;
    @BindView(R.id.tv_sub)
    TextView tvSub;
    @BindView(R.id.y_file_list)
    YFileListView mFileView;
    private List<ApplyType> applyTypes;
    private ArrayAdapter<ApplyType> adapter;
    private ApplyLZInfo lzInfo = new ApplyLZInfo();

    private ApplyLZModel model;
    private ApplyLZPresenter presenter;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    ProcessListBean bean;
    private boolean mIsApply;

    @Override
    public int getRootView() {
        return R.layout.activity_apply_lz;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new ApplyLZModel(mContext);
        presenter = new ApplyLZPresenter(this, model);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initView();
        filMethod();
        return mRootView;
    }

    private void initView() {
        etCompanyName.setEnabled(false);
        etPost.setEnabled(false);

        mIsApply = ((ApplyActivity) getActivity()).isApply;
        if (mIsApply) {
            applyTypes = new ArrayList<>();
            adapter = new ArrayAdapter<>(mContext, android.R.layout.simple_list_item_1, applyTypes);
//            spReasons.setAdapter(adapter);
//            spReasons.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
//                @Override
//                public void onItemSelected(AdapterView<?> adapterView, View view, int position, long id) {
//                    lzInfo.setCause(applyTypes.get(position).getItemguid());
//                    lzInfo.setCauseName(applyTypes.get(position).getItemname());
//                }
//
//                @Override
//                public void onNothingSelected(AdapterView<?> adapterView) {
//
//                }
//            });
            userInfo = SettingManager.getInstance().getUserInfo();
            projectInfo = SettingManager.getInstance().getProject();
            if (userInfo != null) {
                lzInfo.setEmpguid(userInfo.getId());
                lzInfo.setEmpName(userInfo.getName());
                lzInfo.setCorpName(userInfo.getCompanyName());
                lzInfo.setPostName(userInfo.getPostName());
                tvName.setText(userInfo.getName());
                etCompanyName.setText(userInfo.getCompanyName());
                etPost.setText(userInfo.getPostName());
            }
            if (projectInfo != null) {
                lzInfo.setProjectId(projectInfo.getProjectId());
                lzInfo.setProjectName(projectInfo.getProjectName());
            }

            tvQuitDate.setText(DateUtil.getTodayDate());
            lzInfo.setDate(DateUtil.getTodayDate());
            presenter.getApplyType();
        } else {
            tvQuitDate.setEnabled(false);
            etQuitDirect.setEnabled(false);
            etQuitDirect.setHint("");
            etRemark.setEnabled(false);
            etRemark.setHint("");
            tvReasons.setVisibility(View.VISIBLE);
//            spReasons.setVisibility(View.GONE);
            tvSub.setVisibility(View.GONE);

            bean = ((ApplyActivity) getActivity()).bean;
            presenter.getInfo(bean);
        }
    }

    @OnClick({R.id.tv_quit_date, R.id.tv_sub})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_quit_date:
                showDatePickerDialog(tvQuitDate);
                break;
            case R.id.tv_sub:
                String quitDirect = etQuitDirect.getText().toString().trim();
                String remark = etRemark.getText().toString().trim();
                String cause = tvReasons.getText().toString().trim();
                if (!StringUtil.isEmpty(quitDirect)) {
                    lzInfo.setAspect(quitDirect);
                }
                if (!StringUtil.isEmpty(remark)) {
                    lzInfo.setRemark(remark);
                }
                if (!StringUtil.isEmpty(cause)) {
                    lzInfo.setCause(cause);
                }
                uploadMulFile();
                break;
        }
    }

    /**
     * 展示日期选择对话框
     */
    private void showDatePickerDialog(final TextView tv) {
        Calendar c = Calendar.getInstance();
        new DatePickerDialog(mContext, new DatePickerDialog.OnDateSetListener() {

            @Override
            public void onDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                // TODO Auto-generated method stub
                int m = monthOfYear + 1;
                String month = m >= 10 ? m + "" : "0" + m;
                String day = dayOfMonth >= 10 ? dayOfMonth + "" : "0" + dayOfMonth;
                String dateStr = year + "-" + month + "-" + day;
                tv.setText(dateStr);
                lzInfo.setDate(dateStr);
            }
        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)).show();
    }

    @Override
    public void leaveTypeBack(List<ApplyType> applyTypes) {
        this.applyTypes.clear();
        if (applyTypes != null && !applyTypes.isEmpty()) {
            this.applyTypes.addAll(applyTypes);
            adapter.notifyDataSetChanged();

            lzInfo.setCause(applyTypes.get(0).getItemguid());
            lzInfo.setCauseName(applyTypes.get(0).getItemname());
        }
    }

    @Override
    public void infoBack(ApplyLZInfo info) {
        if (info != null) {
            bean.setFlowId(info.getWfId());
            tvName.setText(info.getEmpName());
            etCompanyName.setText(info.getCorpName());
            etPost.setText(info.getPostName());
            tvQuitDate.setText(info.getDate());
            tvReasons.setText(info.getCause());
            etQuitDirect.setText(info.getAspect());
            etRemark.setText(info.getRemark());
            getFileList(info.getDimissguid());
        }
    }

    @Override
    public void applyBack(String str) {
        String[] backs = str.split(",");
        if (backs != null && backs.length == 3) {
            ((ApplyActivity) getContext()).setBack(str);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    private ArrayList<EnclosureListDto> mFileList = new ArrayList<>();
    private boolean isTakePhoto;
    private BGAPhotoHelper mPhotoHelper;

    private void filMethod(){
        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);
        mFileView.setApply(mIsApply);
        // 获取主键
        getPrimarykey(new GetPrimarykeyContract() {
            @Override
            public void getPrimarykey(String str) {
                lzInfo.setDimissguid(str);
            }
        });
        // 点击添加附件
        mFileView.getAddFileBtn().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showFileSelect();
            }
        });
        // 删除附件
        mFileView.setFileOperateContract(new YFileListView.FileOperateContract() {
            @Override
            public void openFile(int index) {
                mFileList.remove(index);
                mFileView.setList(mFileList);
            }
        });
        // 打开附件
        mFileView.setOpenFileContract(new YFileListView.OpenFileContract() {
            @Override
            public void openFile(EnclosureListDto dto) {
                WebActivity.launch(getActivity(),dto);
            }
        });
    }

    private void showFileSelect() {
        // 空照片 ，添加
        String[] str = new String[]{"系统相机", "手机相册", "手机文件"};
        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
        ab.setItems(str, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                switch (which) {
                    case 0://相机
                        isTakePhoto = true;
                        takePhoto();
                        break;
                    case 1://相册
                        isTakePhoto = false;
                        getPhoto();
                        break;
                    case 2://手机文件
                        getFile();
                        break;
                }
            }
        });
        ab.show();
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), Constant.REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), Constant.REQUEST_CODE_TAKE_PHOTO);
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    /**
     * 打开文件选择器
     */
    private void getFile() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("*/*");// 任意类型文件
        this.startActivityForResult(intent, Constant.REQUEST_CODE_FILE_SELECT);
    }

    BGAPhotoHelper1 mPhotoHelper1 = new BGAPhotoHelper1();
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQUEST_CODE_CHOOSE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_TAKE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800, 800),
                            Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_CROP) {
            if (resultCode == Activity.RESULT_OK) {
                // getCropIntent  获取裁剪完图片的路径
                String photoPath = mPhotoHelper1.getCropFilePath();
//                showToast(photoPath);
                File file = new File(mPhotoHelper1.getCropFilePath());
                double fileSize = FileUtil.getFileOrFilesSize(photoPath, 3);
                Log.i("fileSize", fileSize + "");

                //showToast(photoPath);
                if (fileSize > 1) {
                    ImageUtil.compressImageByLuban(photoPath);
                    String s = ImageUtil.compressImage(photoPath);
                    mFileList.add(new EnclosureListDto(file.getName(), s));
                } else {
                    mFileList.add(new EnclosureListDto(file.getName(), photoPath));
                }
                mFileView.setList(mFileList);
            } else {
                mPhotoHelper.deleteCameraFile();
                mPhotoHelper.deleteCropFile();
            }
        } else if (requestCode == Constant.REQUEST_CODE_FILE_SELECT) {
            String path = "";
            if (data == null) {
                // 用户未选择任何文件，直接返回
                return;
            }
            path = FileUtil.getRealPath(this.getContext(), data.getData());
            File file = new File(path);
            mFileList.add(new EnclosureListDto(file.getName(), path));
            mFileView.setList(mFileList);
            //showToast(path);
        }
    }

    private void uploadMulFile(){
        if (mFileList.isEmpty()) {
            presenter.submitApply(lzInfo);
        } else {
            if (lzInfo.getDimissguid().isEmpty()) {
                getPrimarykey(new GetPrimarykeyContract() {
                    @Override
                    public void getPrimarykey(String str) {
                        lzInfo.setDimissguid(str);
                    }
                });
                showToast("未获取到主键");
                return;
            }

            showProgress();
            String s = DateUtil.dateToString(new Date(), DateUtil.YM1);
            HashMap<String, String> paras = new HashMap<>();
            paras.put("filePath", "/hr/emp/dim/");
            paras.put("businessId", lzInfo.getDimissguid());
            paras.put("businessType", "T_org_EmpDimission");
            OkHttpRequestManager.getInstance().uploadMulFile(mFileList, paras, new IRequestCallback() {
                @Override
                public void onSuccess(String response) {
                    Log.i("upload", "onSuccess: ");
                    presenter.submitApply(lzInfo);
                }

                @Override
                public void onFailure(Throwable throwable) {
                    Log.i("upload", "onFailure: ");
                    dismissProgress();
                }
            });
        }
    }
    // 获取附件列表
    private void getFileList(String bt) {
        HashMap<String, Object> paras = new HashMap<>();
        paras.put("businessId", bt);
        paras.put("businessType", "T_org_EmpDimission");
        Api.getGbkApiserver().getEnclosureList(Constant.HTTP_GET_ENCLOSURE_LIST, paras).enqueue(new Callback<NetworkResponse<List<EnclosureListDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<EnclosureListDto>>> call,
                                   Response<NetworkResponse<List<EnclosureListDto>>> response) {
                if (response.body().getData() != null) {
                    mFileList.clear();
                    mFileList.addAll(response.body().getData());
                    mFileView.setList(mFileList);
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<List<EnclosureListDto>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}

