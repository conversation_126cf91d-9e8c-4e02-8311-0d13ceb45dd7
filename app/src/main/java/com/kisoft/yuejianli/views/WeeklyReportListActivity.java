package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.interf.GlobalListener;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.api.NetworkResponse2;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.GsonUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * Created by tudou on 2018/6/9.
 */
public class WeeklyReportListActivity extends BaseActivity implements BaseRefreshListener {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;

    @BindView(R.id.mRecyclerView)
    RecyclerView mRecyclerView;
    @BindView(R.id.prLayout)
    PullToRefreshLayout prLayout;

//    private boolean mXmkn = false;
//    private String selectType;

    private View empotyView;
    private WeeklyReportListAdapter mAdapter;
    private List<Map<String, Object>> mList = new ArrayList<>();

    private List<String> selectPosition = new ArrayList<>();                   //  选中的位置


//    private int selectPos = -1;

    private int page = 1;
    private int pageSize = 20;
    private int total = 0;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    public static void launch(Activity activity){
        Intent intent=new Intent(activity,WeeklyReportListActivity.class);
//        intent.putExtra("type",type);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initData();
        initView();
    }


    private void initView() {
        tvTitle.setText("周报");
        tvSubmit.setText("合并");
        tvSubmit.setVisibility(View.VISIBLE);

        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        if (mRecyclerView.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            mRecyclerView.setLayoutManager(manager);
        }
        mAdapter = new WeeklyReportListAdapter(R.layout.item_un_submit_work, mList, new GlobalListener<Map<String,
                Object>>() {
            @Override
            public void onViewClick(int id, int position, Map<String, Object> model) {

            }

            @Override
            public void onRootViewClick(View view, int position, Map<String, Object> model) {
//                selectPos = position;
                goDetail(mList.get(position), false);
            }
        });
        mAdapter.setEmptyView(empotyView);
//        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
//            @Override
//            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
//
//            }
//        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                mRecyclerView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        page++;
                        if (mList.size() >= total) {
                            mAdapter.loadMoreEnd();
                        } else {
                            getData();
                        }
                    }
                }, 1000);
            }
        }, mRecyclerView);
        mRecyclerView.setAdapter(mAdapter);
        prLayout.setRefreshListener(this);
        refresh();
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();


    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_weekly_report_list;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }


    // 周报预览
    @OnClick(R.id.tv_submit)
    public void uploadWork() {
        // 获得选中的作业i
        this.selectPosition.clear();
        this.selectPosition.addAll(mAdapter.getSelectWork());
        // 遍历选中的数据进行数据提交
//        for (int i = 0; i < selectPosition.size(); i++) {
//            String aLong = selectPosition.get(i);
//        }

        StringBuilder result = new StringBuilder();
        for (String value : selectPosition) {
            result = result.append(value);
            result = result.append(",");
        }

        downloadPreview(result.toString());
    }
    @Override
    public void refresh() {
        page = 1;
        getData();
    }

    @Override
    public void loadMore() {
        page++;
        getData();
    }


    private void getData() {

        String p = Integer.toString(page);
        String z = Integer.toString(pageSize);
        Map<String, Object> pram = new HashMap<>();
        pram.put("projectId", projectInfo.getProjectId());
        pram.put("page", p);
        pram.put("limit", z);
        String url = SettingManager.getInstance().getBaseUrl() + "/pds/ProdWeeklyReportAction" +
                ".do?method=getProdWeeklyReportList";
        OkHttpRequestManager.getInstance().post(url, pram, new IRequestCallback() {
            @Override
            public void onSuccess(String response) {
                prLayout.finishRefresh();
                prLayout.finishLoadMore();
                Log.i("TAG", "onSuccess: " + response);
//                Map<String, Object> map = GsonUtil.GsonToMaps(response);
//                Map<String, Object> map1 = (Map<String, Object>) map.get("data");
//                mList = (List<Map<String, Object>>) map1.get("data");
                try {
                    NetworkResponse2 response2 = GsonUtil.GsonToBean(response, NetworkResponse2.class);
                    NetworkResponse2.NetworkResponse2Data data = response2.getData();
                    total = data.getTotal();
                    if (page == 1) {
                        mList = data.getData();
                    } else {
                        mList.addAll(data.getData());
                    }
                    mAdapter.setNewData(mList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(Throwable throwable) {
                throwable.printStackTrace();
                prLayout.finishRefresh();
                prLayout.finishLoadMore();
            }
        });
    }

    private void goDetail(Map<String, Object> map, boolean isAdd) {

        // 周报
        Intent intent = new Intent();
        intent.setClass(this, ProjectModelCommonDetailWebActivity.class);
        String ids = map.get("id").toString();
        String urlStr = SettingManager.getInstance().getBaseUrl() + "/pds/ProdWeeklyReportAction" +
                ".do?method=toProdWeeklyReportModify&pid=2&ids=" + ids + "&userId=" + userInfo.getId() + "&projectId" +
                "=" + userInfo.getProjectId();

        intent.putExtra("url", urlStr);
        intent.putExtra("bid", ids);
        intent.putExtra("title", "周报");
        intent.putExtra("bType", "T_PROD_WEEKLY_REPORT");
        startActivity(intent);

    }

    private void downloadPreview(String ids) {

        String url = SettingManager.getInstance().getBaseUrl() + "pds/ProdWeeklyReportAction.do?method=getSubmitState&ids=" + ids;
        OkHttpRequestManager.getInstance().post(url, new HashMap(), new IRequestCallback() {
            @Override
            public void onSuccess(String response) {
                Log.i("TAG", "onSuccess: " + response);
//                Map<String, Object> map = GsonUtil.GsonToMaps(response);
//                Map<String, Object> map1 = (Map<String, Object>) map.get("data");
//                mList = (List<Map<String, Object>>) map1.get("data");
                try {
                    NetworkResponse<Map<String, Object>> response1 = GsonUtil.GsonToBean(response, NetworkResponse.class);

                    Map<String, Object> data = response1.getData();

                    EnclosureListDto dto = new EnclosureListDto();
                    dto.setViewUrl(data.get("viewUrl").toString());
                    dto.setFileName("");
                    dto.setFilePath("");
                    WebActivity.launch(WeeklyReportListActivity.this, dto);

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(Throwable throwable) {
                throwable.printStackTrace();
            }
        });
    }


//    @Override
//    public void showUnSubmitInfo(List<UnSubmitInfo> info) {
//        this.infos.clear();
//        this.infos.addAll(info);
//        Log.i("000", "_____________________________________" + info.size());
//        mAdapter.selectAll();
//        mAdapter.notifyDataSetChanged();
//    }




    // 信息管理
    public class WeeklyReportListAdapter extends BaseQuickAdapter<Map<String, Object>, BaseViewHolder> {

        private GlobalListener<Map<String, Object>> globalListener;


        private List<String> selectPosition = new ArrayList<>();

        public WeeklyReportListAdapter(int layoutResId, @Nullable List<Map<String, Object>> data,
                                       GlobalListener<Map<String, Object>> globalListener) {
            super(layoutResId, data);
            this.globalListener = globalListener;
        }

        @Override
        protected void convert(BaseViewHolder helper, final Map<String, Object> item) {
            final int position = helper.getAdapterPosition();
            TextView tvWorkType = helper.itemView.findViewById(R.id.tv_work_type);
            TextView time = helper.itemView.findViewById(R.id.tv_work_time);
            CheckBox checkBox = helper.itemView.findViewById(R.id.cb_submit);
            tvWorkType.setText(item.get("projectName").toString());
            time.setText(item.get("createName").toString());
            if (selectPosition.contains(item.get("id"))) {
                checkBox.setChecked(true);
            } else {
                checkBox.setChecked(false);
            }
            checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                    if (b) {
                        selectPosition.add(item.get("id").toString());
                    } else {
                        selectPosition.remove(item.get("id"));
                    }
                }
            });
            helper.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    globalListener.onRootViewClick(v, position, item);
                }
            });
        }

        public void selectAll() {
            selectPosition.clear();
            for (int i = 0; i < mData.size(); i++) {
//                selectPosition.add(mData.get(i).getId());
            }
        }

        public List<String>  getSelectWork() {
            return selectPosition;
        }

        public boolean deleteComitInfo(Long id) {
            boolean isDelete = false;
            try {
                selectPosition.remove(id);
                isDelete = true;
            } catch (Exception e) {
                e.printStackTrace();
            }
            return isDelete;
        }
    }
}
