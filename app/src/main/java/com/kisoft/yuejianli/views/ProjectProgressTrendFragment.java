package com.kisoft.yuejianli.views;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.contract.ProjectProgressTrendContract;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.ProgTendDto;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ProjectProgressTrendModel;
import com.kisoft.yuejianli.presenter.ProjectProgressTrendPresenter;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;
import com.kisoft.yuejianli.R;

/**
 * Created by Administrator on 2019/4/10 0010.
 */

public class ProjectProgressTrendFragment extends BaseFragment<ProjectProgressTrendModel, ProjectProgressTrendPresenter> implements ProjectProgressTrendContract.ProjectProgressTrendViewContract {

    public View mRootView;
    Unbinder unbinder;

    private ProjectProgressTrendModel model;
    private ProjectProgressTrendPresenter presenter;

    private ProjectInfo projectInfo;

    ProcessListBean bean;

    @Override
    public int getRootView() {
        return R.layout.fragment_project_progress_trend;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new ProjectProgressTrendModel(mContext);
        presenter = new ProjectProgressTrendPresenter(this, model);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initView();
        return mRootView;
    }

    private void initView() {
        projectInfo = SettingManager.getInstance().getProject();
        if (projectInfo != null) {
//            presenter.getProgTendByProjectId();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    @Override
    public void progTendBack(List<ProgTendDto> list) {
        if (list != null) {

        }
    }
}
