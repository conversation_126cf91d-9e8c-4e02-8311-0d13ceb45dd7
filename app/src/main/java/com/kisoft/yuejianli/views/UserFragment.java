package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.facebook.drawee.view.SimpleDraweeView;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.MyProjectAdapter;
import com.kisoft.yuejianli.entity.UserInfo;

/**
 * Created by tudou on 2018/3/12.
 */

public class UserFragment extends BaseFragment implements View.OnClickListener {


    private SimpleDraweeView avatar;
    private TextView tvName;
    private TextView tvWork;
    private View userView;
    private RecyclerView rvMyproject;
    private ImageView ivSwith;
    private TextView tvTakeCard;
    private TextView tvPhoneBook;
    private TextView tvSetting;
    private TextView tvLogout;

    private UserInfo userInfo;
    private List<ProjectInfo> projectInfos = new ArrayList<>();
    private MyProjectAdapter mAdapter;


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);

        initData();
        initView();
        return mRootView;
    }

    private void initView() {

        avatar = mRootView.findViewById(R.id.iv_avatar);
        tvName = mRootView.findViewById(R.id.tv_user_name);
        tvWork = mRootView.findViewById(R.id.tv_user_work);
        userView = mRootView.findViewById(R.id.ll_user_info);
        rvMyproject = mRootView.findViewById(R.id.rv_my_project);
        ivSwith = mRootView.findViewById(R.id.iv_switch);
        tvPhoneBook = mRootView.findViewById(R.id.tv_my_phonebook);
        tvTakeCard = mRootView.findViewById(R.id.tv_take_card);
        tvSetting = mRootView.findViewById(R.id.tv_setting);
        tvLogout = mRootView.findViewById(R.id.tv_logout);
        tvName.setText(userInfo.getName());
        tvWork.setText(userInfo.getCompanyName());
        setSwithStatus();
        userView.setOnClickListener(this);
        ivSwith.setOnClickListener(this);
        tvPhoneBook.setOnClickListener(this);
        tvTakeCard.setOnClickListener(this);
        tvSetting.setOnClickListener(this);
        tvLogout.setOnClickListener(this);

        if (rvMyproject.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(getContext());
            manager.setOrientation(LinearLayoutManager.HORIZONTAL);
            rvMyproject.setLayoutManager(manager);
        }
        mAdapter = new MyProjectAdapter(R.layout.item_my_project, projectInfos);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                changeProject(projectInfos.get(position));

            }
        });
        rvMyproject.setAdapter(mAdapter);


    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        if(SettingManager.getInstance().getAllProjects() != null && SettingManager.getInstance().getAllProjects().size() >0){
            projectInfos.addAll(SettingManager.getInstance().getAllProjects());
        }else {
            /** 测试数据  */
            for(int i=0 ; i<16; i++){
                ProjectInfo projectInfo = new ProjectInfo();
                projectInfo.setProjectId("742368423867"+i);
                projectInfo.setProjectName("测试项目_"+"A1111"+i);
                projectInfos.add(projectInfo);
            }
            /** 测试数据  */
        }


    }

    @Override
    public int getRootView() {
        return R.layout.fragment_user;
    }


    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ll_user_info:
                goSetUserInfo();
                break;
            case R.id.tv_my_phonebook:
                goPhoneBook();
                break;
            case R.id.tv_take_card:

                goTakeCard();
                break;
            case R.id.iv_switch:
                setSignManager();
                break;
            case R.id.tv_setting:
                goSetting();
                break;
            case R.id.tv_logout:
                logout();
                break;

            default:
                break;
        }
    }

    private void setSwithStatus(){
        if(SettingManager.getInstance().getIsAutoSign()){
            ivSwith.setImageResource(R.drawable.ic_open);
        }else {
            ivSwith.setImageResource(R.drawable.ic_close);
        }
    }

    private void goSetUserInfo(){

    }


    private void setSignManager() {
        boolean isAutoSign = !SettingManager.getInstance().getIsAutoSign();
        SettingManager.getInstance().saveIsAutoSign(isAutoSign);
        setSwithStatus();
    }

    private void goTakeCard(){
        Intent intent = new Intent();
        intent.setClass(getContext(), PunchCardActivity.class);
        startActivity(intent);
    }

    private void goPhoneBook(){
        Intent intent = new Intent();
        intent.setClass(getContext(), PhoneDirectoryActivity.class);
        startActivity(intent);
    }


    private void goSetting(){

    }

    private void logout(){

    }

    /**
     * 切换项目
     */
    private void changeProject(ProjectInfo projectInfo){
        SettingManager.getInstance().saveProject(projectInfo);
        Log.i("000------", "------"+SettingManager.getInstance().getProject().getProjectId());
        Log.i("000------", "------"+SettingManager.getInstance().getProject().getProjectName());
    }
}
