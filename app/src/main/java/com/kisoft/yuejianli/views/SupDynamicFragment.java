package com.kisoft.yuejianli.views;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.github.mikephil.charting.animation.Easing;
import com.github.mikephil.charting.charts.BarChart;
import com.github.mikephil.charting.charts.PieChart;
import com.github.mikephil.charting.components.AxisBase;
import com.github.mikephil.charting.components.Description;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.PieData;
import com.github.mikephil.charting.data.PieDataSet;
import com.github.mikephil.charting.data.PieEntry;
import com.github.mikephil.charting.formatter.IAxisValueFormatter;
import com.github.mikephil.charting.formatter.PercentFormatter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.adpter.SupDynamicAdapter;
import com.kisoft.yuejianli.adpter.XmkbRyjcAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.BarChatXName;
import com.kisoft.yuejianli.entity.ProbPerInfo;
import com.kisoft.yuejianli.entity.ProjectApproverInfo;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.ProjectQualityOrSafe;
import com.kisoft.yuejianli.entity.PunchCardInfo;
import com.kisoft.yuejianli.entity.RYJCBean;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.entity.WorkCount;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.BarChartManager;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class SupDynamicFragment extends BaseFragment {

    public View mRootView;
    Unbinder unbinder;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    @BindView(R.id.tv_work_tip)
    TextView tvWorkTip;


    @BindView(R.id.tv_checkwork_num)
    TextView tvCheckworkNum;
    @BindView(R.id.rl_checkwork)
    RelativeLayout rlCheckwork;
    @BindView(R.id.tv_cometo_num)
    TextView tvCometoNum;
    @BindView(R.id.rl_cometo)
    RelativeLayout rlCometo;
    @BindView(R.id.tv_leave_num)
    TextView tvLeaveNum;
    @BindView(R.id.rl_leave)
    RelativeLayout rlLeave;
    @BindView(R.id.tv_err_num)
    TextView tvErrNum;
    @BindView(R.id.rl_err)
    RelativeLayout rlErr;
    @BindView(R.id.ll_num)
    LinearLayout llNum;
    @BindView(R.id.ll_monmth_question)
    LinearLayout llMonmthQuestion;

    @BindView(R.id.pie_chart)
    PieChart pieChart;//饼形统计图
    private PieData pieQuestionData;


    @BindView(R.id.ll_quality_chart)
    LinearLayout llQualityView;
    @BindView(R.id.bar_quality_chart)
    BarChart barQualityChart;//统计图--质量监理

    @BindView(R.id.ll_safe_chart)
    LinearLayout llSafeView;
    @BindView(R.id.bar_safe_chart)
    BarChart barSafeChart;//统计图--安全监理


    @BindView(R.id.ll_progress_chart)
    LinearLayout llProgressView;
    @BindView(R.id.pie_progress_chart)
    PieChart pieProgressChart;//进度统计图
    private PieData pieProgressData;

    @BindView(R.id.bar_fees_chart)
    BarChart barFeesChart;//统计图--费用监理

    // 人员进退场
    @BindView(R.id.ll_ryjc_view)
    LinearLayout llRyjcView;

    @BindView(R.id.tv_ryjc_all_num)
    TextView tvRyjcAllNum;
    @BindView(R.id.tv_ryjc_jc_num)
    TextView tvRyjcJcNum;
    @BindView(R.id.tv_ryjc_tc_num)
    TextView tvRyjcTcNum;

    private List<Integer> partColor = new ArrayList<>();

    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private SupDynamicAdapter supDynamicAdapter;
    private List<WorkCount> mWorkCountList = new ArrayList<>(); // 工作统计
    private String selectType = "1";

//    private XmkbRyjcAdapter mRYJCAdapter;
    private List<RYJCBean> mRYJCList = new ArrayList<>();// 人员进出

    // 问题统计
    private List<ProbPerInfo> probPerInfos = new ArrayList<>();

    private String headCount; //考勤总人数
    private String leaveCount; //请假人数
    private String errCount; //异常人数
    private String workCount; //到岗人数

    private int probCount1 = 0;//已整改
    private int probCount2 = 0;//整改中
    private int probCount3 = 0;//已复查
    private int probCountSize = 0;

    // 质量监理
    private List<Integer> colours = new ArrayList<>();          // 颜色集合
    private List<String> itemName = new ArrayList<>();          // 专项名(线的名字集合)
    private ArrayList<BarChatXName> xQualityValues = new ArrayList<>();       // 设置x轴的数据
    List<List<Float>> yQualityValues = new ArrayList<>();              // 设置y轴的数据()
    private BarChartManager mQualityBarChartManager;

    // 安全监理
    private ArrayList<BarChatXName> xSafeValues = new ArrayList<>();       // 设置x轴的数据
    List<List<Float>> ySafeValues = new ArrayList<>();              // 设置y轴的数据()
    private BarChartManager mSafeBarChartManager;

    // 费用监理
    private ArrayList<BarChatXName> xFeesValues = new ArrayList<>();       // 设置x轴的数据
    List<List<Float>> yFeesValues = new ArrayList<>();              // 设置y轴的数据()
    private BarChartManager mFeesBarChartManager;
    private List<String> feesItemName = new ArrayList<>();          // 专项名(线的名字集合)
    private List mFeesList = new ArrayList<>();

    // 进度监理

    @Override
    public int getRootView() {
        return R.layout.activity_sup_dynamic_fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        // TODO: inflate a fragment view
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initData();
        initColor();
        initView();
        return mRootView;
    }

    private void initColor() {
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_2));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_4));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_1));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_3));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_5));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_6));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_7));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_8));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_9));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_10));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_11));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_12));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_13));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_14));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_15));
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
/*
        WorkCount workCount0 = new WorkCount("现场巡视", "0");
        WorkCount workCount1 = new WorkCount("质量验收", "0");
        WorkCount workCount2 = new WorkCount("设备进场", "0");
        WorkCount workCount3 = new WorkCount("旁站记录", "0");
        WorkCount workCount4 = new WorkCount("隐蔽工程", "0");
        WorkCount workCount5 = new WorkCount("安全检查", "0");
        WorkCount workCount6 = new WorkCount("抽检记录", "0");
        WorkCount workCount7 = new WorkCount("危大巡视", "0");
        WorkCount workCount8 = new WorkCount("见证取样", "0");
        WorkCount workCount9 = new WorkCount("监理指令", "0");
        WorkCount workCount10 = new WorkCount("监理日志", "0");
        WorkCount workCount11 = new WorkCount("监理月报", "0");
        mWorkCountList.add(workCount0);
        mWorkCountList.add(workCount1);
        mWorkCountList.add(workCount2);
        mWorkCountList.add(workCount3);
        mWorkCountList.add(workCount4);
        mWorkCountList.add(workCount5);
        mWorkCountList.add(workCount6);
        mWorkCountList.add(workCount7);
        mWorkCountList.add(workCount8);
        mWorkCountList.add(workCount9);
        mWorkCountList.add(workCount10);
        mWorkCountList.add(workCount11);
        */

        SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM");
        Date date1 = new Date();
        String currDate1 = format1.format(date1);

        //获取整改中和已整改问题占比
        getProbPerByProjectId(projectInfo.getProjectId(), currDate1);

        // 今日到岗情况
        if (!StringUtil.isEmpty(projectInfo.getProjectId())) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            Date date = new Date();
            String currDate = dateFormat.format(date);

            // 工作统计
            getTodayCountsByProjectId("0");// 全部
            // 人员进退场
            getRYJCByProjectId();

            //获取考勤总人数
            getUserIdsAndNamesByProjectId(projectInfo.getProjectId(), currDate);
            //获取今日到岗人数
            getAttendance(projectInfo.getProjectId(), currDate);
            //获取请假人数
            getLeaveCountsByProjectId(projectInfo.getProjectId(), currDate);
            //获取异常考勤人数
            getAbnormalAttCounts(projectInfo.getProjectId(), currDate);
        }
        colours.clear();
        colours.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_3));
        colours.add(ContextCompat.getColor(YueApplacation.mContext, R.color.sign_late));
        colours.add(ContextCompat.getColor(YueApplacation.mContext, R.color.sign_not));

        itemName.clear();
        itemName.add(ProjectQualityOrSafe.MAP_KEY_OK);
        itemName.add(ProjectQualityOrSafe.MAP_KEY_WAR);
        itemName.add(ProjectQualityOrSafe.MAP_KEY_NO);
        // 质量监理
        getTQualityInspection("quality");
        // 安全监理
        getTQualityInspection("safe");
        // 费用监理
        getFeesData();
        // 进度监理
        getProgressData();
    }

    private void initView() {

        initZGTJView();// 工作统计
        // 问题统计
        llMonmthQuestion.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 查看问题详情
                Intent intent = new Intent();
                intent.setClass(getContext(), SupDynamicQuestionListActivity.class);
                startActivity(intent);
            }
        });
        // 质量监理
        initQualityView();
        // 安全监理
        initSafeView();
        // 费用监理
        initFeesView();
        // 人员进退场
        initRYJCView();
        llProgressView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                startActivity(new Intent(mContext, ProgressAllListActivity.class));
            }
        });
    }

    // 工作统计
    private void initZGTJView() {
        tvWorkTip.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //selectType  0：全部  1：一个月   3：三个月   6：六个月
                String str[] = new String[]{"全部", "1个月以内", "3个月以内", "6个月以内"};
                AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
                ab.setItems(str, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        switch (which) {
                            case 0://全部
                                tvWorkTip.setText("全部");
                                getTodayCountsByProjectId("0");
                                break;
                            case 1:// 1个月
                                tvWorkTip.setText("1个月内");
                                getTodayCountsByProjectId("1");
                                break;
                            case 2:// 3个月
                                tvWorkTip.setText("3个月内");
                                getTodayCountsByProjectId("3");
                                break;
                            case 3:// 6个月
                                tvWorkTip.setText("6个月内");
                                getTodayCountsByProjectId("6");
                                break;
                        }
                    }
                });
                ab.show();
            }
        });


        if (rvContent.getLayoutManager() == null) {
            /*GridLayoutManager layoutManager = new GridLayoutManager(mContext, 4);
            layoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);*/
            // 创建布局管理器
            GridLayoutManager gridLayoutManager = new GridLayoutManager(mContext, 4);
            // 设置方向（水平？垂直）
            gridLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            // 设置标准（正向？反向）
//            gridLayoutManager.setReverseLayout();
            rvContent.setLayoutManager(gridLayoutManager);
//            LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
//            layoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
//            rvContent.setLayoutManager(layoutManager);
        }
        supDynamicAdapter = new SupDynamicAdapter(R.layout.item_supdynamic1, this.mWorkCountList);
        rvContent.setAdapter(supDynamicAdapter);
        rvContent.addOnItemTouchListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int position) {
                //工作台账（工作统计）
//                Intent intent = new Intent(mContext, WorkAccountActivity.class);
//                startActivity(intent);
                WorkCount workCount = mWorkCountList.get(position);
                String type = workCount.getType();
                Intent intent = new Intent();
                intent.putExtra("xmkb", true);
                intent.putExtra("selectType", selectType);
                switch (type) {
                    case "getTQualityInspectionBook":// 巡视记录
                        intent.setClass(getContext(), QualityInspectionListActivity.class);
                        startActivity(intent);
                        break;
                    case "getTQuaAcceptanceBook":// 现场质量验收
                        intent.setClass(getContext(), QualityCheckListActivity.class);
                        startActivity(intent);
                        break;
                    case "getTQualitymaterialAcceptanceBook":// 材料设备进场
                        intent.setClass(getContext(), MaterialAddListActivity.class);
                        startActivity(intent);
                        break;
                    case "getTSidereportingBook":// 施工旁站记录
                        intent.setClass(getContext(), OnsideListActivity.class);
                        startActivity(intent);
                        break;
                    case "getTQuInvisibilityBook":// 隐蔽性工程记录
                        intent.setClass(getContext(), QualityInvisibilityListActivity.class);
                        startActivity(intent);
                        break;
                    case "getProjectSafeProblem":// 现场安全检查
                        intent.setClass(getContext(), SafeInspectListActivity.class);
                        startActivity(intent);
                        break;
                    case "getSpotCheckList":// 抽检记录
                        intent.setClass(getContext(), ProjectRecordListActivity.class);
                        intent.putExtra("type", ProjectRecordListActivity.TYPE_SPOT_CHECK);
                        startActivity(intent);
                        break;
                    case "getDangerInspectionList":// 危大工程巡检记录
                        intent.setClass(getContext(), ProjectRecordListActivity.class);
                        intent.putExtra("type", ProjectRecordListActivity.TYPE_DANGER_INSPECTION);
                        startActivity(intent);
                        break;
                    case "getWitnessSamplesInspectList":// 见证取样送检记录
                        intent.setClass(getContext(), ProjectRecordListActivity.class);
                        intent.putExtra("type", ProjectRecordListActivity.TYPE_WITNESS_SAMPLES_INSPECT);
                        startActivity(intent);
                        break;
                    case "getSupInstructList":// 监理指令
                        intent.setClass(getContext(), InstructionsListActivity.class);
                        startActivity(intent);
                        break;
                    case "getSupervisionLogBook":// 监理日志
                        intent.setClass(getContext(), ProjectDailyListActivity.class);
                        startActivity(intent);
                        break;
                    case "getMonthlyReportList":// 监理月报
                        intent.setClass(getContext(), ProjectMonthReportListActivity.class);
                        startActivity(intent);
                        break;
                }
            }
        });
    }

    // 人员进出
    private void initRYJCView() {
        llRyjcView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //到岗情况
                Intent intent1 = new Intent(mContext, XmkbRyjcActivity.class);
                Bundle bundle = new Bundle();
                bundle.putSerializable("alldata", mRYJCList.get(0));
                bundle.putSerializable("jcdata", mRYJCList.get(1));
                bundle.putSerializable("tcdata", mRYJCList.get(2));
                intent1.putExtras(bundle);
                startActivity(intent1);
            }
        });
    }

    // 质量监理
    private void initQualityView() {

        llQualityView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(mContext, ControllerListActivity.class);
                intent.putExtra("pageType", 1);
                startActivity(intent);
            }
        });

        mQualityBarChartManager = new BarChartManager(barQualityChart);
        //barQualityChart.setOnChartValueSelectedListener(this);
        barQualityChart.setDrawBarShadow(false);
        barQualityChart.setDrawValueAboveBar(true);
        barQualityChart.getDescription().setEnabled(false);

        barQualityChart.setTouchEnabled(false); // 设置是否可以触摸
        barQualityChart.setDragEnabled(false);// 是否可以拖拽
        barQualityChart.setScaleEnabled(false);// 是否可以缩放

        barQualityChart.setMaxVisibleValueCount(60);
        barQualityChart.setPinchZoom(false);
        barQualityChart.setDrawGridBackground(false);

        //x轴
        XAxis xl = barQualityChart.getXAxis();
        xl.setPosition(XAxis.XAxisPosition.BOTTOM);
        xl.setDrawAxisLine(true);
        xl.setDrawGridLines(false);
        xl.setGranularity(10f);

        //y轴
        YAxis yl = barQualityChart.getAxisLeft();
        yl.setDrawAxisLine(true);
        yl.setDrawGridLines(true);
        yl.setAxisMinimum(0f);

        //y轴
        YAxis yr = barQualityChart.getAxisRight();
        yr.setDrawAxisLine(true);
        yr.setDrawGridLines(false);
        yr.setAxisMinimum(0f);

        //设置数据
        barQualityChart.setFitBars(true);
        barQualityChart.animateY(1500);

        Legend l = barQualityChart.getLegend();
        l.setVerticalAlignment(Legend.LegendVerticalAlignment.BOTTOM);
        l.setHorizontalAlignment(Legend.LegendHorizontalAlignment.LEFT);
        l.setDrawInside(false);
        l.setFormSize(8f);
        l.setXEntrySpace(4f);
    }

    // 安全监理
    private void initSafeView() {
        llSafeView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(mContext, ControllerListActivity.class);
                intent.putExtra("pageType", 2);
                startActivity(intent);
            }
        });

        mSafeBarChartManager = new BarChartManager(barSafeChart);
        //barQualityChart.setOnChartValueSelectedListener(this);
        barSafeChart.setDrawBarShadow(false);
        barSafeChart.setDrawValueAboveBar(true);
        barSafeChart.getDescription().setEnabled(false);

        barSafeChart.setTouchEnabled(false); // 设置是否可以触摸
        barSafeChart.setDragEnabled(false);// 是否可以拖拽
        barSafeChart.setScaleEnabled(false);// 是否可以缩放

        barSafeChart.setMaxVisibleValueCount(60);
        barSafeChart.setPinchZoom(false);
        barSafeChart.setDrawGridBackground(false);

        //x轴
        XAxis xl = barSafeChart.getXAxis();
        xl.setPosition(XAxis.XAxisPosition.BOTTOM);
        xl.setDrawAxisLine(true);
        xl.setDrawGridLines(false);
        xl.setGranularity(10f);

        //y轴
        YAxis yl = barSafeChart.getAxisLeft();
        yl.setDrawAxisLine(true);
        yl.setDrawGridLines(true);
        yl.setAxisMinimum(0f);

        //y轴
        YAxis yr = barSafeChart.getAxisRight();
        yr.setDrawAxisLine(true);
        yr.setDrawGridLines(false);
        yr.setAxisMinimum(0f);

        //设置数据
        barSafeChart.setFitBars(true);
        barSafeChart.animateY(1500);

        Legend l = barSafeChart.getLegend();
        l.setVerticalAlignment(Legend.LegendVerticalAlignment.BOTTOM);
        l.setHorizontalAlignment(Legend.LegendHorizontalAlignment.LEFT);
        l.setDrawInside(false);
        l.setFormSize(8f);
        l.setXEntrySpace(4f);
    }

    // 费用监理
    private void initFeesView() {

        mFeesList.clear();
        //为第一组添加数据
//        mFeesList.add(new BarEntry(1,0));
//        mFeesList.add(new BarEntry(2,0));
//        mFeesList.add(new BarEntry(3,0));
//        mFeesList.add(new BarEntry(4,0));
//        mFeesList.add(new BarEntry(5,0));
//        mFeesList.add(new BarEntry(6,0));
//        mFeesList.add(new BarEntry(7,0));
//        mFeesList.add(new BarEntry(8,0));
//        mFeesList.add(new BarEntry(9,0));
//        mFeesList.add(new BarEntry(10,0));
//        mFeesList.add(new BarEntry(11,0));
//        mFeesList.add(new BarEntry(12,0));

        BarDataSet barDataSet = new BarDataSet(mFeesList, "万元");
        barDataSet.setColor(Color.RED);    //为第一组柱子设置颜色
        BarData barData = new BarData(barDataSet);   //加上第一组
        barFeesChart.setData(barData);

        barFeesChart.getXAxis().setDrawGridLines(false);  //是否绘制X轴上的网格线（背景里面的竖线）
        barFeesChart.getAxisLeft().setDrawGridLines(false);  //是否绘制Y轴上的网格线（背景里面的横线）

        barFeesChart.setDrawBarShadow(false);
        barFeesChart.setDrawValueAboveBar(true);
        barFeesChart.getDescription().setEnabled(false);


        Legend legend = barFeesChart.getLegend();
        legend.setEnabled(false);    //是否显示图例
//        legend.setPosition(Legend.LegendPosition.BELOW_CHART_CENTER);    //图例的位置

        //X轴
        XAxis xAxis = barFeesChart.getXAxis();
        xAxis.setDrawGridLines(false);  //是否绘制X轴上的网格线（背景里面的竖线）
//        xAxis.setAxisLineColor(Color.RED);   //X轴颜色
//        xAxis.setAxisLineWidth(2);           //X轴粗细
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);        //X轴所在位置   默认为上面
        xAxis.setValueFormatter(new IAxisValueFormatter() {   //X轴自定义坐标
            @Override
            public String getFormattedValue(float v, AxisBase axisBase) {
                if (v < 13) {
                    return String.valueOf((int) v);
                }
                return "";
            }
        });
        xAxis.setAxisMaximum(13);   //X轴最大数值
        xAxis.setAxisMinimum(0);   //X轴最小数值
//        barFeesChart.setFitBars(true);
        //X轴坐标的个数    第二个参数一般填false     true表示强制设置标签数 可能会导致X轴坐标显示不全等问题
        xAxis.setLabelCount(13, false);


        //y轴
        YAxis yl = barFeesChart.getAxisLeft();
        yl.setDrawAxisLine(true);
        yl.setDrawGridLines(true);
        yl.setAxisMinimum(0f);

        //y轴
        YAxis yr = barFeesChart.getAxisRight();
        yr.setDrawAxisLine(true);
        yr.setDrawGridLines(false);
        yr.setAxisMinimum(0f);


        barFeesChart.getAxisRight().setEnabled(false);     //右侧Y轴不显示   默认为显示
        barFeesChart.getDescription().setEnabled(false);    //右下角一串英文字母不显示
        barFeesChart.setTouchEnabled(false); // 设置是否可以触摸
        barFeesChart.setDragEnabled(false);// 是否可以拖拽
        barFeesChart.setScaleEnabled(false);// 是否可以缩放
    }

    // 本月问题统计
    private void getProbPerByProjectId(String projectId, String currDate) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("projectId", projectId);
        pramaras.put("month", currDate);
        Api.getGbkApiserver().getProbPerByProjectId(Constant.HTTP_GET_PROBPER, pramaras).enqueue(new Callback<NetworkResponse<List<ProbPerInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ProbPerInfo>>> call,
                                   Response<NetworkResponse<List<ProbPerInfo>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    List<Float> list = new ArrayList<>();
                    if (response.body().getData() != null) {
                        probPerInfos.clear();
                        probPerInfos.addAll(response.body().getData());
                        probCountSize = probPerInfos.size();
                        if (probPerInfos.size() == 3) {
                            for (int i = 0; i < probPerInfos.size(); i++) {
                                if (probPerInfos.get(i).getStateName().equals("已整改")) {
                                    probCount1 = probPerInfos.get(i).getProbCount();
                                } else if (probPerInfos.get(i).getStateName().equals("整改中")) {
                                    probCount2 = probPerInfos.get(i).getProbCount();
                                } else if (probPerInfos.get(i).getStateName().equals("已复查")) {
                                    probCount3 = probPerInfos.get(i).getProbCount();
                                }
                            }
                        } else if (probPerInfos.size() == 2) {
                            for (int i = 0; i < probPerInfos.size(); i++) {
                                if (probPerInfos.get(i).getStateName().equals("已整改")) {
                                    probCount1 = probPerInfos.get(i).getProbCount();
                                } else if (probPerInfos.get(i).getStateName().equals("整改中")) {
                                    probCount2 = probPerInfos.get(i).getProbCount();
                                } else if (probPerInfos.get(i).getStateName().equals("已复查")) {
                                    probCount3 = probPerInfos.get(i).getProbCount();
                                }
                            }
                        } else if (probPerInfos.size() == 1) {
                            for (int i = 0; i < probPerInfos.size(); i++) {
                                if (probPerInfos.get(i).getStateName().equals("已整改")) {
                                    probCount1 = probPerInfos.get(i).getProbCount();
                                } else if (probPerInfos.get(i).getStateName().equals("整改中")) {
                                    probCount2 = probPerInfos.get(i).getProbCount();
                                } else if (probPerInfos.get(i).getStateName().equals("已复查")) {
                                    probCount3 = probPerInfos.get(i).getProbCount();
                                }
                            }
                        } else {
//                            pieChart.setVisibility(View.GONE);
                        }
                        if (probCountSize == 3) {
                            float f1 =
                                    new BigDecimal((float) probCount1 / (probCount1 + probCount2 + probCount3)).setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
                            float f2 =
                                    new BigDecimal((float) probCount2 / (probCount1 + probCount2 + probCount3)).setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
                            float f3 =
                                    new BigDecimal((float) probCount3 / (probCount1 + probCount2 + probCount3)).setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
                            list.add(f1);
                            list.add(f2);
                            list.add(f3);
                        } else if (probCountSize == 2) {
                            if (probCount1 == 0) {
                                float f2 = new BigDecimal((float) probCount2 / (probCount2 + probCount3)).setScale(2,
                                        BigDecimal.ROUND_HALF_UP).floatValue();
                                float f3 = new BigDecimal((float) probCount3 / (probCount2 + probCount3)).setScale(2,
                                        BigDecimal.ROUND_HALF_UP).floatValue();
                                list.add(f2);
                                list.add(f3);
                            } else if (probCount2 == 0) {
                                float f1 = new BigDecimal((float) probCount1 / (probCount1 + probCount3)).setScale(2,
                                        BigDecimal.ROUND_HALF_UP).floatValue();
                                float f3 = new BigDecimal((float) probCount3 / (probCount1 + probCount3)).setScale(2,
                                        BigDecimal.ROUND_HALF_UP).floatValue();
                                list.add(f1);
                                list.add(f3);
                            } else if (probCount3 == 0) {
                                float f1 = new BigDecimal((float) probCount1 / (probCount1 + probCount2)).setScale(2,
                                        BigDecimal.ROUND_HALF_UP).floatValue();
                                float f2 = new BigDecimal((float) probCount2 / (probCount1 + probCount2)).setScale(2,
                                        BigDecimal.ROUND_HALF_UP).floatValue();
                                list.add(f1);
                                list.add(f2);
                            }
                        } else if (probCountSize == 1) {
                            float f1 = 0;
                            if (probCount1 != 0) {
                                f1 = new BigDecimal((float) probCount1 / probCount1).setScale(2,
                                        BigDecimal.ROUND_HALF_UP).floatValue();
                            }
                            list.add(f1);
                        } else {
//                            float f1 = new BigDecimal((float) 1 / 1).setScale(2, BigDecimal.ROUND_HALF_UP)
//                            .floatValue();
//                            list.add(f1);
                        }
                        pieQuestionData = transCashItem2PieData(list);
                        initReportChart(pieChart, pieQuestionData);
                    } else {
                        float f1 = new BigDecimal((float) 1 / 1).setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
                        list.add(f1);
                        pieQuestionData = transCashItem2PieData(list);
                        initReportChart(pieChart, pieQuestionData);
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ProbPerInfo>>> call, Throwable t) {
                t.printStackTrace();
            }
        });

    }

    private void getAbnormalAttCounts(String projectId, String currDate) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("projectId", projectId);
        pramaras.put("currDate", currDate);
        Api.getGbkApiserver().getAbnormalAttCounts(Constant.HTTP_GET_ABNORMAL_ATT, pramaras).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    if (response.body().getData() != null) {
                        Double num = (Double) response.body().getData();
                        tvErrNum.setText(num.intValue() + "");
                        errCount = num.intValue() + "";

                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });

    }

    private void getLeaveCountsByProjectId(String projectId, String currDate) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("projectId", projectId);
        pramaras.put("currDate", currDate);
        Api.getGbkApiserver().getLeaveCountsByProjectId(Constant.HTTP_GET_LEAVE_COUNTS, pramaras).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    if (response.body().getData() != null) {
                        Double num = (Double) response.body().getData();
                        tvLeaveNum.setText(num.intValue() + "");
                        leaveCount = num.intValue() + "";
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    private void getAttendance(String projectId, String time) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("projectId", projectId);
        pramaras.put("time", time);
        Api.getGbkApiserver().getProjecrtPunchCardRecord(Constant.HTTP_GET_PROJECT_ALL_PUNCHCARD_RECORD, pramaras).enqueue(new Callback<NetworkResponse<List<PunchCardInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<PunchCardInfo>>> call,
                                   Response<NetworkResponse<List<PunchCardInfo>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    if (response.body().getData() != null) {
                        if (response.body().getData().size() == 0) {
                            tvCometoNum.setText("0");
                            workCount = "0";
                        } else {
                            tvCometoNum.setText(response.body().getData().size() + "");
                            workCount = response.body().getData().size() + "";
                        }
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<PunchCardInfo>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    // 工作统计
    private void getTodayCountsByProjectId(String selectType) {
        this.selectType = selectType;
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("projectId", projectInfo.getProjectId());
        parameter.put("selectType", selectType);
        Api.getGbkApiserver().getCountsByProjectId(Constant.HTTP_GET_WORK_COUNTS, parameter).enqueue(new Callback<NetworkResponse<List<WorkCount>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<WorkCount>>> call,
                                   Response<NetworkResponse<List<WorkCount>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    mWorkCountList = response.body().getData();
                    supDynamicAdapter.setNewData(mWorkCountList);
                    supDynamicAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<WorkCount>>> call, Throwable t) {
                t.printStackTrace();
            }
        });


        /*
        Api.getGbkApiserver().getTodayCountsByProjectId(Constant.HTTP_GET_TODAY_COUNTS, pramaras).enqueue(new
        Callback<NetworkResponse<TodayCounts.DataBean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<TodayCounts.DataBean>> call,
            Response<NetworkResponse<TodayCounts.DataBean>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    if (response.body().getData() != null) {
                        todayCounts = new TodayCounts();
                        todayCounts.setData(response.body().getData());
                        mWorkCountList.get(0).setCount(todayCounts.getData().get现场巡视() + "");
                        mWorkCountList.get(1).setCount(todayCounts.getData().get质量验收() + "");
                        mWorkCountList.get(2).setCount(todayCounts.getData().get材料进场() + "");
                        mWorkCountList.get(3).setCount(todayCounts.getData().get旁站记录() + "");
                        mWorkCountList.get(4).setCount(todayCounts.getData().get隐蔽性工程() + "");
                        mWorkCountList.get(5).setCount(todayCounts.getData().get安全检查() + "");
                        supDynamicAdapter.notifyDataSetChanged();

                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<TodayCounts.DataBean>> call, Throwable t) {
                t.printStackTrace();
            }
        });
        */

    }

    private void getRYJCByProjectId() {
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("projectId", projectInfo.getProjectId());
        parameter.put("month", DateUtil.getTodayDate(DateUtil.YM));
        Api.getGbkApiserver().getRYJCData(Constant.HTTP_GET_XMKB_RYJC, parameter).enqueue(new Callback<NetworkResponse<List<RYJCBean>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<RYJCBean>>> call,
                                   Response<NetworkResponse<List<RYJCBean>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    mRYJCList = response.body().getData();
//                    Log.i("TAG", "onResponse: " + mRYJCList.get(0).getCount());
                    tvRyjcAllNum.setText(mRYJCList.get(0).getCount().toString());
                    tvRyjcJcNum.setText(mRYJCList.get(1).getCount().toString());
                    tvRyjcTcNum.setText(mRYJCList.get(2).getCount().toString());
//                    mRYJCAdapter.setNewData(mRYJCList);
//                    mRYJCAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<RYJCBean>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    // 质量/安全监理
    private void getTQualityInspection(String mType) {
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("userId", "");
        parameter.put("projectId", projectInfo.getProjectId());
        parameter.put("month", DateUtil.getTodayDate(DateUtil.YM));
        parameter.put("mType", mType);
        parameter.put("time", DateUtil.getTodayDate(DateUtil.YMD));
        Api.getGbkApiserver().getProjectQualityOrSafe(Constant.HTTP_GET_MONTH_QUALITY_OR_SAFE_INFO, parameter).enqueue(new Callback<NetworkResponse<Map<String, Object>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Map<String, Object>>> call,
                                   Response<NetworkResponse<Map<String, Object>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    if (mType.equals("quality")) {
                        Map map = response.body().getData();
//                        float okCount = (float) map.get(ProjectQualityOrSafe.MAP_KEY_OK);
//                        Float warCount = (Float) map.get(ProjectQualityOrSafe.MAP_KEY_WAR);
//                        Float noCount = (Float) map.get(ProjectQualityOrSafe.MAP_KEY_NO);
                        Float okCount = Float.parseFloat(String.valueOf(map.get(ProjectQualityOrSafe.MAP_KEY_OK)));
                        Float warCount = Float.parseFloat(String.valueOf(map.get(ProjectQualityOrSafe.MAP_KEY_WAR)));
                        Float noCount = Float.parseFloat(String.valueOf(map.get(ProjectQualityOrSafe.MAP_KEY_NO)));
                        yQualityValues.clear();
                        yQualityValues.add(Arrays.asList(okCount));
                        yQualityValues.add(Arrays.asList(warCount));
                        yQualityValues.add(Arrays.asList(noCount));

//                        yyQualityValues.add(okCount);
//                        yyQualityValues.add(warCount);
//                        yyQualityValues.add(noCount);
//                        yValue1.clear();
//                        yValue1.add(okCount.floatValue());
//                        yValue2.clear();
//                        yValue2.add(warCount.floatValue());
//                        yValue3.clear();
//                        yValue3.add(noCount.floatValue());
//
                        initQualityChart();
                    }
                    if (mType.equals("safe")) {

                        Map map = response.body().getData();
//                        float okCount = (float) map.get(ProjectQualityOrSafe.MAP_KEY_OK);
//                        Float warCount = (Float) map.get(ProjectQualityOrSafe.MAP_KEY_WAR);
//                        Float noCount = (Float) map.get(ProjectQualityOrSafe.MAP_KEY_NO);
                        Float okCount = Float.parseFloat(String.valueOf(map.get(ProjectQualityOrSafe.MAP_KEY_OK)));
                        Float warCount = Float.parseFloat(String.valueOf(map.get(ProjectQualityOrSafe.MAP_KEY_WAR)));
                        Float noCount = Float.parseFloat(String.valueOf(map.get(ProjectQualityOrSafe.MAP_KEY_NO)));
                        ySafeValues.clear();
                        ySafeValues.add(Arrays.asList(okCount));
                        ySafeValues.add(Arrays.asList(warCount));
                        ySafeValues.add(Arrays.asList(noCount));
                        initSafeChart();
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Map<String, Object>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    private void initQualityChart() {
        xQualityValues.clear();
        for (int i = 0; i < itemName.size(); i++) {
            xQualityValues.add(new BarChatXName((float) i, itemName.get(i)));
        }
        mQualityBarChartManager.showBarChart(xQualityValues, yQualityValues, itemName, colours);
        mQualityBarChartManager.setXAxis(1f, 0f, 1);
    }

    private void initSafeChart() {
        xSafeValues.clear();
        for (int i = 0; i < itemName.size(); i++) {
            xSafeValues.add(new BarChatXName((float) i, itemName.get(i)));
        }
        mSafeBarChartManager.showBarChart(xSafeValues, ySafeValues, itemName, colours);
        mSafeBarChartManager.setXAxis(1f, 0f, 1);
    }

    // 费用监理
    private void getFeesData() {
        SimpleDateFormat format1 = new SimpleDateFormat("yyyy");
        Date date1 = new Date();
        String currDate1 = format1.format(date1);
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("projectId", projectInfo.getProjectId());
        parameter.put("year", currDate1);
        Api.getGbkApiserver().getAbnormalAttCounts(Constant.HTTP_GET_XMKB_FEES, parameter).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    Map data = (Map) response.body().getData();
                    List<String> fees = (List<String>) data.get("sum");
                    mFeesList.clear();
                    for (int i = 0; i < fees.size(); i++) {
                        mFeesList.add(new BarEntry(i + 1, Float.parseFloat(fees.get(i)) / 10000));
//                        if (fees.get(i).equals("0")){
//                            mFeesList.add(new BarEntry(i+1,0));
//                        }else {
//                        }
                    }

                    BarDataSet barDataSet = new BarDataSet(mFeesList, "万元");
                    barDataSet.setColor(Color.RED);    //为第一组柱子设置颜色
                    BarData barData = new BarData(barDataSet);   //加上第一组
                    barFeesChart.setData(barData);


                    //动态更新x轴刻度自定义的数据
//                    barFeesChart.getXAxis().setValueFormatter(new IndexAxisValueFormatter(mFeesList));
                    //以下两属性必须设置，而且必须每次更新都设置一遍
                    //设置图例上显示多少个柱子，这里是多柱状图，所以这里设置显示多少个组
//                    barFeesChart.setVisibleXRangeMinimum(7f);
//                    barFeesChart.setVisibleXRangeMaximum(7f);
                    //设置柱距和组距的占比，怎么算往上翻看图二图解
                    //更新一次设置一次
//                    barFeesChart.groupBars(0f, 0.2f, 0.06f);

                    //通知BarData更新
                    barFeesChart.getBarData().notifyDataChanged();
                    //通知BarChart更新
                    barFeesChart.notifyDataSetChanged();
                    //使图表更新生效
                    barFeesChart.invalidate();

                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    private void initFeesChart() {
        xFeesValues.clear();
        feesItemName.clear();
        for (int i = 0; i < yFeesValues.size(); i++) {
            xFeesValues.add(new BarChatXName((float) i, (i + 1) + "月"));
//            xFeesValues.add(new BarChatXName((float) i, "x"));
            feesItemName.add((i + 1) + "月");
        }
        mFeesBarChartManager.showBarChart(xFeesValues, yFeesValues, feesItemName, partColor);
        mFeesBarChartManager.setXAxis(1f, 0f, 12);
    }

    // 进度监理
    private void getProgressData() {
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("userId", userInfo.getId());
        parameter.put("projectId", projectInfo.getProjectId());
        Api.getGbkApiserver().getProjectPogreessPlan(Constant.HTTP_GET_PROJECT_PROGRESS, parameter).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    List<Float> list = new ArrayList<>();
                    String procent = response.body().getData();
                    if (!StringUtil.isEmpty(procent)) {
                        float f = Float.parseFloat(procent.replace("%", ""));
                        float p = f > 100 ? 1 : (f < 0 ? 0 : f / 100);
                        list.add(p);
                        list.add(1 - p);
                    }
                    pieProgressData = transProgressCashItem2PieData(list);
                    initProgressChart(pieProgressChart, pieProgressData);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    private PieData transProgressCashItem2PieData(List<Float> list) {

        ArrayList entries = new ArrayList<>();
        ArrayList colors = new ArrayList<>();

        if (list.size() == 0) {

            entries.add(new PieEntry(100f, "暂无数据"));  //没有数据的状态给设置默认值
            colors.add(Color.rgb(228, 228, 228));  //默认为灰色

        } else {
            float p;                          // 专项占比

            for (int i = 0; i < list.size(); i++) {
                p = list.get(i);

                if (i < partColor.size() - 1) {
                    entries.add(new PieEntry(p, i == 0 ? "已完成" : "未完成"));
                    colors.add(partColor.get(i));
                } else if (i == partColor.size() - 1) {
                    entries.add(new PieEntry(p, "其它"));
                    colors.add(partColor.get(i));
                }

            }
        }

        PieDataSet dataSet = new PieDataSet(entries, "");

        dataSet.setSliceSpace(0f);  //设置不同区域之间的间距

        dataSet.setSelectionShift(5f);

        dataSet.setColors(colors);

        PieData data = new PieData(dataSet);

        data.setValueFormatter(new PercentFormatter());

        data.setValueTextSize(11f);

        data.setValueTextColor(Color.WHITE);

        return data;
    }

    private void initProgressChart(PieChart pieProgressChart, PieData pieProgressData) {

        pieProgressChart.setUsePercentValues(true);

        pieProgressChart.setExtraOffsets(5, 10, 5, 5);  //设置间距

        pieProgressChart.setDragDecelerationFrictionCoef(0.95f);

        pieProgressChart.setCenterText("");  //设置饼状图中间文字，我需求里面并没有用到这个。。

        pieProgressChart.setDrawHoleEnabled(true);

        pieProgressChart.setHoleColor(Color.WHITE);

        pieProgressChart.setTransparentCircleColor(Color.WHITE);

        pieProgressChart.setTransparentCircleAlpha(110);

        pieProgressChart.setHoleRadius(15f);

        pieProgressChart.setTransparentCircleRadius(20f);

        pieProgressChart.setTouchEnabled(false);  //设置是否响应点击触摸

        pieProgressChart.setDrawCenterText(true);  //设置是否绘制中心区域文字

        pieProgressChart.setDrawEntryLabels(false);  //设置是否绘制标签

        pieProgressChart.setRotationAngle(0); //设置旋转角度

        pieProgressChart.setRotationEnabled(true); //设置是否旋转

        pieProgressChart.setHighlightPerTapEnabled(false);  //设置是否高亮显示触摸的区域

        pieProgressChart.setData(pieProgressData);  //设置数据

        pieProgressChart.setDrawMarkerViews(false);  //设置是否绘制标记
        Description des = new Description();
        des.setText("");
        pieProgressChart.setDescription(des);

        pieProgressChart.animateY(1000, Easing.EasingOption.EaseInOutQuad);  //设置动画效果

    }

    private void getUserIdsAndNamesByProjectId(String projectId, String currDate) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("projectId", projectId);
        pramaras.put("time", currDate);
        Api.getGbkApiserver().getUserIdsAndNamesByProjectId(Constant.HTTP_GET_USERIDS_AND_NAMES_BY_PROJECTID,
                pramaras).enqueue(new Callback<NetworkResponse<List<ProjectApproverInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ProjectApproverInfo>>> call,
                                   Response<NetworkResponse<List<ProjectApproverInfo>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    if (response.body().getData() != null) {
                        if (response.body().getData().size() == 0) {
                            tvCheckworkNum.setText("0");
                            headCount = "0";
                        } else {
                            tvCheckworkNum.setText(response.body().getData().size() + "");
                            headCount = response.body().getData().size() + "";
                        }
                        /*if(!num.isEmpty()){
                            tvLeaveNum.setText(num.size()+"");
                        }else{
                            tvLeaveNum.setText("0");
                        }*/

                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ProjectApproverInfo>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    /**
     * 饼状统计图 数据交互
     * <p>
     * tempItemList 填充统计图的数据集合
     * <p>
     * return 环状统计图所需的数据对象
     */
    private PieData transCashItem2PieData(List<Float> tempItemList) {

        ArrayList entries = new ArrayList<>();
        ArrayList colors = new ArrayList<>();
        if (tempItemList.size() == 0) {
            entries.add(new PieEntry(100f, "暂无数据"));  //没有数据的状态给设置默认值
            colors.add(Color.rgb(228, 228, 228));  //默认为灰色

        } else {
            float p;                          // 专项占比
            for (int i = 0; i < tempItemList.size(); i++) {
                p = tempItemList.get(i);
                if (i < partColor.size() - 1) {
                    if (i == 0) {
                        entries.add(new PieEntry(p, i == 0 ? "已整改" : "整改中"));
                        colors.add(partColor.get(i));
                    } else if (i == 1) {
                        entries.add(new PieEntry(p, i == 0 ? "已整改" : "整改中"));
                        colors.add(partColor.get(i));
                    } else if (i == 2) {
                        entries.add(new PieEntry(p, "已复查"));
                        colors.add(partColor.get(i));
                    }
                } else if (i == partColor.size() - 1) {
                    entries.add(new PieEntry(p, "其它"));
                    colors.add(partColor.get(i));
                }
            }
        }

        PieDataSet dataSet = new PieDataSet(entries, "");
        dataSet.setSliceSpace(0f);  //设置不同区域之间的间距
        dataSet.setSelectionShift(5f);
        dataSet.setColors(colors);
        PieData data = new PieData(dataSet);
        data.setValueFormatter(new PercentFormatter());
        data.setValueTextSize(11f);
        data.setValueTextColor(Color.WHITE);
        return data;

    }

    /**
     * 初始化环状统计表
     *
     * @parammChart环状统计图控件
     * @parampieData填充统计图的数据对象
     */
    private void initReportChart(PieChart mChart, PieData pieData) {

        mChart.setUsePercentValues(true);

        mChart.setExtraOffsets(5, 10, 5, 5);  //设置间距

        mChart.setDragDecelerationFrictionCoef(0.95f);

        mChart.setCenterText("");  //设置饼状图中间文字，我需求里面并没有用到这个。。

        mChart.setDrawHoleEnabled(true);

        mChart.setHoleColor(Color.WHITE);

        mChart.setTransparentCircleColor(Color.WHITE);

        mChart.setTransparentCircleAlpha(110);

        mChart.setHoleRadius(15f);

        mChart.setTransparentCircleRadius(20f);

        mChart.setTouchEnabled(false);  //设置是否响应点击触摸

        mChart.setDrawCenterText(true);  //设置是否绘制中心区域文字

        mChart.setDrawEntryLabels(false);  //设置是否绘制标签

        mChart.setRotationAngle(0); //设置旋转角度

        mChart.setRotationEnabled(true); //设置是否旋转

        mChart.setHighlightPerTapEnabled(false);  //设置是否高亮显示触摸的区域

        mChart.setData(pieData);  //设置数据

        mChart.setDrawMarkerViews(false);  //设置是否绘制标记
        Description des = new Description();
        des.setText("");
        mChart.setDescription(des);

        mChart.animateY(1000, Easing.EasingOption.EaseInOutQuad);  //设置动画效果
    }

//    @OnClick(R.id.ll_work)
//    public void onViewClicked() {
//
//    }

    @OnClick(R.id.ll_num)
    public void onViewClicked() {

        //到岗情况
        Intent intent1 = new Intent(mContext, ArrivalActivity.class);
        Bundle bundle = new Bundle();
        bundle.putString("headCount", headCount);
        bundle.putString("leaveCount", leaveCount);
        bundle.putString("errCount", errCount);
        bundle.putString("workCount", workCount);
        intent1.putExtras(bundle);
        startActivity(intent1);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }
}
