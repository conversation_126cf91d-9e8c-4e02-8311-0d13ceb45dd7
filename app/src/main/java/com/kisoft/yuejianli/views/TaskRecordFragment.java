package com.kisoft.yuejianli.views;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.Spinner;

import com.kisoft.yuejianli.adpter.TaskTrackAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.contract.TaskRecordContract;
import com.kisoft.yuejianli.entity.TaskDTO;
import com.kisoft.yuejianli.entity.TaskTrackDto;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.TaskRecordDto;
import com.kisoft.yuejianli.model.TaskRecordModel;
import com.kisoft.yuejianli.presenter.TaskRecordPresenter;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import com.kisoft.yuejianli.R;

/**
 * Description: 工作记录
 * Author     : yanlu
 * Date       : 2018/12/29 15:40
 */
public class TaskRecordFragment extends BaseFragment<TaskRecordModel, TaskRecordPresenter>
        implements TaskRecordContract.TaskRecordViewContract {

    @BindView(R.id.rv_list)
    RecyclerView rvList;
    @BindView(R.id.tv_task_content)
    EditText tvTaskContent;
    @BindView(R.id.tv_complite_ratio)
    EditText tvCompliteRatio;
    @BindView(R.id.spinner_complite_status)
    Spinner  spinnerCompliteStatus;
    ArrayAdapter<String> sAdapter;
    String[] stringArrayList;
    private TaskTrackAdapter   mAdapter;
    private List<TaskRecordDto> mDatas = new ArrayList<>();

    private TaskRecordModel     mModel;
    private TaskRecordPresenter presenter;
    private Unbinder            unbinder;
    private String tskGuid;
    private UserInfo userInfo;

    private int CompliteStatus = 0;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        tskGuid = getArguments().getString("tskGuid");
        mModel = new TaskRecordModel(mContext);
        presenter = new TaskRecordPresenter(this, mModel);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initData();
        initView();
        return mRootView;
    }

    @Override
    public int getRootView() {
        return R.layout.fragment_task_record;
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        mAdapter = new TaskTrackAdapter(mDatas);
        LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
        layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        layoutManager.setSmoothScrollbarEnabled(false);
        rvList.setLayoutManager(layoutManager);
        rvList.setNestedScrollingEnabled(false);
        rvList.setAdapter(mAdapter);

        presenter.getTaskInfoById(tskGuid);
        presenter.getTaskTrackList(tskGuid);
    }

    private void initView() {

        spinnerCompliteStatus.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                CompliteStatus = position+1;
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    TaskDTO taskDTO;

    @Override
    public void taskInfoBack(TaskDTO taskDTO) {   // 任务详情
        if (taskDTO != null) {
            this.taskDTO = taskDTO;
            //（0=负责人答复、1=参与人答复、2=发起人答复）
            if (userInfo.getId().equals(taskDTO.getCreator())) {
                reporttype = 2;
                stringArrayList = mContext.getResources().getStringArray(R.array.array_task1);
            }else if (userInfo.getId().equals(taskDTO.getMasterGuid())) {
                reporttype = 0;
                stringArrayList = mContext.getResources().getStringArray(R.array.array_task2);
            }else if (userInfo.getId().equals(taskDTO.getMinorGuid())) {
                reporttype = 1;
                stringArrayList = mContext.getResources().getStringArray(R.array.array_task3);
            }else { // 默认
                reporttype = 1;
                stringArrayList = mContext.getResources().getStringArray(R.array.array_task3);
            }
            sAdapter = new ArrayAdapter<>(mContext, android.R.layout.simple_spinner_item, stringArrayList);
            spinnerCompliteStatus.setAdapter(sAdapter);
            sAdapter.notifyDataSetChanged();
        }
    }

    @OnClick({R.id.tv_sub})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tv_sub:
                submit();
                break;
        }
    }
    int reporttype;

    /**
     * 提交
     */
    private void submit() {
        showProgress();
        TaskTrackDto dto = new TaskTrackDto();
        dto.setTskGuid(tskGuid);
        dto.setUserGuid(userInfo.getId());
        dto.setReport(tvTaskContent.getText().toString());
        dto.setTtpercent(Long.parseLong(tvCompliteRatio.getText().toString()));
        dto.setProgress(CompliteStatus+"");
        dto.setCreatedate(DateUtil.getTodayDate());
        dto.setReporttype(reporttype+"");
        dto.setUsername(userInfo.getName());
        presenter.addTaskTrack(dto);
    }

    @Override
    public void showTaskTrackList(List<TaskRecordDto> dtoList) {
        mDatas.clear();
        if (dtoList !=null && !dtoList.isEmpty()) {
            mDatas.addAll(dtoList);
        }
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void showAddTaskTrack(Boolean result) {
        dismissProgress();
        if (result) {
            showToast("添加成功");
        }
        presenter.getTaskTrackList(tskGuid);
        tvTaskContent.setText("");
        tvCompliteRatio.setText("");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }
}
