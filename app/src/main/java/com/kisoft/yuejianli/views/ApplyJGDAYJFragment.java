package com.kisoft.yuejianli.views;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ImageAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ApplyJGDAYJContract;
import com.kisoft.yuejianli.entity.ArchivesTransferInfo;
import com.kisoft.yuejianli.entity.PhotoUploadResulte;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ApplyJGDAYJModel;
import com.kisoft.yuejianli.presenter.ApplyJGDAYJPresenter;
import com.kisoft.yuejianli.ui.ImageDialog;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.PermissionsChecker;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.views.watermark.CallBackActivity;
import com.kisoft.yuejianli.views.watermark.ImageUtil;
import com.kisoft.yuejianli.views.watermark.PhotoCaptureActivity;
import com.kisoft.yuejianli.views.watermark.PhotoListener;
import com.kisoft.yuejianli.views.watermark.WaterMask;
import com.kisoft.yuejianli.views.watermark.WaterMaskHelper;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;

public class ApplyJGDAYJFragment extends BaseFragment<ApplyJGDAYJModel, ApplyJGDAYJPresenter>
implements ApplyJGDAYJContract.ApplyJGDAYJViewContract, WaterMask.WaterMaskListener, PhotoListener
{

    @BindView(R.id.tv_projectname)
    TextView tvProjectname;
    @BindView(R.id.tv_transusername)
    TextView tvTransusername;
    @BindView(R.id.tv_transdate)
    TextView tvTransdate;
    @BindView(R.id.et_legacyremark)
    EditText etLegacyremark;
    @BindView(R.id.tv_legacyusername)
    TextView tvLegacyusername;
    @BindView(R.id.iv_today_quality11)
    ImageView ivTodayQuality11;
    @BindView(R.id.tv_legacydate)
    TextView tvLegacydate;
    @BindView(R.id.et_processresult)
    EditText etProcessresult;
    @BindView(R.id.et_projectopinion)
    EditText etProjectopinion;
    @BindView(R.id.et_remark)
    EditText etRemark;
    @BindView(R.id.tv_createName)
    TextView tvCreateName;
    @BindView(R.id.tv_createtime)
    TextView tvCreatetime;
    @BindView(R.id.rv_ptenclosure)
    RecyclerView rvPtenclosure;
    @BindView(R.id.tv_save)
    TextView tvSave;
    @BindView(R.id.tv_sub)
    TextView tvSub;
    @BindView(R.id.ll_answer)
    LinearLayout llAnswer;
    @BindView(R.id.ll_tab_content1)
    LinearLayout llTabContent1;
    @BindView(R.id.et_project_manage)
    EditText etProjectManage;
    @BindView(R.id.iv_today_quality111)
    ImageView ivTodayQuality111;
    @BindView(R.id.et_position1)
    EditText etPosition1;
    @BindView(R.id.tv_save1)
    TextView tvSave1;
    @BindView(R.id.tv_sub1)
    TextView tvSub1;
    @BindView(R.id.ll_answer1)
    LinearLayout llAnswer1;
    @BindView(R.id.ll_tab_content2)
    LinearLayout llTabContent2;
    @BindView(R.id.rv_list)
    RecyclerView rvList;
    @BindView(R.id.ll_tab_content3)
    LinearLayout llTabContent3;

    Unbinder unbinder;


    private ApplyJGDAYJModel model;
    private ApplyJGDAYJPresenter presenter;

    ArchivesTransferInfo archivesTransferInfo = new ArchivesTransferInfo();

    private String transType;

    ProcessListBean bean;


    private boolean isApply;
    private PermissionsChecker permissionsChecker;
    private List<String> images = new ArrayList<>();
    private BGAPhotoHelper mPhotoHelper;
    private ImageAdapter imageAdapter;
    static final String[] PERMISSIONS = new String[]{
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA
    };
    private static final int REQUEST_CODE_CHOOSE_PHOTO = 1;
    private static final int REQUEST_CODE_TAKE_PHOTO = 2;
    private static final int REQUEST_CODE_CROP = 3;
    private static final int REQUEST_CONSTRUCTIONUNITLIST_ACTIVITY = 4;
    private ImageDialog imageDialog;

    private View lastView;
    private int maskLocation=4;
    private ArrayList<String> uris;
    WaterMaskHelper waterMaskHelper = new WaterMaskHelper(mContext);
    private boolean isTakePhoto;
    AMapLocation locationdata = null;
    String address="";

    @Override
    public int getRootView() {
        return R.layout.activity_archives_transfer_add;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        // TODO: inflate a fragment view
        View rootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, rootView);
        model = new ApplyJGDAYJModel(mContext);
        presenter = new ApplyJGDAYJPresenter(this, model);
        initWater();
        initData();
        initView();
        return rootView;
    }

    //初始化水印
    private void initWater() {
        //binding.setPresenter(new Presenter());
        //初始化水印工具
        waterMaskHelper = new WaterMaskHelper(mContext, this, this);
        //lastView = binding.txtCenter;
        //lastView.setSelected(true);
        maskLocation = WaterMask.DefWaterMaskParam.Location.center;
    }

    @Override                       //添加水印：
    public WaterMask.WaterMaskParam onDraw() {

        //拍照后调用，设置水印的基本参数
        WaterMask.WaterMaskParam param = new WaterMask.WaterMaskParam();
        //创建当前时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        Date date = new Date();
        String format = dateFormat.format(date);
        param.txt.add(format+" "+address);
        param.location = 3;
        param.itemCount = 60;
        return param;
    }

    @Override
    public void onChoose(ArrayList<String> photos) {
        uris = photos;
        //Glide.with(CompleteCheckAddActivity.this).load(photos.get(0)).placeholder(R.mipmap.ic_launcher).centerCrop().error(R.mipmap.ic_launcher).crossFade().into(ivSfenclosure);
    }

    private void initData() {
        isApply = ((ApplyActivity) getActivity()).isApply;
        transType=((ApplyActivity) getActivity()).transType;
        if(isApply){
            permissionsChecker = new PermissionsChecker(mContext);
            // 初始添加现场照片
            images.add("");
            //images.add("");
            //inspection.setQiEnclosure(StringUtil.imageArryToString(images));
        }
    }

    private void initView() {
        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);
        //判断照片是否为空
        if (rvPtenclosure.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(mContext);
            manager.setOrientation(LinearLayoutManager.HORIZONTAL);
            rvPtenclosure.setLayoutManager(manager);
        }
        imageAdapter = new ImageAdapter(R.layout.item_iamge_show, images);
        imageAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (StringUtil.isEmpty(images.get(position))) {
                    if (permissionsChecker.lacksPermissions(PERMISSIONS)) {
                        getPermissions(Constant.REQUEST_CODE_TAKE_POHO);
                    } else {
                        // todo 空照片 ，添加
                        String str[] = new String[]{"系统相机", "手机相册"};
                        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
                        ab.setItems(str, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                switch (which) {
                                    case 0://相机
                                        isTakePhoto = true;
                                        takePhoto();
                                        break;
                                    case 1://相册
                                        isTakePhoto = false;
                                        getPhoto();
                                        break;
                                }
                            }
                        });
                        ab.show();
                    }
                } else {
                    showImage(images.get(position));
                }
            }
        });
        imageAdapter.setApply(isApply);
        imageAdapter.setDeleteBtnClickListener(new ImageAdapter.DeleteBtnClickListener() {
            @Override
            public void deletePicture(String item) {
                if (!StringUtil.isEmpty(item)){
                    images.remove(item);
                    imageAdapter.notifyDataSetChanged();
                }
            }
        });
        rvPtenclosure.setAdapter(imageAdapter);
        imageDialog = new ImageDialog();

        if(isApply){//走增加界面
            tvCreateName.setText(SettingManager.getInstance().getUserInfo().getName());
            tvProjectname.setText(SettingManager.getInstance().getProject().getProjectName());
            //创建时间
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            Date date = new Date();
            String format = dateFormat.format(date);
            tvCreatetime.setText(format);

        }else{//走详情界面
            tvTransusername.setEnabled(false);
            tvTransdate.setEnabled(false);
            tvLegacyusername.setEnabled(false);
            tvLegacydate.setEnabled(false);
            etLegacyremark.setEnabled(false);
            etProcessresult.setEnabled(false);
            etProjectopinion.setEnabled(false);
            etRemark.setEnabled(false);
            tvSub.setVisibility(View.GONE);
            bean = ((ApplyActivity) getActivity()).bean;
            presenter.getApplyJGDAYJInfo(SettingManager.getInstance().getUserInfo().getId(),"",bean.getFlowTaskId(),bean.getWfType(),bean.getFlowTaskState(),bean.getBusinessId(),transType);
        }
    }
//拍照功能代码
    /**
     * 6.0之上权限
     */
    private void getPermissions(int code) {
        ActivityCompat.requestPermissions(getActivity(), PERMISSIONS, code);
    }
    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), REQUEST_CODE_TAKE_PHOTO);
            PhotoCaptureActivity.setWaterListener(waterMaskHelper.getWaterMarkListener());
            PhotoCaptureActivity.setPhotoListener(waterMaskHelper.getPhotoListener());
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }
    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), REQUEST_CODE_CHOOSE_PHOTO);
    }
    /**
     * 通过url，展示照片
     *
     * @param url
     */
    private void showImage(String url) {
        if (imageDialog != null) {
            imageDialog.showImageDialog(getActivity().getFragmentManager(), url);
        }
    }
    private void initImage(String url) {
        Log.i("000", "______图片地址：" + url);
        images.add(0, url);
        imageAdapter.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(PhotoUploadResulte resulte) {
        dismissProgress();
        showToast(resulte.getMessge());
        if (resulte.isIsok()) {
            // todo 图片上传成功
            String imageUri = SettingManager.getInstance().getBaseUrl() + resulte.getImageUrl();
            initImage(imageUri);
        } else {
            initImage(resulte.getImageUrl());
        }
    }
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == Constant.REQUEST_CODE_TAKE_POHO) {
            takePhoto();
        }
    }
    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this);
    }

    @Override
    public void onStop() {
        super.onStop();
        if (EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().unregister(this);
    }

    BGAPhotoHelper1 mPhotoHelper1=new BGAPhotoHelper1();
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_CODE_CHOOSE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        }
        if (requestCode == REQUEST_CODE_TAKE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {

                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800, 800), REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        }
        if(requestCode==REQUEST_CODE_CROP){
            if (resultCode == Activity.RESULT_OK) {
                if (isTakePhoto) {
                    //重点在这里
                    // getCropIntent  获取裁剪完图片的路径
                    File file = new File(mPhotoHelper1.getCropFilePath());
                    //应该在这里把绘制完的水印图片路径传过去
                    ArrayList<String> strings = new ArrayList<>();
                    strings.add(file.toString());
                    if (CallBackActivity.getPhotoListener() != null){
                        //选择照片的uri，默认为下标1的元素
                        CallBackActivity.getPhotoListener().onChoose(strings);
                    }
                    if (CallBackActivity.getWaterMarkListener() != null) {
                        WaterMask.WaterMaskParam maskParam = CallBackActivity.getWaterMarkListener().onDraw();
                        CallBackActivity.getWaterMarkListener().onDraw();
                        Bitmap bitmap = ImageUtil.getBitmap(String.valueOf((file)));
                        WaterMask.draw(mContext, bitmap, String.valueOf((file)), maskParam);
                        mContext.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(file)));
                    }
                    presenter.uploadPhotoImage(file.getPath());
                    Log.i("tag",file.getPath());
                }else{
                    presenter.uploadPhotoImage(mPhotoHelper1.getCropFilePath());
                }

            } else {
                mPhotoHelper.deleteCameraFile();
                mPhotoHelper.deleteCropFile();
            }
        }



        if (requestCode == 210) {
            if(resultCode== Constant.REQEST_CODE_SELECT_COMPANY_ORG_INFO){
                if (data!= null){
                    List<String> ids = new ArrayList<>();
                    List<String> names = new ArrayList<>();
                    ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                    names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                    if (names != null  && names.size() >0   ){
                        //遗留事项处理人
                        archivesTransferInfo.setLegacyUserId(ids.get(0));
                        archivesTransferInfo.setLegacyUserName(names.get(0));
                        tvLegacyusername.setText(names.get(0));
                    }
                }
            }
        }else if(requestCode == 220){
            if(resultCode== Constant.REQEST_CODE_SELECT_COMPANY_ORG_INFO){
                if (data!= null){
                    List<String> ids = new ArrayList<>();
                    List<String> names = new ArrayList<>();
                    ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                    names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                    if (names != null  && names.size() >0   ){
                        //移交人
                        archivesTransferInfo.setTransUserId(ids.get(0));
                        archivesTransferInfo.setTransUserName(names.get(0));
                        tvTransusername.setText(names.get(0));
                    }
                }
            }

        }

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    @OnClick({R.id.tv_transdate, R.id.tv_legacyusername, R.id.tv_legacydate,R.id.tv_transusername,R.id.tv_sub})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_transdate://移交日期
                showDatePickerDialog(tvTransdate);
                break;
            case R.id.tv_legacyusername://遗留事项处理人
                Intent intent = new Intent();
                intent.putExtra("isSingle", true);
                intent.setClass(mContext, CompanyOrgInfoActivity.class);
                startActivityForResult(intent, 210);
                break;
            case R.id.tv_transusername://移交人
                Intent intent1 = new Intent();
                intent1.putExtra("isSingle", true);
                intent1.setClass(mContext, CompanyOrgInfoActivity.class);
                startActivityForResult(intent1, 220);
                break;
            case R.id.tv_legacydate://处理日期
                showDatePickerDialog(tvLegacydate);
                break;
            case R.id.tv_sub://提交
                Log.d("新增竣工档案移交","haha");
                 String projectName= SettingManager.getInstance().getProject().getProjectName();      //项目名称
                 String projectId=SettingManager.getInstance().getProject().getProjectId();         //项目Id
                 String createId=SettingManager.getInstance().getUserInfo().getId();          //创建人Id （提交人）
                 String createName=SettingManager.getInstance().getUserInfo().getName();       //创建人姓名

                 String ptId="";        //主键Id
                 String transDate=tvTransdate.getText().toString();      //移交日期
                 String transUserName=tvTransusername.getText().toString();  //移交人姓名
                 String legacyRemark=etLegacyremark.getText().toString();   //遗留事项处理
                 String legacyUserName=tvLegacyusername.getText().toString(); //遗留事项处理人姓名
                 String legacyDate=tvLegacydate.getText().toString();       //处理时间
                 String processResult=etProcessresult.getText().toString();   //处理结果
                 String projectOpinion=etProjectopinion.getText().toString();  //项目总监意见
                 String receiveUnit="";     //接收单位
                 String transRemark="";     //移交归档情况
                 String remark=etRemark.getText().toString();          //备注

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
                Date date = new Date();
                String format = dateFormat.format(date);
                //创建时间  preAcceptanceInfo.setProjectTime(format);
                 String createTime=format;   //创建时间
                 String transType="0";      //移交类型(0=项目竣工档案移交；1=外部资料移交)
                 String wfId="";            //流程Id
                 String wfStatus="";        //流程状态	（审批状态）


                //手机端照片路径
                String imgUrls = "";
                for (String url : images) {
                    imgUrls += url + ",";
                }
                if (!StringUtil.isEmpty(imgUrls)) {
                    imgUrls = imgUrls.substring(0, imgUrls.length() - 1);
                }
                 String ptEnclosure=imgUrls;//手机端照片

                archivesTransferInfo.setProjectName(projectName);
                archivesTransferInfo.setProjectId(projectId);
                archivesTransferInfo.setCreateId(createId);
                archivesTransferInfo.setCreateName(createName);
                archivesTransferInfo.setPtId(ptId);
                archivesTransferInfo.setTransDate(transDate);
                //archivesTransferInfo.setTransUserId(transUserId);
                archivesTransferInfo.setTransUserName(transUserName);
                archivesTransferInfo.setLegacyRemark(legacyRemark);
                //archivesTransferInfo.setLegacyUserId(legacyUserId);
                archivesTransferInfo.setLegacyUserName(legacyUserName);
                archivesTransferInfo.setLegacyDate(legacyDate);
                archivesTransferInfo.setProcessResult(processResult);
                archivesTransferInfo.setProjectOpinion(projectOpinion);
                archivesTransferInfo.setReceiveUnit(receiveUnit);
                archivesTransferInfo.setTransRemark(transRemark);
                archivesTransferInfo.setRemark(remark);
                archivesTransferInfo.setCreateTime(createTime);
                archivesTransferInfo.setTransType(transType);
                archivesTransferInfo.setWfId(wfId);
                archivesTransferInfo.setWfStatus(wfStatus);
                archivesTransferInfo.setPtEnclosure(ptEnclosure);

                presenter.submitApplyJGDAYJ(archivesTransferInfo,createId,projectId);

        }
    }
    /**
     * 展示日期选择对话框
     */
    private void showDatePickerDialog(final TextView tv) {
        Calendar c = Calendar.getInstance();
        new DatePickerDialog(mContext, new DatePickerDialog.OnDateSetListener() {
            @Override
            public void onDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                // TODO Auto-generated method stub
                int m = monthOfYear + 1;
                String month = m >= 10 ? m + "" : "0" + m;
                String day = dayOfMonth >= 10 ? dayOfMonth + "" : "0" + dayOfMonth;
                String dateStr = year + "-" + month + "-" + day;
                tv.setText(dateStr);
            }
        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)).show();
    }

    @Override
    public void applyJGDAYJBack(String str) {
        String[] backs = str.split(",");
        if (backs.length == 3) {
            ((ApplyActivity) getContext()).setBack(str);
        }
    }

    @Override
    public void applyJGDAYJInfoBack(ArchivesTransferInfo info) {
        if (info != null) {
            tvProjectname.setText(info.getProjectName());
            tvTransusername.setText(info.getTransUserName());
            tvTransdate.setText(info.getTransDate());
            etLegacyremark.setText(info.getLegacyRemark());
            tvLegacyusername.setText(info.getLegacyUserName());
            tvLegacydate.setText(info.getLegacyDate());
            etProcessresult.setText(info.getProcessResult());
            etProjectopinion.setText(info.getProjectOpinion());
            etRemark.setText(info.getRemark());
            tvCreateName.setText(info.getCreateName());
            tvCreatetime.setText(info.getCreateTime());

            images.clear();
            String[] imgArr = info.getPtEnclosure().split(",");
            for (String url : imgArr) {
                if (!StringUtil.isEmpty(url)||!url.equals(""))
                    images.add(url);
            }
            imageAdapter.notifyDataSetChanged();

        }
    }
    private AMapLocationClient locationClient = null;
    private AMapLocationClientOption locationOption = null;
    private void initLocation(){
        //初始化client
        locationClient = new AMapLocationClient(mContext);
        locationOption = getDefaultOption();
        //设置定位参数
        locationClient.setLocationOption(locationOption);
        // 设置定位监听
        locationClient.setLocationListener(locationListener);

    }
    /**
     * 默认的定位参数
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private AMapLocationClientOption getDefaultOption() {
        AMapLocationClientOption mOption = new AMapLocationClientOption();
        mOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);//可选，设置定位模式，可选的模式有高精度、仅设备、仅网络。默认为高精度模式
        mOption.setGpsFirst(false);//可选，设置是否gps优先，只在高精度模式下有效。默认关闭
        mOption.setHttpTimeOut(30000);//可选，设置网络请求超时时间。默认为30秒。在仅设备模式下无效
        mOption.setInterval(10000);//可选，设置定位间隔。默认为2秒
        mOption.setNeedAddress(true);//可选，设置是否返回逆地理地址信息。默认是true
        mOption.setOnceLocation(false);//可选，设置是否单次定位。默认是false
        mOption.setOnceLocationLatest(false);//可选，设置是否等待wifi刷新，默认为false.如果设置为true,会自动变为单次定位，持续定位时不要使用
        AMapLocationClientOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTP);//可选， 设置网络请求的协议。可选HTTP或者HTTPS。默认为HTTP
        mOption.setSensorEnable(false);//可选，设置是否使用传感器。默认是false
        mOption.setWifiScan(true); //可选，设置是否开启wifi扫描。默认为true，如果设置为false会同时停止主动刷新，停止以后完全依赖于系统刷新，定位位置可能存在误差
        mOption.setLocationCacheEnable(true); //可选，设置是否使用缓存定位，默认为true
        mOption.setGeoLanguage(AMapLocationClientOption.GeoLanguage.DEFAULT);//可选，设置逆地理信息的语言，默认值为默认语言（根据所在地区选择语言）
        return mOption;
    }
    /**
     * 定位监听
     */
    AMapLocationListener locationListener = new AMapLocationListener() {
        @Override
        public void onLocationChanged(AMapLocation location) {
            if (null != location) {
                locationdata = location;
                //errCode等于0代表定位成功，其他的为定位失败，具体的可以参照官网定位错误码说明
                if (location.getErrorCode() == 0) {
                    address=location.getAddress();
                    stopLocation();
                    destroyLocation();
                }
            } else {
                showToast("定位失败");
            }
        }
    };
    /**
     * 开始定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void startLocation() {
        // 设置定位参数
        locationClient.setLocationOption(locationOption);
        // 启动定位
        locationClient.startLocation();
    }
    /**
     * 停止定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void stopLocation() {
        // 停止定位
        locationClient.stopLocation();
    }

    /**
     * 销毁定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void destroyLocation() {
        if (null != locationClient) {
            /**
             * 如果AMapLocationClient是在当前Activity实例化的，
             * 在Activity的onDestroy中一定要执行AMapLocationClient的onDestroy
             */
            locationClient.onDestroy();
            locationClient = null;
            locationOption = null;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        //初始化定位
        initLocation();
        //启动定位
        startLocation();
    }
}
