package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.viewpager.widget.ViewPager;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.MainFrameAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.FunctionPermission;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.entity.httpresult.MonthReportListResult;
import com.kisoft.yuejianli.hdmh.HdmhApplyFormPageConfigData;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.scgl.ScglApplyFormPageConfigData;
import com.kisoft.yuejianli.ui.NoScrollViewPager;
import com.kisoft.yuejianli.ui.YSelectTextViewCell;
import com.kisoft.yuejianli.ui.badgeview.YBadgeImageView;
import com.kisoft.yuejianli.ui.dragview.FloatLayer;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import org.xutils.common.util.LogUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Description: 流程审批基本信息  审批办理
 * Author     : bhd119
 * QQ         : 602394773
 */
public class ApplyActivity extends BaseActivity {
    private static final String TAG = "ApplyActivity";
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rb_task_detail)
    RadioButton rbTaskDetail;
    @BindView(R.id.rb_task_record)
    RadioButton rbTaskRecord;
    @BindView(R.id.rg_title)
    RadioGroup rgTitle;
    @BindView(R.id.view_task_detail)
    View viewTaskDetail;
    @BindView(R.id.view_task_record)
    View viewTaskRecord;
    @BindView(R.id.vp_mainframe_content)
    NoScrollViewPager vpMainframeContent;

    // 附件
    @BindView(R.id.rb_task_attachment)
    RadioButton rbTaskAttachment;

    @BindView(R.id.view_task_attachment)
    View viewTaskAttachment;

    @BindView(R.id.tv_submit)
    TextView tvSubmit;

    public static final String businessTypes = "t_org_empleave,t_org_empsupple,T_ORG_OFFICIAL_APPLY,t_org_empdimission,T_BUSINESS_TRAVEL,t_org_emploan,t_org_empreim,t_con_payment,t_cvs_sealuseapply,t_inv_docborrowapp,t_inv_boundaccpt,t_con_info,t_con_settlapply,t_supervision_appeal,T_CANCELLATION_APPLICATION,T_Doc_reArchives,T_OA_OTHER_COST_BUDGET,T_OA_UNION_REIMBURSEMENT,T_OA_INVOICE_ISSUANCE,T_OA_LOAN_APPLY,T_OA_PAYMENT_APPLY,T_OA_BAIL_PAYMENT,T_OA_GUARANTEE_PAYMENT,T_OA_NETWORK_APPLY,T_OA_BUS_RECEPTION_QH,T_OA_BUS_RECEPTION,T_OA_TEMPORARY_RECEPTION,T_OA_LIN_JIAN_APPLY,T_OA_HOUSING_LEASE,T_OA_LONG_RENTAL_HOTEL,T_OA_CAR_RENTAL,T_OA_CAR_MAINTENANCE,T_OA_CAR_ETC,T_OA_EQUIPMENT_RENTAL,T_OA_TRAINING_FEES,T_OA_CONFERENCE_FEE,T_OA_WORK_OVERTIME,T_OA_ORGAN_PROJECT_ASSETS,T_OA_POST_REASON,T_OA_GO_OUT,T_OA_SNAP_BLEND,T_OA_ENGRAVED_CHAPTER,T_OA_BUY_CONTRACT,T_OA_LAW_CHECK,T_OA_COMPANY_TAX,T_OA_TECH_TREATMENT,T_OA_PHOTO_CHECK,T_OA_CHECK_EXPERT,T_OA_COVID_19,T_OA_DISSERTATION,T_OA_PRO_NEED,T_OA_COMPANY_PRO_TAX,T_OA_COMPANY_PRO_CASH,T_OA_MAINTENANCE_RENOVATION,T_OA_FIELD_TESTING,T_OA_PERSONNEL_LEAVE,T_OA_RELOCATION_APPLY,T_OA_HOUSE_RENOVATION,T_OA_PRINT_BINDING,T_PDS_MANUSCRIPT_SUBMISSION,T_PROJECT_SHUTDOWN,T_CONTRACT_STOP,T_PDS_VEHICLE_ALLOCATION,t_cvs_carmaintain,t_cvs_carapply,T_PDS_VEHICLE_REFUELING,T_CHANGE_PERSON_CARD,T_OA_POST_UP,T_PROJ_LOCK_PERSON,T_ASSE_ANNUPUN,T_ASSE_ANNUAWARD,T_CERTIFICATE_FEE_APPLY,T_OA_AGENCY_REIMBURSEMENT,T_OA_PROJECT_REIMBURSEMENT,T_OA_MANAGE,T_OA_PRODUCTION_ASSIGNMENT,T_OA_CONTRACT_HANDOVER,T_OA_COUNTERSIGNED,T_PROMISE_PAY_APPLY,T_OA_SUBCONTRACT_PAYMENT,T_OA_CONTRACT_APPROVE,T_OA_EMPLOYEE_PROMOTION,T_OA_BACKLETTER,T_OA_SEAL_TAPE,T_OA_CANTEEN_VEHICLE,T_OA_OFFICE_SUPPLIES,T_OA_DAILY_NECESSITIES,T_OA_LABOR_DEFENSE,T_OA_PREVENTION,T_OA_ASSET_REGISTRATION,T_OA_ASSET_ACCEPTANCE,T_OA_QUALITY_EVALUATION,T_OA_CERTIFICATE_BORROWING,T_FORM_DATA_STORE,T_OA_CAR_APPLY,";

    public static String businessTypes() {
       return HdmhApplyFormPageConfigData.getAllBusinessTypes() + businessTypes;
    }

    /**
     * 导航的fragment界面集合
     **/
    private List<Fragment> list_fragments = new ArrayList<>();

    /**
     * 适配器
     **/
    private MainFrameAdapter adapter = null;

    /**
     * 页面类型  true：发起申请  false：申请详情
     */
    public boolean isApply;

    /**
     * 申请类型     0：员工请假  1：借款申请  2：补签申请      3：费用报销
     * 4：离职申请  5：开票申请  6：用印申请  7：保证金申请    8：证件借用申请  9：现场信息申诉（日志、月报）
     */

    //流程类型（1=待审批、2=已审批、3=我发起的）
    public String workType;

    //业务类型
    private String businessType;
    //回调名称
    public String callBackName;


    public ProcessListBean bean;

    private String titleName;

    public String transType;

    public String flowStateName;

    //private boolean isMonthReport=false;
    public MonthReportListResult.DataBean monthReportBean;


    CardView floatCardView = null;
    FloatLayer floatLayer = null;


    CardView floatCardView1 = null;
    FloatLayer floatLayer1 = null;
    private Map<String, Object> zfData;
    YSelectTextViewCell lczhuanfa;
    private String mTitle;

    @Override
    public int getLayoutId() {
        return R.layout.activity_document_detail;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        isApply = getIntent().getBooleanExtra("isApply", true);
        businessType = getIntent().getStringExtra("businessType");
        mTitle = getIntent().getStringExtra("title");

        workType = getIntent().getStringExtra("workType");
        transType = getIntent().getStringExtra("transType");
        flowStateName = getIntent().getStringExtra("flowStateName");
        //isMonthReport=getIntent().getBooleanExtra("isMonthReport",false);
        bean = (ProcessListBean) getIntent().getSerializableExtra("bean");
        String pageState = getIntent().getStringExtra("pageState");
       /* if(isMonthReport){
            monthReportBean=(MonthReportListResult.DataBean) getIntent().getSerializableExtra("bean");
        }else{
            bean = (ProcessListBean) getIntent().getSerializableExtra("bean");
        }*/
        initData();
        initView();


        FunctionPermission permissionFunction = SettingManager.getInstance().getPermissionFunction();
        if (permissionFunction != null && permissionFunction.isHasProcessMenu()) {
            if (!isApply) {
                initMenuPopView();
                initZhuanfaPopView();
                tvSubmit.setVisibility(View.VISIBLE);
                tvSubmit.setText("菜单");
            }
            // 转发查阅，送阅查阅
            if (StringUtil.isEqual("1", pageState) || StringUtil.isEqual("2", pageState)) {
                tvSubmit.setVisibility(View.GONE);
            }
        } else {
            tvSubmit.setVisibility(View.GONE);
        }


    }


    // 弹窗
    @OnClick(R.id.tv_submit)
    public void popMenu() {
        if (floatLayer == null) {
            initMenuPopView();
        } else {
            floatLayer.show(false);
        }
    }


    private void initMenuPopView() {

        //View inflate = LayoutInflater.from(mContext).inflate(R.layout.pop_bottom_menu, null, false);
        View headerView = LayoutInflater.from(mContext).inflate(R.layout.ease_search_bar, null);
        View inflate = LayoutInflater.from(mContext).inflate(R.layout.pop_bottom_menu, null);

        floatCardView = new CardView(mContext);
        floatCardView.addView(inflate);
        floatCardView.setCardBackgroundColor(Color.TRANSPARENT);
        floatCardView.setAlpha(0.5f);

        floatLayer = new FloatLayer(mContext);
        floatLayer.floatView(floatCardView);
        floatLayer.snapEdge(FloatLayer.Edge.NONE);
        floatLayer.outside(false);
        floatLayer.defPercentX(1);
        floatLayer.defPercentY(1F);
        floatLayer.paddingLeft(0);
        floatLayer.paddingTop(0);
        floatLayer.paddingRight(0);
        floatLayer.paddingBottom(0);
        floatLayer.marginLeft(0);
        floatLayer.marginTop(0);
        floatLayer.marginRight(0);
        floatLayer.marginBottom(0);

        FrameLayout pbm_bg_clear = inflate.findViewById(R.id.pbm_bg_clear);
        pbm_bg_clear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer.dismiss(false);
            }
        });

        // 流程转发
        YBadgeImageView lczhuanfa = inflate.findViewById(R.id.lczhuanfa);
        lczhuanfa.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer.dismiss(false);
                zfData = new HashMap<>();
                // 转发
                if (floatLayer1 == null) {
                    initZhuanfaPopView();
                } else {
                    floatLayer1.show(false);
                }
            }
        });

        // 流程撤回
        YBadgeImageView lcchehui = inflate.findViewById(R.id.lcchehui);
        lcchehui.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer.dismiss(false);
                lcchehui();
            }
        });

        // 流程催办
        YBadgeImageView lcchuiban = inflate.findViewById(R.id.lcchuiban);
        lcchuiban.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer.dismiss(false);
                lcChuiBan();
            }
        });

        // 流程记录
        YBadgeImageView lcjilu = inflate.findViewById(R.id.lcjilu);
        lcjilu.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer.dismiss(false);
                YApprovalLogsActivity.launch(ApplyActivity.this, 1, bean.getFlowId(), bean.getBusinessId());
            }
        });

        // 审批日志
        YBadgeImageView lcrizhi = inflate.findViewById(R.id.lcrizhi);
        lcrizhi.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer.dismiss(false);
                YApprovalLogsActivity.launch(ApplyActivity.this, 2, bean.getFlowId(), bean.getBusinessId());
            }
        });

        // 转发记录
        YBadgeImageView zhuanfajilu = inflate.findViewById(R.id.zhuanfajilu);
        zhuanfajilu.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer.dismiss(false);
                YApprovalLogsActivity.launch(ApplyActivity.this, 3, bean.getFlowId(), bean.getBusinessId());
            }
        });


        //流程类型（1=待审批、2=已审批、3=我发起的）
        if (StringUtil.isEqual("1", workType)) {
            lczhuanfa.setEnabled(true);
            lcjilu.setEnabled(true);
            lcrizhi.setEnabled(true);
            zhuanfajilu.setEnabled(true);
            lcchuiban.setEnabled(false);
            lcchehui.setEnabled(false);
        } else if (StringUtil.isEqual("2", workType)) {
            lczhuanfa.setEnabled(true);
            lcjilu.setEnabled(true);
            lcrizhi.setEnabled(true);
            zhuanfajilu.setEnabled(true);
            lcchuiban.setEnabled(true);
            lcchehui.setEnabled(false);
        } else if (StringUtil.isEqual("3", workType)) {
            lczhuanfa.setEnabled(true);
            lcjilu.setEnabled(true);
            lcrizhi.setEnabled(true);
            zhuanfajilu.setEnabled(true);
            lcchuiban.setEnabled(true);
            lcchehui.setEnabled(true);
        }

        if (!isApply) {
            if (StringUtil.isEqual("完成", bean.getFlowStateName()) || StringUtil.isEqual("已终止",
                    bean.getFlowStateName())) {
                lcchuiban.setEnabled(false);
                lcchehui.setEnabled(false);
            }
        }
    }

    private void initZhuanfaPopView() {

        LayoutInflater layoutInflater = LayoutInflater.from(mContext);
        View inflate = layoutInflater.inflate(R.layout.pop_menu_lczhuanfa, null, false);
        floatCardView1 = new CardView(mContext);
        floatCardView1.addView(inflate);
        floatCardView1.setCardBackgroundColor(Color.TRANSPARENT);
        floatCardView1.setAlpha(0.5f);

        floatLayer1 = new FloatLayer(mContext);
        floatLayer1.floatView(floatCardView1);
        floatLayer1.snapEdge(FloatLayer.Edge.NONE);
        floatLayer1.outside(false);
        floatLayer1.defPercentX(1);
        floatLayer1.defPercentY(1F);
        floatLayer1.paddingLeft(0);
        floatLayer1.paddingTop(0);
        floatLayer1.paddingRight(0);
        floatLayer1.paddingBottom(0);
        floatLayer1.marginLeft(0);
        floatLayer1.marginTop(0);
        floatLayer1.marginRight(0);
        floatLayer1.marginBottom(0);


        FrameLayout pbm_bg_clear = inflate.findViewById(R.id.pbm_bg_clear);
        pbm_bg_clear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer1.dismiss(false);
            }
        });


        lczhuanfa = inflate.findViewById(R.id.lcZhuanfaCell);
        // 转发人
        lczhuanfa.getSelectView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent2 = new Intent();
                intent2.setClass(mContext, CompanyOrgInfoActivity.class);
                startActivityForResult(intent2, 2369);
            }
        });

        EditText et_remark = (EditText) inflate.findViewById(R.id.et_remark);

        TextView tv_sub = (TextView) inflate.findViewById(R.id.tv_sub);
        tv_sub.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                lczhuanfa(et_remark.getText().toString());
            }
        });
    }


    // 流程转发
    private void lczhuanfa(String s) {
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        zfData.put("remark", s);
        zfData.put("createId", userInfo.getId());
        zfData.put("createName", userInfo.getName());
        zfData.put("createTime", DateUtil.dateToString(new Date(), DateUtil.YMD_HM));
        zfData.put("businessId", bean.getBusinessId());
        zfData.put("businessType", bean.getBusinessType());
        zfData.put("state", "0");
        zfData.put("fileName", bean.getName());
        zfData.put("wfId", bean.getFlowId());
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("data", StringUtil.objectToJson(zfData));
        Api.getGbkApiserver().saveSysForwardRecord("saveSysForwardRecord_APP", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    showToast("转发成功");
                    floatLayer1.dismiss(false);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }


    // 流程撤回
    private void lcchehui() {
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("wfId", bean.getFlowId());
        parameters.put("userId", userInfo.getId());
        Api.getGbkApiserver().withdrawnew("withdraw_new", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    showToast(response.body().getData().toString());
                    floatLayer.dismiss(false);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    // 流程催办
    private void lcChuiBan() {
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("wfId", bean.getFlowId());
        parameters.put("userId", userInfo.getId());

        Api.getGbkApiserver().hastenWork("hastenWork", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    showToast("催办成功");
                    floatLayer.dismiss(false);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    private void initData() {
        rbTaskAttachment.setVisibility(View.GONE);
        viewTaskAttachment.setVisibility(View.GONE);

        ApplyOtherFragment fragment = new ApplyOtherFragment();
        List data = new ArrayList();
        if (businessType.equals("t_org_empleave")) {
            if (SettingManager.getInstance().getCompanyCode().equals("hdmh") || SettingManager.getInstance().getCompanyCode().equals("local")) {
                //华东-员工请假
                tvTitle.setText("员工请假");
//                fragment.saveType = "saveOaOtherCostBudget";
//                fragment.getType = "getOaOtherCostBudgetById";
//                fragment.businessType = "T_OA_OTHER_COST_BUDGET";
                callBackName = "hr_WFCallBackService";
//                data = new ArrayList() {{
//                    add(new HashMap() {{ put("title", "标题:");put("key", "projectDepartment");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "申请日期:");put("key", "officeNumber");put("viewType", Constant.VIEWTYPE_DATESEL_CELL); }});
//                    add(new HashMap() {{ put("title", "请假日期（起）:");put("key", "numExpense");put("viewType", Constant.VIEWTYPE_DATESEL_CELL); }});
//                    add(new HashMap() {{ put("title", "请假日期（止）:");put("key", "numExpense");put("viewType", Constant.VIEWTYPE_DATESEL_CELL); }});
//                    add(new HashMap() {{ put("title", "请假时间（起）:");put("key", "numExpense");put("viewType", Constant.VIEWTYPE_DATESEL_CELL); }});
//                    add(new HashMap() {{ put("title", "请假时间（止）:");put("key", "numExpense");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
//                    add(new HashMap() {{ put("title", "请假天数:");put("key", "numExpense");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
//                    add(new HashMap() {{ put("title", "外出地点:");put("key", "numExpense");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
//                    add(new HashMap() {{ put("title", "请假事由:");put("key", "numExpense");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
//
//                    add(new HashMap() {{ put("title", "病假:");put("key", "d1");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "哺乳假:");put("key", "d2");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "产假:");put("key", "d3");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "公假:");put("key", "d4");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "护理假:");put("key", "d5");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "婚假:");put("key", "d6");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "计划生育手术假:");put("key", "d7");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "年休假:");put("key", "d8");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "女工假:");put("key", "d9");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "其他:");put("key", "d10");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "丧假:");put("key", "d11");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "生育假:");put("key", "d12");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "事假:");put("key", "d13");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "探亲假:");put("key", "d14");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "停工留薪期:");put("key", "d15");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "育儿假:");put("key", "d16");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "路程假:");put("key", "d17");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "法定节日:");put("key", "d18");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                    add(new HashMap() {{ put("title", "休息日:");put("key", "d19");put("keyboardType", "number");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
//                }};
//                fragment.setData(data);
                list_fragments.add(new ApplyYGQJHdmhFragment());
                if (isApply) {
                    list_fragments.add(new ApplySPFragment());
                } else {
                    list_fragments.add(new ApplySPFragment1());
                }

            }else{
                list_fragments.add(new ApplyYGQJFragment());
                tvTitle.setText("员工请假");
                callBackName = "hr_WFCallBackService";
                if (isApply) {
                    list_fragments.add(new ApplySPFragment());
                } else {
                    list_fragments.add(new ApplySPFragment1());
                }
            }
        }


        if (businessType.equals("T_Doc_reArchives")) {
            list_fragments.add(new ShouWenDJNewFragment());
            tvTitle.setText("收文登记");
            callBackName = "roa_wfCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        if (businessType.equals("t_org_emploan")) {
            list_fragments.add(new ApplyJKFragment());
            tvTitle.setText("借款申请");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        if (businessType.equals("t_org_empsupple")) {
            list_fragments.add(new ApplyBQFragment());
            tvTitle.setText("补签申请");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        if (businessType.equals("t_org_empreim")) {
            list_fragments.add(new ApplyFYBXFragment());
            tvTitle.setText("费用报销");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        if (businessType.equals("t_org_empdimission")) {
            list_fragments.add(new ApplyLZFragment());
            tvTitle.setText("离职申请");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        if (businessType.equals("t_con_payment")) {
            list_fragments.add(new ApplyKPFragment());
            tvTitle.setText("开票申请");
            callBackName = "ConWFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }
        if (businessType.equals("t_cvs_sealuseapply")) {
            list_fragments.add(new ApplyUseSealFragment());
            tvTitle.setText("用印申请");
            callBackName = "sealUseWFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        if (businessType.equals("T_SUPERVISION_SETTLEMENT")) {
            //  T_SUPERVISION_SETTLEMENT//公司咨询费用结算
            ProcessWebFragment webFragment = new ProcessWebFragment();
            Bundle bundle = new Bundle();
            // bundle.putString("url", SettingManager.getInstance().getBaseUrl() + "pds/SupervisionSettlementAction
            // .do?method=toSupervisionSettlementModify&ids="+ bean.getBusinessId() + "&pid=2");
//            bundle.putString("url", "http://192.168.0.107:8080/kaiyue/pds/SupervisionSettlementAction
//            .do?method=toSupervisionSettlementModify&ids="+ bean.getBusinessId() + "&pid=2");
            UserInfo userInfo = SettingManager.getInstance().getUserInfo();
            String urls = SettingManager.getInstance().getBaseUrl() + "pds/SupervisionSettlementAction" +
                    ".do?method=toSupervisionSettlementModify&ids=" + bean.getBusinessId() + "&pid=3&workflow_type=" + bean.getWfType() + "&userId=" + userInfo.getId() + "&wfTaskId=" + bean.getFlowTaskId() + "&projectId=" + userInfo.getProjectId();
            bundle.putString("url", urls);
            bundle.putString("bid", bean.getBusinessId());
            bundle.putString("bType", "T_SUPERVISION_SETTLEMENT");
            webFragment.setArguments(bundle);
            list_fragments.add(webFragment);
            tvTitle.setText("公司咨询费用结算");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        if (businessType.equals("T_PROD_WEEKLY_REPORT")) {
            //  T_SUPERVISION_SETTLEMENT//周报
            ProcessWebFragment webFragment = new ProcessWebFragment();
            Bundle bundle = new Bundle();
            // bundle.putString("url", SettingManager.getInstance().getBaseUrl() + "pds/SupervisionSettlementAction
            // .do?method=toSupervisionSettlementModify&ids="+ bean.getBusinessId() + "&pid=2");
//            bundle.putString("url", "http://192.168.0.107:8080/kaiyue/pds/SupervisionSettlementAction
//            .do?method=toSupervisionSettlementModify&ids="+ bean.getBusinessId() + "&pid=2");
            UserInfo userInfo = SettingManager.getInstance().getUserInfo();
            String urls = SettingManager.getInstance().getBaseUrl() + "pds/ProdWeeklyReportAction" +
                    ".do?method=toProdWeeklyReportModifyApp&ids=" + bean.getBusinessId() + "&pid=3&workflow_type=" + bean.getWfType() + "&userId=" + userInfo.getId() + "&wfTaskId=" + bean.getFlowTaskId() + "&projectId=" + userInfo.getProjectId();
            bundle.putString("url", urls);
            bundle.putString("bid", bean.getBusinessId());
            bundle.putString("bType", "T_PROD_WEEKLY_REPORT");
            webFragment.setArguments(bundle);
            list_fragments.add(webFragment);
            tvTitle.setText("周报");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }
        if (businessType.equals("T_OA_OVERTIME")) {
            //  T_SUPERVISION_SETTLEMENT//项目部人员加班计划审批表
            ProcessWebFragment webFragment = new ProcessWebFragment();
            Bundle bundle = new Bundle();
            // bundle.putString("url", SettingManager.getInstance().getBaseUrl() + "pds/SupervisionSettlementAction
            // .do?method=toSupervisionSettlementModify&ids="+ bean.getBusinessId() + "&pid=2");
//            bundle.putString("url", "http://192.168.0.107:8080/kaiyue/pds/SupervisionSettlementAction
//            .do?method=toSupervisionSettlementModify&ids="+ bean.getBusinessId() + "&pid=2");
            UserInfo userInfo = SettingManager.getInstance().getUserInfo();
            String urls = SettingManager.getInstance().getBaseUrl() + "roa/OaOvertimeAction" +
                    ".do?method=toOaOvertimeModifyApp&ids=" + bean.getBusinessId() + "&pid=3&workflow_type=" + bean.getWfType() + "&userId=" + userInfo.getId() + "&wfTaskId=" + bean.getFlowTaskId() + "&projectId=" + userInfo.getProjectId();
            bundle.putString("url", urls);
            bundle.putString("bid", bean.getBusinessId());
            bundle.putString("bType", "T_OA_OVERTIME");
            webFragment.setArguments(bundle);
            list_fragments.add(webFragment);
            tvTitle.setText("项目部人员加班计划审批表");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        if (businessType.equals("t_inv_boundaccpt")) {
            list_fragments.add(new ApplyBZJFragment());
            tvTitle.setText("保证金申请");
            callBackName = "tenderWFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        if (businessType.equals("t_inv_docborrowapp")) {
            list_fragments.add(new ApplyZJJYFragment());
            tvTitle.setText("证件借用申请");
            callBackName = "tenderWFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        if (businessType.equals("t_sys_projecttrans")) {
            if (transType.equals("0")) {
                list_fragments.add(new ApplyJGDAYJFragment());
                tvTitle.setText("竣工档案移交");
                callBackName = "tenderWFCallBackService";
                if (isApply) {
                    list_fragments.add(new ApplySPFragment());
                } else {
                    list_fragments.add(new ApplySPFragment1());
                }
            } else if (transType.equals("1")) {
                list_fragments.add(new ApplyWBZLYJFragment());
                tvTitle.setText("外部资料移交");
                callBackName = "tenderWFCallBackService";
                if (isApply) {
                    list_fragments.add(new ApplySPFragment());
                } else {
                    list_fragments.add(new ApplySPFragment1());
                }
            }
        }


        // "质量评估报告
        if (businessType.equals("T_QUALITY_ASSESSMENT")) {
            list_fragments.add(new ApplyZLPGBGFragment());
            tvTitle.setText("质量评估报告");
            //这块要修改
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 分部工程验收
        if (businessType.equals("T_BRANCH_ACCEPTANCE")) {
            list_fragments.add(new ApplyFenbuFragment());
            tvTitle.setText("分部工程验收");
            //这块要修改
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 分项工程验收
        if (businessType.equals("T_SUB_PROJECT_ACCEPTANCE")) {
            list_fragments.add(new ApplyFenbuFragment());
            tvTitle.setText("分项工程验收");
            //这块要修改
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 危大工程验收
        if (businessType.equals("T_DANGEROUS_ACCEPTANCE")) {
            list_fragments.add(new ApplyDangerFragment());
            tvTitle.setText("危大工程验收");
            //这块要修改
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 工序报验
        if (businessType.equals("T_GONGXU_BAOYAN")) {
            tvTitle.setText("工序报验");
            fragment.saveType = "saveGongxuBaoyan";
            fragment.getType = "getGongxuBaoyanById";
            fragment.businessType = "T_GONGXU_BAOYAN";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "监理单位:");
                    put("key", "pjjgsjdw");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "完成工作:");
                    put("key", "finishWork");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        //现场信息申诉(日志、月报)
        if (businessType.equals("t_supervision_appeal")) {
            list_fragments.add(new ApplyXXSSFragment());
            tvTitle.setText("现场信息申诉(日志、月报)");
            callBackName = "tenderWFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        if (businessType.equals("t_monthly_supervision")) {
            list_fragments.add(new ApplyJLYBFragment());
            tvTitle.setText("监理月报");
            callBackName = "tenderWFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }
//            case "t_con_info")) {
//                list_fragments.add(new ApplyHTSPFragment());
//                tvTitle.setText("合同审批");
//                callBackName = "ConWFCallBackService";
//                if (isApply) {
//                    list_fragments.add(new HdmhApplySQFragment());
//                } else {
//                    list_fragments.add(new HdmhApplySPFragment());
//                }
//                break;
        if (businessType.equals(InitiateProcessFragment.BUSINESS_TRAVEL_TYPE)) {
            list_fragments.add(new ApplyBusinessTravelFragment());
            tvTitle.setText("出差申请");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }
        if (businessType.equals(InitiateProcessFragment.OFFICIAL_APPLY_TYPE)) {
            list_fragments.add(new ApplyOfficialFragment());
            tvTitle.setText("转正申请");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }
        if (businessType.equals(InitiateProcessFragment.CON_APPLY_TYPE)) {
            list_fragments.add(new ApplyConFragment());
            tvTitle.setText("合同申请");
            callBackName = "ConWFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }
        if (businessType.equals(InitiateProcessFragment.CON_SETTL_TYPE)) {
            list_fragments.add(new ApplyConSettlFragment());
            tvTitle.setText("子合同结算");
            callBackName = "ConWFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        /*
        if (businessType.equals( "T_Doc_reArchives")) {
            tvTitle.setText("收文待办");
            fragment.saveType = "";
            fragment.getType = "getReAchivesById";
            callBackName = "hr_WFCallBackService";
            //初始化List
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "形成年度:");put("key", "curYear");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "承办部门:");put("key", "cbdept");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "收文序号:");put("key", "raNo");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "文件日期:");put("key", "fileDate");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "收文分类:");put("key", "rfName");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "标题:");   put("key","title");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "来文单位:");put("key", "company");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "密级:");   put("key","security");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "文件字号:");put("key", "fileNo");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "保密期限:");put("key", "securityTime");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "文件份数:");put("key", "fileNumber");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "承办人:");  put("key","userid");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "受控号:");  put("key","skNo");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "登记部门:");put("key", "deptId");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "登记日期:");put("key", "createDate");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "查阅人:");  put("key","ffNames");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new HdmhApplySQFragment());
            } else {
                list_fragments.add(new HdmhApplySPFragment());
            }
        }
        */

        if (businessType.equals("T_OA_OTHER_COST_BUDGET")) {
            tvTitle.setText("其他费用预算申请");
            fragment.saveType = "saveOaOtherCostBudget";
            fragment.getType = "getOaOtherCostBudgetById";
            fragment.businessType = "T_OA_OTHER_COST_BUDGET";
            callBackName = "hr_WFCallBackService";
            //初始化List
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "预算费用①:");
                    put("key", "fee1");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "预算费用②:");
                    put("key", "fee2");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "预算费用③:");
                    put("key", "fee3");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "预算费用④:");
                    put("key", "fee4");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "预算费用⑤:");
                    put("key", "fee5");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "预算费用⑥:");
                    put("key", "fee6");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "预算费用⑦:");
                    put("key", "fee7");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "预算费用⑧:");
                    put("key", "fee8");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "预算费用⑨:");
                    put("key", "fee9");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请事由:");
                    put("key", "reason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        //民航-工会委员会报销单
        if (businessType.equals("T_OA_UNION_REIMBURSEMENT")) {
            tvTitle.setText("工会委员会报销");
            fragment.saveType = "saveOaUnionReimbursement";
            fragment.getType = "getOaUnionReimbursementById";
            fragment.businessType = "T_OA_UNION_REIMBURSEMENT";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "报销事由（请分类填写）:");
                    put("key", "reason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "收款人:");
                    put("key", "payee");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "收款账号:");
                    put("key", "receivingAccount");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "开户行:");
                    put("key", "accountBank");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "票据张数:");
                    put("key", "numberOfBills");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经办人:");
                    put("key", "manager");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }
        //民航-发票开具申请
        if (businessType.equals("T_OA_INVOICE_ISSUANCE")) {
            tvTitle.setText("发票开具申请");
            fragment.saveType = "saveOaInvoiceIssuance";
            fragment.getType = "getOaInvoiceIssuanceById";
            fragment.businessType = "T_OA_INVOICE_ISSUANCE";
            callBackName = "hr_WFCallBackService";
            List<String> list1 = new ArrayList() {{
                add("增值税专用发票");
                add("增值税普通发票");
                add("红字发票");
            }};
            List<String> list2 = new ArrayList() {{
                add("自取");
                add("邮寄");
                add("送达");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目负责人:");
                    put("key", "projectManager");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "发票类型:");
                    put("key", "invoiceType");
                    put("data", list1);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "发票金额大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "发票金额小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用类型:");
                    put("key", "feeType");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "付款单位名称:");
                    put("key", "paymentUnitName");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "付款单位税号:");
                    put("key", "paymentUnitTaxNumber");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "付款单位开户行:");
                    put("key", "paymentUnitBank");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "付款单位银行账号:");
                    put("key", "paymentUnitBankAccount");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "付款方地址及电话:");
                    put("key", "addressAndPhone");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "电子邮箱:");
                    put("key", "mailbox");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "红字发票开具说明:");
                    put("key", "explain");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "备注:");
                    put("key", "remark");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //民航-借款申请
        if (businessType.equals("T_OA_LOAN_APPLY")) {
            tvTitle.setText("借款申请");
            fragment.saveType = "saveOaLoanApply";
            fragment.getType = "getOaLoanApplyById";
            fragment.businessType = "T_OA_LOAN_APPLY";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "借款人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "借款金额大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "借款金额小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "收款人:");
                    put("key", "payee");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经办人:");
                    put("key", "manager");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "收款账号:");
                    put("key", "receivingAccount");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "开户行:");
                    put("key", "accountBank");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "借款明细:");
                    put("key", "loanDetails");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //民航-付款申请
        if (businessType.equals("T_OA_PAYMENT_APPLY")) {
            tvTitle.setText("付款申请");
            fragment.saveType = "saveOaPaymentApply";
            fragment.getType = "getOaPaymentApplyById";
            fragment.businessType = "T_OA_PAYMENT_APPLY";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "借款人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "金额大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "金额小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "收款单位名称:");
                    put("key", "receivingUnitName");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "对方开户银行:");
                    put("key", "accountBank");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "对方账号:");
                    put("key", "receivingAccount");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //民航-履约保证金付款申请
        if (businessType.equals("T_OA_BAIL_PAYMENT")) {
            tvTitle.setText("履约保证金付款申请");
            fragment.saveType = "saveOaBailPayment";
            fragment.getType = "getOaBailPaymentById";
            fragment.businessType = "T_OA_BAIL_PAYMENT";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "工程开工时间:");
                    put("key", "projectStartTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同完工时间:");
                    put("key", "completionTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同金额:");
                    put("key", "contractAmount");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "占合同金额比例:");
                    put("key", "amountRatio");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "金额大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "金额小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "保证金收款单位名称:");
                    put("key", "receivingUnitName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "收款单位开户行:");
                    put("key", "accountBank");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "收款单位银行账号:");
                    put("key", "receivingAccount");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //民航-银行保函付款申请单
        if (businessType.equals("T_OA_GUARANTEE_PAYMENT")) {
            tvTitle.setText("银行保函付款申请单");
            fragment.saveType = "saveOaGuaranteePayment";
            fragment.getType = "getOaGuaranteePaymentById";
            fragment.businessType = "T_OA_GUARANTEE_PAYMENT";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList() {{
                add("履约保函");
                add("预付款保函");
                add("质保金保函");
                add("见索即付保函");
                add("投标保函");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "工程开工时间:");
                    put("key", "projectStartTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同完工时间:");
                    put("key", "completionTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同金额:");
                    put("key", "contractAmount");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "占合同金额比例:");
                    put("key", "amountRatio");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "金额大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "金额小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "保函类别:");
                    put("key", "guaranteeType");
                    put("data", list);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "保函截止时间:");
                    put("key", "guaranteeDeadline");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "保函受益人联系方式:");
                    put("key", "beneficiaryPhone");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "保函受益人地址:");
                    put("key", "beneficiaryAddress");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "保函受益人:");
                    put("key", "beneficiary");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "取函方式:");
                    put("key", "wayOfPickup");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "收件人及电话:");
                    put("key", "recipientAndPhone");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "邮寄地址:");
                    put("key", "mailingAddress");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-网络通讯申请单
        if (businessType.equals("T_OA_NETWORK_APPLY")) {
            tvTitle.setText("网络通讯申请");
            fragment.saveType = "saveOaNetworkApply";
            fragment.getType = "getOaNetworkApplyById";
            fragment.businessType = "T_OA_NETWORK_APPLY";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList() {{
                add("宽带");
                add("光纤");
                add("无线宽带");
                add("其他");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "开始使用时间:");
                    put("key", "startUseTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "结束使用时间:");
                    put("key", "endUseTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "网络类型:");
                    put("key", "networkType");
                    put("data", list);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "网络宽带:");
                    put("key", "internetBroadband");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "运营商:");
                    put("key", "operator");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-启航预约业务招待申请单
        if (businessType.equals("T_OA_BUS_RECEPTION_QH")) {
            tvTitle.setText("启航预约业务招待申请");
            fragment.saveType = "saveOaBusReceptionQh";
            fragment.getType = "getOaBusReceptionQhById";
            fragment.businessType = "T_OA_BUS_RECEPTION_QH";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList() {{
                add("重要来宾");
                add("专家");
                add("一般来宾");
                add("其他");
            }};
            List<String> list1 = new ArrayList() {{
                add("机关人员");
                add("项目部人员");
                add("项目部负责人");
                add("部门经理");
                add("公司领导");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请人级别:");
                    put("key", "applicantLevel");
                    put("data", list1);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "招待部门（项目）:");
                    put("key", "receptionDept");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "招待时间:");
                    put("key", "receptionTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "来访单位:");
                    put("key", "visitingUnit");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "来宾级别:");
                    put("key", "guestLevel");
                    put("data", list);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "参加人数:");
                    put("key", "participantsNumber");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-预约业务招待申请单
        if (businessType.equals("T_OA_BUS_RECEPTION")) {
            tvTitle.setText("预约业务招待申请");
            fragment.saveType = "saveOaBusReception";
            fragment.getType = "getOaBusReceptionById";
            fragment.businessType = "T_OA_BUS_RECEPTION";
            callBackName = "hr_WFCallBackService";
            List<String> list1 = new ArrayList() {{
                add("机关人员");
                add("项目部人员");
                add("项目部负责人");
                add("部门经理");
                add("公司领导");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请人级别:");
                    put("key", "applicantLevel");
                    put("data", list1);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "来访事由:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "来访单位:");
                    put("key", "visitingUnit");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "来访日期:");
                    put("key", "dateOfVisit");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "来访人数:");
                    put("key", "numberOfVisitors");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "陪同人数:");
                    put("key", "numberOfEscorts");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算（餐饮）:");
                    put("key", "repast");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算（交通）:");
                    put("key", "traffic");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算（住宿）:");
                    put("key", "stay");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算（纪念品）:");
                    put("key", "souvenir");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算（其他）:");
                    put("key", "other");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-临时招待情况说明
        if (businessType.equals("T_OA_TEMPORARY_RECEPTION")) {
            tvTitle.setText("临时招待情况说明");
            fragment.saveType = "saveOaTemporaryReception";
            fragment.getType = "getOaTemporaryReceptionById";
            fragment.businessType = "T_OA_TEMPORARY_RECEPTION";
            callBackName = "hr_WFCallBackService";
            List<String> list1 = new ArrayList() {{
                add("机关人员");
                add("项目部人员");
                add("项目部负责人");
                add("部门经理");
                add("公司领导");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请人级别:");
                    put("key", "applicantLevel");
                    put("data", list1);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "来访事由及未事前未申请原因:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "来访单位:");
                    put("key", "visitingUnit");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "来访日期:");
                    put("key", "dateOfVisit");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "来访人数:");
                    put("key", "numberOfVisitors");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "陪同人数:");
                    put("key", "numberOfEscorts");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算（餐饮）:");
                    put("key", "repast");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算（交通）:");
                    put("key", "traffic");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算（住宿）:");
                    put("key", "stay");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算（纪念品）:");
                    put("key", "souvenir");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算（其他）:");
                    put("key", "other");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 民航-项目部临建申请单
        if (businessType.equals("T_OA_LIN_JIAN_APPLY")) {
            tvTitle.setText("项目部临建申请");
            fragment.saveType = "saveOaLinJianApply";
            fragment.getType = "getOaLinJianApplyById";
            fragment.businessType = "T_OA_LIN_JIAN_APPLY";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经办人:");
                    put("key", "manager");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "临建房建设开始日期:");
                    put("key", "buildStartTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "临建房建设结束日期:");
                    put("key", "buildEndTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "临建房使用开始日期:");
                    put("key", "useStartTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "临建房使用结束日期:");
                    put("key", "useEndTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "临建房面积:");
                    put("key", "area");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "地址:");
                    put("key", "address");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "施工单位/个人:");
                    put("key", "unitOrIndividual");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-房屋租赁申请单
        if (businessType.equals("T_OA_HOUSING_LEASE")) {
            tvTitle.setText("房屋租赁申请单");
            fragment.saveType = "saveOaHousingLease";
            fragment.getType = "getOaHousingLeaseById";
            fragment.businessType = "T_OA_HOUSING_LEASE";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList() {{
                add("包括电费");
                add("不包括电费");
            }};
            List<String> list1 = new ArrayList() {{
                add("包括水费");
                add("不包括水费");
            }};
            List<String> list2 = new ArrayList() {{
                add("包括网费");
                add("不包括网费");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经办人:");
                    put("key", "manager");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "租赁期限(起):");
                    put("key", "leaseStartTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "租赁期限(止):");
                    put("key", "leaseEndTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "房屋面积:");
                    put("key", "area");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "房屋业主:");
                    put("key", "homeowner");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "房屋地址:");
                    put("key", "address");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "年租金/月租金:");
                    put("key", "rent");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "是否包括电费:");
                    put("key", "electricityBill");
                    put("data", list);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "是否包括水费:");
                    put("key", "waterFee");
                    put("data", list1);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "是否包括网费:");
                    put("key", "internetFee");
                    put("data", list2);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 民航-项目部长租宾馆申请单
        if (businessType.equals("T_OA_LONG_RENTAL_HOTEL")) {
            tvTitle.setText("项目部长租宾馆申请");
            fragment.saveType = "saveOaLongRentalHotel";
            fragment.getType = "getOaLongRentalHotelById";
            fragment.businessType = "T_OA_LONG_RENTAL_HOTEL";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经办人:");
                    put("key", "manager");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "住宿时间(起):");
                    put("key", "leaseStartTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "住宿时间(止):");
                    put("key", "leaseEndTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "房间号及间数:");
                    put("key", "roomNumber");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "地址:");
                    put("key", "address");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "名称:");
                    put("key", "hotelName");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 民航-车辆租赁申请单
        if (businessType.equals("T_OA_CAR_RENTAL")) {
            tvTitle.setText("车辆租赁申请");
            fragment.saveType = "saveOaCarRental";
            fragment.getType = "getOaCarRentalById";
            fragment.businessType = "T_OA_CAR_RENTAL";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经办人:");
                    put("key", "manager");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "租赁期限(起):");
                    put("key", "leaseStartTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "租赁期限(止):");
                    put("key", "leaseEndTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "日/月租金:");
                    put("key", "dailyRent");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经费预算小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "车辆型号:");
                    put("key", "vehicleModel");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "出租单位/姓名:");
                    put("key", "rentalUnit");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-车辆维修保养申请单
        if (businessType.equals("T_OA_CAR_MAINTENANCE")) {
            tvTitle.setText("车辆维修保养申请");
            fragment.saveType = "saveOaCarMaintenance";
            fragment.getType = "getOaCarMaintenanceById";
            fragment.businessType = "T_OA_CAR_MAINTENANCE";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经办人:");
                    put("key", "manager");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "驾驶员:");
                    put("key", "pilot");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "车牌号:");
                    put("key", "numberPlate");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "行驶里程:");
                    put("key", "mileage");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "车辆故障:");
                    put("key", "vehicleBreakdown");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "维修建议:");
                    put("key", "repairSuggestions");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算:");
                    put("key", "budget");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合计金额大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合计金额小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-车辆燃油、ETC（充值）、保险费用申请
        if (businessType.equals("T_OA_CAR_ETC")) {
            tvTitle.setText("车辆燃油、ETC充值、保险费用申请");
            fragment.saveType = "saveOaCarEtc";
            fragment.getType = "getOaCarEtcById";
            fragment.businessType = "T_OA_CAR_ETC";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经办人:");
                    put("key", "manager");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "加油费用预算:");
                    put("key", "fuelCosts");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "ETC充值预算:");
                    put("key", "etcRecharge");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "保险费日期(起):");
                    put("key", "insuranceStartTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "保险费日期(止):");
                    put("key", "insuranceEndTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "车辆保险费用预算:");
                    put("key", "vehicleInsurance");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合计金额大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合计金额小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-启航项目部设备租赁申请单
        if (businessType.equals("T_OA_EQUIPMENT_RENTAL")) {
            tvTitle.setText("启航项目部设备租赁申请");
            fragment.saveType = "saveOaEquipmentRental";
            fragment.getType = "getOaEquipmentRentalById";
            fragment.businessType = "T_OA_EQUIPMENT_RENTAL";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经办人:");
                    put("key", "manager");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "租赁期限(起):");
                    put("key", "leaseStartTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "租赁期限(止):");
                    put("key", "leaseEndTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "日租金:");
                    put("key", "dailyRent");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "月租金:");
                    put("key", "monthlyRent");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合计金额大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合计金额小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "租赁设备型号:");
                    put("key", "vehicleModel");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "出租单位/姓名:");
                    put("key", "rentalUnit");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 民航-培训费用申请单
        if (businessType.equals("T_OA_TRAINING_FEES")) {
            tvTitle.setText("培训费用申请单");
            fragment.saveType = "saveOaTrainingFees";
            fragment.getType = "getOaTrainingFeesById";
            fragment.businessType = "T_OA_TRAINING_FEES";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经办人:");
                    put("key", "manager");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "主办单位:");
                    put("key", "organizer");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "培训名称:");
                    put("key", "trainingName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "培训时间(起):");
                    put("key", "trainingStartTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "培训时间(止):");
                    put("key", "trainingEndTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "培训人数:");
                    put("key", "numberOfTrainees");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "培训地点:");
                    put("key", "trainingLocation");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算①:");
                    put("key", "budget1");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算②:");
                    put("key", "budget2");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算③:");
                    put("key", "budget3");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算④:");
                    put("key", "budget4");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算⑤:");
                    put("key", "budget5");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-会议费用申请单
        if (businessType.equals("T_OA_CONFERENCE_FEE")) {
            tvTitle.setText("会议费用申请");
            fragment.saveType = "saveOaConferenceFee";
            fragment.getType = "getOaConferenceFeeById";
            fragment.businessType = "T_OA_CONFERENCE_FEE";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经办人:");
                    put("key", "manager");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "主办单位:");
                    put("key", "organizer");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "会议名称:");
                    put("key", "conferenceName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "会议时间(起):");
                    put("key", "conferenceStartTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "会议时间(止):");
                    put("key", "conferenceEndTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "会议人数:");
                    put("key", "numberOfConference");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "会议地点:");
                    put("key", "conferenceLocation");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算①:");
                    put("key", "budget1");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算②:");
                    put("key", "budget2");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算③:");
                    put("key", "budget3");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算④:");
                    put("key", "budget4");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算⑤:");
                    put("key", "budget5");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-加班审批表
        if (businessType.equals("T_OA_WORK_OVERTIME")) {
            tvTitle.setText("加班审批");
            fragment.saveType = "saveOaWorkOvertime";
            fragment.getType = "getOaWorkOvertimeById";
            fragment.businessType = "T_OA_WORK_OVERTIME";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "加班人员:");
                    put("key", "workName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "加班时间(起):");
                    put("key", "workStartTime");
                    put("timeType", "minute");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "加班时间(止):");
                    put("key", "workEndTime");
                    put("timeType", "minute");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "加班事由:");
                    put("key", "applyReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 民航-采购申请
        if (businessType.equals("T_OA_ORGAN_PROJECT_ASSETS")) {
            tvTitle.setText("采购申请");
            fragment.saveType = "saveOaOrganProjectAssets";
            fragment.getType = "getOaOrganProjectAssetsById";
            fragment.businessType = "T_OA_ORGAN_PROJECT_ASSETS";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经办人:");
                    put("key", "manager");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "保管人:");
                    put("key", "custodian");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算①:");
                    put("key", "budget1");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算②:");
                    put("key", "budget2");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算③:");
                    put("key", "budget3");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算④:");
                    put("key", "budget4");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算⑤:");
                    put("key", "budget5");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "reason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-机关发文审批
        if (businessType.equals("T_OA_POST_REASON")) {
            tvTitle.setText("机关发文审批");
            fragment.saveType = "saveOaPostReason";
            fragment.getType = "getOaPostReasonById";
            fragment.businessType = "T_OA_POST_REASON";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "文件标题:");
                    put("key", "wenjianTitle");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "发文单位:");
                    put("key", "fawenCompany");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "文号:");
                    put("key", "wenhao");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "发文日期:");
                    put("key", "fawenDate");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "发文事由:");
                    put("key", "reasonForPosting");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-外出登记表
        if (businessType.equals("T_OA_GO_OUT")) {
            tvTitle.setText("外出登记表");
            fragment.saveType = "saveOaGoOut";
            fragment.getType = "getOaGoOutById";
            fragment.businessType = "T_OA_GO_OUT";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "外出事由:");
                    put("key", "reasonForGoingOut");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "目的地:");
                    put("key", "destination");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "外出时间(起):");
                    put("key", "startTime");
                    put("timeType", "minute");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "外出时间(止):");
                    put("key", "endTime");
                    put("timeType", "minute");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-项目部临时人员调配申请单
        if (businessType.equals("T_OA_SNAP_BLEND")) {
            tvTitle.setText("项目部临时人员调配申请");
            fragment.saveType = "saveOaSnapBlend";
            fragment.getType = "getOaSnapBlendById";
            fragment.businessType = "T_OA_SNAP_BLEND";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "派出人员:");
                    put("key", "goPersonnel");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "派出日期:");
                    put("key", "goTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "目的地:");
                    put("key", "destination");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "事由:");
                    put("key", "reasonForGoingOut");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-项目部刻章申请单
        if (businessType.equals("T_OA_ENGRAVED_CHAPTER")) {
            tvTitle.setText("项目部刻章申请");
            fragment.saveType = "saveOaEngravedChapter";
            fragment.getType = "getOaEngravedChapterById";
            fragment.businessType = "T_OA_ENGRAVED_CHAPTER";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "刻章事由:");
                    put("key", "remark");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "刻章内容:");
                    put("key", "content");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 民航-采购合同审批单
        if (businessType.equals("T_OA_BUY_CONTRACT")) {
            tvTitle.setText("采购合同审批");
            fragment.saveType = "saveOaBuyContract";
            fragment.getType = "getOaBuyContractById";
            fragment.businessType = "T_OA_BUY_CONTRACT";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同编号:");
                    put("key", "contractId");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同名称:");
                    put("key", "contractName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "供应商:");
                    put("key", "supplier");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "采购事项:");
                    put("key", "byItem");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 民航-法律顾问审核
        if (businessType.equals("T_OA_LAW_CHECK")) {
            tvTitle.setText("法律顾问审核");
            fragment.saveType = "saveOaLawCheck";
            fragment.getType = "getOaLawCheckById";
            fragment.businessType = "T_OA_LAW_CHECK";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请事由:");
                    put("key", "reason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 民航-公司社保缴纳申请表审批详情信息
        if (businessType.equals("T_OA_COMPANY_TAX")) {
            tvTitle.setText("社保缴纳申请表审批");
            fragment.saveType = "saveOaCompanyTax";
            fragment.getType = "getOaCompanyTaxById";
            fragment.businessType = "T_OA_COMPANY_TAX";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请税种:");
                    put("key", "applyTax");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "reason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 民航-技术津贴待遇申请单
        if (businessType.equals("T_OA_TECH_TREATMENT")) {
            tvTitle.setText("技术津贴待遇申请");
            fragment.saveType = "saveOaTechTreatment";
            fragment.getType = "getOaTechTreatmentById";
            fragment.businessType = "T_OA_TECH_TREATMENT";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "工作年限:");
                    put("key", "workYear");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "工作岗位:");
                    put("key", "workDuty");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "资格证书名称:");
                    put("key", "bookName");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "证书编号:");
                    put("key", "bookNameNo");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "奖励标准:");
                    put("key", "rewardRule");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "授予日期:");
                    put("key", "bookGiveTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "发放日期:");
                    put("key", "bookPutTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "第一年:");
                    put("key", "yearFirst");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "第二年:");
                    put("key", "yearSecond");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "第三年:");
                    put("key", "yearThird");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "第四年:");
                    put("key", "yearFourth");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "第五年:");
                    put("key", "yearFifth");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 民航-晒图审批单
        if (businessType.equals("T_OA_PHOTO_CHECK")) {
            tvTitle.setText("晒图审批");
            fragment.saveType = "saveOaPhotoCheck";
            fragment.getType = "getOaPhotoCheckById";
            fragment.businessType = "T_OA_PHOTO_CHECK";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请时间:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "设计阶段:");
                    put("key", "designStep");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "份数:");
                    put("key", "photoNum");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 民航-图纸审查专家费预算申请
        if (businessType.equals("T_OA_CHECK_EXPERT")) {
            tvTitle.setText("图纸审查专家费预算申请");
            fragment.saveType = "saveOaCheckExpert";
            fragment.getType = "getOaCheckExpertById";
            fragment.businessType = "T_OA_CHECK_EXPERT";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算①:");
                    put("key", "fee1");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算②:");
                    put("key", "fee2");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算③:");
                    put("key", "fee3");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算④:");
                    put("key", "fee4");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用预算⑤:");
                    put("key", "fee5");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "费用合计小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "reason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-疫情防控期间项目复工申请表
        if (businessType.equals("T_OA_COVID_19")) {
            tvTitle.setText("项目复工申请");
            fragment.saveType = "saveOaCovid19";
            fragment.getType = "getOaCovid19ById";
            fragment.businessType = "T_OA_COVID_19";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目负责人:");
                    put("key", "projectLeader");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目所在地:");
                    put("key", "projectAddress");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "复工进场人数:");
                    put("key", "workStartNum");
                    put("keyboardType", "number");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "从疫情重点地区返回的人数:");
                    put("key", "fromCovidFocusNum");
                    put("keyboardType", "number");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "有疫情重点地区亲友接触史的人数:");
                    put("key", "covidFocusNum");
                    put("keyboardType", "number");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "停工日期:");
                    put("key", "projectStopTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "计划复工日期:");
                    put("key", "projectDoTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目完成进度及需要特殊说明的情况:");
                    put("key", "projectScheduleTips");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "复工原因:");
                    put("key", "workStartReason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-论文申请奖励表
        if (businessType.equals("T_OA_DISSERTATION")) {
            tvTitle.setText("论文申请奖励");
            fragment.saveType = "saveOaDissertation";
            fragment.getType = "getOaDissertationById";
            fragment.businessType = "T_OA_DISSERTATION";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "论文名称:");
                    put("key", "dissertation");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "发表时间:");
                    put("key", "dissertationTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "刊号:");
                    put("key", "isbn");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "刊物名称:");
                    put("key", "isbnName");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "稿费:");
                    put("key", "isbnRmb");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 民航-项目部人员需求申请单
        if (businessType.equals("T_OA_PRO_NEED")) {
            list_fragments.add(new ApplyMHPersonRequireFragment());
            tvTitle.setText("项目部人员需求申请");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
            /*
            tvTitle.setText("项目部人员需求申请");
            ApplyOtherFragment fragment1 = new ApplyOtherFragment();
            fragment1.saveType = "saveOaProNeedApp";
            fragment1.getType = "getOaProNeedById";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList(){{
                add("包括电费");
                add("不包括电费");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "申请人:");put("key", "createName");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "部门:");put("key", "deptName");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "论文名称:");put("key", "dissertation");put("viewType", Constant
                .VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "发表时间:");put("key", "dissertationTime");put("viewType", Constant
                .VIEWTYPE_DATESEL_CELL); }});
                add(new HashMap() {{ put("title", "刊号:");put("key", "isbn");put("viewType", Constant
                .VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "刊物名称:");put("key", "isbnName");put("viewType", Constant
                .VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "稿费:");put("key", "isbnRmb");put("viewType", Constant
                .VIEWTYPE_TEXTFIELD_CELL); }});
            }};
            fragment1.setData(data);
            list_fragments.add(fragment1);
            if (isApply) {
                list_fragments.add(new HdmhApplySQFragment());
            } else {
                list_fragments.add(new HdmhApplySPFragment());
            }
            */
        }

        // 民航-税款缴纳申请
        if (businessType.equals("T_OA_COMPANY_PRO_TAX")) {
            list_fragments.add(new ApplyMHShuiKuanJiaoNaFragment());
            tvTitle.setText("税款缴纳申请");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
            /*
            tvTitle.setText("税款缴纳申请");
            fragment.saveType = "saveOaCompanyProTax";
            fragment.getType = "getOaCompanyProTaxById";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "申请人:");put("key", "createName");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "部门:");put("key", "deptName");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "税种:");put("key", "fee5");put("viewType", Constant
                .VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "金额合计大写:");put("key", "totalFeesCapital");put("viewType", Constant
                .VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "金额合计小写:");put("key", "totalFees");put("capitalKey",
                "totalFeesCapital");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "申请原因:");put("key", "reason");put("viewType", Constant
                .VIEWTYPE_TEXTFIELD_CELL); }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new HdmhApplySQFragment());
            } else {
                list_fragments.add(new HdmhApplySPFragment());
            }
            */
        }
        // 民航-财务费用付款申请
        if (businessType.equals("T_OA_COMPANY_PRO_CASH")) {
            tvTitle.setText("财务费用付款申请");
            fragment.saveType = "saveOaCompanyProCash";
            fragment.getType = "getOaCompanyProCashById";
            fragment.businessType = "T_OA_COMPANY_PRO_CASH";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "财务种类:");
                    put("key", "applyTax");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "金额合计大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "金额合计小写:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "reason");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 方正-维修改造（保养）申请
        if (businessType.equals("T_OA_MAINTENANCE_RENOVATION")) {
            tvTitle.setText("维修改造（保养）申请");
            fragment.saveType = "saveOaMaintenanceRenovation";
            fragment.getType = "getOaMaintenanceRenovationById";
            fragment.businessType = "T_OA_MAINTENANCE_RENOVATION";
            fragment.systemItemMap = new HashMap() {{
                put("spkey", Constant.SP_KEY_ALL_MAINTENANCE_RENOVATION_TYPES);
                put("index", "1");
            }};
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "维修类别:");
                    put("extra", "typeCode");
                    put("key", "repairCategory");
                    put("showKey", "repairCategoryName");
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请原因:");
                    put("key", "cause");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合计费用大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合计费用小写:");
                    put("key", "totalCost");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 方正-外业检测申请
        if (businessType.equals("T_OA_FIELD_TESTING")) {
            tvTitle.setText("外业检测申请");
            fragment.saveType = "saveOaFieldTesting";
            fragment.getType = "getOaFieldTestingById";
            fragment.businessType = "T_OA_FIELD_TESTING";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList() {{
                add("六区内");
                add("烟内");
                add("烟外省内");
                add("省外");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "外检事由:");
                    put("key", "cause");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "外检人员:");
                    put("key", "personnel");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "出发时间:");
                    put("key", "departureTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "计划返回时间:");
                    put("key", "plannedReturnTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "所属区域:");
                    put("key", "area");
                    put("extra", "notIndex");
                    put("data", list);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "目的地:");
                    put("key", "destination");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "使用车辆:");
                    put("key", "useVehicle");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "驾驶员:");
                    put("key", "driver");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});

            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 方正-人员离场（冬闲）
        if (businessType.equals("T_OA_PERSONNEL_LEAVE")) {
            tvTitle.setText("人员离场(冬闲)申请");
            fragment.saveType = "saveOaPersonnelLeave";
            fragment.getType = "getOaPersonnelLeaveById";
            fragment.businessType = "T_OA_PERSONNEL_LEAVE";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "冬休人员及时间:");
                    put("key", "contents");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 方正-搬迁申请
        if (businessType.equals("T_OA_RELOCATION_APPLY")) {
            tvTitle.setText("搬迁申请");
            fragment.saveType = "saveOaRelocationApply";
            fragment.getType = "getOaRelocationApplyById";
            fragment.businessType = "T_OA_RELOCATION_APPLY";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "搬迁原因及项目:");
                    put("key", "reasonAndItem");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "原地址:");
                    put("key", "originalAddress");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "拟搬地址:");
                    put("key", "addressToBeMoved");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "搬运费大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "搬运费:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 方正-房屋装修、改造、仪器维修申请
        if (businessType.equals("T_OA_HOUSE_RENOVATION")) {
            tvTitle.setText("房屋装修、改造、仪器维修申请");
            fragment.saveType = "saveOaHouseRenovation";
            fragment.getType = "getOaHouseRenovationById";
            fragment.businessType = "T_OA_HOUSE_RENOVATION";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList() {{
                add("房屋装修");
                add("房屋改造");
                add("仪器维修");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请事由:");
                    put("key", "cause");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "维修类型:");
                    put("key", "repairCategory");
                    put("data", list);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "名称:");
                    put("key", "name");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "单位及数量:");
                    put("key", "unitAndQuantity");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "规格型号:");
                    put("key", "model");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "单价:");
                    put("key", "unitPrice");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "人工费大写:");
                    put("key", "laborCostCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "人工费（元）:");
                    put("key", "laborCost");
                    put("capitalKey", "laborCostCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合计金额大写:");
                    put("key", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合计金额:");
                    put("key", "totalFees");
                    put("capitalKey", "totalFeesCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 方正-印刷装订申请
        if (businessType.equals("T_OA_PRINT_BINDING")) {
            tvTitle.setText("印刷装订申请");
            fragment.saveType = "saveOaPrintBinding";
            fragment.getType = "getOaPrintBindingById";
            fragment.businessType = "T_OA_PRINT_BINDING";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "依据或原因:");
                    put("key", "contents");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "名称:");
                    put("key", "name");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "单位:");
                    put("key", "unit");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "数量:");
                    put("key", "amount");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "单价:");
                    put("key", "unitPrice");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "总价大写:");
                    put("key", "capital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "总价（元）:");
                    put("key", "totalPrice");
                    put("capitalKey", "capital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 方正-稿件报送
        if (businessType.equals("T_PDS_MANUSCRIPT_SUBMISSION")) {
            tvTitle.setText("稿件报送");
            fragment.saveType = "savePdsManuscriptSubmission";
            fragment.getType = "getPdsManuscriptSubmissionById";
            fragment.businessType = "T_PDS_MANUSCRIPT_SUBMISSION";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "稿件名称:");
                    put("key", "name");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "备注:");
                    put("key", "remark");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 首胜-项目关闭申请
        if (businessType.equals("T_PROJECT_SHUTDOWN")) {
            list_fragments.add(new ApplyEndProjectFragment());
            tvTitle.setText("项目关闭申请");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 首胜-合同终止申请
        if (businessType.equals("T_CONTRACT_STOP")) {
            list_fragments.add(new ApplyEndContractFragment());
            tvTitle.setText("合同终止申请");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // -车辆调拨
        if (businessType.equals("T_PDS_VEHICLE_ALLOCATION")) {
            tvTitle.setText("车辆调拨");
            list_fragments.add(new ApplyCheLiangDiaoBoFragment());
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // -车辆维修
        if (businessType.equals("t_cvs_carmaintain")) {
            list_fragments.add(new ApplyCheLiangWeiXiuFragment());
            tvTitle.setText("车辆维修");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // -车辆申请
        if (businessType.equals("t_cvs_carapply")) {
            list_fragments.add(new ApplyCheLiangFragment());
            tvTitle.setText("车辆申请");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // -车辆加油申请
        if (businessType.equals("T_PDS_VEHICLE_REFUELING")) {
            tvTitle.setText("车辆加油");
            list_fragments.add(new ApplyCheLiangJiaYouFragment());
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        if (businessType.equals("T_PROJ_LOCK_PERSON")) {
            tvTitle.setText("人员锁定申请");
            list_fragments.add(new ApplySSRenYuanSuoDingFragment());
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        if (businessType.equals("T_CHANGE_PERSON_CARD")) {
            tvTitle.setText("人员证件变更");
            list_fragments.add(new ApplySSRenYuanBianGengFragment());
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        if (businessType.equals("T_OA_POST_UP")) {
            tvTitle.setText("总监级别晋升");
            list_fragments.add(new ApplySSZongJianJinShenFragment());
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 首胜-年度质量安全处罚
        if (businessType.equals("T_ASSE_ANNUPUN")) {
            tvTitle.setText("年度质量安全处罚");
            fragment.saveType = "saveAsseAnnupun";
            fragment.getType = "getAsseAnnupunById";
            fragment.noFile = true;
            fragment.businessType = "T_ASSE_ANNUPUN";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "年度:");
                    put("key", "currYear");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "处罚类别或支持文件:");
                    put("key", "punType");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "处罚对象:");
                    put("key", "punObject");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "建设单位或相关部门处罚:");
                    put("key", "constPun");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "公司处罚:");
                    put("key", "compPun");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "备注:");
                    put("key", "remark");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 首胜-年度质量安全奖励
        if (businessType.equals("T_ASSE_ANNUAWARD")) {
            tvTitle.setText("年度质量安全奖励");
            fragment.saveType = "saveAsseAnnuaward";
            fragment.getType = "getAsseAnnuawardById";
            fragment.noFile = true;
            fragment.businessType = "T_ASSE_ANNUAWARD";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList() {{
                add("年度质量安全奖励");
                add("年度受建设单位和相关单位奖励");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "年度:");
                    put("key", "currYear");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "奖励类别:");
                    put("key", "awardType");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "奖励对象:");
                    put("key", "awardObject");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "公司奖励:");
                    put("key", "compReward");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "类型:");
                    put("key", "type");
                    put("data", list);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "备注:");
                    put("key", "remark");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 首胜-项目部撤销申请
        if (businessType.equals("T_CANCELLATION_APPLICATION")) {
            tvTitle.setText("项目部撤销申请");
            fragment.saveType = "saveCancellationApplication";
            fragment.getType = "getCancellationApplicationById";
            fragment.noFile = true;
            fragment.businessType = "T_CANCELLATION_APPLICATION";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "监管部门:");
                    put("key", "regulatoryName");
                    put("extra", "regulatoryId");
                    put("type", "Depart");
                    put("viewType", Constant.VIEWTYPE_SELECTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "组建日期:");
                    put("key", "formationDate");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "撤销日期:");
                    put("key", "revokeDate");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "撤销事由:");
                    put("key", "reasonRevocation");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "备注:");
                    put("key", "remark");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        // 首胜公司机关报销申请
        if (businessType.equals("T_OA_AGENCY_REIMBURSEMENT")) {
            tvTitle.setText("公司机关报销申请");
            fragment.saveType = "saveOaAgencyReimbursement";
            fragment.getType = "getOaAgencyReimbursementById";
            fragment.businessType = "T_OA_AGENCY_REIMBURSEMENT";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList() {{
                add("有票");
                add("无票");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "报销人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "报销部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "报销时间:");
                    put("key", "createTime");
//                    put("dateFormat", "YMD_HMS");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "报销单号:");
                    put("key", "reimbursementNumber");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "有票无票:");
                    put("key", "banknote");
                    put("extra", "notIndex");
                    put("data", list);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "本次报销金额（大写）:");
                    put("key", "amountCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "本次报销金额（小写）:");
                    put("key", "amountLowercase");
                    put("capitalKey", "amountCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "报销事由:");
                    put("key", "content");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "收款账号:");
                    put("key", "account");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "开户行:");
                    put("key", "bank");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "收款人名称:");
                    put("key", "beneficiaryName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "备注:");
                    put("key", "remark");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 首胜项目报销申请
        if (businessType.equals("T_OA_PROJECT_REIMBURSEMENT")) {
            tvTitle.setText("项目报销申请");
            fragment.saveType = "saveOaProjectReimbursement";
            fragment.getType = "getOaProjectReimbursementById";
            fragment.businessType = "T_OA_PROJECT_REIMBURSEMENT";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList() {{
                add("有票");
                add("无票");
            }};
            List<String> list1 = new ArrayList() {{
                add("监理");
                add("造价");
                add("代理");
                add("设计");
                add("咨询");
                add("项管");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "报销人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "报销项目:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "报销部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "报销时间:");
                    put("key", "createTime");
//                    put("dateFormat", "YMD_HMS");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "报销单号:");
                    put("key", "reimbursementNumber");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目类别:");
                    put("key", "typeOfContract");
                    put("extra", "notIndex");
                    put("data", list1);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "有票无票:");
                    put("key", "banknote");
                    put("extra", "notIndex");
                    put("data", list);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "本次报销金额（大写）:");
                    put("key", "amountCapital");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "本次报销金额（小写）:");
                    put("key", "amountLowercase");
                    put("capitalKey", "amountCapital");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "报销事由:");
                    put("key", "content");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "收款账号:");
                    put("key", "account");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "开户行:");
                    put("key", "bank");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "收款人名称:");
                    put("key", "beneficiaryName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "备注:");
                    put("key", "remark");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        if (businessType.equals("T_CERTIFICATE_FEE_APPLY")) {
            tvTitle.setText("证件费用申请");
            list_fragments.add(new ApplySSZhengJianFeiyongFragment());
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 四川公路-自主经营项目审批
        if (businessType.equals("T_OA_MANAGE")) {
            tvTitle.setText("自主经营项目审批");
            fragment.saveType = "saveOaManage";
            fragment.getType = "getOaManageById";
            fragment.businessType = "T_OA_MANAGE";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "经办人:");
                    put("key", "operator");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "主投部门:");
                    put("key", "investmentDepartment");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "配合部门:");
                    put("key", "cooperateDepartment");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请日期:");
                    put("key", "filingDate");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目情况:");
                    put("key", "projectStatus");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目类型:");
                    put("key", "projectType");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目所处阶段:");
                    put("key", "projectPhase");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "初拟工作思路与所处阶段:");
                    put("key", "thinking");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "备注:");
                    put("key", "remark");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});

            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 四川公路-生产任务书
        if (businessType.equals("T_OA_PRODUCTION_ASSIGNMENT")) {
            tvTitle.setText("生产任务书");
            fragment.saveType = "saveOaProductionAssignment";
            fragment.getType = "getOaProductionAssignmentById";
            fragment.businessType = "T_OA_PRODUCTION_ASSIGNMENT";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "使用编号:");
                    put("key", "serialNumber");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "承担任务部门:");
                    put("key", "taskDeptName");
                    put("extra", "taskDeptId");
                    put("type", "Depart");
                    put("viewType", Constant.VIEWTYPE_SELECTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "抄送部门:");
                    put("key", "copyDeptName");
                    put("extra", "copyDeptId");
                    put("type", "Depart");
                    put("viewType", Constant.VIEWTYPE_SELECTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目业主:");
                    put("key", "projectOwner");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "工期:");
                    put("key", "timeLimit");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "联系人:");
                    put("key", "contacts");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "主要工作内容及要求:");
                    put("key", "content");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "备注:");
                    put("key", "remark");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 四川公路-合同交接申请
        if (businessType.equals("T_OA_CONTRACT_HANDOVER")) {
            tvTitle.setText("合同交接申请");
            fragment.saveType = "saveOaHandoverOfContract";
            fragment.getType = "getOaHandoverOfContractById";
            fragment.businessType = "T_OA_CONTRACT_HANDOVER";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList() {{
                add("是");
                add("否");
            }};
            List<String> list1 = new ArrayList() {{
                add("市政");
                add("公路");
                add("房建");
                add("其他");
            }};
            List<String> list2 = new ArrayList() {{
                add("填勘察设计");
                add("监理");
                add("咨询");
                add("检测");
                add("招标代理");
                add("其他");
            }};
            List<String> list3 = new ArrayList() {{
                add("已签");
                add("未签");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "业主名称:");
                    put("key", "ownerName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "是否为联合体:");
                    put("key", "comboFix");
                    put("extra", "notIndex");
                    put("data", list);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "成员方:");
                    put("key", "memberName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "成员方占比(%):");
                    put("key", "memberProportion");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "牵头方:");
                    put("key", "leadName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "牵头方占比(%):");
                    put("key", "leadProportion");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同金额是否确定:");
                    put("key", "amountFix");
                    put("extra", "notIndex");
                    put("data", list);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同金额(元):");
                    put("key", "amountQuantity");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同金额说明:");
                    put("key", "amountDescription");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "行业类型:");
                    put("key", "industryType");
                    put("extra", "notIndex");
                    put("data", list1);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "业务大类:");
                    put("key", "businessSuperclass");
                    put("extra", "notIndex");
                    put("data", list2);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "业务子类:");
                    put("key", "businessSubclass");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "中标方式:");
                    put("key", "bidWay");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "中标日期:");
                    put("key", "bidTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "说明:");
                    put("key", "bidDescriptions");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "备注:");
                    put("key", "bidComment");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同负责人:");
                    put("key", "principalName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "是否自揽:");
                    put("key", "seizeSelf");
                    put("extra", "notIndex");
                    put("data", list);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "签订状态:");
                    put("key", "signState");
                    put("extra", "notIndex");
                    put("data", list3);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "签订时间:");
                    put("key", "signTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "移交人:");
                    put("key", "transferName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "接收时间:");
                    put("key", "acceptTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "接收人:");
                    put("key", "recipientName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "正本份数:");
                    put("key", "originalQuantity");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "副本份数:");
                    put("key", "ectypeQuantity");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "复印份数:");
                    put("key", "copyQuantity");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "补充协议主合同名称:");
                    put("key", "masterContractName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "补充协议签订时间:");
                    put("key", "supplementSignTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "内容简述:");
                    put("key", "contentDescription");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});

            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 四川公路-合同签订/协议申请
        if (businessType.equals("T_OA_COUNTERSIGNED")) {
            tvTitle.setText("合同签订/协议申请");
            fragment.saveType = "saveOaCountersigned";
            fragment.getType = "getOaCountersignedById";
            fragment.businessType = "T_OA_COUNTERSIGNED";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "服务范围:");
                    put("key", "fwService");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "服务工期:");
                    put("key", "fwTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同申请备注:");
                    put("key", "remark");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同申请其他:");
                    put("key", "contractOther");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "项目业主:");
                    put("key", "ownerName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同申请金额:");
                    put("key", "contractMoney");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同申请比例(%):");
                    put("key", "contractProportion");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同申请日期:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "合同申请部门:");
                    put("key", "deptName");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});

            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        // 首胜-投标保证金申请
        if (businessType.equals("T_PROMISE_PAY_APPLY")) {
            tvTitle.setText("投标保证金申请");
            fragment.saveType = "savePromisePayApply";
            fragment.getType = "getPromisePayApplyById";
            fragment.businessType = "T_PROMISE_PAY_APPLY";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList() {{
                add("是");
                add("否");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{
                    put("title", "项目名称:");
                    put("key", "projectName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请人:");
                    put("key", "createName");
                    put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "申请日期:");
                    put("key", "createTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "报名开始日期:");
                    put("key", "beginDate");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "报名结束日期:");
                    put("key", "endDate");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "开标日期:");
                    put("key", "bidOpeningTime");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "保证金缴纳截止日期:");
                    put("key", "datePromiseRmb");
                    put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "地区:");
                    put("key", "area");
                    put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
                }});
                add(new HashMap() {{
                    put("title", "负责人:");
                    put("key", "managePerson");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{
                    put("title", "保证金金额（大写）:");
                    put("key", "promiseMoneyToUpperCase");
                    put("viewType", Constant.VIEWTYPE_LABEL_CELL);
                }});
                add(new HashMap() {{
                    put("title", "保证金金额（小写）:");
                    put("key", "promiseMoney");
                    put("capitalKey", "promiseMoneyToUpperCase");
                    put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL);
                }});
                add(new HashMap() {{ put("title", "(已转)保证金金额:");put("key", "transferPromiseMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(已转)报名费:");put("key", "transferSignupMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(已转)资料费:");put("key", "transferFileMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(已转)加急费:");put("key", "transferHurryMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(已转)开标费:");put("key", "transferOpenMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(已转)保函费:");put("key", "transferBaohanMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(已转)利息费:");put("key", "transferInterestMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(已转)技术费:");put("key", "transferTechMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(已转)其他:");put("key", "transferOtherMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(未支付)保证金金额:");put("key", "noPayPromiseMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(未支付)报名费:");put("key", "noPaySignupMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(未支付)资料费:");put("key", "noPayFileMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(未支付)加急费:");put("key", "noPayHurryMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(未支付)开标费:");put("key", "noPayOpenMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(未支付)保函费:");put("key", "noPayBaohanMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(未支付)利息费:");put("key", "noPayInterestMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(未支付)技术费:");put("key", "noPayTechMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "(未支付)其他:");put("key", "noPayOtherMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "项目总费用合计:");put("key", "projectCountMoney");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "制单人:");put("key", "makeMenuPerson");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "汇出日期:");put("key", "dateExport");put("viewType", Constant.VIEWTYPE_DATESEL_CELL); }});
                add(new HashMap() {{
                    put("title", "是否需要先转入建设网:");
                    put("key", "isToNetwork");
                    put("extra", "notIndex");
                    put("data", list);
                    put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
                }});
                add(new HashMap() {{ put("title", "转入银行账号信息:");put("key", "transferBankAccountInfo");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "需按招标文件备注的内容:");put("key", "remark1");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "备注:");put("key", "remark3");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //华东民航-分包合同付款
        if (businessType.equals("T_OA_SUBCONTRACT_PAYMENT")) {
            tvTitle.setText("分包合同付款");
            fragment.saveType = "saveOaSubcontractPayment";
            fragment.getType = "getOaSubcontractPaymentById";
            fragment.businessType = "T_OA_SUBCONTRACT_PAYMENT";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "支付项目名称:");put("key", "projectName");put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL); }});
                add(new HashMap() {{ put("title", "收款单位名称:");put("key", "payeeName");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "付款事由及依据:");put("key", "causeAndBasis");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "收入合同名称（编号）:");put("key", "revenueContractName");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "收入合同金额:");put("key", "revenueContractAmount");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "支付合同金额:");put("key", "paymentContractAmount");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "已付金额:");put("key", "amountPaid");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "本次申请金额:");put("key", "appliedAmount");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "余额:");put("key", "balance");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //华东民航-合同审批表
        if (businessType.equals("T_OA_CONTRACT_APPROVE")) {
            tvTitle.setText("合同审批表");
            fragment.saveType = "saveOaContractApprove";
            fragment.getType = "getOaContractApproveById";
            fragment.businessType = "T_OA_CONTRACT_APPROVE";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "合同名称:");put("key", "contractName");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "合同相对方:");put("key", "counterparty");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "合同条款主要内容:");put("key", "mainContent");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "合同金额:");put("key", "contractAmount");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "备注:");put("key", "remark");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //华东民航-纵向晋升申请
        if (businessType.equals("T_OA_EMPLOYEE_PROMOTION")) {
            tvTitle.setText("纵向晋升申请");
            fragment.saveType = "saveOaEmployeePromotion";
            fragment.getType = "getOaEmployeePromotionById";
            fragment.businessType = "T_OA_EMPLOYEE_PROMOTION";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList() {{
                add("维持原岗位");
                add("岗位提升");
            }};
            List<String> list1 = new ArrayList() {{
                add("干部任免和岗位任命");
                add("取得从业/执业资格证书");
                add("其他任职资格和能力的提升");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "姓名:");put("key", "name");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "部门:");put("key", "projectDepartment");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "学历:");put("key", "educationalBackground");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "入职时间:");put("key", "entryTime");put("viewType", Constant.VIEWTYPE_DATESEL_CELL); }});
                add(new HashMap() {{ put("title", "专业:");put("key", "major");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "职称:");put("key", "professionalTitle");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "从业/执业职业资格:");put("key", "professionalQualification");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "纵向晋升情况:");put("key", "verticalPromotion");put("data", list1);put("viewType", Constant.VIEWTYPE_CHECKBOX_CELL); }});
                add(new HashMap() {{ put("title", "岗位是否发生变化:");put("key", "post"); put("data", list);put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "现任岗位:");put("key", "presentPost");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "现拟申请晋升岗位:");put("key", "promotionPost");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //华东民航-履约保函申请
        if (businessType.equals("T_OA_BACKLETTER")) {
            tvTitle.setText("履约保函申请");
            fragment.saveType = "saveOaBackletter";
            fragment.getType = "getOaBackletterById";
            fragment.businessType = "T_OA_BACKLETTER";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList() {{
                add("履约保函");
                add("预付款保函");
                add("质量保函");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "项目名称:");put("key", "projectName");put("viewType", Constant.VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "申请业务:");put("key", "business");put("data", list);put("viewType", Constant.VIEWTYPE_CHECKBOX_CELL); }});
                add(new HashMap() {{ put("title", "部门:");put("key", "deptName");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "合同号:");put("key", "contractNumber");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "受益人:");put("key", "beneficiary");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "保函金额（大写）:");put("key", "amountCapital");put("viewType", Constant.VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "保函金额（小写）:");put("key", "amountLower");put("capitalKey", "amountCapital");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "保函期限开始:");put("key", "termBegin");put("viewType", Constant.VIEWTYPE_DATESEL_CELL); }});
                add(new HashMap() {{ put("title", "保函期限结束:");put("key", "termEnd");put("viewType", Constant.VIEWTYPE_DATESEL_CELL); }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //华东民航-印章外带申请
        if (businessType.equals("T_OA_SEAL_TAPE")) {
            tvTitle.setText("印章外带申请");
            fragment.saveType = "saveOaSealTape";
            fragment.getType = "getOaSealTapeById";
            fragment.businessType = "T_OA_SEAL_TAPE";
            callBackName = "hr_WFCallBackService";
            List<String> list = new ArrayList() {{
                add("公司公章");
                add("法人印签");
            }};
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "申请部门:");put("key", "deptName");put("viewType", Constant.VIEWTYPE_LABEL_CELL); }});
                add(new HashMap() {{ put("title", "外带的印章:");put("key", "sealType");put("data", list);put("viewType", Constant.VIEWTYPE_CHECKBOX_CELL); }});
                add(new HashMap() {{ put("title", "申请事由:");put("key", "cause");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //华东民航-项目车辆、食堂配置申请
        if (businessType.equals("T_OA_CANTEEN_VEHICLE")) {
            tvTitle.setText("项目车辆、食堂配置申请");
            fragment.saveType = "saveOaCanteenVehicle";
            fragment.getType = "getOaCanteenVehicleById";
            fragment.businessType = "T_OA_CANTEEN_VEHICLE";
            callBackName = "hr_WFCallBackService";
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "项目编号:");put("key", "projectNumber");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "项目名称:");put("key", "projectName");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "项目负责人:");put("key", "projectLeader");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "项目人数:");put("key", "projectQuantity");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "车辆配置:");put("key", "vehicleConfiguration");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "食堂配置:");put("key", "canteenConfiguration");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //华东民航-办公用品购置申请
        if (businessType.equals("T_OA_OFFICE_SUPPLIES")) {
            tvTitle.setText("办公用品购置申请");
            fragment.saveType = "saveOaOfficeSupplies";
            fragment.getType = "getOaOfficeSuppliesById";
            fragment.businessType = "T_OA_OFFICE_SUPPLIES";
            callBackName = "hr_WFCallBackService";
            List<Map<String,String>> list = new ArrayList() {{
                add(new HashMap(){{put("key", "officeName");put("title", "办公用品名称");}});
                add(new HashMap(){{put("key", "officeSpecification");put("title", "规格/型号");}});
                add(new HashMap(){{put("key", "quantity");put("title", "数量");}});
                add(new HashMap(){{put("key", "amount");put("title", "金额");}});
                add(new HashMap(){{put("key", "remark");put("title", "附注");}});
            }};
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "部门/项目部:");put("key", "projectDepartment");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "编号:");put("key", "officeNumber");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "办公用品:");put("key", "oaOfficeSuppliesDetailList"); put("data",list);put("viewType", Constant.VIEWTYPE_ROWTABLE_CELL); }});
                add(new HashMap() {{ put("title", "合计:");put("key", "numExpense");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});

            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //华东民航-生活用品购置申请
        if (businessType.equals("T_OA_DAILY_NECESSITIES")) {
            tvTitle.setText("生活用品购置申请");
            fragment.saveType = "saveOaDailyNecessities";
            fragment.getType = "getOaDailyNecessitiesById";
            fragment.businessType = "T_OA_DAILY_NECESSITIES";
            callBackName = "hr_WFCallBackService";
            List<Map<String,String>> list = new ArrayList() {{
                add(new HashMap(){{put("key", "necessitiesName");put("title", "生活用品名称");}});
                add(new HashMap(){{put("key", "officeSpecification");put("title", "规格/型号");}});
                add(new HashMap(){{put("key", "quantity");put("title", "数量");}});
                add(new HashMap(){{put("key", "amount");put("title", "金额");}});
                add(new HashMap(){{put("key", "remark");put("title", "附注");}});
            }};
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "部门/项目部:");put("key", "projectDepartment");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "编号:");put("key", "officeNumber");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "生活用品:");put("key", "oaDailyNecessitiesDetailList"); put("data",list);put("viewType", Constant.VIEWTYPE_ROWTABLE_CELL); }});
                add(new HashMap() {{ put("title", "合计:");put("key", "numExpense");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //华东民航-劳防用品购置申请
        if (businessType.equals("T_OA_LABOR_DEFENSE")) {
            tvTitle.setText("劳防用品购置申请");
            fragment.saveType = "saveOaLaborDefense";
            fragment.getType = "getOaLaborDefenseById";
            fragment.businessType = "T_OA_LABOR_DEFENSE";
            callBackName = "hr_WFCallBackService";
            List<Map<String,String>> list = new ArrayList() {{
                add(new HashMap(){{put("key", "defenseName");put("title", "劳防用品名称");}});
                add(new HashMap(){{put("key", "officeSpecification");put("title", "规格/型号");}});
                add(new HashMap(){{put("key", "quantity");put("title", "数量");}});
                add(new HashMap(){{put("key", "amount");put("title", "金额");}});
                add(new HashMap(){{put("key", "remark");put("title", "附注");}});
            }};
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "部门/项目部:");put("key", "projectDepartment");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "编号:");put("key", "officeNumber");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "劳防用品:");put("key", "oaLaborDefenseDetailList"); put("data",list);put("viewType", Constant.VIEWTYPE_ROWTABLE_CELL); }});
                add(new HashMap() {{ put("title", "合计:");put("key", "numExpense");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        //华东民航-防疫用品购置申请
        if (businessType.equals("T_OA_PREVENTION")) {
            tvTitle.setText("防疫用品购置申请");
            fragment.saveType = "saveOaPrevention";
            fragment.getType = "getOaPreventionById";
            fragment.businessType = "T_OA_PREVENTION";
            callBackName = "hr_WFCallBackService";
            List<Map<String,String>> list = new ArrayList() {{
                add(new HashMap(){{put("key", "preventionName");put("title", "防疫用品名称");}});
                add(new HashMap(){{put("key", "officeSpecification");put("title", "规格/型号");}});
                add(new HashMap(){{put("key", "quantity");put("title", "数量");}});
                add(new HashMap(){{put("key", "amount");put("title", "金额");}});
                add(new HashMap(){{put("key", "remark");put("title", "附注");}});
            }};
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "部门/项目部:");put("key", "projectDepartment");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "编号:");put("key", "officeNumber");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "防疫用品:");put("key", "oaPreventionDetailList"); put("data",list);put("viewType", Constant.VIEWTYPE_ROWTABLE_CELL); }});
                add(new HashMap() {{ put("title", "合计:");put("key", "numExpense");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }


        //华东民航-固定资产申请
        if (businessType.equals("T_OA_ASSET_REGISTRATION")) {
            tvTitle.setText("固定资产申请");
            fragment.saveType = "saveOaAssetRegistration";
            fragment.getType = "getOaAssetRegistrationById";
            fragment.businessType = "T_OA_ASSET_REGISTRATION";
            callBackName = "hr_WFCallBackService";
            List<Map<String,String>> list = new ArrayList() {{
                add(new HashMap(){{put("key", "name");put("title", "名称");}});
                add(new HashMap(){{put("key", "modelNumber");put("title", "型号");}});
                add(new HashMap(){{put("key", "quantity");put("title", "数量");}});
                add(new HashMap(){{put("key", "expense");put("title", "费用");}});
            }};
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "部门/项目部:");put("key", "projectDepartment");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "申请原因:");put("key", "cause");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "资产申请:");put("key", "oaAssetRegistrationDetailList"); put("data",list);put("viewType", Constant.VIEWTYPE_ROWTABLE_CELL); }});
                add(new HashMap() {{ put("title", "合计:");put("key", "numExpense");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
                add(new HashMap() {{ put("title", "备注:");put("key", "remark");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //华东民航-固定资产验收单
        if (businessType.equals("T_OA_ASSET_ACCEPTANCE")) {
            tvTitle.setText("固定资产验收单");
            fragment.saveType = "saveOaAssetAcceptance";
            fragment.getType = "getOaAssetAcceptanceById";
            fragment.businessType = "T_OA_ASSET_ACCEPTANCE";
            callBackName = "hr_WFCallBackService";
            List<Map<String,String>> list = new ArrayList() {{
                add(new HashMap(){{put("key", "name");put("title", "名称");}});
                add(new HashMap(){{put("key", "modelNumber");put("title", "型号");}});
                add(new HashMap(){{put("key", "quantity");put("title", "数量");}});
                add(new HashMap(){{put("key", "remarks");put("title", "备注");}});
            }};
            data = new ArrayList() {{
                add(new HashMap() {{ put("title", "验收部门:");put("key", "department");put("viewType", Constant.VIEWTYPE_TEXTFIELD_CELL); }});
                add(new HashMap() {{ put("title", "资产验收:");put("key", "oaAssetAcceptanceDetailList"); put("data",list);put("viewType", Constant.VIEWTYPE_ROWTABLE_CELL); }});
                add(new HashMap() {{ put("title", "备注:");put("key", "remark");put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL); }});
            }};
            fragment.setData(data);
            list_fragments.add(fragment);
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }

        //华东民航-证书借用申请-新增
        if (businessType.equals("T_OA_CERTIFICATE_BORROWING")) {
            list_fragments.add(new ApplyHDZhengShuJieYongFragment());
            tvTitle.setText("证书借用申请");
            callBackName = "hr_WFCallBackService";
            if (isApply) {
                list_fragments.add(new ApplySPFragment());
            } else {
                list_fragments.add(new ApplySPFragment1());
            }
        }





        // 华东民航-流程
//        HdmhApplyFormPageConfigData.setFormPageData(ApplyActivity.this,businessType,tvTitle,fragment,callBackName,data,list_fragments,isApply);
//        HdmhApplyFormPageConfigData.setFormPageData(ApplyActivity.this,mTitle,businessType,tvTitle,fragment,callBackName,data,list_fragments,isApply);

        // 四川公路流程
//        ScglApplyFormPageConfigData.setFormPageData(ApplyActivity.this,businessType,tvTitle,fragment,callBackName,data,list_fragments,isApply);
        ScglApplyFormPageConfigData.setFormPageData(ApplyActivity.this,mTitle,businessType,tvTitle,fragment,callBackName,data,list_fragments,isApply);

        adapter = new MainFrameAdapter(getSupportFragmentManager(), list_fragments);
        vpMainframeContent.setAdapter(adapter);
    }


    private void initView() {

        /**
         *	viewPager页面改变事件
         */
        vpMainframeContent.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageSelected(int position) {
                switch (position) {
                    case 0:
                        rbTaskDetail.setChecked(true);
                        rbTaskDetail.setTextColor(getResources().getColor(R.color.colorAccent));
                        viewTaskDetail.setBackgroundColor(getResources().getColor(R.color.colorAccent));
                        rbTaskRecord.setTextColor(getResources().getColor(R.color.text_main_black));
                        viewTaskRecord.setBackgroundColor(getResources().getColor(R.color.line_space));
                        break;
                    case 1:
                        rbTaskRecord.setChecked(true);
                        rbTaskRecord.setTextColor(getResources().getColor(R.color.colorAccent));
                        rbTaskDetail.setTextColor(getResources().getColor(R.color.text_main_black));
                        viewTaskDetail.setBackgroundColor(getResources().getColor(R.color.line_space));
                        viewTaskRecord.setBackgroundColor(getResources().getColor(R.color.colorAccent));
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });

        rgTitle.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                if (!isApply) {
                    switch (checkedId) {
                        case R.id.rb_task_detail:
                            vpMainframeContent.setCurrentItem(0, false);
                            break;
                        case R.id.rb_task_record:
                            vpMainframeContent.setCurrentItem(1, false);
                            break;
                    }
                }
            }
        });

        vpMainframeContent.setOffscreenPageLimit(2);//预加载
    }

    @OnClick(R.id.iv_back)
    public void onClick() {
        finish();
    }

    /**
     * 基本信息提交返回数据
     */
    private String back;

    public void setBack(String back) {
        this.back = back;
        vpMainframeContent.setCurrentItem(1, false);
    }

    public String getBack() {
        return back;
    }

    public String getBusinessType() {
        return businessType;
    }

    public String getCallBackName() {
        return callBackName;
    }

    public FragmentManager fragmentManager;


    //在fragment的管理类中，我们要实现这部操作，而他的主要作用是，当D这个activity回传数据到这里碎片管理器下面的fragnment中时，往往会经过这个管理器中的onActivityResult的方法。
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 2369) { // 送阅入
            if (data != null) {
                StringBuilder name = new StringBuilder();
                StringBuilder id = new StringBuilder();
                List<String> ids = new ArrayList<>(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                List<String> names = new ArrayList<>(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));

                if (ids.size() > 0) {
                    String idsStr = ids.get(0);
                    String namesStr = names.get(0);
                    for (int i = 1; i < ids.size(); i++) {
                        idsStr = idsStr + "," + ids.get(i);
                        namesStr = namesStr + "," + names.get(i);
                    }

                    zfData.put("recipient", namesStr);
                    zfData.put("recipientId", idsStr);

                    lczhuanfa.getEtContent().setText(namesStr);
                }
            }
        }
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        LogUtil.e(TAG + " onDestroy");
    }


    public String getAllBusinessTypes(){
        String btypes = ApplyActivity.businessTypes();
        return btypes;
    }

}
