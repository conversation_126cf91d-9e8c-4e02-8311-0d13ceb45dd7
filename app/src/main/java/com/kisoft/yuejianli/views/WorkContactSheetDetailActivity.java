package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.WorkContactSheet;

import butterknife.BindView;
import butterknife.OnClick;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/14.
 */

public class WorkContactSheetDetailActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.tv_project_name)
    TextView tvProjectName;
    @BindView(R.id.tv_sheet_number)
    TextView tvSheetNumber;
    @BindView(R.id.tv_company)
    TextView tv_company;
    @BindView(R.id.tv_accept_company)
    TextView tvAcceptCom;
    @BindView(R.id.tv_send_company)
    TextView tvSendCom;

    @BindView(R.id.tv_receive_time)
    TextView tv_receive_time;

    @BindView(R.id.tv_send_company_person)
    TextView tvSendComPerson;

    @BindView(R.id.tv_send_time)
    TextView tvSendTime;

    @BindView(R.id.tv_type)
    TextView tv_type;
    @BindView(R.id.tv_feedback)
    TextView tv_feedback;
    @BindView(R.id.tv_content)
    TextView tvContent;
    @BindView(R.id.tv_con)
    TextView tvCon;
    @BindView(R.id.tv_remark)
    TextView tvRemark;

    @BindView(R.id.ll_answer)
    LinearLayout btnLayout;


    /**
     * 页面类型  true：发起申请  false：申请详情
     */
    public boolean isApply;

    private WorkContactSheet sheet;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        initData();
        initView();
    }
    @Override
    public int getLayoutId() {
        return R.layout.activity_work_contact_detail;
    }
    private void initView() {
        if (isApply) {
            // 发起申请
            btnLayout.setVisibility(View.VISIBLE);

        } else {
            btnLayout.setVisibility(View.GONE);
            // 展示详情
            if (sheet != null) {
                tvTitle.setText(sheet.getWorkContactSheet());
                tvProjectName.setText(sheet.getProjectName());
                tvSheetNumber.setText(sheet.getSheetNumber());
                tv_company.setText(sheet.getUnitName());
                tvAcceptCom.setText(sheet.getReceivUnit());
                tvSendCom.setText(sheet.getSendingUnit());
                tvSendComPerson.setText(sheet.getLeadingOfficial());
                tv_receive_time.setText(sheet.getReceiptTime());
                tvSendTime.setText(sheet.getSubmissionTime());

                tv_type.setText(sheet.getStatus());
                
                tv_feedback.setText(sheet.getFeedback());
                tvContent.setText(sheet.getContent());
                tvCon.setText(sheet.getConSzj());
                tvRemark.setText(sheet.getRemark());
            }
        }
    }

    private void initData() {
        Intent intent = getIntent();
        isApply = getIntent().getBooleanExtra("isApply", true);
        if (intent != null) {
            Bundle bd = intent.getExtras();
            /**
             * 页面类型isApply  true：发起申请  false：申请详情
             */
            isApply = bd.getBoolean("isApply");
            if (isApply) {
                // 发起申请

            } else {
                // 申请详情
                sheet = (WorkContactSheet) intent.getSerializableExtra(Constant.INTENT_KEY_WORK_CONTACT_SHEET);
            }
        }
    }




    @OnClick({R.id.iv_back, R.id.tv_sub})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.tv_sub:
                if (TextUtils.isEmpty(tvSheetNumber.getText().toString())) {
                    showToast("请输入编号");
                    return;
                }
                if (TextUtils.isEmpty(tv_company.getText().toString())) {
                    showToast("请输入文件名称");
                    return;
                }






               break;
        }
    }
}
