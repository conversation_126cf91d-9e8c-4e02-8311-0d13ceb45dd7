package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;

import com.bigkoo.pickerview.TimePickerView;
import com.bigkoo.pickerview.lib.WheelView;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.TaskBaseInfoContract;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.TPwTasktype;
import com.kisoft.yuejianli.entity.TaskDTO;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.TaskBaseInfoModel;
import com.kisoft.yuejianli.presenter.TaskBaseInfoPresenter;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.PhoneUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import butterknife.BindView;
import butterknife.BindViews;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import com.kisoft.yuejianli.R;

/**
 * Description: 任务下发
 * Author     : yanlu
 * Date       : 2018/12/28 15:42
 */

public class TaskBaseInfoFragment extends BaseFragment<TaskBaseInfoModel, TaskBaseInfoPresenter>
        implements TaskBaseInfoContract.TaskBaseInfoViewContract, RadioGroup.OnCheckedChangeListener {

    public View mRootView;
    Unbinder unbinder;

    @BindView(R.id.et_task_title)
    EditText etTaskTitle;
    @BindView(R.id.spinner_task_type)
    Spinner spinnerTaskType;
    @BindView(R.id.tv_task_type)
    TextView tvTaskType;
    @BindView(R.id.tv_begin_time)
    TextView tvBeginTime;
    @BindView(R.id.tv_end_time)
    TextView tvEndTime;

    @BindView(R.id.et_task_content)
    EditText etTaskContent;

    @BindView(R.id.rg_task_price)
    RadioGroup rgTaskPrice;

    @BindView(R.id.rg_task_schedule)
    RadioGroup rgTaskSchedule;

    @BindView(R.id.tv_master)
    TextView tvMaster;
    @BindView(R.id.tv_minor)
    TextView tvMinor;
    @BindView(R.id.tv_infor)
    TextView tvInfor;
    @BindView(R.id.tv_sub)
    TextView tvSub;
    @BindView(R.id.ll_master)
    LinearLayout llMaster;
    @BindView(R.id.ll_minor)
    LinearLayout llMinor;
    @BindView(R.id.ll_infor)
    LinearLayout llInfor;

    /**
     * 级别  进度
     **/
    private int price, schedule;

    private TaskBaseInfoModel mModel;
    private TaskBaseInfoPresenter presenter;

    private List<TPwTasktype> taskTypeList;
    /**
     * 任务类型id
     **/
    public String ttGuid;
    public String ttName;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private static final int MASTER = 100;
    private static final int MINOR = 200;
    private static final int INFOR = 300;


    //知会人ids
    private StringBuffer inforGuid;    //知会人id
    private StringBuffer inforGuidName;//知会人            //逗号间隔
    //负责人ids
    private String masterGuid;  //负责人Id
    private String masterGuidName;//负责人名称
    //参与人ids
    private StringBuffer minorGuid;
    private StringBuffer minorGuidName;//参与人

    /**
     * 页面类型：0 下发任务  1 任务详情
     */
    private int type;

    private String taskId;

    @Override
    public int getRootView() {
        return R.layout.activity_task_delivery;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            type = getArguments().getInt("type", 0);
            taskId = getArguments().getString("taskId");
        }
        mModel = new TaskBaseInfoModel(mContext);
        presenter = new TaskBaseInfoPresenter(this, mModel);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initView();
        return mRootView;
    }

    private void initView() {
        switch (type) {
            case 0: // 任务下发
                userInfo = SettingManager.getInstance().getUserInfo();
                projectInfo = SettingManager.getInstance().getProject();
                taskTypeList = new ArrayList<>();

                rgTaskPrice.setOnCheckedChangeListener(this);
                rgTaskSchedule.setOnCheckedChangeListener(this);
                spinnerTaskType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {

                    @Override
                    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                        if (taskTypeList.size() > 0) {
                            ttGuid = taskTypeList.get(position).getTtGuid();
                            ttName = taskTypeList.get(position).getTtname();
                        }
                    }

                    @Override
                    public void onNothingSelected(AdapterView<?> parent) {

                    }
                });
                initDatePick();

                presenter.getTaskTypeList();
                break;
            case 1: // 任务详情
                etTaskTitle.setEnabled(false);
                etTaskTitle.setHint("");
                spinnerTaskType.setVisibility(View.GONE);
                tvSub.setVisibility(View.INVISIBLE);
                tvTaskType.setVisibility(View.VISIBLE);
                tvBeginTime.setEnabled(false);
                tvBeginTime.setHint("");
                tvEndTime.setEnabled(false);
                tvEndTime.setHint("");
                llMaster.setEnabled(false);
                llMinor.setEnabled(false);
                llInfor.setEnabled(false);
                etTaskContent.setEnabled(false);
                etTaskContent.setHint("");
                presenter.getTaskInfoById(taskId);
                break;
        }
    }

    @Override
    public void taskInfoBack(TaskDTO taskDTO) {   // 任务详情
        if (taskDTO != null) {
            etTaskTitle.setText(StringUtil.isEmpty(taskDTO.getTopic()) ? "" : taskDTO.getTopic());
            tvTaskType.setText(StringUtil.isEmpty(taskDTO.getTtName()) ? "" : taskDTO.getTtName());
            tvBeginTime.setText(StringUtil.isEmpty(taskDTO.getBeginDate()) ? "" : taskDTO.getBeginDate());
            tvEndTime.setText(StringUtil.isEmpty(taskDTO.getEndDate()) ? "" : taskDTO.getEndDate());
            tvMaster.setText(StringUtil.isEmpty(taskDTO.getMasterGuidName()) ? "" : taskDTO.getMasterGuidName());
            tvMinor.setText(StringUtil.isEmpty(taskDTO.getMinorGuidName()) ? "" : taskDTO.getMinorGuidName());
            tvInfor.setText(StringUtil.isEmpty(taskDTO.getInforGuidName()) ? "" : taskDTO.getInforGuidName());
            etTaskContent.setText(StringUtil.isEmpty(taskDTO.getContent()) ? "" : taskDTO.getContent());

            switch (taskDTO.getTsklevel()) {
                case "0":
                    radioButtons.get(0).setChecked(true);
                    radioButtons.get(1).setEnabled(false);
                    radioButtons.get(2).setChecked(false);
                    break;
                case "1":
                    radioButtons.get(1).setChecked(true);
                    radioButtons.get(0).setEnabled(false);
                    radioButtons.get(2).setEnabled(false);
                    break;
                case "2":
                    radioButtons.get(2).setChecked(true);
                    radioButtons.get(1).setEnabled(false);
                    radioButtons.get(0).setEnabled(false);
                    break;
            }
            switch (taskDTO.getProgress()) {
                case "0":
                    radioButtons.get(3).setChecked(true);
                    radioButtons.get(4).setEnabled(false);
                    break;
                case "1":
                    radioButtons.get(4).setChecked(true);
                    radioButtons.get(3).setEnabled(false);
                    break;
            }
        }
    }

    private void clearViewData() {
        presenter.getTaskTypeList();
        etTaskTitle.setText("");
        tvBeginTime.setText("");
        tvEndTime.setText("");
        tvMaster.setText("");
        tvMinor.setText("");
        tvInfor.setText("");
        etTaskContent.setText("");
        minorGuid = null;
        minorGuidName = null;
        masterGuid = null;
        masterGuidName = null;
        inforGuid = null;
        inforGuidName = null;
        price = 0;
        schedule = 0;
        radioButtons.get(0).setChecked(true);
        radioButtons.get(3).setChecked(true);
    }

    @OnClick({R.id.tv_sub, R.id.tv_begin_time, R.id.tv_end_time, R.id.ll_master, R.id.ll_minor, R.id.ll_infor})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tv_sub:
                submit();
                break;
            case R.id.tv_begin_time:
            case R.id.tv_end_time:
                tpv.show(view);
                break;
            case R.id.ll_master:    //负责人
                getPersonInfo(MASTER);
                break;
            case R.id.ll_minor:     //参与人
                getPersonInfo(MINOR);
                break;
            case R.id.ll_infor:     //知会人
                getPersonInfo(INFOR);
                break;
        }
    }

    public void getPersonInfo(int requestCode) {
        Intent intent = new Intent();
        if(requestCode==MASTER){
            intent.putExtra("isSingle", true);
        }
        intent.setClass(mContext, CompanyOrgInfoActivity.class);
        startActivityForResult(intent, requestCode);
    }

    /**
     * 提交
     */
    private void submit() {
        TaskDTO dto = new TaskDTO();
        dto.setTtGuid(ttGuid);
        dto.setTtName(ttName);
        dto.setTopic(etTaskTitle.getText().toString());
        dto.setTsklevel(price + "");
        dto.setBeginDate(tvBeginTime.getText().toString());
        dto.setEndDate(tvEndTime.getText().toString());
        dto.setContent(etTaskContent.getText().toString());
        dto.setCreator(userInfo.getId());
        dto.setCreateId(userInfo.getId());
        dto.setCreatename(userInfo.getName());
        dto.setCreatedate(DateUtil.dateToString(new Date(), DateUtil.YMD_HM));
        dto.setProgress(schedule + "");
        dto.setMasterGuid(masterGuid);
        dto.setMasterGuidName(masterGuidName);
        dto.setMinorGuid(minorGuid != null ? minorGuid.toString() : "");
        dto.setMinorGuidName(minorGuidName != null ? minorGuidName.toString() : "");
        dto.setInforGuid(inforGuid != null ? inforGuid.toString() : "");
        dto.setInforGuidName(inforGuidName != null ? inforGuidName.toString() : "");
        dto.setUserRoles(userInfo.getUserOrgRelation());
        dto.setLevel(price + "");
        presenter.addTask(dto);
    }

    private TimePickerView tpv;
    /**
     * 展示日期选择对话框
     */
    private void initDatePick() {
        Calendar selectedDate = Calendar.getInstance();
        Calendar startDate = Calendar.getInstance();
        startDate.set(1950, 0, 1, 0, 0);
        Calendar endDate = Calendar.getInstance();
        endDate.set(2033, 11, 30, 23, 30);
        //时间选择器
        tpv = new TimePickerView.Builder(mContext, new TimePickerView.OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {//选中事件回调
                // 这里回调过来的v,就是show()方法里面所添加的 View 参数，如果show的时候没有添加参数，v则为null
                ((TextView) v).setText(DateUtil.dateToString(date, DateUtil.YMD_HM));
            }
        })
                //年月日时分秒 的显示与否，不设置则默认全部显示
                .setType(new boolean[]{true, true, true, true, true, false})
                .setLabel("年", "月", "日", "时", "分", "")
                .isCenterLabel(false)
                .setDividerType(WheelView.DividerType.WRAP)
                .setDividerColor(ContextCompat.getColor(YueApplacation.mContext, R.color.colorAccent))
                .setContentSize(PhoneUtil.dpTopx(YueApplacation.mContext, 8))
                .setDate(selectedDate)
                .setRangDate(startDate, endDate)
                .setDecorView(null)
                .build();
    }

    @BindViews({R.id.rb_task_price1, R.id.rb_task_price2, R.id.rb_task_price3, R.id.rb_task_schedule1, R.id.rb_task_schedule2})
    List<RadioButton> radioButtons;

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {

        switch (checkedId) {
            case R.id.rb_task_price1: //低
                price = 0;
                break;
            case R.id.rb_task_price2: //普通
                price = 1;
                break;
            case R.id.rb_task_price3: //高
                price = 2;
                break;
            case R.id.rb_task_schedule1: //未开始
                schedule = 0;
                break;
            case R.id.rb_task_schedule2: //进行中
                schedule = 1;
                break;
        }
    }

    @Override
    public void showAddTask(TaskDTO info) {
        dismissProgress();
        if (!TextUtils.isEmpty(info.getTopic())) {
            showToast("提交成功");
            clearViewData();

            ((TaskActivity) getActivity()).setCurItem(1);
        }
    }

    @Override
    public void showTaskTypeList(List<TPwTasktype> taskTypeList) {
        this.taskTypeList = taskTypeList;

        if (taskTypeList.size() > 0) {
            ttGuid = taskTypeList.get(0).getTtGuid();
            ttName = taskTypeList.get(0).getTtname();
        }
        /**spinner数据**/
        String[] m_Countries = new String[taskTypeList.size()];
        for (int i = 0; i < taskTypeList.size(); i++) {
            m_Countries[i] = taskTypeList.get(i).getTtname();
        }
        ArrayAdapter<String> mAdapter = new ArrayAdapter<String>(mContext, android.R.layout.simple_spinner_item, m_Countries);
        spinnerTaskType.setAdapter(mAdapter);
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case MASTER:
                if (data != null) {
                    List<String> ids = new ArrayList<>();
                    List<String> names = new ArrayList<>();
                    ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                    names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                    if (names != null && names.size() > 0) {
                        //plan.setConName(names.get(0));
                        tvMaster.setText(names.get(0));
                        masterGuidName = names.get(0);
                        masterGuid = ids.get(0);
                    }
                }
                break;
            case MINOR: {
                if (data != null) {
                    minorGuidName = new StringBuffer();
                    minorGuid = new StringBuffer();
                    List<String> ids = new ArrayList<>();
                    List<String> names = new ArrayList<>();
                    ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                    names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                    if (names != null && names.size() > 0) {
                        for (int i = 0; i < names.size(); i++) {
                            if (i != 0) {
                                minorGuidName.append(",");
                            }
                            minorGuidName.append(names.get(i));
                        }
                        tvMinor.setText(minorGuidName.toString());

                        for (int i = 0; i < ids.size(); i++) {
                            if (i != 0) {
                                minorGuid.append(",");
                            }
                            minorGuid.append(ids.get(i));
                        }
                    }
                }
            }
            break;
            case INFOR:
                if (data != null) {
                    inforGuidName = new StringBuffer();
                    inforGuid = new StringBuffer();
                    List<String> ids = new ArrayList<>();
                    List<String> names = new ArrayList<>();
                    ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                    names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                    if (names != null && names.size() > 0) {
                        for (int i = 0; i < names.size(); i++) {
                            if (i != 0) {
                                inforGuidName.append(",");
                            }
                            inforGuidName.append(names.get(i));
                        }
                        tvInfor.setText(inforGuidName.toString());

                        for (int i = 0; i < ids.size(); i++) {
                            if (i != 0) {
                                inforGuid.append(",");
                            }
                            inforGuid.append(ids.get(i));
                        }
                    }
                }
                break;

            default:

                break;
        }

    }
}
