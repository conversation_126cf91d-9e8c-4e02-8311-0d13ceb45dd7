package com.kisoft.yuejianli.views.autoform;

import com.kisoft.yuejianli.base.Base;

import java.util.List;

public class PublicAutoFormSetDataListInfo extends Base {

    /*
    allowNull = false,
    createId = 170303100034474392e50 aee4951a4e8,
    createName = 测试员,
    createTime = 2023 - 06 - 06 14: 09,
    defaultValue = 必填项,
    editBoxType = text,
    fieldIdentification = v1,
    fieldName = 申请人,
    fieldSort = 0.0,
    fieldType = text,
    formDefinitionId = 230606093704204 ab83897799baf7100,
    icon = layui - icon - edit,
    id = 230606140933992e9008354 eaff46d34,
    length = 50,
    modifyId = 170303100034474392e50 aee4951a4e8,
    modifyName = 测试员,
    modifyTime = 2023 - 06 - 06 14: 09,
    optionContent = [],
    optionContentList = [],
    whetherToDisplay = false
    */

    private boolean allowNull;
    private String defaultValue;
    private String editBoxType;
    private String fieldIdentification;
    private String fieldName;
    private String fieldSort;
    private String fieldType;
    private String formDefinitionId;
    private String icon;
    private List<String> optionContentList;
    private boolean whetherToDisplay;


    public boolean getAllowNull() {
        return allowNull;
    }

    public void setAllowNull(boolean allowNull) {
        this.allowNull = allowNull;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getEditBoxType() {
        return editBoxType;
    }

    public void setEditBoxType(String editBoxType) {
        this.editBoxType = editBoxType;
    }

    public String getFieldIdentification() {
        return fieldIdentification;
    }

    public void setFieldIdentification(String fieldIdentification) {
        this.fieldIdentification = fieldIdentification;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldSort() {
        return fieldSort;
    }

    public void setFieldSort(String fieldSort) {
        this.fieldSort = fieldSort;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public String getFormDefinitionId() {
        return formDefinitionId;
    }

    public void setFormDefinitionId(String formDefinitionId) {
        this.formDefinitionId = formDefinitionId;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public List<String> getOptionContentList() {
        return optionContentList;
    }

    public void setOptionContentList(List<String> optionContentList) {
        this.optionContentList = optionContentList;
    }

    public boolean getWhetherToDisplay() {
        return whetherToDisplay;
    }

    public void setWhetherToDisplay(boolean whetherToDisplay) {
        this.whetherToDisplay = whetherToDisplay;
    }
}
