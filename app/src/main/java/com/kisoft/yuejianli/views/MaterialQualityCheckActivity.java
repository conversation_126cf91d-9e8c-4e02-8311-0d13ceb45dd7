package com.kisoft.yuejianli.views;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.bigkoo.pickerview.TimePickerView;
import com.bigkoo.pickerview.lib.WheelView;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.adpter.ImageAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.MaterailQualityCheckContract;
import com.kisoft.yuejianli.entity.Material;
import com.kisoft.yuejianli.entity.MaterialInspect;
import com.kisoft.yuejianli.entity.MaterialUnit;
import com.kisoft.yuejianli.entity.PhotoUploadResulte;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UnSubmitInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.entity.event.ImageCompressEvent;
import com.kisoft.yuejianli.greendao.DaoUtil;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.MaterialQualityCheckModel;
import com.kisoft.yuejianli.presenter.MaterialQualityCheckPresenter;
import com.kisoft.yuejianli.ui.ImageDialog;
import com.kisoft.yuejianli.ui.MaterialPopWindow;
import com.kisoft.yuejianli.ui.TipsQuestionView;
import com.kisoft.yuejianli.ui.UnitListActivity;
import com.kisoft.yuejianli.ui.UnitPopWindow;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.PermissionsChecker;
import com.kisoft.yuejianli.utils.PhoneUtil;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.views.watermark.ImageUtil;
import com.kisoft.yuejianli.views.watermark.PhotoCaptureActivity;
import com.kisoft.yuejianli.views.watermark.PhotoListener;
import com.kisoft.yuejianli.views.watermark.WaterMask;
import com.kisoft.yuejianli.views.watermark.WaterMaskHelper;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.common.util.LogUtil;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;

/**
 * Created by tudou on 2018/3/14.
 */

public class MaterialQualityCheckActivity extends BaseActivity<MaterialQualityCheckModel, MaterialQualityCheckPresenter>
        implements MaterailQualityCheckContract.MaterialQualityCheckViewContract,
        MaterialPopWindow.OnClickMaterialListener, UnitPopWindow.OnClickMaterialUnitListener,
        ActivityCompat.OnRequestPermissionsResultCallback, WaterMask.WaterMaskListener, PhotoListener {

    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.tv_project)
    TextView tvProject;

    // 材料设备名称
    @BindView(R.id.et_material_name)
    EditText tvCheckName;

    // 材料设备编号
    @BindView(R.id.et_material_num)
    EditText tvCheckNum;
    // 材料设备规格型号
    @BindView(R.id.et_material_serial)
    EditText tvCheckSerial;

    // 材料设备生产厂商
    @BindView(R.id.et_material_manufacturer)
    EditText tvCheckManufacturer;

    // 材料设备购入数量
    @BindView(R.id.et_material_count)
    EditText tvCheckBuyCount;

    // 购入时间
    @BindView(R.id.tv_buyTime)
    TextView tvBuyTime;
    @BindView(R.id.iv_buytime_more)// 购入时间
     ImageView ivBuyTimeMore;

    @BindView(R.id.tips_view)
    TipsQuestionView mQuestionView;// 检查部位
    // 拟用🙅部位
//    @BindView(R.id.et_intend_parts)
//    EditText etIntendParts;

    // 出厂合格证
//    @BindView(R.id.iv_quality_card)
//    SimpleDraweeView ivQualityCard;
    @BindView(R.id.rg_isQualityCard)
    RadioGroup rgIsQualityCard;
    @BindView(R.id.rb_isQualityCard_yes)
    RadioButton rbIsQualityCard_yes;
    @BindView(R.id.rb_isQualityCard_no)
    RadioButton rbIsQualityCard_no;

    // 是否进场
    @BindView(R.id.rg_isOk)
    RadioGroup rgOk;
    @BindView(R.id.rb_yes)
    RadioButton rbYes;
    @BindView(R.id.rb_no)
    RadioButton rbNo;

    // 是否复试
    @BindView(R.id.rg_isReexam)
    RadioGroup rgisReexam;
    @BindView(R.id.rb_isReexam_yes)
    RadioButton rbIsReexamYes;
    @BindView(R.id.rb_isReexam_no)
    RadioButton rbIsReexamNo;

    // 是否平行试验
    @BindView(R.id.rg_isPartestBool)
    RadioGroup rgIsPartestBool;
    @BindView(R.id.rb_isPartestBool_yes)
    RadioButton rbIsPartestBoolYes;
    @BindView(R.id.rb_isPartestBool_no)
    RadioButton rbIsPartestBoolNo;

    // 验收结果
    @BindView(R.id.rg_acceptResultBool)
    RadioGroup rgAcceptResultBool;
    @BindView(R.id.rb_acceptResultBool_yes)
    RadioButton rbAcceptResultBoolYes;
    @BindView(R.id.rb_acceptResultBool_no)
    RadioButton rbAcceptResultBoolNo;

    // 进场时间
    @BindView(R.id.tv_enterTime)
    TextView tvEnterTime;

    @BindView(R.id.iv_time_more)// 进场时间
            ImageView ivTimeMore;

    // 验收人员
    @BindView(R.id.et_accept_person)
    EditText etAcceptPerson;

//    @BindView(R.id.et_count)
//    EditText etCount;
//    @BindView(R.id.tv_unit) //数量
//    TextView tvUnit;

    // 图片
    @BindView(R.id.rv_check_picture)
    RecyclerView rvCheckPicture;

    // 描述
    @BindView(R.id.et_dis)
    EditText etDis;

    // 提交
    @BindView(R.id.ll_answer)
    View answerView;

//    @BindView(R.id.iv_unit_more)
//    ImageView ivUnitMore;

    private boolean canEdit = false;
    boolean modify; // 是否可以修改
    private MaterialInspect inspect = new MaterialInspect();
    private ImageAdapter imageAdapter;
    private MaterialPopWindow materialPopWindow;
    private UnitPopWindow unitPopWindow;

    private ImageDialog imageDialog;
    private List<Material> materials = new ArrayList<>();
    private List<String> images = new ArrayList<>();


    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    private MaterialQualityCheckModel model;
    private MaterialQualityCheckPresenter presenter;
    private int imageType = 1;   //用于区分是合格证还是附件
//    private static final int IMAGE_TYPE_1 = 1;
    private static final int IMAGE_TYPE_2 = 2;

    private TimePickerView tpv;
    private int timeType = 1;
    private static final int TIME_TYPE_BUY = 1;
    private static final int TIME_TYPE_ENTER = 2;


    static final String[] PERMISSIONS = new String[]{
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA

    };
    private PermissionsChecker permissionsChecker;

    private View lastView;
    private int maskLocation = 4;
    private ArrayList<String> uris;
    WaterMaskHelper waterMaskHelper = new WaterMaskHelper(this);
    private boolean isTakePhoto;
    AMapLocation locationdata = null;
    String address = "";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new MaterialQualityCheckModel(this);
        presenter = new MaterialQualityCheckPresenter(this, model);
        initMVP(model, presenter);
        initWater();
        initData();
        initView();
    }

    //初始化水印
    private void initWater() {
        //binding.setPresenter(new Presenter());
        //初始化水印工具
        waterMaskHelper = new WaterMaskHelper(this, this, this);
        //lastView = binding.txtCenter;
        //lastView.setSelected(true);
        maskLocation = WaterMask.DefWaterMaskParam.Location.center;
    }

    @Override                       //添加水印：
    public WaterMask.WaterMaskParam onDraw() {

        //拍照后调用，设置水印的基本参数
        WaterMask.WaterMaskParam param = new WaterMask.WaterMaskParam();
        //创建当前时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        Date date = new Date();
        String format = dateFormat.format(date);
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        if (userInfo != null) {
            param.txt.add("记录人：" + userInfo.getName());
        }
        ProjectInfo project = SettingManager.getInstance().getProject();
        if (project != null) {
            param.txt.add("项目：" + project.getProjectName());
        }
        if (isTakePhoto) {
            param.txt.add(format + "  " + address);
        } else {
            param.txt.add("日期：" + format);
        }
        param.location = WaterMask.DefWaterMaskParam.Location.left_bottom;
        param.itemCount = 60;
        return param;
    }

    @Override
    public void onChoose(ArrayList<String> photos) {
        uris = photos;
        //Glide.with(CompleteCheckAddActivity.this).load(photos.get(0)).placeholder(R.mipmap.ic_launcher).centerCrop
        // ().error(R.mipmap.ic_launcher).crossFade().into(ivSfenclosure);
    }

    private void initData() {
        permissionsChecker = new PermissionsChecker(this);
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        Intent intent = getIntent();
        if (intent != null) {
            modify = intent.getBooleanExtra("modify", false);
            canEdit = intent.getBooleanExtra(Constant.INTENT_KEY_CAN_EDIT, false);
            if (canEdit) {
                inspect = new MaterialInspect();
                // 初始添加现场照片
                images.add("");
                inspect.setQiEnclosure(StringUtil.imageArryToString(images));
                inspect.setCreateId(userInfo.getId());
                inspect.setItemId(projectInfo.getProjectId());
                //  获得项目的材料列表
//                mPresenter.getMaterialList("1,2");
            } else {
                inspect = (MaterialInspect) intent.getSerializableExtra(Constant.INTENT_KEY_MATERAIL_CHECK);
                images.addAll(StringUtil.stringToImageArry(inspect.getQiEnclosure()));
                Iterator<String> iterator = images.iterator();
                while (iterator.hasNext()) {
                    String url = iterator.next();
                    if (StringUtil.isEmpty(url))
                        iterator.remove();
                }
                if (modify) {
                    images.add("");
//                    mPresenter.getMaterialList("1,2");
                }
            }
        }
    }

    private BGAPhotoHelper mPhotoHelper;

    private static final int REQUEST_CODE_CHOOSE_PHOTO = 1;
    private static final int REQUEST_CODE_TAKE_PHOTO = 2;
    private static final int REQUEST_CODE_CROP = 3;

    private void initView() {
        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);

        tvTitle.setText("材料、设备质量安全");
        tvProject.setText(projectInfo.getProjectName());
        if (canEdit || modify) {
//            tvCheckName.post(new Runnable() {
//                @Override
//                public void run() {
//                    materialPopWindow = new MaterialPopWindow(MaterialQualityCheckActivity.this,
//                            tvCheckName.getWidth());
//                    materialPopWindow.setOnClickMaterialListener(MaterialQualityCheckActivity.this);
//                }
//            });
            /*
            tvUnit.post(new Runnable() {
                @Override
                public void run() {
                    unitPopWindow = new UnitPopWindow(MaterialQualityCheckActivity.this,
                            tvUnit.getWidth());
                    unitPopWindow.setOnClickMaterialUnitListener(MaterialQualityCheckActivity.this);
                }
            });
            */

            // 合格
            rgIsQualityCard.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(RadioGroup radioGroup, int i) {
                    switch (i) {
                        case R.id.rb_isQualityCard_yes:
                            inspect.setIsQualified("是");
                            break;
                        case R.id.rb_isQualityCard_no:
                            inspect.setIsQualified("否");
                            break;
                    }
                }
            });

            // 进场
            rgOk.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(RadioGroup radioGroup, int i) {
                    switch (i) {
                        case R.id.rb_yes:
                            inspect.setIsEnter("是");
                            break;
                        case R.id.rb_no:
                            inspect.setIsEnter("否");
                            break;
                    }
                }
            });

            // 复试
            rgisReexam.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(RadioGroup radioGroup, int i) {
                    switch (i) {
                        case R.id.rb_isReexam_yes:
                            inspect.setIsReexam("是");
                            break;
                        case R.id.rb_isReexam_no:
                            inspect.setIsReexam("否");
                            break;
                    }
                }
            });
            // 平行试验
            rgIsPartestBool.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(RadioGroup radioGroup, int i) {
                    switch (i) {
                        case R.id.rb_isPartestBool_yes:
                            inspect.setIsPartest("是");
                            break;
                        case R.id.rb_isPartestBool_no:
                            inspect.setIsPartest("否");
                            break;
                    }
                }
            });
            // 合格
            rgAcceptResultBool.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(RadioGroup radioGroup, int i) {
                    switch (i) {
                        case R.id.rb_acceptResultBool_yes:
                            inspect.setAcceptResult("合格");
                            break;
                        case R.id.rb_acceptResultBool_no:
                            inspect.setAcceptResult("不合格");
                            break;
                    }
                }
            });

            // 描述
            etDis.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void afterTextChanged(Editable editable) {
                    inspect.setQiContent(editable.toString().trim());
                }
            });

            // 选择单位工程
            mQuestionView.getTitle().setText("选择检查部位");
            mQuestionView.getSelectView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    Intent intent = new Intent(mContext, UnitListActivity.class);
                    intent.putExtra("mType",1);//分项工程3
                    intent.putExtra("mEndType",3);//分项工程3
                    startActivityForResult(intent,Constant.REQUEST_CODE_UNIT_DANWEI);
                }
            });
            mQuestionView.getDocBtn().setVisibility(View.VISIBLE);
        }



        if (!canEdit) {

            answerView.setVisibility(View.GONE);
            ivTimeMore.setVisibility(View.GONE);
            ivBuyTimeMore.setVisibility(View.GONE);
            tvCheckName.setText(inspect.getSerialName());//名称
            tvCheckNum.setText(inspect.getEquipmentNumber());// 材料设备编号
            tvCheckSerial.setText(inspect.getSerialSpec());// 材料设备规格型号
            tvCheckManufacturer.setText(inspect.getManufacturer());// 生产厂商
            tvCheckBuyCount.setText(inspect.getCount());// 料设备购入数量
            // 选择单位工程
            mQuestionView.getTitle().setText("检查部位");
            mQuestionView.getQuestEdit().setText(inspect.getIntendParts());
            mQuestionView.getDocBtn().setVisibility(View.GONE);
            mQuestionView.setStatus(false);



            etAcceptPerson.setText(inspect.getAcceptPerson());//验收人
            etAcceptPerson.setEnabled(modify);
//            if (modify) {
//                Pattern p = Pattern.compile("[^0-9]");
//                Matcher m = p.matcher(inspect.getCount());
//                String count = m.replaceAll("").trim();
//                etCount.setText(count);
//                tvUnit.setText(inspect.getCount().substring(inspect.getCount().indexOf(count) + count.length(),
//                        inspect.getCount().length()));
//            } else {
//                etCount.setText(inspect.getCount());
//            }
//            etCount.setEnabled(modify);
//            tvUnit.setVisibility(View.GONE);
//
//            if (!StringUtil.isEmpty(inspect.getCertiFicate())) {
//                ivQualityCard.setImageURI(inspect.getCertiFicate());
//            } else {
//                ivQualityCard.setEnabled(modify);
//            }


            // 合格
            if (StringUtil.isEqual("是", inspect.getIsQualified())) {
                rgIsQualityCard.check(R.id.rb_isQualityCard_yes);
                rbIsQualityCard_no.setEnabled(modify);
            } else if (StringUtil.isEqual("否", inspect.getIsEnter())) {
                rgIsQualityCard.check(R.id.rb_isQualityCard_no);
                rbIsQualityCard_yes.setEnabled(modify);
            } else {
                rbIsQualityCard_yes.setEnabled(modify);
                rbIsQualityCard_no.setEnabled(modify);
            }

            if (StringUtil.isEqual("是", inspect.getIsEnter())) {
                rgOk.check(R.id.rb_yes);
                rbNo.setEnabled(modify);
            } else if (StringUtil.isEqual("否", inspect.getIsEnter())) {
                rgOk.check(R.id.rb_no);
                rbYes.setEnabled(modify);
            } else {
                rbYes.setEnabled(modify);
                rbNo.setEnabled(modify);
            }
            if (StringUtil.isEqual("是", inspect.getIsReexam())) {
                rbIsReexamYes.setChecked(true);
                rbIsReexamNo.setEnabled(modify);
            } else if (StringUtil.isEqual("否", inspect.getIsReexam())) {
                rbIsReexamNo.setChecked(true);
                rbIsReexamYes.setEnabled(modify);
            } else {
                rbIsReexamYes.setEnabled(modify);
                rbIsReexamNo.setEnabled(modify);
            }
            if (StringUtil.isEqual("是", inspect.getIsPartest())) {
                rbIsPartestBoolYes.setChecked(true);
                rbIsPartestBoolNo.setEnabled(modify);
            } else if (StringUtil.isEqual("否", inspect.getIsPartest())) {
                rbIsPartestBoolNo.setChecked(true);
                rbIsPartestBoolYes.setEnabled(modify);
            } else {
                rbIsPartestBoolYes.setEnabled(modify);
                rbIsPartestBoolNo.setEnabled(modify);
            }
            if (StringUtil.isEqual("合格", inspect.getAcceptResult())) {
                rbAcceptResultBoolYes.setChecked(true);
                rbAcceptResultBoolNo.setEnabled(modify);
            } else if (StringUtil.isEqual("不合格", inspect.getAcceptResult())) {
                rbAcceptResultBoolNo.setChecked(true);
                rbAcceptResultBoolYes.setEnabled(modify);
            } else {
                rbAcceptResultBoolYes.setEnabled(modify);
                rbAcceptResultBoolNo.setEnabled(modify);
            }
            rgOk.setEnabled(modify);
            tvBuyTime.setText(inspect.getVerificationTimeStr());// 购入时间
            tvEnterTime.setText(inspect.getEnterTimeStr());
            etDis.setText(inspect.getQiContent());
            etDis.setEnabled(modify);
        }

        if (modify) {
            answerView.setVisibility(View.VISIBLE);
//            ivMore.setVisibility(View.VISIBLE);
            ivTimeMore.setVisibility(View.VISIBLE);
            ivBuyTimeMore.setVisibility(View.VISIBLE);
//            ivUnitMore.setVisibility(View.VISIBLE);
//            tvUnit.setVisibility(View.VISIBLE);
        }

        if (rvCheckPicture.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.HORIZONTAL);
            rvCheckPicture.setLayoutManager(manager);
        }
        imageAdapter = new ImageAdapter(R.layout.item_iamge_show, images);
        imageAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (StringUtil.isEmpty(images.get(position))) {
                    imageType = IMAGE_TYPE_2;
                    if (permissionsChecker.lacksPermissions(PERMISSIONS)) {
                        getPermissions(Constant.REQUEST_CODE_TAKE_POHO);
                    } else {
                        // todo 空照片 ，添加
                        String[] str = new String[]{"系统相机", "手机相册"};
                        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
                        ab.setItems(str, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                switch (which) {
                                    case 0://相机
                                        isTakePhoto = true;
                                        takePhoto();
                                        break;
                                    case 1://相册
                                        isTakePhoto = false;
                                        getPhoto();
                                        break;
                                }
                            }
                        });
                        ab.show();
                    }
                } else {
                    showImage(images.get(position));
                }
            }
        });
        imageAdapter.setApply(canEdit);
        imageAdapter.setDeleteBtnClickListener(new ImageAdapter.DeleteBtnClickListener() {
            @Override
            public void deletePicture(String item) {
                if (!StringUtil.isEmpty(item)){
                    images.remove(item);
                    imageAdapter.notifyDataSetChanged();
                }
            }
        });
        rvCheckPicture.setAdapter(imageAdapter);
        imageDialog = new ImageDialog();
        initDatePick();
    }


    private void initDatePick() {
        Calendar selectedDate = Calendar.getInstance();
        Calendar startDate = Calendar.getInstance();
        startDate.set(1950, 0, 1, 0, 0);
        Calendar endDate = Calendar.getInstance();
        endDate.set(2033, 11, 30, 23, 30);
        //时间选择器
        tpv = new TimePickerView.Builder(this, new TimePickerView.OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {//选中事件回调
                // 这里回调过来的v,就是show()方法里面所添加的 View 参数，如果show的时候没有添加参数，v则为null
                TextView tvDate = (TextView) v;
                tvDate.setText(DateUtil.dateToString(date, DateUtil.YMD_HM));
                if (timeType == TIME_TYPE_BUY) {  //购入时间
                    inspect.setVerificationTimeStr(DateUtil.dateToString(date,DateUtil.YMD_HM));
                }else if (timeType == TIME_TYPE_ENTER) {  //进场时间
                    inspect.setEnterTimeStr(DateUtil.dateToString(date, DateUtil.YMD_HM));
                }
            }
        })
                //年月日时分秒 的显示与否，不设置则默认全部显示
                .setType(new boolean[]{true, true, true, true, true, false})
                .setLabel("年", "月", "日", "时", "分", "")
                .isCenterLabel(false)
                .setDividerType(WheelView.DividerType.WRAP)
                .setDividerColor(ContextCompat.getColor(YueApplacation.mContext, R.color.colorAccent))
                .setContentSize(PhoneUtil.dpTopx(YueApplacation.mContext, 8))
                .setDate(selectedDate)
                .setRangDate(startDate, endDate)
                .setDecorView(null)
                .build();
    }


    @Override
    public int getLayoutId() {
        return R.layout.activity_material_check;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

//    @OnClick(R.id.iv_more)
//    public void showCheckItem() {
//        materialPopWindow.refreshData(materials);
//        materialPopWindow.showAsDropDown(tvCheckName);
//    }
//
//    @OnClick(R.id.iv_unit_more)
//    public void showAllUnit() {
//        unitPopWindow.refreshData(StringUtil.getUnit());
//        unitPopWindow.showAsDropDown(tvUnit);
//    }

//    @OnClick(R.id.iv_quality_card)
//    public void aboutQualityCard() {
//        if (StringUtil.isEmpty(inspect.getCertiFicate())) {
//            imageType = IMAGE_TYPE_1;
//            // todo 如果合格证为空  ，拍照
//            if (permissionsChecker.lacksPermissions(PERMISSIONS)) {
//                getPermissions(Constant.REQUEST_CODE_TAKE_POHO);
//            } else {
//                // todo 空照片 ，添加
//                String[] str = new String[]{"系统相机", "手机相册"};
//                AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
//                ab.setItems(str, new DialogInterface.OnClickListener() {
//                    @Override
//                    public void onClick(DialogInterface dialog, int which) {
//                        switch (which) {
//                            case 0://相机
//                                isTakePhoto = true;
//                                takePhoto();
//                                break;
//                            case 1://相册
//                                isTakePhoto = false;
//                                getPhoto();
//                                break;
//                        }
//                    }
//                });
//                ab.show();
//            }
//        } else {
//            showImage(inspect.getCertiFicate());
//        }
//    }

    @OnClick({R.id.iv_time_more})
    public void showDateSelector() {
        if (tpv != null) {
            timeType = TIME_TYPE_ENTER;
            tpv.show(tvEnterTime);
        }
    }

    @OnClick({R.id.iv_buytime_more})
    public void showBuyTimeDateSelector() {
        if (tpv != null) {
            timeType = TIME_TYPE_BUY;
            tpv.show(tvBuyTime);
        }
    }



    private boolean linkData() {
//        String count = etCount.getText().toString() + tvUnit.getText().toString();
//        inspect.setCount(count);
//        inspect.setIntendParts(etIntendParts.getText().toString());

        inspect.setSerialName(tvCheckName.getText().toString());// 名称
        inspect.setEquipmentNumber(tvCheckNum.getText().toString());// 材料设备编号
        inspect.setSerialSpec(tvCheckSerial.getText().toString());// 材料设备规格型号
        inspect.setManufacturer(tvCheckManufacturer.getText().toString());// 生产厂商
        inspect.setCount(tvCheckBuyCount.getText().toString());// 料设备购入数量
        inspect.setIntendParts(mQuestionView.getQuestEdit().getText().toString());

        inspect.setAcceptPerson(etAcceptPerson.getText().toString());
        if (StringUtil.isEmpty(inspect.getSerialName())) {
            showToast("请输入材料");
            return false;
        }
        if (StringUtil.isEmpty(inspect.getIsEnter())) {
            showToast("请选择是否进场");
            return false;
        }
        if (StringUtil.isEmpty(inspect.getEnterTimeStr())) {
            showToast("请选择进场时间");
            return false;
        }
        if (StringUtil.isEmpty(inspect.getAcceptResult())) {
            showToast("请选择验收结果");
            return false;
        }
        List<String> imageContent = new ArrayList<>();
        if (images.size() > 1) {
            for (int i = 0; i < images.size() - 1; i++) {
                imageContent.add(images.get(i));
            }
            inspect.setQiEnclosure(StringUtil.imageArryToString(imageContent));
        } else {
            inspect.setQiEnclosure("");
        }
        return true;
    }

    @OnClick(R.id.tv_sub)
    public void submitCheck() {
        if (linkData()) {
            // todo 检测是否填写完整
            mPresenter.commitCheck(inspect, userInfo.getFather());
        }
    }

    @OnClick(R.id.tv_save)
    public void save() {
        if (linkData()) {
            // todo 检测是否填写完整, 保存
            UnSubmitInfo unSubmitInfo = new UnSubmitInfo(Constant.HTTP_ADD_MATERIAL_INSPECT,
                    WorkSubmitActivity.WORK_TYPE_NAME_MATERIAL_INSPECT
                    , userInfo.getId(), projectInfo.getProjectId(), projectInfo.getProjectName(),
                    DateUtil.getTime(DateUtil.YMD_HM),
                    StringUtil.objectToJson(inspect));
            DaoUtil.addUnSubmitInfo(unSubmitInfo);
            showToast("保存成功");
            setResult(1);
            finish();
        }
    }

    @Override
    public void onClickMaterial(Material material) {
        if (StringUtil.isEmpty(material.getSerialId())) {
            goAddMaterial();
        } else {
            // 选择检查材料
            tvCheckName.setText(material.getSerialName() + "(" + material.getRank() + ")");
            inspect.setSerialName(tvCheckName.getText().toString());
            inspect.setSerialNumber(material.getSerialNumber());
        }
        materialPopWindow.dismiss();
    }

    private void goAddMaterial() {
        Intent intent = new Intent();
        intent.setClass(this, MaterialAddActivity.class);
        startActivityForResult(intent, Constant.REQUEST_CODE_ADD_MATERIAL);
    }

    @Override
    public void showCheckCommitResulte(boolean isok) {
        if (isok) {
            showToast("检测报告已送审");
            setResult(2);
            finish();
        } else {
            showToast("提交失败");
        }
    }

    @Override
    public void showMaterials(List<Material> materials) {
        this.materials.clear();
        this.materials.addAll(materials);

    }

//    @Override
//    public void onClickMaterialUnit(MaterialUnit material) {
//        tvUnit.setText(material.getName());
//        unitPopWindow.dismiss();
//    }


    /**
     * 通过url，展示照片
     *
     * @param url
     */
    private void showImage(String url) {

        if (imageDialog != null) {
            imageDialog.showImageDialog(getFragmentManager(), url);
        }
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), REQUEST_CODE_TAKE_PHOTO);
            PhotoCaptureActivity.setWaterListener(waterMaskHelper.getWaterMarkListener());
            PhotoCaptureActivity.setPhotoListener(waterMaskHelper.getPhotoListener());
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    BGAPhotoHelper1 mPhotoHelper1 = new BGAPhotoHelper1();

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_CODE_CHOOSE_PHOTO:
                if (resultCode == Activity.RESULT_OK) {
                    try {
                        startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), REQUEST_CODE_CROP);
                    } catch (Exception e) {
                        mPhotoHelper.deleteCropFile();
                        BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                        e.printStackTrace();
                    }
                }
                break;
            case REQUEST_CODE_TAKE_PHOTO:
                if (resultCode == Activity.RESULT_OK) {
                    try {
                        startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800,
                                800), REQUEST_CODE_CROP);
                    } catch (Exception e) {
                        mPhotoHelper.deleteCameraFile();
                        mPhotoHelper.deleteCropFile();
                        BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                        e.printStackTrace();
                    }
                }
                break;
            case REQUEST_CODE_CROP:
                if (resultCode == Activity.RESULT_OK) {
                    // getCropIntent  获取裁剪完图片的路径
                    String photoPath = mPhotoHelper1.getCropFilePath();
                    File file = new File(mPhotoHelper1.getCropFilePath());
                    double fileSize = FileUtil.getFileOrFilesSize(photoPath, 3);
                    Log.i("fileSize", fileSize + "");
                    if (fileSize > 1) {
                        ImageUtil.compressImageByLuban(photoPath);
                    } else {
                        uploadCorpPhoto(file);
                    }
                } else {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                }
                break;

            case Constant.REQUEST_CODE_UNIT_DANWEI:
                if (resultCode == Activity.RESULT_OK) {
                    mQuestionView.getQuestEdit().setText(data.getStringExtra("data"));
                    mQuestionView.setDocContent(data.getStringExtra("data1"));
                    inspect.setUnitProjectId(data.getStringExtra("ids"));
                }
                break;
        }
    }

    //加水印上传图片
    private void uploadCorpPhoto(File file) {
        //应该在这里把绘制完的水印图片路径传过去
        ArrayList<String> strings = new ArrayList<>();
        strings.add(file.toString());
        if (waterMaskHelper.getPhotoListener() != null) {
            //选择照片的uri，默认为下标1的元素
            waterMaskHelper.getPhotoListener().onChoose(strings);
        }
        if (waterMaskHelper.getWaterMarkListener() != null) {
            LogUtil.e("getWaterMarkListener");
            WaterMask.WaterMaskParam maskParam = waterMaskHelper.getWaterMarkListener().onDraw();
            waterMaskHelper.getWaterMarkListener().onDraw();
            Bitmap bitmap = ImageUtil.getBitmap(String.valueOf((file)));
            WaterMask.draw(mContext, bitmap, String.valueOf((file)), maskParam);
            mContext.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(file)));
        }
        presenter.uploadPhotoImage(file.getPath());
        LogUtil.e("uploadPhotoImage " + file.getPath());
    }

    //新图片压缩
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void messageEvent(ImageCompressEvent event) {
        if (event == null) return;
        if (event.getType() == ImageCompressEvent.TYPE_START) {
            showProgress();
        } else if (event.getType() == ImageCompressEvent.TYPE_END) {
            dismissProgress();
            uploadCorpPhoto(event.getFile());
        } else if (event.getType() == ImageCompressEvent.TYPE_ERROR) {
            showToast("图片压缩失败");
            dismissProgress();
        }
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        BGAPhotoHelper.onSaveInstanceState(mPhotoHelper, outState);
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        BGAPhotoHelper.onRestoreInstanceState(mPhotoHelper, savedInstanceState);
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this);
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().unregister(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(PhotoUploadResulte resulte) {
        dismissProgress();
        showToast(resulte.getMessge());
        if (resulte.isIsok()) {
            // todo 图片上传成功
            String imageUrl = SettingManager.getInstance().getBaseUrl() + resulte.getImageUrl();
            initImage(imageUrl);
        } else {
            initImage(resulte.getImageUrl());
        }


    }

    private void initImage(String url) {
        Log.i("000", "______图片地址：" + url);
//        if (imageType == IMAGE_TYPE_1) {
//            inspect.setCertiFicate(url);
//            ivQualityCard.setImageURI(Uri.parse(url));
//        } else
        if (imageType == IMAGE_TYPE_2) {
            images.add(0, url);
            imageAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 6.0之上权限生情
     */
    private void getPermissions(int code) {
        ActivityCompat.requestPermissions(this, PERMISSIONS, code);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == Constant.REQUEST_CODE_TAKE_POHO) {
            takePhoto();
        }
    }

    private AMapLocationClient locationClient = null;
    private AMapLocationClientOption locationOption = null;

    private void initLocation() {
        //初始化client
        locationClient = new AMapLocationClient(this.getApplicationContext());
        locationOption = getDefaultOption();
        //设置定位参数
        locationClient.setLocationOption(locationOption);
        // 设置定位监听
        locationClient.setLocationListener(locationListener);

    }

    /**
     * 默认的定位参数
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private AMapLocationClientOption getDefaultOption() {
        AMapLocationClientOption mOption = new AMapLocationClientOption();
        mOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
        //可选，设置定位模式，可选的模式有高精度、仅设备、仅网络。默认为高精度模式
        mOption.setGpsFirst(false);//可选，设置是否gps优先，只在高精度模式下有效。默认关闭
        mOption.setHttpTimeOut(30000);//可选，设置网络请求超时时间。默认为30秒。在仅设备模式下无效
        mOption.setInterval(10000);//可选，设置定位间隔。默认为2秒
        mOption.setNeedAddress(true);//可选，设置是否返回逆地理地址信息。默认是true
        mOption.setOnceLocation(false);//可选，设置是否单次定位。默认是false
        mOption.setOnceLocationLatest(false);//可选，设置是否等待wifi刷新，默认为false.如果设置为true,会自动变为单次定位，持续定位时不要使用
        AMapLocationClientOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTP);//可选，
        // 设置网络请求的协议。可选HTTP或者HTTPS。默认为HTTP
        mOption.setSensorEnable(false);//可选，设置是否使用传感器。默认是false
        mOption.setWifiScan(true); //可选，设置是否开启wifi扫描。默认为true，如果设置为false会同时停止主动刷新，停止以后完全依赖于系统刷新，定位位置可能存在误差
        mOption.setLocationCacheEnable(true); //可选，设置是否使用缓存定位，默认为true
        mOption.setGeoLanguage(AMapLocationClientOption.GeoLanguage.DEFAULT);//可选，设置逆地理信息的语言，默认值为默认语言（根据所在地区选择语言）
        return mOption;
    }

    /**
     * 定位监听
     */
    AMapLocationListener locationListener = new AMapLocationListener() {
        @Override
        public void onLocationChanged(AMapLocation location) {
            if (null != location) {
                locationdata = location;
                //errCode等于0代表定位成功，其他的为定位失败，具体的可以参照官网定位错误码说明
                if (location.getErrorCode() == 0) {
                    address = location.getAddress();
                    stopLocation();
                    destroyLocation();
                }
            } else {
                showToast("定位失败");
            }
        }
    };

    /**
     * 开始定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void startLocation() {
        // 设置定位参数
        locationClient.setLocationOption(locationOption);
        // 启动定位
        locationClient.startLocation();
    }

    /**
     * 停止定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void stopLocation() {
        // 停止定位
        locationClient.stopLocation();
    }

    /**
     * 销毁定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void destroyLocation() {
        if (null != locationClient) {
            /**
             * 如果AMapLocationClient是在当前Activity实例化的，
             * 在Activity的onDestroy中一定要执行AMapLocationClient的onDestroy
             */
            locationClient.onDestroy();
            locationClient = null;
            locationOption = null;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        //初始化定位
        initLocation();
        //启动定位
        startLocation();
    }

    @Override
    public void onClickMaterialUnit(MaterialUnit material) {

    }
}
