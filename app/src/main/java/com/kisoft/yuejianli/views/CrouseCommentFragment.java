package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.ContentFrameLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ExamMaterialsInfo;
import com.kisoft.yuejianli.ui.UnitListActivity;
import com.kisoft.yuejianli.utils.GsonUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class CrouseCommentFragment extends BaseFragment {
    Unbinder unbinder;

    @BindView(R.id.addNote_tv)
    TextView addNote;

    @BindView(R.id.comment_rv)
    RecyclerView rvComment;

//    @BindView(R.id.chapter_rv)
//    RecyclerView rvChapter;
//    private ChapterListAdapter mChapterListAdapter;

    private CommentListAdapter mCommentListAdapter;
    ExamMaterialsInfo mData;
    Map mMap;
    int mType;
    @Override
    public int getRootView() {
        return R.layout.fragment_crouse_comment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle != null) {
            mData = (ExamMaterialsInfo) bundle.getSerializable("data");
            mType = bundle.getInt("type");
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);



        initData();
        initView();
        return mRootView;
    }

    void setInfo(Map map){
        mMap = map;
    };

    private void initData() {
        if (mType == 0){
            getExamCourseCommentList(mData.getId());
        }else {
            getExamCourseNoteList(mData.getId());
        }
    }

    private void initView() {
        initCommentView();
        addNote.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mMap != null) {
//                    ExamMaterialsAddNoteActivity.launch(getActivity(),mMap,mType);

//                    ExamMaterialsAddNoteActivity activity1 = new ExamMaterialsAddNoteActivity();
//                    activity1.setSubmitListener(new ExamMaterialsAddNoteActivity.SubmitListener() {
//                        @Override
//                        public void submit(int type) {
//                            mType = type;
//                            initData();
//                        }
//                    });

                    Intent intent = new Intent(mContext, ExamMaterialsAddNoteActivity.class);
                    String s = GsonUtil.GsonString(mMap);
                    intent.putExtra("data",s);
                    intent.putExtra("type",mType);
                    startActivityForResult(intent,10212);


//                    Intent intent = new Intent(mContext, UnitListActivity.class);
//                    intent.putExtra("mType",1);//分项工程3
//                    intent.putExtra("mEndType",4);//抽检工程4
//                    startActivityForResult(intent,Constant.REQUEST_CODE_UNIT_DANWEI);


                }else {
                    showToast("请先播放视频");
                }
            }
        });
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 10212) {
            if (data != null) {
                int data1 = data.getIntExtra("data", 0);
                    mType = data1;
                    initData();
            }
        }
    }

    private void initCommentView() {
        if (rvComment.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            layoutManager.setSmoothScrollbarEnabled(false);
            rvComment.setLayoutManager(layoutManager);
            rvComment.setNestedScrollingEnabled(false);
        }

        mCommentListAdapter=new CommentListAdapter(null);
        rvComment.setAdapter(mCommentListAdapter);
    }

    public class CommentListAdapter extends BaseQuickAdapter<ExamCommentListBean, CommentListAdapter.ViewHolder> {

        public CommentListAdapter(@Nullable List<ExamCommentListBean> data) {
            super(R.layout.item_comment_list, data);
        }

        @Override
        protected void convert(@NonNull CommentListAdapter.ViewHolder helper, ExamCommentListBean item) {
            if (item != null) {
                helper.nameTv.setText(item.getCreateName());
                if (mType == 0){
                    helper.commentTv.setText(item.getCommentContent());
                }else {
                    helper.commentTv.setText(item.getNoteContent());
                }
            }
        }

        public class ViewHolder extends BaseViewHolder {
            @BindView(R.id.name_tv)
            TextView nameTv;
            @BindView(R.id.comment_tv)
            TextView commentTv;

            ViewHolder(View view) {
                super(view);
                ButterKnife.bind(this, view);
            }
        }
    }


    public void getExamCourseCommentList(String categoryId) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("id", categoryId);
        Api.getGbkApiserver().getCourseCommentData("getExamCourseCommentList", parameters).enqueue(new Callback<NetworkResponse<List<ExamCommentListBean>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ExamCommentListBean>>> call,
                                   Response<NetworkResponse<List<ExamCommentListBean>>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    List<ExamCommentListBean> data = response.body().getData();
                    mCommentListAdapter.setNewData(data);
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<List<ExamCommentListBean>>> call, Throwable t) {

            }
        });
    }


    public void getExamCourseNoteList(String categoryId) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("id", categoryId);
        Api.getGbkApiserver().getCourseCommentData("getExamCourseNoteList", parameters).enqueue(new Callback<NetworkResponse<List<ExamCommentListBean>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ExamCommentListBean>>> call,
                                   Response<NetworkResponse<List<ExamCommentListBean>>> response) {
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    List<ExamCommentListBean> data = response.body().getData();
                    mCommentListAdapter.setNewData(data);
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<List<ExamCommentListBean>>> call, Throwable t) {

            }
        });
    }

    public class ExamCommentListBean{

        private String catalogId;
        private String catalogName;
        private String commentContent;
        private String courseInfoId;
        private String courseName;
        private String coursewareId;
        private String coursewareName;
        private String createId;
        private String createName;
        private String createTime;
        private String id;
        private String noteContent;
        private String secret;

        public String getCatalogId() {
            return catalogId;
        }

        public void setCatalogId(String catalogId) {
            this.catalogId = catalogId;
        }

        public String getCatalogName() {
            return catalogName;
        }

        public void setCatalogName(String catalogName) {
            this.catalogName = catalogName;
        }

        public String getCommentContent() {
            return commentContent;
        }

        public void setCommentContent(String commentContent) {
            this.commentContent = commentContent;
        }

        public String getCourseInfoId() {
            return courseInfoId;
        }

        public void setCourseInfoId(String courseInfoId) {
            this.courseInfoId = courseInfoId;
        }

        public String getCourseName() {
            return courseName;
        }

        public void setCourseName(String courseName) {
            this.courseName = courseName;
        }

        public String getCoursewareId() {
            return coursewareId;
        }

        public void setCoursewareId(String coursewareId) {
            this.coursewareId = coursewareId;
        }

        public String getCoursewareName() {
            return coursewareName;
        }

        public void setCoursewareName(String coursewareName) {
            this.coursewareName = coursewareName;
        }

        public String getCreateId() {
            return createId;
        }

        public void setCreateId(String createId) {
            this.createId = createId;
        }

        public String getCreateName() {
            return createName;
        }

        public void setCreateName(String createName) {
            this.createName = createName;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getNoteContent() {
            return noteContent;
        }

        public void setNoteContent(String noteContent) {
            this.noteContent = noteContent;
        }

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }
    }



/*
    private void initChapterView() {
        List<ChapterBean> bean = new ArrayList<>();
        for (int i = 1; i < 27; i++) {
            bean.add(new ChapterBean(i + ""));
        }
        if (rvChapter.getLayoutManager() == null) {
            GridLayoutManager layoutManager = new GridLayoutManager(getContext(), 1);
            layoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
            rvChapter.setLayoutManager(layoutManager);
        }
        mChapterListAdapter=new ChapterListAdapter(bean);
        rvChapter.setAdapter(mChapterListAdapter);

    }
    */

    public class ChapterListAdapter extends BaseQuickAdapter<ChapterBean, ChapterListAdapter.ViewHolder> {

        public ChapterListAdapter(@Nullable List<ChapterBean> data) {
            super(R.layout.item_number_list, data);
        }

        @Override
        protected void convert(@NonNull ChapterListAdapter.ViewHolder helper, ChapterBean item) {
            if (item != null) {
                helper.tvCapaterNumber.setText(item.getNumber());
            }
        }

        public class ViewHolder extends BaseViewHolder {
            @BindView(R.id.tv_capater_number)
            TextView tvCapaterNumber;

            ViewHolder(View view) {
                super(view);
                ButterKnife.bind(this, view);
            }
        }
    }


    private class ChapterBean{
        String name;
        String number;


        public ChapterBean(String number) {
            this.number = number;
        }

        public String getNumber() {
            return number;
        }

        public void setNumber(String number) {
            this.number = number;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}