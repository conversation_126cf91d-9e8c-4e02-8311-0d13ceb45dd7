package com.kisoft.yuejianli.views;

import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.github.mikephil.charting.animation.Easing;
import com.github.mikephil.charting.charts.PieChart;
import com.github.mikephil.charting.components.Description;
import com.github.mikephil.charting.data.PieData;
import com.github.mikephil.charting.data.PieDataSet;
import com.github.mikephil.charting.data.PieEntry;
import com.github.mikephil.charting.formatter.PercentFormatter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.contract.Count1Contract;
import com.kisoft.yuejianli.entity.CalendarData;
import com.kisoft.yuejianli.entity.ProjectprobPieDto;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.Count1Model;
import com.kisoft.yuejianli.presenter.Count1Presenter;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;

public class CountFragment1 extends BaseFragment<Count1Model, Count1Presenter>
        implements Count1Contract.Count1ViewContract {

    @BindView(R.id.tv_month)
    TextView tvTime;
    @BindView(R.id.pie_chart)
    PieChart pieChart;
    private PieData pieData;
    List<PieEntry> datas = new ArrayList<>();


    private String month = "";

    private Count1Model mModel;
    private Count1Presenter presenter;

    private Unbinder unbinder;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        mModel = new Count1Model(mContext);
        presenter = new Count1Presenter(this, mModel);
    }

    @Override
    public int getRootView() {
        return R.layout.fragment_count1;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initView();
        return mRootView;
    }

    private void initView() {
        month = DateUtil.getMonthDate();
        tvTime.setText(month);
        getData();
    }

    private void getData() {
        presenter.getProjectProbPie(SettingManager.getInstance().getProject().getProjectId(), month);
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    List<ProjectprobPieDto> dtoList;

    @Override
    public void dataBack(List<ProjectprobPieDto> dtoList) {
        this.dtoList = dtoList;
        datas.clear();
        if (dtoList != null && !dtoList.isEmpty()) {
            float count = 0;
            for (ProjectprobPieDto dto : dtoList) {
                count += dto.getProbCount();
            }
            for (ProjectprobPieDto dto : dtoList) {
                float f = 0;
                if (dto.getProbCount()!= 0)
                    f = dto.getProbCount();
                if (f!=0) {
                    datas.add(new PieEntry(f / count, dto.getProbTypeName()+":"+dto.getProbCount()+"个"));
                }
            }
        }
        initChart();
    }

    private void initChart() {
        pieData = transCashItem2PieData(this.datas);
        initReportChart(pieChart, pieData);
    }

    /**
     * 饼状统计图 数据交互
     * <p>
     * tempItemList 填充统计图的数据集合
     * <p>
     * return 环状统计图所需的数据对象
     */
    private PieData transCashItem2PieData(List<PieEntry> tempItemList) {

        ArrayList entries = new ArrayList<>();

        ArrayList colors = new ArrayList<>();

        if (tempItemList.size() == 0) {
            entries.add(new PieEntry(100f, "暂无数据"));  //没有数据的状态给设置默认值
            colors.add(Color.rgb(228, 228, 228));  //默认为灰色
        } else {
            for (int i = 0; i < tempItemList.size(); i++) {
                entries.add(tempItemList.get(i));
                colors.add(Color.parseColor(dtoList.get(i).getColorValue()));
            }
        }

        PieDataSet dataSet = new PieDataSet(entries, "");

        dataSet.setSliceSpace(0f);  //设置不同区域之间的间距

        dataSet.setSelectionShift(5f);

        dataSet.setColors(colors);

        PieData data = new PieData(dataSet);

        data.setDrawValues(true);
        data.setValueFormatter(new PercentFormatter());

        data.setValueTextSize(11f);
        data.setValueTextColor(Color.WHITE);

        return data;
    }

    /**
     * 初始化环状统计表
     *
     * @parammChart环状统计图控件
     * @parampieData填充统计图的数据对象
     */
    private void initReportChart(PieChart mChart, PieData pieData) {
        if(mChart==null)return;
        mChart.setUsePercentValues(true);

        mChart.setExtraOffsets(5, 5, 5, 5);  //设置间距

        mChart.setDragDecelerationFrictionCoef(0.95f);

        mChart.setCenterText("");  //设置饼状图中间文字，我需求里面并没有用到这个。。

        mChart.setDrawHoleEnabled(true);

        mChart.setHoleColor(Color.WHITE);

        mChart.setTransparentCircleColor(Color.WHITE);

        mChart.setTransparentCircleAlpha(110);

        mChart.setHoleRadius(15f);

        mChart.setTransparentCircleRadius(20f);

        mChart.setTouchEnabled(false);  //设置是否响应点击触摸

        mChart.setDrawCenterText(true);  //设置是否绘制中心区域文字

        mChart.setDrawEntryLabels(false);  //设置是否绘制标签

        mChart.setRotationAngle(0); //设置旋转角度

        mChart.setRotationEnabled(true); //设置是否旋转

        mChart.setHighlightPerTapEnabled(false);  //设置是否高亮显示触摸的区域

        mChart.setData(pieData);  //设置数据

        mChart.setDrawMarkerViews(false);  //设置是否绘制标记
        Description description = new Description();
        description.setText("");
        mChart.setDescription(description);

        mChart.animateY(1000, Easing.EasingOption.EaseInOutQuad);  //设置动画效果
    }

    @OnClick(R.id.iv_left)
    public void getLastMonth(){
        String time = month;
        month = DateUtil.getLsatMonth(time);
        tvTime.setText(month);
        getData();
    }


    @OnClick(R.id.iv_right)
    public void getNextMonth(){
        String time = month;
        month = DateUtil.getAfterMonth(time);
        tvTime.setText(month);
        getData();
    }
}
