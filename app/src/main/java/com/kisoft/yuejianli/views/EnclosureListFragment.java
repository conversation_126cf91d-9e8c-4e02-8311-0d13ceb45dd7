package com.kisoft.yuejianli.views;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.EnclosureListAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.contract.EnclosureListContract;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.InfoIssueDto;
import com.kisoft.yuejianli.model.EnclosureListModel;
import com.kisoft.yuejianli.presenter.EnclosureListPresenter;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;

/**
 * Description: 我发起的 列表
 * Author     : yanlu
 * Date       : 2019/1/16 17:59
 */
public class EnclosureListFragment extends BaseFragment<EnclosureListModel, EnclosureListPresenter> implements EnclosureListContract.EnclosureListViewContract {
    @BindView(R.id.rv_content)
    RecyclerView rvContent;
    Unbinder unbinder;

    InfoIssueDto.InfoIssueBean bean;

    private EnclosureListAdapter adapter;
    private List<EnclosureListDto> dtoList = new ArrayList<>();

    private EnclosureListModel model;
    private EnclosureListPresenter presenter;

    @Override
    public int getRootView() {
        return R.layout.fragment_enclosure_list;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        bean = (InfoIssueDto.InfoIssueBean) getArguments().getSerializable("data");
        model = new EnclosureListModel(mContext);
        presenter = new EnclosureListPresenter(this, model);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initView();
        return mRootView;
    }

    private void initView() {
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            layoutManager.setSmoothScrollbarEnabled(false);
            rvContent.setLayoutManager(layoutManager);
            rvContent.setNestedScrollingEnabled(false);
        }
        adapter = new EnclosureListAdapter(dtoList);
        rvContent.setAdapter(adapter);
        rvContent.addOnItemTouchListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int position) {
                EnclosureListDto dto1 = dtoList.get(position);
//
//                EnclosureListDto dto = new EnclosureListDto()
                WebActivity.launch(getActivity(),dto1);
//                if (!StringUtil.isEmpty(dtoList.get(position).getFilePath())) {
////                    presenter.downFile(dtoList.get(position).getFileName(), dtoList.get(position).getFilePath());
//                    Intent intent = new Intent();
//                    intent.setAction(Intent.ACTION_VIEW);
//                    intent.setData(Uri.parse(StringUtil.handlerUrl(dtoList.get(position).getFilePath()+"&filename="+dtoList.get(position).getFileName())));
//                    mContext.startActivity(intent);
//                }else {
//                    showToast("附件地址为空");
//                }
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    @Override
    public void EnclosureListBack(List<EnclosureListDto> dtoList) {
        this.dtoList.clear();
        if (dtoList != null) {
            this.dtoList.addAll(dtoList);
        }
        adapter.notifyDataSetChanged();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && presenter != null && bean != null) {
            presenter.getEnclosureList(bean.getId());
        }
    }
}
