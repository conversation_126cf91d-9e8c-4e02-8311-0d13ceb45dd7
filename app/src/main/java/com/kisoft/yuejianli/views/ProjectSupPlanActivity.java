package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.kisoft.yuejianli.adpter.BLGCAdapter;
import com.kisoft.yuejianli.adpter.EnclosureListAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectSupPlanContract;
import com.kisoft.yuejianli.entity.ApproveListBean;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.ProjectFilingSupPlan;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.BindViews;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.model.ProjectSupPlanModel;
import com.kisoft.yuejianli.presenter.ProjectSupPlanPresenter;
import com.kisoft.yuejianli.ui.YFileListView;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/22.
 */

public class ProjectSupPlanActivity extends BaseActivity<ProjectSupPlanModel, ProjectSupPlanPresenter> implements ProjectSupPlanContract.ProjectSupPlanViewContract {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rg_tab)
    RadioGroup rgTab;
    @BindViews({R.id.rb_tab1, R.id.rb_tab2})
    List<RadioButton> radioButtons;
    @BindViews({R.id.view1, R.id.view2})
    List<View> vLines;
    @BindViews({R.id.ll_tab_content1, R.id.ll_tab_content2})
    List<View> llTabContents;

    @BindView(R.id.tv_project_name)
    TextView tvProjectName;

    @BindView(R.id.tv_subject)
    TextView tvSubject;

    @BindView(R.id.tv_charger)
    TextView tvCharger;

    @BindView(R.id.tv_verifier_status)
    TextView tvVerifierStatus;

    @BindView(R.id.tv_content)
    TextView tvContent;


    @BindView(R.id.rv_list)
    RecyclerView rvList;

    @BindView(R.id.y_file_list)
    YFileListView mFileView;
//    @BindView(R.id.rv_list_fj)
//    RecyclerView rvListFJ;

    private ArrayList<EnclosureListDto> mFileList = new ArrayList<>();
    List<ApproveListBean> approveList = new ArrayList<>();
    BLGCAdapter adapter;
    private EnclosureListAdapter enclosureListAdapter;

    private ProjectFilingSupPlan plan;

    private List<EnclosureListDto> dtoList = new ArrayList<>();
    private ProjectSupPlanModel model;
    private ProjectSupPlanPresenter presenter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new ProjectSupPlanModel(this);
        presenter = new ProjectSupPlanPresenter(this, model);
        initMVP(model, presenter);
        initData();
        initView();
    }

    private void initView(){

        if(plan != null){
            switch (plan.getType()){
                case ProjectFilingSupPlan.PLAN_TYPE_A:
                    tvTitle.setText(ProjectFilingSupPlan.PLAN_TYPE_A_NAME);
                    break;
                case ProjectFilingSupPlan.PLAN_TYPE_B:
                    tvTitle.setText(ProjectFilingSupPlan.PLAN_TYPE_B_NAME);
                    break;
                case ProjectFilingSupPlan.PLAN_TYPE_C:
                    tvTitle.setText(ProjectFilingSupPlan.PLAN_TYPE_C_NAME);
                    break;
                case ProjectFilingSupPlan.PLAN_TYPE_D:
                    tvTitle.setText(ProjectFilingSupPlan.PLAN_TYPE_D_NAME);
                    break;
                default:
                    break;
            }
            tvProjectName.setText(plan.getProjectName());
            tvSubject.setText(plan.getSpSubject());
            tvCharger.setText(plan.getSupDirectorName());
            String status = "";
            switch (plan.getWfStatus().trim()) {
                case "1":
                    status = "提交中";
                    break;
                case "2":
                    status = "审批中";
                    break;
                case "3":
                    status = "终止";
                    break;
                case "4":
                    status = "已审批";
                    break;
            }
            tvVerifierStatus.setText(status);
            tvContent.setText(plan.getSpContent());

            rgTab.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(RadioGroup radioGroup, int id) {
                    switch (id) {
                        case R.id.rb_tab1:
                            tabChange(0);
                            break;
                        case R.id.rb_tab2:
                            tabChange(1);
                            break;
                    }
                }
            });

            adapter = new BLGCAdapter(approveList);
            LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            layoutManager.setSmoothScrollbarEnabled(false);
            rvList.setLayoutManager(layoutManager);
            rvList.setNestedScrollingEnabled(false);
            rvList.setAdapter(adapter);

/*
            if (rvListFJ.getLayoutManager() == null) {
                LinearLayoutManager layoutManager1 = new LinearLayoutManager(mContext);
                layoutManager1.setOrientation(LinearLayoutManager.VERTICAL);
                layoutManager1.setSmoothScrollbarEnabled(false);
                rvListFJ.setLayoutManager(layoutManager1);
                rvListFJ.setNestedScrollingEnabled(false);
            }
            enclosureListAdapter = new EnclosureListAdapter(dtoList);
            rvListFJ.setAdapter(enclosureListAdapter);
            rvListFJ.addOnItemTouchListener(new OnItemClickListener() {
                @Override
                public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int position) {
                    if (!StringUtil.isEmpty(dtoList.get(position).getFilePath())) {
                        Intent intent = new Intent();
                        intent.setAction(Intent.ACTION_VIEW);
                        intent.setData(Uri.parse(StringUtil.handlerUrl(dtoList.get(position).getFilePath() + "&filename=" + dtoList.get(position).getFileName())));
                        mContext.startActivity(intent);
                    } else {
                        showToast("附件地址为空");
                    }
                }
            });
            */

            mFileView.setApply(false);

            mFileView.setFileOperateContract(new YFileListView.FileOperateContract() {
                @Override
                public void openFile(int index) {
                    mFileList.remove(index);
                    mFileView.setList(mFileList);
                }
            });
            // 打开附件
            mFileView.setOpenFileContract(new YFileListView.OpenFileContract() {
                @Override
                public void openFile(EnclosureListDto dto) {
                    WebActivity.launch(ProjectSupPlanActivity.this,dto);
                }
            });
            getWfApproveList(plan.getWfId());
            presenter.getEnclosureList(plan.getId());
        }
    }

    private void initData(){
        Intent intent =  getIntent();
        if(intent!= null){
            plan = (ProjectFilingSupPlan) intent.getSerializableExtra(Constant.INTENT_KEY_SUP_PLAN);
        }

    }


    @Override
    public void EnclosureListBack(List<EnclosureListDto> dtoList) {
//        this.dtoList.clear();
//        if (dtoList != null) {
//            this.dtoList.addAll(dtoList);
//        }
//        enclosureListAdapter.notifyDataSetChanged();
        mFileList.clear();
        mFileList.addAll(dtoList);
        mFileView.setList(mFileList);
    }

    private void tabChange(int index) {
        if (index <0 || index>radioButtons.size()) {
            return;
        }
        for (int i=0; i<radioButtons.size(); i++) {
            if (i == index) {
                llTabContents.get(i).setVisibility(View.VISIBLE);
                radioButtons.get(i).setChecked(true);
                radioButtons.get(i).setTextColor(getResources().getColor(R.color.colorAccent));
                vLines.get(i).setBackgroundColor(getResources().getColor(R.color.colorAccent));
            }else {
                llTabContents.get(i).setVisibility(View.GONE);
                radioButtons.get(i).setTextColor(getResources().getColor(R.color.text_main_black));
                vLines.get(i).setBackgroundColor(getResources().getColor(R.color.line_space));
            }
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_project_sup_plan;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    private void getWfApproveList(String wfId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("wfId", wfId);
        Api.getGbkApiserver().getWfApproveList(Constant.HTTP_GET_WF_APPROVE_LIST, pramaras).enqueue(new Callback<NetworkResponse<List<ApproveListBean>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ApproveListBean>>> call, Response<NetworkResponse<List<ApproveListBean>>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    if (response.body().getData() != null) {
                        approveList.clear();
                        approveList.addAll(response.body().getData());
                        adapter.notifyDataSetChanged();
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ApproveListBean>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}
