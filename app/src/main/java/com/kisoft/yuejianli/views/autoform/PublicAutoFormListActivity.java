package com.kisoft.yuejianli.views.autoform;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.PublicAutoFormListAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Description: 流程审批基本信息  审批办理
 * Author     : bhd119
 * QQ         : 602394773
 */
public class PublicAutoFormListActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_wbsList)
    RecyclerView rvWbsList;

    public static final int SECTION_VIEW = 0;
    public static final int ITEM_VIEW = 1;

    private View empotyView;

    private List mList = new ArrayList<>();
    private PublicAutoFormListAdapter mAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initData();
        initView();
    }

    private void initData() {
        getData();
    }

    private void initView() {
        tvTitle.setText("流程表单发起");
        if (rvWbsList.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(this);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvWbsList.setLayoutManager(layoutManager);
        }
        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        mAdapter = new PublicAutoFormListAdapter(mList);
        rvWbsList.setAdapter(mAdapter);
        mAdapter.setOnClickListItemListener(new PublicAutoFormListAdapter.OnClickListItemListener() {
            @Override
            public void onClickItemListener(PublicAutoFormListDataInfo.PublicAutoFormListDataBean info) {
                getFormSetData(info.getId());
            }
        });
    }

//    private void getData() {
//        UserInfo info = SettingManager.getInstance().getUserInfo();
//        Map<String, Object> pramaras = new HashMap<>();
//        pramaras.put("userId", info.getId());
//        Api.getGbkApiserver().getFormDefinitionListIsNotPaged("getFormDefinitionListIsNotPaged_APP", pramaras)
//        .enqueue(new Callback<NetworkResponse<Object>>() {
//            @Override
//            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
//                System.out.println(response.body().getData());
//            }
//
//            @Override
//            public void onFailure(Call<NetworkResponse<Object>> call, Throwable throwable) {
//
//            }
//        });
//    }

    private void getData() {
        UserInfo info = SettingManager.getInstance().getUserInfo();
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId", info.getId());
        Api.getGbkApiserver().getFormDefinitionListIsNotPaged("getFormDefinitionListIsNotPaged_APP", pramaras).enqueue(new Callback<NetworkResponse<List<PublicAutoFormListDataInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<PublicAutoFormListDataInfo>>> call,
                                   Response<NetworkResponse<List<PublicAutoFormListDataInfo>>> response) {

                List<PublicAutoFormListDataInfo> list = response.body().getData();
                System.out.println(list);
                mList.clear();
                for (PublicAutoFormListDataInfo info : list) {
                    mList.add(info);
                    for (PublicAutoFormListDataInfo.PublicAutoFormListDataBean bean : info.getValue()) {
                        mList.add(bean);
                    }
                }
                mAdapter.notifyDataSetChanged();
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<PublicAutoFormListDataInfo>>> call, Throwable throwable) {

            }
        });
    }


    // 获取页面配置信息
//    private void getFormSetData(String formDefinitionId) {
//        Map<String, Object> pramaras = new HashMap<>();
//        pramaras.put("formDefinitionId", formDefinitionId);
//        Api.getGbkApiserver().getFormFieldSetList("getFormFieldSetList_APP", pramaras).enqueue(new Callback<NetworkResponse<Object>>() {
//            @Override
//            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
//                                    Object data = response.body().getData();
//                Log.i("TAG", "onResponse: "+data.toString());
//                if (response.body()!= null && response.body().getCode() == NetworkResponse.OK){
////                    Object data = response.body().getData();
////                    HdmhPublicFormApplyActivity.launch(PublicAutoFormListActivity.this, data);
//                }else {
//                    showToast("页面信息获取失败");
//                }
//            }
//
//            @Override
//            public void onFailure(Call<NetworkResponse<Object>> call, Throwable throwable) {
//
//            }
//        });
//    }
    private void getFormSetData(String formDefinitionId) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("formDefinitionId", formDefinitionId);
        Api.getGbkApiserver().getFormFieldSetList("getFormFieldSetList_APP", pramaras).enqueue(new Callback<NetworkResponse<PublicAutoFormSetDataInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<PublicAutoFormSetDataInfo>> call,
                                   Response<NetworkResponse<PublicAutoFormSetDataInfo>> response) {
                if (response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    PublicAutoFormSetDataInfo data = response.body().getData();
                    PublicAutoFormApplyActivity.launch(PublicAutoFormListActivity.this, data,formDefinitionId);
                }else {
                    showToast("页面信息获取失败");
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<PublicAutoFormSetDataInfo>> call, Throwable throwable) {
                throwable.printStackTrace();
            }
        });
    }


    @Override
    public int getLayoutId() {
        return R.layout.activity_wbs_list;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }
}
