package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.entity.AppPermission;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by tudou on 2018/7/9.
 */

public class ProjectSceneManagerFragment extends BaseFragment {

    private RecyclerView rvContent;

    //    public static final int CONTORL_1 = 1;   //现场巡视
//    public static final int CONTORL_2 = 2;   //检查验收
//    public static final int CONTORL_3 = 3;   //材料验收
//    public static final int CONTORL_4 = 4;   //施工旁站
//    public static final int CONTORL_5 = 5;   //隐蔽工程
//    public static final int CONTORL_6 = 6;   //监理日志
//    public static final int CONTORL_7 = 7;   // 现场安全检测
//    public static final int CONTORL_8 = 8;   //随手拍
//    public static final int CONTORL_9 = 9;   //监理指令
//    public static final int CONTORL_10 = 10;   //危大工程巡检记录
//    public static final int CONTORL_11 = 11;   //见证取样送检记录
//    public static final int CONTORL_12 = 12;   //抽检记录
//    public static final int CONTORL_13 = 13;   //检验批质量验收记录
//    private List<ContorlContent> contents = new ArrayList<>();
//    private ContorlContentAdapter mAdapter;
    List<AppPermission> menuList = new ArrayList<>();
    private ProjectSceneManagerFragmentAdapter mAdapter;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);

        initData();
        initView(inflater);
        return mRootView;

    }

    private void initView(LayoutInflater inflater) {
        rvContent = mRootView.findViewById(R.id.rv_content);
        if (rvContent.getLayoutManager() == null) {
            GridLayoutManager manager = new GridLayoutManager(getContext(), 3);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new ProjectSceneManagerFragmentAdapter(R.layout.item_project_content, menuList);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
//                ContorlContent content = contents.get(position);
//                onClickContorl(content);
                onClickContorl(menuList.get(position));
            }
        });
        rvContent.setAdapter(mAdapter);
    }

    private void onClickContorl(AppPermission item) {
        if (StringUtil.isEqual("YWalkThroughVC", item.getMenuHref())) {
            // 巡视记录
            goProcessQualityCheck();
        } else if (StringUtil.isEqual("YQualityInspectionVC", item.getMenuHref())) {
            // 现场质量验收
            goTaskQualityCheck();
        } else if (StringUtil.isEqual("YMaterialSafeVC", item.getMenuHref())) {
            // 材料进场
            goMeterialQualityCheck();
        } else if (StringUtil.isEqual("YBuildSiderReportVC", item.getMenuHref())) {
            // 施工旁站记录
            goOnsideCheck();
        } else if (StringUtil.isEqual("YHideProjectVC", item.getMenuHref())) {
            // 隐蔽性工程记录
            goShelteredCheck();
        } else if (StringUtil.isEqual("YSafetyInspectionVC", item.getMenuHref())) {
            // 安全检查
            goSafeInspect();
        } else if (StringUtil.isEqual("SpotCheckRecordViewController", item.getMenuHref())) {
            // 抽检记录
            ProjectRecordListActivity.launch(getActivity(), ProjectRecordListActivity.TYPE_SPOT_CHECK);
        } else if (StringUtil.isEqual("SafeRecrodViewController", item.getMenuHref())) {
            // 危大工程巡检记录
            ProjectRecordListActivity.launch(getActivity(), ProjectRecordListActivity.TYPE_DANGER_INSPECTION);
        } else if (StringUtil.isEqual("InspectionRecordViewController", item.getMenuHref())) {
            // 见证取样送检记录
            ProjectRecordListActivity.launch(getActivity(), ProjectRecordListActivity.TYPE_WITNESS_SAMPLES_INSPECT);
        } else if (StringUtil.isEqual("supInstruction", item.getMenuHref())) {
            // 监理指令
            //
            goLog();
        } else if (StringUtil.isEqual("takePicturesVC", item.getMenuHref())) {
            // 随手拍
            goTakePicture();
        } else if (StringUtil.isEqual("QualityRecordViewController", item.getMenuHref())) {
            // 检验批
            ProjectRecordListActivity.launch(getActivity(), ProjectRecordListActivity.TYPE_CHECKOUT_RECORD);
        } else if (StringUtil.isEqual("testEngineeringApp", item.getButtonHtmlContent())) {
            // 工程门户
            // 项目季度、年终考评
            Intent intent = new Intent();
            intent.setClass(getActivity(), ProjectModelCommonDetailWebActivity.class);
            String urlStr = SettingManager.getInstance().getBaseUrl() + "/pds/portal/statementAction.do?method=testEngineeringApp";
            intent.putExtra("url",urlStr);
            intent.putExtra("bid","");
            intent.putExtra("bType",item.getButtonHtmlContent());
            intent.putExtra("noFile",true);
            startActivity(intent);
        } else if (StringUtil.isEqual("WeeklyReportViewController", item.getMenuHref())) {
            // 周报
            WeeklyReportListActivity.launch(getActivity());
        }else {
            // 环保监理日志
            ProjectSceneListActivity.launch(getActivity(),item);
        }
    }


    private void initData() {
//        contents.clear();
        menuList = SettingManager.getInstance().getVisitScenceModelMenuList();

//        AppPermission appPermission = new AppPermission();
//        appPermission.setMenuCode("visit_walk_through");
//        appPermission.setMenuName("周报");
//        appPermission.setMenuHref("WeeklyReportViewController");
//        appPermission.setButtonHtmlContent("T_PROD_WEEKLY_REPORT");
//        menuList.add(appPermission);
//
//        AppPermission appPermission1 = new AppPermission();
//        appPermission1.setMenuCode("visit_walk_through");
//        appPermission1.setMenuName("周报汇总");
//        appPermission1.setButtonHtmlContent("T_PROD_WEEKLY_REPORT_SUMMARY");
//        menuList.add(appPermission1);


//        contents.add(new ContorlContent(CONTORL_1, "现场巡视记录"));
//        contents.add(new ContorlContent(CONTORL_2, "现场质量验收"));
//        contents.add(new ContorlContent(CONTORL_3, "材料、设备进场"));
//        contents.add(new ContorlContent(CONTORL_4, "施工旁站记录"));
//        contents.add(new ContorlContent(CONTORL_5, "隐蔽工程记录"));
//        //contents.add(new ContorlContent(CONTORL_6, "监理日志"));
//        contents.add(new ContorlContent(CONTORL_7, "现场安全检查"));
//        contents.add(new ContorlContent(CONTORL_12, "抽检记录"));
//        contents.add(new ContorlContent(CONTORL_10,"危大工程巡检"));
//        contents.add(new ContorlContent(CONTORL_11,"见证取样送检"));
//        contents.add(new ContorlContent(CONTORL_9,"监理指令"));
//        contents.add(new ContorlContent(CONTORL_8, "随手拍"));
//        contents.add(new ContorlContent(CONTORL_13, "检验批质量验收记录"));

    }

    @Override
    public int getRootView() {
        return R.layout.fragment_scene_manager;
    }

//    private void onClickContorl(ContorlContent content){
//        switch (content.getId()){
//            case CONTORL_1:
//                goProcessQualityCheck();
//                break;
//            case CONTORL_2:
//                goTaskQualityCheck();
//                break;
//            case CONTORL_3:
//                goMeterialQualityCheck();
//                break;
//            case CONTORL_4:
//                goOnsideCheck();
//                break;
//            case CONTORL_5:
//                goShelteredCheck();
//                break;
//            case CONTORL_6:
////                goSupervisionLog();
//                break;
//            case CONTORL_7:
//                goSafeInspect();
//                break;
//            case CONTORL_8:
//                goTakePicture();
//                break;
//            case CONTORL_9:
//                goLog();
//                break;
//            case CONTORL_10:
//                ProjectRecordListActivity.launch(getActivity(),ProjectRecordListActivity.TYPE_DANGER_INSPECTION);
//                break;
//            case CONTORL_11:
//                ProjectRecordListActivity.launch(getActivity(),ProjectRecordListActivity
//                .TYPE_WITNESS_SAMPLES_INSPECT);
//                break;
//            case CONTORL_12:
//                ProjectRecordListActivity.launch(getActivity(),ProjectRecordListActivity.TYPE_SPOT_CHECK);
//                break;
//            case CONTORL_13:
//                ProjectRecordListActivity.launch(getActivity(),ProjectRecordListActivity.TYPE_CHECKOUT_RECORD);
//                break;
//            default:
//                break;
//        }
//    }

    private void goLog() {
        //监理指令
        Intent intent = new Intent();
//        intent.setClass(getContext(),InstructionsActivity.class);
//        startActivity(intent);
        intent.setClass(getContext(), InstructionsListActivity.class);
        startActivity(intent);
    }

    //随手拍
    private void goTakePicture() {
        Intent intent = new Intent();
        intent.setClass(getContext(), TakePictureActivity.class);
        startActivity(intent);
    }

    private void goMeterialQualityCheck() {
        Intent intent = new Intent();
        intent.setClass(getContext(), MaterialAddListActivity.class);
        startActivity(intent);
    }

    private void goTaskQualityCheck() {
        Intent intent = new Intent();
        intent.setClass(getContext(), QualityCheckListActivity.class);
        startActivity(intent);
    }

    private void goProcessQualityCheck() {
        Intent intent = new Intent();
        intent.setClass(getContext(), QualityInspectionListActivity.class);
        startActivity(intent);
    }

    private void goOnsideCheck() {
        Intent intent = new Intent();
        intent.setClass(getContext(), OnsideListActivity.class);
        startActivity(intent);
    }

    private void goShelteredCheck() {
        Intent intent = new Intent();
        intent.setClass(getContext(), QualityInvisibilityListActivity.class);
        startActivity(intent);
    }

    private void goSupervisionLog() {
        Intent intent = new Intent();
        intent.setClass(getContext(), SupervisionLogListActivity.class);
        startActivity(intent);
    }

    private void goSafeInspect() {
        Intent intent = new Intent();
        intent.setClass(getContext(), SafeInspectListActivity.class);
        startActivity(intent);
    }

    public class ProjectSceneManagerFragmentAdapter extends BaseQuickAdapter<AppPermission, BaseViewHolder> {

        public ProjectSceneManagerFragmentAdapter(int layoutResId, @Nullable List<AppPermission> data) {
            super(layoutResId, data);
        }

        @Override
        protected void convert(BaseViewHolder helper, AppPermission item) {
            TextView tvContent = helper.itemView.findViewById(R.id.tv_content);
            Drawable contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_07);
            tvContent.setText(item.getMenuName());

            if (StringUtil.isEqual("YWalkThroughVC", item.getMenuHref())) {
                // 巡视记录
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_01);
            } else if (StringUtil.isEqual("YQualityInspectionVC", item.getMenuHref())) {
                // 现场质量验收
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_02);
            } else if (StringUtil.isEqual("YMaterialSafeVC", item.getMenuHref())) {
                // 材料进场
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_03);
            } else if (StringUtil.isEqual("YBuildSiderReportVC", item.getMenuHref())) {
                // 施工旁站记录
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_04);
            } else if (StringUtil.isEqual("YHideProjectVC", item.getMenuHref())) {
                // 隐蔽性工程记录
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_05);
            } else if (StringUtil.isEqual("YSafetyInspectionVC", item.getMenuHref())) {
                // 安全检查
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_06);
            } else if (StringUtil.isEqual("SpotCheckRecordViewController", item.getMenuHref())) {
                // 抽检记录
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_07);
            } else if (StringUtil.isEqual("SafeRecrodViewController", item.getMenuHref())) {
                // 危大工程巡检记录
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_07);
            } else if (StringUtil.isEqual("InspectionRecordViewController", item.getMenuHref())) {
                // 见证取样送检记录
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_07);
            } else if (StringUtil.isEqual("supInstruction", item.getMenuHref())) {
                // 监理指令
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_07);
            } else if (StringUtil.isEqual("takePicturesVC", item.getMenuHref())) {
                // 随手拍
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_08);
            } else if (StringUtil.isEqual("QualityRecordViewController", item.getMenuHref())) {
                // 检验批
                contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_07);
            }
            contentIc.setBounds(0, 0, contentIc.getMinimumWidth(), contentIc.getMinimumHeight());
            tvContent.setCompoundDrawables(null, contentIc, null, null);

//            switch (item.getId()) {
//                case ControlFragment.CONTORL_1:
//                    contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_01);
//                    break;
//                case ControlFragment.CONTORL_2:
//                    contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_02);
//                    break;
//                case ControlFragment.CONTORL_3:
//                    contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_03);
//                    break;
//                case ControlFragment.CONTORL_4:
//                    contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_04);
//                    break;
//                case ControlFragment.CONTORL_5:
//                    contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_05);
//                    break;
//                case ControlFragment.CONTORL_6:
//                    contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_06);
//                    break;
//                case ControlFragment.CONTORL_7:
//                case ControlFragment.CONTORL_9:
//                case ControlFragment.CONTORL_10:
//                case ControlFragment.CONTORL_11:
//                case ControlFragment.CONTORL_12:
//                case ControlFragment.CONTORL_13:
//                    contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_07);
//                    break;
//                case ControlFragment.CONTORL_8:
//                    contentIc = ContextCompat.getDrawable(YueApplacation.mContext, R.drawable.jianli_08);
//                    break;
//            }

//            }

        }
    }
}
