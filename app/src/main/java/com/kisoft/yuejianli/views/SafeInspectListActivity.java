package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.OnsideAdapter;
import com.kisoft.yuejianli.adpter.SafeInspectAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.SafeInspectListContract;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.ProjectSafeInspection;
import com.kisoft.yuejianli.entity.QualityOnsideInfo;
import com.kisoft.yuejianli.entity.SafeInspectListDto;
import com.kisoft.yuejianli.entity.SideReport;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.SafeInspectListModel;
import com.kisoft.yuejianli.presenter.SafeInspectListPresenter;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * Created by tudou on 2018/5/28.
 */

public class SafeInspectListActivity extends BaseActivity<SafeInspectListModel,SafeInspectListPresenter> implements SafeInspectListContract.SafeInspectListViewContract {


    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.iv_action)
    ImageView ivAction;

    private SafeInspectAdapter mAdapter;
    private List<ProjectSafeInspection> inspects = new ArrayList<>();

    private View empotyView;

    private int count = 0;
    private int page = 1;
    private int pageSize = 20;
    private String month;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private SafeInspectListModel model;
    private SafeInspectListPresenter presenter;
    private boolean mXmkn = false;
    private String selectType;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new SafeInspectListModel(this);
        presenter = new SafeInspectListPresenter(this, model);
        initMVP(model,presenter);
        Intent it = getIntent();
        if (it != null){
            mXmkn = it.getBooleanExtra("xmkb", false);
            selectType = it.getStringExtra("selectType");
        }
        initView();
        initData();
    }

    private void initView(){
        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        tvTitle.setText("现场安全检查");
        if (mXmkn){
            tvSubmit.setVisibility(View.GONE);
        }else {
            tvSubmit.setVisibility(View.VISIBLE);
        }
        tvSubmit.setText("新增");
        ivAction.setVisibility(View.GONE);
        if(rvContent.getLayoutManager() == null){
            LinearLayoutManager layoutManager = new LinearLayoutManager(this);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(layoutManager);
        }
        mAdapter = new SafeInspectAdapter(R.layout.item_material_add , inspects);
        mAdapter.setEmptyView(empotyView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                seeDetail(inspects.get(position));
            }
        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                rvContent.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (inspects.size() >= count) {
                            mAdapter.loadMoreEnd();
                        } else {
                            getData();
                        }
                    }
                }, Constant.LOAD_MORE_DELAY);

            }
        }, rvContent);
        rvContent.setAdapter(mAdapter);
    }

    String c;
    String s;
    String p;

    private void initData(){
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo =SettingManager.getInstance().getProject();
        if(projectInfo != null){
            getData();
        }
    }

    private void getData(){
        c = Integer.toString(count);
        s = Integer.toString(pageSize);
        p = Integer.toString(page);
        month = DateUtil.dateToString(new Date(), DateUtil.YM);
        if (mXmkn) {
            mPresenter.getSafeInspectList("", projectInfo.getProjectId(),month,c,s,p,selectType);
        }else {
            mPresenter.getSafeInspectList(userInfo.getId(), projectInfo.getProjectId(),month,c,s,p,"");
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_onside_list;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }


    @OnClick(R.id.tv_submit)
    public void addMore(){
        Intent intent = new Intent();
        intent.setClass(SafeInspectListActivity.this, SceneSafeInspectActivity.class);
        startActivityForResult(intent, 1);
    }

    private void seeDetail(ProjectSafeInspection inspection){
        Intent intent = new Intent(SafeInspectListActivity.this, SceneSafeInspectActivity.class);
        intent.putExtra("data", inspection);
        startActivity(intent);
    }

    @Override
    public void showSafeInspect(SafeInspectListDto info, int type) {
        if(info!= null){
            count = info.getCount();
            if(info.getList() != null){
                if(type == 0){
                    inspects.clear();
                    page++;
                    inspects.addAll(info.getList());
                }else {
                    page++;
                    inspects.addAll(info.getList());
                    mAdapter.loadMoreComplete();
                }

            }
            mAdapter.notifyDataSetChanged();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case 1:
                count = 0;
                page = 1;
                pageSize = 20;
                getData();
                break;
        }
    }
}
