package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.common.SimpleTextWatcher;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.ui.YFileListView;
import com.kisoft.yuejianli.ui.YLabelCell;
import com.kisoft.yuejianli.ui.YSelectDateCell;
import com.kisoft.yuejianli.ui.YSelectTextFieldCell;
import com.kisoft.yuejianli.ui.YSpinnerCell;
import com.kisoft.yuejianli.ui.YSubmitCell;
import com.kisoft.yuejianli.ui.YTextFieldCell;
import com.kisoft.yuejianli.ui.YTextViewCell;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.GsonUtil;
import com.kisoft.yuejianli.utils.PermissionsChecker;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;
import com.kisoft.yuejianli.views.watermark.ImageUtil;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 见证取样送检
 */
public class EnvironmentalInspectionActivity extends BaseActivity {


    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;

    @BindView(R.id.projectName)
    YLabelCell projectName; // 工程名称

    @BindView(R.id.checkType)
    YSpinnerCell checkType; // 检查类型

    @BindView(R.id.serialNumber)
    YTextFieldCell serialNumber; // 编号

    @BindView(R.id.inspectedUnit)
    YTextViewCell inspectedUnit; // 受检单位

    @BindView(R.id.checkTime)
    YSelectDateCell checkTime; // 检查日期

    @BindView(R.id.organizationUnit)
    YTextViewCell organizationUnit; // 组织单位

    @BindView(R.id.participatingUnit)
    YTextViewCell participatingUnit; // 参检人员监理单位

    @BindView(R.id.participatingDepartment)
    YTextViewCell participatingDepartment; // 参检人员项目部

    @BindView(R.id.recorderName)
    YSelectTextFieldCell recorderName; // 记录人


    @BindView(R.id.tvAddDetail)
    TextView tvAddDetail;
    @BindView(R.id.tvDeleteDetail)
    TextView tvDeleteDetail;
    @BindView(R.id.llOpDetail)
    LinearLayout llOpDetail;
    @BindView(R.id.recycleViewDetail)
    RecyclerView recycleViewDetail;

    @BindView(R.id.y_file_list)
    YFileListView mFileView;

    @BindView(R.id.submit_cell)
    YSubmitCell submitCell;// 提交

    private boolean isAdd;
    private String id;


    private PermissionsChecker permissionsChecker;

    private ProjectInfo projectInfo;
    private UserInfo userInfo;
    Map mData = new HashMap();
    private EnvInspectionAdapter mAdapter1;
    private String formKey = "";
    private String bType = "";
    private String bId = "";
    private static final int SELECT_CHECK_USER = 10001;
    private Map<String, Object> mPageData;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getIntent() != null) {
            bType = getIntent().getStringExtra("btype");
            isAdd = getIntent().getBooleanExtra("isAdd", false);
            if (!isAdd) {
                bId = getIntent().getStringExtra("bId");
                mData = (Map<String, Object>) getIntent().getSerializableExtra(
                        "EnvironmentalInspectionActivityData");
            }
        }
        mPageData = ProjectModelCommonDetailData.getPageData(bType);
        init();
        fileMethod();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_environmental_inspection;
    }


    private void init() {
        tvTitle.setText(mPageData.get("pageTitle").toString());
        permissionsChecker = new PermissionsChecker(this);
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();

        if (mData == null) {
            mData = new HashMap<>();
            mData.put("createId", userInfo.getId());
            mData.put("projectId", projectInfo.getProjectId());
            mData.put("projectName", projectInfo.getProjectName());
        }


        recycleViewDetail.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
        mAdapter1 = new EnvInspectionAdapter(!isAdd);
        List<Map> m1 = (List<Map>) mData.get("prodEnvironmentalCheckItem");
        mAdapter1.setNewData(m1);
        recycleViewDetail.setAdapter(mAdapter1);

        mContext = this;
        permissionsChecker = new PermissionsChecker(this);

        if (isAdd) {
            llOpDetail.setVisibility(View.VISIBLE);
            if (projectInfo != null) {
                projectName.getTvContent().setText(projectInfo.getProjectName());
                mData.put("projectId", projectInfo.getProjectId());
                mData.put("projectName", projectInfo.getProjectName());


                mData.put("createTime", DateUtil.dateToString(new Date(), DateUtil.YMD_HMS));
                mData.put("projectName", projectInfo.getProjectName());
                mData.put("projectId", projectInfo.getProjectId());
                mData.put("createId", userInfo.getId());
                mData.put("createName", userInfo.getName());
                mData.put("deptName", userInfo.getFatherName());
                mData.put("deptId", userInfo.getFather());
                mData.put("companyId", userInfo.getCompanyId());
                mData.put("companyName", userInfo.getCompanyName());


            }
            // 数据绑定
            projectName.getTvContent().setText(mData.get("projectName").toString());

            // 检查类型
            ArrayAdapter<String> mCheckTypeAdapter;
            ArrayList<String> checkTypeList = new ArrayList<>(Arrays.asList("专项", "其它"));
            mCheckTypeAdapter = new ArrayAdapter<>(mContext, android.R.layout.simple_list_item_1, checkTypeList);
            checkType.getSpinner().setAdapter(mCheckTypeAdapter);
            checkType.getSpinner().setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> adapterView, View view, int position, long id) {
                    //mInfo.setSecurityTime(position + "");
                    String s = checkTypeList.get(position);
                    //mInfo.setSecurityTime(s);
                    mData.put("checkType", s);//检查类型
                }

                @Override
                public void onNothingSelected(AdapterView<?> adapterView) {

                }
            });

            submitCell.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    uploadMulFile();
                }
            });

        } else {

            try {
                projectName.getTvContent().setText(mData.get("projectName").toString()); // 工程名称
                checkType.getSpinnerText().setText(mData.get("checkType").toString()); // 检查类型
                serialNumber.getEtContent().setText(mData.get("serialNumber").toString()); // 编号
                inspectedUnit.getEtContent().setText(mData.get("inspectedUnit").toString()); // 受检单位
                checkTime.getTvContent().setText(mData.get("checkTime").toString()); // 检查日期
                organizationUnit.getEtContent().setText(mData.get("organizationUnit").toString()); // 组织单位
                participatingUnit.getEtContent().setText(mData.get("participatingUnit").toString()); // 参检人员监理单位
                participatingDepartment.getEtContent().setText(mData.get("participatingDepartment").toString()); //
                // 参检人员项目部
                recorderName.getTvContent().setText(mData.get("recorderName").toString()); // 记录人
            } catch (Exception e) {

            }


            llOpDetail.setVisibility(View.INVISIBLE);
            projectName.getTvContent().setEnabled(false); // 工程名称
            checkType.getSpinnerText().setEnabled(false); //检查类型
            serialNumber.getEtContent().setEnabled(false); // 编号
            inspectedUnit.getEtContent().setEnabled(false); // 受检单位
            checkTime.getTvContent().setEnabled(false);
            ;//检查日期
            organizationUnit.getEtContent().setEnabled(false); // 组织单位
            participatingUnit.getEtContent().setEnabled(false); // 参检人员监理单位
            participatingDepartment.getEtContent().setEnabled(false); // 参检人员项目部
            recorderName.getTvContent().setEnabled(false); // 记录人
            submitCell.setVisibility(View.GONE);

            // 查看详情
            getFileList(bId);
        }
    }


    private void sendData(Map param) {
//        Log.i("TAG", "messageEvent: " + adapter1.getData());

//        String recorde = recorderName.getTvContent().getText().toString().trim();
//        if (StringUtil.isEmpty(recorde)) {
//            showToast("请选择记录人");
//            return;
//        }
//        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
//        if (userInfo != null) {
//            mData.put("createId", userInfo.getId());
//            mData.put("createName", userInfo.getName());
//        }
//        mData.put("createTime", DateUtil.dateToString(new Date(), DateUtil.YMD));
//        mData.put("projectName", projectName.getTvContent().getText().toString()); // 工程名称
        // mData.put("checkType", checkType.getSpinnerText().getText().toString()); // 检查类型
        mData.put("serialNumber", serialNumber.getEtContent().getText().toString()); // 编号
        mData.put("inspectedUnit", inspectedUnit.getEtContent().getText().toString()); // 受检单位
        mData.put("checkTime", checkTime.getTvContent().getText().toString()); // 检查日期
        mData.put("organizationUnit", organizationUnit.getEtContent().getText().toString()); // 组织单位
        mData.put("participatingUnit", participatingUnit.getEtContent().getText().toString()); // 参检人员监理单位
        mData.put("participatingDepartment", participatingDepartment.getEtContent().getText().toString()); // 参检人员项目部
//        mData.put("recorderName", recorderName.getTvContent().getText().toString()); // 记录人
        mData.put("prodEnvironmentalCheckItem", mAdapter1.getData());
        Log.i("TAG", "save: " + mData);
        Map<String, Object> parameters = new HashMap<>();
        try {
            parameters.put("data", URLEncoder.encode(StringUtil.objectToJson(mData), "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        String url = SettingManager.getInstance().getBaseUrl() + mPageData.get("saveUrl").toString();
        OkHttpRequestManager.getInstance().post(url, parameters, new IRequestCallback() {
            @Override
            public void onSuccess(String response) {
                Log.i("TAG", "onSuccess: " + response);
//                Map<String, Object> map = GsonUtil.GsonToMaps(response);
//                Map<String, Object> map1 = (Map<String, Object>) map.get("data");
//                mList = (List<Map<String, Object>>) map1.get("data");
                dismissProgress();
                try {
                    Map<String, Object> map = GsonUtil.GsonToMaps(response);
                    int code = (int) Double.parseDouble(map.get("code").toString());
                    if (code == 200) {
                        finish();
                    } else {
                        showToast(map.get("message").toString());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    showToast("提交失败！");
                }
            }

            @Override
            public void onFailure(Throwable throwable) {
                throwable.printStackTrace();
                dismissProgress();
                showToast("提交失败！");
            }
        });
    }


    @OnClick({R.id.iv_back, R.id.tvAddDetail, R.id.tvDeleteDetail, R.id.recorderName, R.id.checkTime})
    void buttonClick(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.tvAddDetail:
                mAdapter1.addData(getAddDetail1());
                break;
            case R.id.tvDeleteDetail:
                if (mAdapter1.getData().size() > 0) {
                    mAdapter1.remove(mAdapter1.getData().size() - 1);
                }
                break;
            case R.id.recorderName:
                // 记录人
                Intent intent = new Intent();
                intent.putExtra("isSingle", true);
                intent.setClass(mContext, CompanyOrgInfoActivity.class);
                startActivityForResult(intent, SELECT_CHECK_USER);
                break;
            case R.id.checkTime:
                // 移交日期
                showDatePickerDialog(checkTime, "checkTime");
                break;
        }
    }

    private Map getAddDetail1() {
        HashMap map = new HashMap<>();
        map.put("checkCategoryName", "");
        map.put("checkContentName", "");
        map.put("checkSituation", "");
        map.put("rectificationRequest", "");
        return map;
    }

    /**
     * 展示日期选择对话框
     */
    private void showDatePickerDialog(YSelectDateCell tv, String key) {
        Calendar c = Calendar.getInstance();
        new DatePickerDialog(mContext, new DatePickerDialog.OnDateSetListener() {
            @Override
            public void onDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                // TODO Auto-generated method stub
                c.set(year, monthOfYear, dayOfMonth);
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                Log.i("TAG", "onDateSet: " + format.format(c.getTime()));
                int m = monthOfYear + 1;
                String month = m >= 10 ? m + "" : "0" + m;
                String day = dayOfMonth >= 10 ? dayOfMonth + "" : "0" + dayOfMonth;
                String dateStr = year + "-" + month + "-" + day;
                tv.getTvContent().setText(dateStr);
                mData.put(key, dateStr);
                Log.i("mDataMap", "showDatePickerDialog: ---->" + mData);
            }
        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)).show();
    }


    private ArrayList<EnclosureListDto> mFileList = new ArrayList<>();
    private boolean isTakePhoto;
    private BGAPhotoHelper mPhotoHelper;

    private void fileMethod() {
        mFileView.setApply(isAdd);
        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);
        // 获取主键
        getPrimarykey();

        // 点击添加附件
        mFileView.getAddFileBtn().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showFileSelect();
            }
        });
        // 删除附件
        mFileView.setFileOperateContract(new YFileListView.FileOperateContract() {
            @Override
            public void openFile(int index) {
                mFileList.remove(index);
                mFileView.setList(mFileList);
            }
        });
        // 打开附件
        mFileView.setOpenFileContract(new YFileListView.OpenFileContract() {
            @Override
            public void openFile(EnclosureListDto dto) {
                WebActivity.launch(EnvironmentalInspectionActivity.this, dto);
            }
        });
    }

    private void showFileSelect() {
        // 空照片 ，添加
        String[] str = new String[]{"系统相机", "手机相册", "手机文件"};
        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
        ab.setCancelable(true);
        ab.setItems(str, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                switch (which) {
                    case 0://相机
                        isTakePhoto = true;
                        takePhoto();
                        break;
                    case 1://相册
                        isTakePhoto = false;
                        getPhoto();
                        break;
                    case 2://手机文件
                        getFile();
                        break;
                }
            }
        });
        ab.show();
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), Constant.REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), Constant.REQUEST_CODE_TAKE_PHOTO);
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    /**
     * 打开文件选择器
     */
    private void getFile() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("*/*");// 任意类型文件
        this.startActivityForResult(intent, Constant.REQUEST_CODE_FILE_SELECT);
    }

    BGAPhotoHelper1 mPhotoHelper1 = new BGAPhotoHelper1();

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQUEST_CODE_CHOOSE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_TAKE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800, 800),
                            Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_CROP) {
            if (resultCode == Activity.RESULT_OK) {
                // getCropIntent  获取裁剪完图片的路径
                String photoPath = mPhotoHelper1.getCropFilePath();
//                showToast(photoPath);
                File file = new File(mPhotoHelper1.getCropFilePath());
                double fileSize = FileUtil.getFileOrFilesSize(photoPath, 3);
                Log.i("fileSize", fileSize + "");

                //showToast(photoPath);
                if (fileSize > 1) {
                    ImageUtil.compressImageByLuban(photoPath);
                    String s = ImageUtil.compressImage(photoPath);
                    mFileList.add(new EnclosureListDto(file.getName(), s));
                } else {
                    mFileList.add(new EnclosureListDto(file.getName(), photoPath));
                }
                mFileView.setList(mFileList);
            } else {
                mPhotoHelper.deleteCameraFile();
                mPhotoHelper.deleteCropFile();
            }
        } else if (requestCode == Constant.REQUEST_CODE_FILE_SELECT) {
            String path = "";
            if (data == null) {
                // 用户未选择任何文件，直接返回
                return;
            }
            path = FileUtil.getRealPath(this, data.getData());
            File file = new File(path);
            mFileList.add(new EnclosureListDto(file.getName(), path));
            mFileView.setList(mFileList);
//            showToast(path);
        } else if (requestCode == SELECT_CHECK_USER) {
            if (data != null) {
                List<String> ids = new ArrayList<>(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                List<String> names = new ArrayList<>(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                if (names.size() > 0) {
                    recorderName.getTvContent().setText(names.get(0));
                    mData.put("recorderName", names.get(0)); // 记录人
                    mData.put("recorderId", ids.get(0)); // 记录人id
                }
            }
        }
    }

    private void uploadMulFile() {
        if (mFileList.isEmpty()) {
            sendData(mData);
        } else {
            if (formKey.isEmpty()) {
                getPrimarykey();
                showToast("未获取到主键");
                return;
            }
            mData.put("id", formKey);
            showProgress();
            String s = DateUtil.dateToString(new Date(), DateUtil.YM1);
            HashMap<String, String> paras = new HashMap<>();
            paras.put("filePath", mPageData.get("filePath").toString() + s);

            paras.put("businessId", formKey);
            paras.put("businessType", bType);
            Log.i("TAG", "uploadMulFile: " + paras.toString());
            OkHttpRequestManager.getInstance().uploadMulFile(mFileList, paras, new IRequestCallback() {
                @Override
                public void onSuccess(String response) {
                    Log.i("upload", "onSuccess: ");
                    dismissProgress();
                    sendData(mData);
                }

                @Override
                public void onFailure(Throwable throwable) {
                    Log.i("upload", "onFailure: ");
                    dismissProgress();
                }
            });
        }
    }

    // 获取附件列表
    private void getFileList(String bt) {
        HashMap<String, Object> paras = new HashMap<>();
        paras.put("businessId", bt);
        paras.put("businessType", bType);
        Api.getGbkApiserver().getEnclosureList(Constant.HTTP_GET_ENCLOSURE_LIST, paras).enqueue(new Callback<NetworkResponse<List<EnclosureListDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<EnclosureListDto>>> call,
                                   Response<NetworkResponse<List<EnclosureListDto>>> response) {
                if (response.body().getData() != null) {
                    mFileList.clear();
                    mFileList.addAll(response.body().getData());
                    mFileView.setList(mFileList);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<EnclosureListDto>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }


    // 获取UUID
    protected void getPrimarykey() {
        String busId = "";
        Api.getGbkApiserver().testApi("getUUID", new HashMap<>()).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    formKey = response.body().getData().toString();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }


//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void messageEvent(BeanEvent beanEvent) {
//        if (beanEvent != null) {
//            if (BeanEvent.TYPE_CHECKOUT_RECORD_ITEMSEL1.equals(beanEvent.getDataType())) {
//                Map map = (Map) beanEvent.getData();
//                mAdapter1.getData().get(index1).put("sn",map.get("id"));
//                mAdapter1.getData().get(index1).put("snText",map.get("sn"));
//                mAdapter1.getData().get(index1).put("checksAndCheckmethod",map.get("checks"));
//                mAdapter1.notifyDataSetChanged();
//            }
//        }
//    }


    public class EnvInspectionAdapter extends BaseQuickAdapter<Map, YBaseViewHolder> {

        private boolean isOnlyShow = true;

        public EnvInspectionAdapter(boolean isOnlyShow) {
            super(R.layout.item_env_inspection);
            this.isOnlyShow = isOnlyShow;
        }

        @Override
        protected void convert(@NonNull YBaseViewHolder helper, Map map) {
            EditText checkCategoryName = helper.getView(R.id.checkCategoryName);
            EditText checkContentName = helper.getView(R.id.checkContentName);
            EditText checkSituation = helper.getView(R.id.checkSituation);
            EditText rectificationRequest = helper.getView(R.id.rectificationRequest);
            if (isOnlyShow) {
                checkCategoryName.setFocusable(false);
                checkContentName.setFocusable(false);
                checkSituation.setFocusable(false);
                rectificationRequest.setFocusable(false);
            } else {

                //清除焦点
                checkCategoryName.clearFocus();
                checkContentName.clearFocus();
                checkSituation.clearFocus();
                rectificationRequest.clearFocus();
                //先清除之前的文本改变监听
                if (checkCategoryName.getTag() instanceof TextWatcher) {
                    checkCategoryName.removeTextChangedListener((TextWatcher) checkCategoryName.getTag());
                }
                if (checkContentName.getTag() instanceof TextWatcher) {
                    checkContentName.removeTextChangedListener((TextWatcher) checkContentName.getTag());
                }
                if (checkSituation.getTag() instanceof TextWatcher) {
                    checkSituation.removeTextChangedListener((TextWatcher) checkSituation.getTag());
                }
                if (rectificationRequest.getTag() instanceof TextWatcher) {
                    rectificationRequest.removeTextChangedListener((TextWatcher) rectificationRequest.getTag());
                }
            }

            //设置数据
            checkCategoryName.setText(StringUtil.isEmpty(map.get("checkCategoryName").toString()) ? "" : map.get(
                    "checkCategoryName").toString());
            checkContentName.setText(StringUtil.isEmpty(map.get("checkContentName").toString()) ? "" : map.get(
                    "checkContentName").toString());
            checkSituation.setText(StringUtil.isEmpty(map.get("checkSituation").toString()) ? "" : map.get(
                    "checkSituation").toString());
            rectificationRequest.setText(StringUtil.isEmpty(map.get("rectificationRequest").toString()) ? "" :
                    map.get("rectificationRequest").toString());

            if (!isOnlyShow) {

                //文本改变监听
                final TextWatcher getCheckCategoryName = new SimpleTextWatcher() {
                    @Override
                    public void afterTextChanged(Editable editable) {
                        if (TextUtils.isEmpty(editable)) {
//                        item.setSampleAction(null);
                            map.put("checkCategoryName", "");
                        } else {
//                        item.setSampleAction(String.valueOf(editable));
                            map.put("checkCategoryName", String.valueOf(editable));
                        }
                    }
                };

                final TextWatcher getCheckContentName = new SimpleTextWatcher() {
                    @Override
                    public void afterTextChanged(Editable editable) {
                        if (TextUtils.isEmpty(editable)) {
//                        item.setSampleNum(null);
                            map.put("checkContentName", "");
                        } else {
//                        item.setSampleNum(String.valueOf(editable));
                            map.put("checkContentName", String.valueOf(editable));
                        }
                    }
                };

                final TextWatcher getCheckSituation = new SimpleTextWatcher() {
                    @Override
                    public void afterTextChanged(Editable editable) {
                        if (TextUtils.isEmpty(editable)) {
//                        item.setSampleNum(null);
                            map.put("checkSituation", "");
                        } else {
//                        item.setSampleNum(String.valueOf(editable));
                            map.put("checkSituation", String.valueOf(editable));
                        }
                    }
                };


                final TextWatcher getRectificationRequest = new SimpleTextWatcher() {
                    @Override
                    public void afterTextChanged(Editable editable) {
                        if (TextUtils.isEmpty(editable)) {
//                        item.setSampleNum(null);
                            map.put("rectificationRequest", "");
                        } else {
//                        item.setSampleNum(String.valueOf(editable));
                            map.put("rectificationRequest", String.valueOf(editable));
                        }
                    }
                };


                //监听设置到不同的EditText上
                checkCategoryName.addTextChangedListener(getCheckCategoryName);
                checkCategoryName.setTag(getCheckCategoryName);

                checkContentName.addTextChangedListener(getCheckContentName);
                checkContentName.setTag(getCheckContentName);

                checkSituation.addTextChangedListener(getCheckSituation);
                checkSituation.setTag(getCheckSituation);

                rectificationRequest.addTextChangedListener(getRectificationRequest);
                rectificationRequest.setTag(getRectificationRequest);
            }
        }
    }
}
