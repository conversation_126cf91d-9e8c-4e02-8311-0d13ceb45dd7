package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.widget.TextView;

import com.chad.library.adapter.base.entity.MultiItemEntity;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ComBookAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ComFileContract;
import com.kisoft.yuejianli.entity.ComFile;
import com.kisoft.yuejianli.entity.ComFileType;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ComFileModel;
import com.kisoft.yuejianli.presenter.ComFilePresenter;
import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/3/17.
 */

public class KnowledgeActivity extends BaseActivity<ComFileModel, ComFilePresenter> implements
        ComFileContract.ComFileViewContract, ComBookAdapter.OnClickComBookListener {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;
    private List<MultiItemEntity> mList = new ArrayList<>();
    private List<ComFileType> types = new ArrayList<>();
    private List<ComFile> files = new ArrayList<>();
    private ComBookAdapter mAdapter;


    private UserInfo us;
    private ComFileModel model;
    private ComFilePresenter presenter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        model = new ComFileModel(this);
        presenter = new ComFilePresenter(this, model);
        initMVP(model, presenter);
        initView();
        initData();
    }

    private void initView() {
        tvTitle.setText("公共资源");
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
            linearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(linearLayoutManager);
        }
        mAdapter = new ComBookAdapter(mList);
        mAdapter.setOnClickComBookListener(this);
        rvContent.setAdapter(mAdapter);

    }

    private void initData() {
        us = SettingManager.getInstance().getUserInfo();
        if (us != null) {
            mPresenter.getComFileType(us.getId());
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_knowledge;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @Override
    public void showComFileType(List<ComFileType> comFileTypes) {
        if (comFileTypes == null || comFileTypes.size() == 0) {
            // 模拟数据
            types.clear();
            types.add(new ComFileType("1", "政策法规"));
            types.add(new ComFileType("2", "技术规范"));
            types.add(new ComFileType("3", "图集"));
            types.add(new ComFileType("4", "技术文件"));
            types.add(new ComFileType("5", "安全健康"));
        } else {
            types.clear();
            types.addAll(comFileTypes);
        }
        mPresenter.getComFile(us.getId());
    }

    @Override
    public void showConFile(List<ComFile> files) {
        if (files == null) {
            this.files.clear();
            this.files.add(new ComFile("生产安全事故报告和调查处理条例","1","http://www.hebeijz.com/falvfagui/193.html"));
            this.files.add(new ComFile("中华人民共和国城市房地产管理法","1","http://www.hebeijz.com/falvfagui/205.html"));
            this.files.add(new ComFile("中国建设工程质量管理条例","2","http://www.hebeijz.com/falvfagui/160.html"));
            this.files.add(new ComFile("建筑起重机械安全监督管理规定","2","http://www.hebeijz.com/falvfagui/192.html"));
            this.files.add(new ComFile("建筑构造","3","http://www.chinabuilding.com.cn/books.html"));
            this.files.add(new ComFile("工程施工技术","4","http://www.shigongjishu.cn/Soft/"));
            this.files.add(new ComFile("建筑工地安全防护手册","5","http://www.safehoo.com/Manage/Trade/Build/201601/426609.shtml"));

        } else {
            this.files.clear();
            this.files.addAll(files);
        }
        // todo 配置自己的参数
        handelData();
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void onClickComBook(ComFile file) {
        Intent intent = new Intent();
        intent.setClass(this, PdfViewActivity.class);
        intent.putExtra(Constant.INTENT_KEY_FILE_URL, file.getViewUrl());
        intent.putExtra(Constant.INTENT_CAN_ZOOM, true);
        intent.putExtra(Constant.INTENT_KEY_FILE_NAME, file.getFileName());
        startActivity(intent);
    }


    /**
     * 处理树型数据
     */
    private void handelData() {
        mList.clear();
        for (ComFileType fileType : types) {
            for(ComFile file : files){
                if(StringUtil.isEqual(fileType.getTypeId(), file.getTypeId())){
                    fileType.addSubItem(file);
                }
            }

            mList.add(fileType);
        }
    }
}
