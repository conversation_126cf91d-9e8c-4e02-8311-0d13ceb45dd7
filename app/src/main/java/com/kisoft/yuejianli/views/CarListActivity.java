package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.CarListAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.CarListDataInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class CarListActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.p_list)
    PullToRefreshLayout pList;
    @BindView(R.id.r_list)
    RecyclerView rList;


    private int page = 1;           // 第几页
    private int count = 0;          // 总条数

    CarListAdapter adapter;
    List<CarListDataInfo> carInfos = new ArrayList<>();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_car_list;
    }

    private void initView() {
        tvTitle.setText("车辆列表");
        pList.setRefreshListener(new BaseRefreshListener() {
            @Override
            public void refresh() {
                pList.setCanLoadMore(false);
                page = 1;
                carInfos.clear();
                requestData();
            }

            @Override
            public void loadMore() {
                if (carInfos.size() >= count){
                    pList.finishLoadMore();
                    pList.setCanLoadMore(false);
                    showToast("已经到底了");
                    return;
                }
                page++;
                requestData();
            }
        });

        if(rList.getLayoutManager() == null){
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rList.setLayoutManager(manager);
        }
        adapter = new CarListAdapter(R.layout.item_contract_info_list, carInfos);
        rList.setAdapter(adapter);
        rList.addOnItemTouchListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int position) {
                Intent intent = new Intent();
                intent.putExtra("data", carInfos.get(position));
                setResult(Activity.RESULT_OK, intent);
                finish();
            }
        });

        pList.setCanRefresh(true);
        pList.autoRefresh();
    }

    @OnClick(R.id.iv_back)
    public void onClick() {
        finish();
    }

    private void requestData() {
        Api.getGbkApiserver().getCarListJSON(Constant.HTTP_GET_CAR_LIST, new HashMap<String, Object>()).enqueue(new Callback<NetworkResponse<List<CarListDataInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<CarListDataInfo>>> call,
                                   Response<NetworkResponse<List<CarListDataInfo>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    List<CarListDataInfo> info = response.body().getData();
                    if (info != null) {
                        carInfos.addAll(info);
                        adapter.notifyDataSetChanged();
                    }
                }
                pList.finishRefresh();
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<CarListDataInfo>>> call, Throwable t) {
                pList.finishRefresh();
                t.printStackTrace();
            }
        });


//        Api.getGbkApiserver().getCarListJSON(Constant.HTTP_GET_CAR_LIST, new HashMap<String, Object>()).enqueue(new Callback<NetworkResponse<Object>>() {
//            @Override
//            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
//                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
//                    String s = GsonUtil.GsonString(response.body());
//                    Log.i("TAG", "onResponse: s = " + s);
//
//                }
//                pList.finishRefresh();
//            }
//
//            @Override
//            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
//                pList.finishRefresh();
//                t.printStackTrace();
//            }
//        });



    }

}


//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_subcontract_list);
//    }
//}