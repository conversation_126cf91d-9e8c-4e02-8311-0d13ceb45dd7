package com.kisoft.yuejianli.views;

import android.app.AlertDialog;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.os.Environment;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.JsPromptResult;
import android.webkit.JsResult;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ProgressBar;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.gaof.utils.CleanLeakUtils;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.QuestionListAdapter;
import com.kisoft.yuejianli.adpter.interf.GlobalListener;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.entity.ExamMaterialsInfo;
import com.kisoft.yuejianli.entity.ProjectProbDto;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.QuestionListModel;
import com.kisoft.yuejianli.presenter.QuestionListPresenter;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.UpdateAppHttpUtil;
import com.vector.update_app.HttpManager;

import java.io.File;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;


public class CourseIntroductionFragment extends BaseFragment {
    Unbinder unbinder;


    @BindView(R.id.course_name)
    TextView courseName;
    @BindView(R.id.course_teacher_tv)
    TextView teacherName;
    @BindView(R.id.learns_tv)
    TextView learns;
    @BindView(R.id.hours_tv)
    TextView hours;
    @BindView(R.id.intro_tv)
    TextView intro;


//    @BindView(R.id.crouse_webView)
//    WebView webView;
//    @BindView(R.id.progressBar)
//    ProgressBar progressBar;

    ExamMaterialsInfo data = null;
    @Override
    public int getRootView() {
        return R.layout.fragment_course_introduction;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle != null) {
            data = (ExamMaterialsInfo) bundle.getSerializable("data");
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initData();
        initView();
        return mRootView;
    }


    private void initData() {


    }

    private void initView() {
        //initWebView();

        if (data != null){

            courseName.setText(data.getCourseName());
            teacherName.setText(data.getCreateName());
            learns.setText(data.getNumberOfStudents() + "人学过");
            hours.setText("学时：" +   data.getHoursOfStudents());
//            hours.setText("学时：" + StringUtil.transSecondTimeToHours(Integer.valueOf(data.getHoursOfStudents())));
            intro.setText(data.getCourseIntroduction());
        }
    }
/*
    private void initWebView() {

        String url = "https://www.easemob.com/protocol";
        webView.setWebChromeClient(webChromeClient);
        webView.setWebViewClient(webViewClient);
        WebSettings webSettings = webView.getSettings();
        webSettings.setTextZoom(100);
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setJavaScriptEnabled(true);//允许使用js
        webView.loadUrl(url);
    }


    private boolean isLoad = false;
    //WebViewClient主要帮助WebView处理各种通知、请求事件
    private WebViewClient webViewClient = new WebViewClient() {
        @Override
        public void onPageFinished(WebView view, String url) {//页面加载完成
            isLoad = true;
            progressBar.setVisibility(View.GONE);
        }

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {//页面开始加载
            progressBar.setVisibility(View.VISIBLE);
        }

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            Log.i("ansen", "拦截url:" + url);
            return super.shouldOverrideUrlLoading(view, url);
        }
    };

    //WebChromeClient主要辅助WebView处理Javascript的对话框、网站图标、网站title、加载进度等
    private WebChromeClient webChromeClient = new WebChromeClient() {
        //不支持js的alert弹窗，需要自己监听然后通过dialog弹窗
        @Override
        public boolean onJsAlert(WebView webView, String url, String message, JsResult result) {
            AlertDialog.Builder localBuilder = new AlertDialog.Builder(webView.getContext());
            localBuilder.setMessage(message).setPositiveButton("确定", null);
            localBuilder.setCancelable(false);
            localBuilder.create().show();

            //注意:
            //必须要这一句代码:result.confirm()表示:
            //处理结果为确定状态同时唤醒WebCore线程
            //否则不能继续点击按钮
            result.confirm();
            return true;
        }

        @Override
        public boolean onJsPrompt(WebView view, String url, String message, String defaultValue,
                                  JsPromptResult result) {
            return super.onJsPrompt(view, url, message, defaultValue, result);
        }

        //获取网页标题
        @Override
        public void onReceivedTitle(WebView view, String title) {
            super.onReceivedTitle(view, title);
            Log.i("ansen", "网页标题:" + title);
//            if(null!=tvTitle&&!StringUtil.isEmpty(title)){
//                tvTitle.setText(title);
//            }
        }

        //加载进度回调
        @Override
        public void onProgressChanged(WebView view, int newProgress) {
            progressBar.setProgress(newProgress);
        }
    };


    @Override
    public void onDestroy() {
        super.onDestroy();
        if (null != webView) {
            CleanLeakUtils.freeWebview(webView);
            webView = null;
        }
    }
*/
}
