package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import android.view.View;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.MailDetailContract;
import com.kisoft.yuejianli.entity.MailInfoDetail;
import com.kisoft.yuejianli.entity.event.MessageEvent;
import com.kisoft.yuejianli.model.MailDetailModel;
import com.kisoft.yuejianli.presenter.MailDetailPresenter;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;

/**
 * 邮箱详情转发
 */
public class MailTransmitFragment extends BaseFragment<MailDetailModel, MailDetailPresenter> implements MailDetailContract.View {
    private static final String ARG_PARAM1 = "param1";
    private static final int SELECT_RECEIVER = 100;
    private static final int SELECT_CCMAN = 200;
    @BindView(R.id.tvReceiverName)
    TextView tvReceiverName;
    @BindView(R.id.tvCcmanName)
    TextView tvCcmanName;
    @BindView(R.id.tvCommit)
    TextView tvCommit;
    Unbinder unbinder;

    private String fmailId;
    private StringBuffer ccmanNames=new StringBuffer(),ccmanIds=new StringBuffer(),
            receiverNames=new StringBuffer(),receiverIds=new StringBuffer();

    public MailTransmitFragment() {
        // Required empty public constructor
    }

    /**
     * Use this factory method to create a new instance of
     * this fragment using the provided parameters.
     *
     * @param param1 Parameter 1.
     * @return A new instance of fragment MailTransmitFragment.
     */
    public static MailTransmitFragment newInstance(String param1) {
        MailTransmitFragment fragment = new MailTransmitFragment();
        Bundle args = new Bundle();
        args.putString(ARG_PARAM1, param1);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            fmailId = getArguments().getString(ARG_PARAM1);
        }
        mModel = new MailDetailModel(getContext());
        mPresenter = new MailDetailPresenter(this, mModel);
    }

    @Override
    public int getRootView() {
        return R.layout.fragment_mail_transmit;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
    }

    @OnClick({R.id.tvReceiverName,R.id.tvCcmanName,R.id.tvCommit})
    void buttonClick(View view){
        switch (view.getId()){
            case R.id.tvReceiverName:
                getPersonInfo(SELECT_RECEIVER);
                break;
            case R.id.tvCcmanName:
                getPersonInfo(SELECT_CCMAN);
                break;
            case R.id.tvCommit:
                if(StringUtil.isEmpty(tvReceiverName.getText().toString().trim())){
                    showToast("请选择收件人");
                    return;
                }
                mPresenter.transImailInBox(fmailId);
                break;
        }
    }

    @Override
    public void showMailDetail(MailInfoDetail mailInfoDetail) {

    }

    @Override
    public void transOk(MailInfoDetail mailInfoDetail) {
        if(null!=mailInfoDetail){
            mailInfoDetail.setFcreateDate(DateUtil.dateToString(new Date(),DateUtil.YMD_HMS));
            mailInfoDetail.setFreceiverNames(receiverNames.toString());
            mailInfoDetail.setFreceivers(receiverIds.toString());
            if(!StringUtil.isEmpty(tvCcmanName.getText().toString().trim())){
                mailInfoDetail.setFccmanNames(ccmanNames.toString());
                mailInfoDetail.setFccmanIds(ccmanIds.toString());
            }
            mPresenter.sendMail(mailInfoDetail);
        }else {
            showToast("发送失败！");
        }
    }

    @Override
    public void sendMail(boolean isOk) {
        if (isOk) {
            showToast("邮件已发送");
            EventBus.getDefault().post(new MessageEvent(MessageEvent.TYPE_UPDATE_MAIL_LIST));
            getActivity().setResult(2);
            getActivity().finish();
        } else {
            showToast("发送失败！");
        }
    }

    @Override
    public void showError() {
        showToast("转发失败");
    }

    public void getPersonInfo(int requestCode) {
        Intent intent = new Intent();
        intent.putExtra("isSingle", false);
        intent.setClass(mContext, CompanyOrgInfoActivity.class);
        startActivityForResult(intent, requestCode);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case SELECT_RECEIVER:
                if (data != null) {
                    receiverNames = new StringBuffer();
                    receiverIds = new StringBuffer();
                    List<String> ids = new ArrayList<>(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                    List<String> names = new ArrayList<>(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                    if (names.size() > 0) {
                        for (int i = 0; i < names.size(); i++) {
                            if (i != 0) {
                                receiverNames.append(",");
                            }
                            receiverNames.append(names.get(i));
                        }
                        tvReceiverName.setText(receiverNames.toString());

                        for (int i = 0; i < ids.size(); i++) {
                            if (i != 0) {
                                receiverIds.append(",");
                            }
                            receiverIds.append(ids.get(i));
                        }
                    }
                }
                break;
            case SELECT_CCMAN: {
                if (data != null) {
                    ccmanNames = new StringBuffer();
                    ccmanIds = new StringBuffer();
                    List<String> ids = new ArrayList<>(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                    List<String> names = new ArrayList<>(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                    if (names.size() > 0) {
                        for (int i = 0; i < names.size(); i++) {
                            if (i != 0) {
                                ccmanNames.append(",");
                            }
                            ccmanNames.append(names.get(i));
                        }
                        tvCcmanName.setText(ccmanNames.toString());

                        for (int i = 0; i < ids.size(); i++) {
                            if (i != 0) {
                                ccmanIds.append(",");
                            }
                            ccmanIds.append(ids.get(i));
                        }
                    }
                }
            }
            break;
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }
}
