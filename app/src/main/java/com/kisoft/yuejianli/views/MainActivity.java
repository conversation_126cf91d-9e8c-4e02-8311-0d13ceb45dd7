package com.kisoft.yuejianli.views;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.ViewPager;

import android.view.View;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.adpter.MFragmentAdapter;
import com.kisoft.yuejianli.contract.MainActiivtyContract;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.ProjectWsInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.MainActivityModel;
import com.kisoft.yuejianli.presenter.MainActivitypresenter;
import com.kisoft.yuejianli.utils.PhoneUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.ui.ProjectSelector;

/**
 * Created by tudou on 2018/3/8.
 */
public class MainActivity extends BaseActivity<MainActivityModel, MainActivitypresenter> implements
        MainActiivtyContract.MainActivityViewContract, RadioGroup.OnCheckedChangeListener,
        ViewPager.OnPageChangeListener, ProjectSelector.OnClickProjectListener {


    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;

    @BindView(R.id.main_page)
    ViewPager mainPage;
    @BindView(R.id.main_bottom_bar)
    RadioGroup mianBottom;
    @BindView(R.id.rb_project)
    RadioButton rbproject;
    @BindView(R.id.rb_control)
    RadioButton rbcontrol;
    @BindView(R.id.rb_news)
    RadioButton rbNews;
    @BindView(R.id.rb_user)
    RadioButton rbUser;

    private UserInfo userInfo;
    private ProjectSelector projectSelector;
    private List<ProjectInfo> projects = new ArrayList<>();
    private ProjectInfo defalProject;
    private ArrayList<Fragment> fragments = new ArrayList<>();
    private int foucs = 0;
    private MainActivityModel model;
    private MainActivitypresenter presenter;
    private int colorChecked = ContextCompat.getColor(YueApplacation.mContext, R.color.colorAccent);
    private int colorUncheck = ContextCompat.getColor(YueApplacation.mContext, R.color.ic_text_normal);


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new MainActivityModel(this);
        presenter = new MainActivitypresenter(this, model);
        initMVP(model, presenter);
        initData();
        initView();
    }

    public void initView() {
        tvTitle.setPadding(0,0, PhoneUtil.dpTopx(this,50),0);
        mainPage.setAdapter(new MFragmentAdapter(getSupportFragmentManager(), fragments));
        mainPage.setCurrentItem(foucs);
        mainPage.addOnPageChangeListener(this);
        mianBottom.setOnCheckedChangeListener(this);
        ivBack.setVisibility(View.INVISIBLE);
        ivAction.setVisibility(View.VISIBLE);
        ivAction.setImageResource(R.drawable.ic_project_list);
        projectSelector = new ProjectSelector();
        projectSelector.setOnClickProjectListener(this);
        rbNews.setTextColor(colorChecked);
        initDefalProject();
        if(userInfo != null){
            getAllProjects();
        }
    }

    public void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        fragments.add(new NewsFragment());       // 协同办公
        fragments.add(new ProjectFragment());    // 经营管理
        fragments.add(new ControlFragment());    // 工程监理
        fragments.add(new UserFragment());       // 人力资源

        if (foucs == 0) {
            mianBottom.check(R.id.rb_news);
        } else if (foucs == 1) {
            mianBottom.check(R.id.rb_project);
        } else if (foucs == 2) {
            mianBottom.check(R.id.rb_control);
        } else if (foucs == 3) {
            mianBottom.check(R.id.rb_user);
        }

//        PhoneUtil.getUniqueInstance().getAddress(this);

    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_main;
    }


    @OnClick(R.id.iv_action)
    public void changeProject() {
        if (projectSelector != null) {
            projectSelector.showProjectSeletor(this, getFragmentManager(), projects);
        }
    }

    private void getAllProjects() {
        mPresenter.getUserProjects(userInfo.getId(), userInfo.getCompanyId(), userInfo.getUserOrgRelation());

    }

    private void initDefalProject(){
        if(SettingManager.getInstance().getProject() != null){
            this.defalProject = SettingManager.getInstance().getProject();
            tvTitle.setPadding(0,0, PhoneUtil.dpTopx(this,50),0);
            tvTitle.setText(defalProject.getProjectName());
        }
    }


    @Override
    public void onCheckedChanged(RadioGroup radioGroup, int i) {
        switch (i) {
            case R.id.rb_project:
                foucs = 1;
                rbproject.setTextColor(colorChecked);
                rbcontrol.setTextColor(colorUncheck);
                rbNews.setTextColor(colorUncheck);
                rbUser.setTextColor(colorUncheck);
                break;
            case R.id.rb_control:
                foucs = 2;
                rbproject.setTextColor(colorUncheck);
                rbcontrol.setTextColor(colorChecked);
                rbNews.setTextColor(colorUncheck);
                rbUser.setTextColor(colorUncheck);
                break;
            case R.id.rb_news:
                foucs = 0;
                rbproject.setTextColor(colorUncheck);
                rbcontrol.setTextColor(colorUncheck);
                rbNews.setTextColor(colorChecked);
                rbUser.setTextColor(colorUncheck);
                break;
            case R.id.rb_user:
                foucs = 3;
                rbproject.setTextColor(colorUncheck);
                rbcontrol.setTextColor(colorUncheck);
                rbNews.setTextColor(colorUncheck);
                rbUser.setTextColor(colorChecked);
                break;
            default:
                break;

        }
        setTitleBar(foucs);
        mainPage.setCurrentItem(foucs);
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        this.foucs = position;
        switch (position) {
            case 1:
                mianBottom.check(R.id.rb_project);
                break;
            case 2:
                mianBottom.check(R.id.rb_control);
                break;
            case 0:
                mianBottom.check(R.id.rb_news);
                break;
            case 3:
                mianBottom.check(R.id.rb_user);
                break;
            default:
                break;

        }
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    private void setTitleBar(int id) {
        switch (id) {
            case 1:
                tvTitle.setPadding(0,0,PhoneUtil.dpTopx(this,50),0);
                tvTitle.setText(defalProject.getProjectName());
                ivAction.setVisibility(View.VISIBLE);
                break;
            case 2:
                tvTitle.setPadding(9,0,9,0);
                tvTitle.setText("项目监理");
                ivAction.setVisibility(View.INVISIBLE);
                break;
            case 0:
                tvTitle.setPadding(9,0,9,0);
                tvTitle.setText("消息");
                ivAction.setVisibility(View.INVISIBLE);
                break;
            case 3:
                tvTitle.setPadding(9,0,9,0);
                tvTitle.setText("我的");
                ivAction.setVisibility(View.INVISIBLE);
                break;
        }
    }

    @Override
    public void onClickProject(ProjectInfo info) {
        SettingManager.getInstance().saveProject(info);
        this.defalProject = info;
        tvTitle.setText(defalProject.getProjectName());
    }

    @Override
    public void showUserprojects(List<ProjectInfo> projects) {
        if (projects != null && projects.size() > 0) {
            this.projects.clear();
            this.projects.addAll(projects);

            SettingManager.getInstance().saveAllProjects(this.projects);
            if(defalProject == null){
                this.defalProject = projects.get(projects.size()-1);
                SettingManager.getInstance().saveProject(defalProject);
//                if(tvTitle!= null){
//                    tvTitle.setText(defalProject.getProjectName());
//                }
            }
        }
        mPresenter.getProjectWsInfo(userInfo.getId(), defalProject.getProjectId());
        mPresenter.getProjectOrgInfo(defalProject.getProjectId());
    }

    @Override
    public void showProjectWsInfo(List<ProjectWsInfo> infos) {
        if(infos != null && infos.size()>0){
            SettingManager.getInstance().saveProjectWsInfo(infos);
        }
    }
}
