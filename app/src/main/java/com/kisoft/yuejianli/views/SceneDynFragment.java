package com.kisoft.yuejianli.views;

import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.github.mikephil.charting.animation.Easing;
import com.github.mikephil.charting.charts.PieChart;
import com.github.mikephil.charting.components.Description;
import com.github.mikephil.charting.data.PieData;
import com.github.mikephil.charting.data.PieDataSet;
import com.github.mikephil.charting.data.PieEntry;
import com.github.mikephil.charting.formatter.PercentFormatter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;

public class SceneDynFragment extends BaseFragment {

    @BindView(R.id.pie_chart)
    PieChart pieChart;//饼形统计图
    private PieData pieData;
    private Unbinder unbinder;

    private List<Integer> partColor = new ArrayList<>();

    @Override
    public int getRootView() {
        return R.layout.activity_scene_dyn_fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initColor();
        initView();
        return mRootView;
    }

    private void initView() {
        List<Float> list = new ArrayList<>();
        String procent = "4.0";
        if (!StringUtil.isEmpty(procent)) {
            float f = Float.parseFloat(procent.replace("%", ""));
            float p = f > 100 ? 1 : (f < 0 ? 0 : f / 100);
            list.add(p);
            list.add(1 - p);
        }
        pieData = transCashItem2PieData(list);
        initReportChart(pieChart, pieData);
    }

    private void initColor() {
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_2));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_4));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_1));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_3));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_5));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_6));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_7));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_8));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_9));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_10));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_11));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_12));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_13));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_14));
        partColor.add(ContextCompat.getColor(YueApplacation.mContext, R.color.chat_color_15));
    }

    /**
     * 饼状统计图 数据交互
     * <p>
     * tempItemList 填充统计图的数据集合
     * <p>
     * return 环状统计图所需的数据对象
     */

    private PieData transCashItem2PieData(List<Float> tempItemList) {

        ArrayList entries = new ArrayList<>();

        ArrayList colors = new ArrayList<>();


        if (tempItemList.size() == 0) {

            entries.add(new PieEntry(100f, "暂无数据"));  //没有数据的状态给设置默认值

            colors.add(Color.rgb(228, 228, 228));  //默认为灰色

        } else {
            float p;                          // 专项占比

            for (int i = 0; i < tempItemList.size(); i++) {
                p = tempItemList.get(i);

                if (i < partColor.size() - 1) {
                    entries.add(new PieEntry(p, i == 0 ? "已整改" : "整改中"));
                    colors.add(partColor.get(i));
                } else if (i == partColor.size() - 1) {
                    entries.add(new PieEntry(p, "其它"));
                    colors.add(partColor.get(i));
                }

            }
        }

        PieDataSet dataSet = new PieDataSet(entries, "");

        dataSet.setSliceSpace(0f);  //设置不同区域之间的间距

        dataSet.setSelectionShift(5f);

        dataSet.setColors(colors);

        PieData data = new PieData(dataSet);

        data.setValueFormatter(new PercentFormatter());

        data.setValueTextSize(11f);

        data.setValueTextColor(Color.WHITE);

        return data;

    }

    /**
     * 初始化环状统计表
     *
     * @parammChart环状统计图控件
     * @parampieData填充统计图的数据对象
     */

    private void initReportChart(PieChart mChart, PieData pieData) {

        mChart.setUsePercentValues(true);

        mChart.setExtraOffsets(5, 10, 5, 5);  //设置间距

        mChart.setDragDecelerationFrictionCoef(0.95f);

        mChart.setCenterText("");  //设置饼状图中间文字，我需求里面并没有用到这个。。

        mChart.setDrawHoleEnabled(true);

        mChart.setHoleColor(Color.WHITE);

        mChart.setTransparentCircleColor(Color.WHITE);

        mChart.setTransparentCircleAlpha(110);

        mChart.setHoleRadius(15f);

        mChart.setTransparentCircleRadius(20f);

        mChart.setTouchEnabled(false);  //设置是否响应点击触摸

        mChart.setDrawCenterText(true);  //设置是否绘制中心区域文字

        mChart.setDrawEntryLabels(false);  //设置是否绘制标签

        mChart.setRotationAngle(0); //设置旋转角度

        mChart.setRotationEnabled(true); //设置是否旋转

        mChart.setHighlightPerTapEnabled(false);  //设置是否高亮显示触摸的区域

        mChart.setData(pieData);  //设置数据

        mChart.setDrawMarkerViews(false);  //设置是否绘制标记
        Description des = new Description();
        des.setText("");
        mChart.setDescription(des);

        mChart.animateY(1000, Easing.EasingOption.EaseInOutQuad);  //设置动画效果
    }

}
