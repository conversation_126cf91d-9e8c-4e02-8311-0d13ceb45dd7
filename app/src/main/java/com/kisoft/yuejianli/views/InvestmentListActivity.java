package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.adpter.InvestmentListAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.InvestmentListContract;
import com.kisoft.yuejianli.entity.ContractInfo;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.InvestmentListModel;
import com.kisoft.yuejianli.presenter.InvestmentListPresnter;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/7.
 */
public class InvestmentListActivity extends BaseActivity<InvestmentListModel, InvestmentListPresnter> implements
        InvestmentListContract.InvestmentListViewContract{

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_month)
    TextView tvTime;

    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    private List<ContractInfo> contractInfos = new ArrayList<>();
    private InvestmentListAdapter mAdapter;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    private InvestmentListModel model;
    private InvestmentListPresnter presnter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new InvestmentListModel(this);
        presnter = new InvestmentListPresnter(this ,model);
        initMVP(model, presnter);
        initView();
    }

    private void initView(){
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        if(projectInfo!= null){
            mPresenter.getContracts(userInfo.getId(), projectInfo.getProjectId());
        }

        tvTitle.setText("投资控制");
        if(rvContent.getLayoutManager() == null){
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new InvestmentListAdapter(R.layout.item_investment_list, contractInfos);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                goContractDetail(contractInfos.get(position));
            }
        });
        rvContent.setAdapter(mAdapter);
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_contract_list;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    private void goContractDetail(ContractInfo contractInfo){
        Intent intent = new Intent(this, InvestmentInfoActivity.class);
        intent.putExtra(Constant.INTENT_KEY_INVESTMENT_INFO ,contractInfo);
        startActivity(intent);
    }

    @Override
    public void showContracts(List<ContractInfo> infos) {
        this.contractInfos.clear();
        if(infos!= null){
            this.contractInfos.addAll(infos);
        }
        mAdapter.notifyDataSetChanged();
    }
}
