package com.kisoft.yuejianli.views;


import android.os.Bundle;
import androidx.recyclerview.widget.RecyclerView;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;

public class ArchivesTransferAddActivity extends BaseActivity {
    @BindView(R.id.tv_submit)
    TextView textViewAdd;

    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.rv_ptenclosure)
    RecyclerView rvPtenclosure;

    @Override
    public int getLayoutId() {
        return R.layout.activity_archives_transfer_add;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initData();
        initView();

    }

    private void initData() {

    }

    private void initView() {
        tvTitle.setText("竣工档案移交");
    }
}
