package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.KeyProcessListAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ProcessKeyProjectBean;
import com.kisoft.yuejianli.utils.GsonUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class KeyProcessListActivity extends BaseActivity {
    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    private List<ProcessKeyProjectBean> mData = new ArrayList<>();
    private KeyProcessListAdapter mAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
        initData();
    }

    private void initView() {

        tvTitle.setText("关键工序");
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            manager.setSmoothScrollbarEnabled(false);
            rvContent.setLayoutManager(manager);
            rvContent.setNestedScrollingEnabled(false);
        }

        mAdapter = new KeyProcessListAdapter(R.layout.item_dangers_list, mData);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                ProcessKeyProjectBean bean = mData.get(position);
                // 直接返回
                Intent intent = new Intent();
                intent.putExtra("KeyProject", bean.getKeyProject());
                intent.putExtra("CheckProject", bean.getCheckProject());
                setResult(Activity.RESULT_OK, intent);
                finish();
            }
        });
        rvContent.setAdapter(mAdapter);
        //        mAdapter = new UnitListAdapter(R.layout.item_dangers_list, mEndType, mDatas,
//                new GlobalListener<UnitListBean.ListDataBean>() {
//                    @Override
//                    public void onViewClick(int id, int position, UnitListBean.ListDataBean model) {
//
//                    }
//                    @Override
//                    public void onRootViewClick(View view, int position, UnitListBean.ListDataBean model) {
//                        if (model != null) {
//                                    // 直接返回
//                                    Intent intent = new Intent();
//                                    intent.putExtra("data", model.getName());
//                                    intent.putExtra("ids", model.getId());
//                                    intent.putExtra("childData", (Serializable) model.getChildList());
//                                    Log.d("TAG", "onRootViewClick: " + model.toString());
//                                    setResult(Activity.RESULT_OK, intent);
//                                    finish();
//
//                        }
//                    }
//                });
    }

    private void initData() {
        Map<String, Object> pramars = new HashMap<>();
        pramars.put("keyProject" , "");
        Api.getGbkApiserver().getKeyProcessAcceptList(Constant.HTTP_GET_KEY_PROCESS_LIST, pramars).enqueue(new Callback<NetworkResponse<List<ProcessKeyProjectBean>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ProcessKeyProjectBean>>> call,
                                   Response<NetworkResponse<List<ProcessKeyProjectBean>>> response) {
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    String s = GsonUtil.GsonString(response.body().getData());
                    mData = response.body().getData();
                    mAdapter.setNewData(mData);
                    mAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ProcessKeyProjectBean>>> call, Throwable t) {

            }
        });
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_key_process_list;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

}