package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.DatePicker;
import android.widget.LinearLayout;

import androidx.annotation.MainThread;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.ui.YFileListView;
import com.kisoft.yuejianli.ui.YLabelCell;
import com.kisoft.yuejianli.ui.YSelectDateCell;
import com.kisoft.yuejianli.ui.YSelectTextViewCell;
import com.kisoft.yuejianli.ui.YSpinnerCell;
import com.kisoft.yuejianli.ui.YSubmitCell;
import com.kisoft.yuejianli.ui.YTextViewCell;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.views.watermark.ImageUtil;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;


public class ApplySSRenYuanSuoDingFragment  extends BaseFragment {

    public static final int TRANSFERUSERNAME_CODE = 43438;//移交人
    public static final int RECEIVEUSERNAME_CODE = 43978;//接收人

    public View mRootView;
    Unbinder unbinder;
    boolean mIsApply = false;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    String saveType;// 保存
    String getType;// 获取


    @BindView(R.id.projectName)
    YLabelCell projectName;//项目名称

    @BindView(R.id.userName)
    YSelectTextViewCell userName;//姓名/锁人

    @BindView(R.id.phone)
    YTextViewCell phone;//联系方式

    @BindView(R.id.person)
    YSelectTextViewCell person;//负责人

    @BindView(R.id.tell)
    YTextViewCell tell;//负责人联系方式

    @BindView(R.id.area)
    YLabelCell area;//省市区/县

    @BindView(R.id.type)
    YSpinnerCell type;//锁人类型


    //成都市锁人
    @BindView(R.id.lockType1)
    LinearLayout lockType1;

    @BindView(R.id.cardName)
    YTextViewCell cardName; // 证件名称

    @BindView(R.id.dateLock)
    YSelectDateCell dateLock; // 锁定日期

    @BindView(R.id.bePerson)
    YSpinnerCell bePerson; // 人员归属

    @BindView(R.id.dateUnlock)
    YSelectDateCell dateUnlock; // 计划解锁日期

    @BindView(R.id.dateInfactUnlock)
    YSelectDateCell dateInfactUnlock; // 实际解锁日期

    @BindView(R.id.unlockRequiredFile)
    YTextViewCell unlockRequiredFile; // 解锁必备资料

    @BindView(R.id.process)
    YTextViewCell process; // 办理进度


    //省建厅在建锁人
    @BindView(R.id.lockType2)
    LinearLayout lockType2;

    @BindView(R.id.pLockType)
    YSpinnerCell pLockType; // 锁人类型

    @BindView(R.id.personType)
    YSpinnerCell personType; // 人员类别

    @BindView(R.id.post)
    YSpinnerCell post; // 职务

    @BindView(R.id.dateOutnetLock)
    YSelectDateCell dateOutnetLock; // 外网锁定日期

    @BindView(R.id.dateOutnetUnlock)
    YSelectDateCell dateOutnetUnlock; // 外网解锁日期

    @BindView(R.id.dateInnetLock)
    YSelectDateCell dateInnetLock; // 内网锁定日期

    @BindView(R.id.dateInnetUnlock)
    YSelectDateCell dateInnetUnlock; // 内网解锁日期

    @BindView(R.id.remark)
    YTextViewCell remark; // 备注

    @BindView(R.id.submit_cell)
    YSubmitCell tvSub;

    @BindView(R.id.y_file_list)
    YFileListView mFileView;
    Map<String, Object> mDataMap = new HashMap<>();


    private String formKey = "";

    ArrayList<String> pLockTypeList = new ArrayList<>(Arrays.asList("公共服务平台-(外网锁定)","公共服务平台-(外网解锁)","一体化工作平台-(内网锁定)","一体化工作平台-(内网解锁)"));

    @Override
    public int getRootView() {
        return R.layout.fragment_apply_ss_renyuan_suoding;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initData();
        initView();
        fileMethod();
        initListener();
        return mRootView;
    }

    private void initView() {
        //锁人类型
        ArrayList<String> typeList = new ArrayList<>(Arrays.asList("成都市锁人","省建厅在建锁人","其他省份锁人","职务"));
        ArrayAdapter<String> typeAdapter = new ArrayAdapter<>(mContext, android.R.layout.simple_list_item_1, typeList);
        type.getSpinner().setAdapter(typeAdapter);
        type.getSpinner().setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                String s = typeList.get(i);
                if (0 == i || 2==i){
                    lockType1.setVisibility(View.VISIBLE);
                    lockType2.setVisibility(View.GONE);
                    unlockRequiredFile.getEtContent().setText("1、工程施工许可证/安监备案表原件扫描件 2、被锁人员社保证明 3、线下经办人身份证"); // 解锁必备资料
                }else {
                    lockType2.setVisibility(View.VISIBLE);
                    lockType1.setVisibility(View.GONE);
                }
                mDataMap.put("type",s);
            }
            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });

        //人员归属
        ArrayList<String> bePersonList = new ArrayList<>(Arrays.asList("公司","分公司","事业合伙人"));
        ArrayAdapter<String> bePersonAdapter = new ArrayAdapter<>(mContext, android.R.layout.simple_list_item_1, bePersonList);
        bePerson.getSpinner().setAdapter(bePersonAdapter);
        bePerson.getSpinner().setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                String s = bePersonList.get(i);
                mDataMap.put("bePerson",s);
            }
            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });

        //锁人类型

        ArrayAdapter<String> pLockTypeAdapter = new ArrayAdapter<>(mContext, android.R.layout.simple_list_item_1, pLockTypeList);
        pLockType.getSpinner().setAdapter(pLockTypeAdapter);
        pLockType.getSpinner().setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
//                String s = pLockTypeList.get(i);
                mDataMap.put("pLockType",i+"");
            }
            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });

        // 人员类别
        ArrayList<String> personTypeList = new ArrayList<>(Arrays.asList("公司","分公司","项目负责人","合作方","挂证"));
        ArrayAdapter<String> personTypeAdapter = new ArrayAdapter<>(mContext, android.R.layout.simple_list_item_1, personTypeList);
        personType.getSpinner().setAdapter(personTypeAdapter);
        personType.getSpinner().setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                String s = personTypeList.get(i);
                mDataMap.put("personType",s);
            }
            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });

        // 职务
        ArrayList<String> postList = new ArrayList<>(Arrays.asList("总监","专监","监理员","其他"));
        ArrayAdapter<String> postAdapter = new ArrayAdapter<>(mContext, android.R.layout.simple_list_item_1, postList);
        post.getSpinner().setAdapter(postAdapter);
        post.getSpinner().setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                String s = postList.get(i);
                mDataMap.put("post",s);
            }
            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });

        // 姓名/锁人 TRANSFERUSERNAME_CODE
        userName.getSelectView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                Intent intent2 = new Intent();
                intent2.putExtra("isSingle", true);
                intent2.setClass(mContext, CompanyOrgInfoActivity.class);
                startActivityForResult(intent2, TRANSFERUSERNAME_CODE);
            }
        });

        // 负责人 RECEIVEUSERNAME_CODE
        person.getSelectView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                Intent intent2 = new Intent();
                intent2.putExtra("isSingle", true);
                intent2.setClass(mContext, CompanyOrgInfoActivity.class);
                startActivityForResult(intent2, RECEIVEUSERNAME_CODE);
            }
        });
    }

    private void initListener() {

    }

    private void initData() {

        saveType = "saveProjLockPerson";
        getType = "getProjLockPersonById";

        mIsApply = ((ApplyActivity) getActivity()).isApply;
        // 申请页面
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        mDataMap.put("createTime", DateUtil.dateToString(new Date(), DateUtil.YMD));
        mDataMap.put("projectName", projectInfo.getProjectName());
        mDataMap.put("projectId", projectInfo.getProjectId());
        mDataMap.put("createId", userInfo.getId());
        mDataMap.put("createName", userInfo.getName());
        mDataMap.put("deptName", userInfo.getFatherName());
        mDataMap.put("deptId", userInfo.getFather());


        mDataMap.put("province", projectInfo.getProvince());
        mDataMap.put("city", projectInfo.getCity());
        mDataMap.put("area", projectInfo.getCountry());
        mDataMap.put("deptId", userInfo.getFather());
        mDataMap.put("deptId", userInfo.getFather());
        mDataMap.put("deptId", userInfo.getFather());

        area.getTvContent().setText(projectInfo.getProvince() + "/" + projectInfo.getCity() + "/" + projectInfo.getCountry());


        projectName.setContent(mDataMap.get("projectName").toString());
        projectName.setEnabled(mIsApply);//项目名称

        userName.getEtContent().setEnabled(false);//姓名/锁人

        phone.getEtContent().setEnabled(mIsApply);//联系方式

        person.getEtContent().setEnabled(false);//负责人

        tell.getEtContent().setEnabled(mIsApply);//负责人联系方式

        area.setEnabled(mIsApply);//省市区/县

        type.getSpinner().setEnabled(mIsApply);//锁人类型


        cardName.getEtContent().setEnabled(mIsApply); // 证件名称

        dateLock.setEnabled(mIsApply); // 锁定日期

        bePerson.getSpinner().setEnabled(mIsApply); // 人员归属

        dateUnlock.setEnabled(mIsApply); // 计划解锁日期

        dateInfactUnlock.setEnabled(mIsApply); // 实际解锁日期

        unlockRequiredFile.getEtContent().setEnabled(mIsApply); // 解锁必备资料

        process.getEtContent().setEnabled(mIsApply); // 办理进度


        //省建厅在建锁人
        pLockType.getSpinner().setEnabled(mIsApply); // 锁人类型

        personType.getSpinner().setEnabled(mIsApply); // 人员类别

        post.getSpinner().setEnabled(mIsApply); // 职务

        dateOutnetLock.setEnabled(mIsApply); // 外网锁定日期

        dateOutnetUnlock.setEnabled(mIsApply); // 外网解锁日期

        dateInnetLock.setEnabled(mIsApply); // 内网锁定日期

        dateInnetUnlock.setEnabled(mIsApply); // 内网解锁日期

        remark.getEtContent().setEnabled(mIsApply); // 备注

        if (mIsApply) {
            // 申请页面
            tvSub.setVisibility(View.VISIBLE);

        }else {
            // 查看详情
            tvSub.setVisibility(View.GONE);

            ProcessListBean bean = ((ApplyActivity) getActivity()).bean;
            getData(bean);
        }
    }

    @OnClick({R.id.dateLock, R.id.dateUnlock,R.id.dateInfactUnlock,
            R.id.dateOutnetLock, R.id.dateOutnetUnlock,
            R.id.dateInnetLock,R.id.dateInnetUnlock,R.id.submit_cell})
    void buttonClick(View view) {
        switch (view.getId()) {
            // 锁定日期
            case R.id.dateLock:
                showDatePickerDialog(dateLock,"dateLock");
                break;
            case R.id.dateUnlock:
                // 计划解锁日期
                showDatePickerDialog(dateUnlock,"dateUnlock");
                break;
            case R.id.dateInfactUnlock:
                // 实际解锁日期
                showDatePickerDialog(dateInfactUnlock,"dateInfactUnlock");
                break;
            case R.id.dateOutnetLock:
                // 外网锁定日期
                showDatePickerDialog(dateOutnetLock,"dateOutnetLock");
                break;
            case R.id.dateOutnetUnlock:
                // 外网解锁日期
                showDatePickerDialog(dateOutnetUnlock,"dateOutnetUnlock");
                break;
            case R.id.dateInnetLock:
                // 内网锁定日期
                showDatePickerDialog(dateInnetLock,"dateInnetLock");
                break;
            case R.id.dateInnetUnlock:
                // 内网解锁日期
                showDatePickerDialog(dateInnetUnlock,"dateInnetUnlock");
                break;


            case R.id.submit_cell:


                // 成都市锁人
               // mDataMap.put("userName",userName.getTvContent().getText().toString().trim());//姓名/锁人
                mDataMap.put("phone",phone.getEtContent().getText().toString().trim());//联系方式
                //mDataMap.put("person",person.getTvContent().getText().toString().trim());//负责人
                mDataMap.put("tell",tell.getEtContent().getText().toString().trim());//负责人联系方式
                mDataMap.put("area",area.getTvContent().getText().toString().trim());//省市区/县
                //mDataMap.put("type",type.getTvContent().getText().toString().trim());//锁人类型



                mDataMap.put("cardName",cardName.getEtContent().getText().toString().trim());// 证件名称
                //mDataMap.put("dateLock",dateLock.getEtContent().getText().toString().trim());// 锁定日期
                //mDataMap.put("bePerson",bePerson.getEtContent().getText().toString().trim());// 人员归属
//                mDataMap.put("dateUnlock",dateUnlock.getEtContent().getText().toString().trim());// 计划解锁日期
//                mDataMap.put("dateInfactUnlock",dateInfactUnlock.getEtContent().getText().toString().trim());// 实际解锁日期
                mDataMap.put("unlockRequiredFile",unlockRequiredFile.getEtContent().getText().toString().trim());// 解锁必备资料
                mDataMap.put("process",process.getEtContent().getText().toString().trim()); //办理进度

                //省建厅在建锁人
//                mDataMap.put("pLockType",pLockType.getEtContent().getText().toString().trim()); // 锁人类型
//                mDataMap.put("personType",personType.getEtContent().getText().toString().trim()); // 人员类别
//                mDataMap.put("post",post.getEtContent().getText().toString().trim()); // 职务
//                mDataMap.put("dateOutnetLock",dateOutnetLock.getEtContent().getText().toString().trim()); // 外网锁定日期
//                mDataMap.put("dateOutnetUnlock",dateOutnetUnlock.getEtContent().getText().toString().trim()); // 外网解锁日期
//                mDataMap.put("dateInnetLock",dateInnetLock.getEtContent().getText().toString().trim()); // 内网锁定日期
//                mDataMap.put("dateInnetUnlock",dateInnetUnlock.getEtContent().getText().toString().trim()); // 内网解锁日期
                mDataMap.put("remark",remark.getEtContent().getText().toString().trim());// 备注

                uploadMulFile();

                break;
        }
    }

    //提交
    private void sendData(Map param){

//        if (param.get("type").equals("成都市锁人") || param.get("type").equals("其他省份锁人")){
//            param.remove("pLockType");
//        }

        Map<String, Object> pramaras = new HashMap<>();

        pramaras.put("data" , StringUtil.objectToJson(param));
        Log.i("TAG", "saveType== " + saveType + "getType===" + getType + "mData===" + param);
        Api.getGbkApiserver().submitApply1(saveType, pramaras).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                Log.i("TAG", "onResponse: " + response.body().getData());
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    //mView.applyBack(response.body().getData());
                    String str = response.body().getData();
                    String[] backs = str.split(",");
                    if (backs != null && backs.length == 3) {
                        ((ApplyActivity) getContext()).setBack(str);
                    }
                }else {
//                    mView.applyBack(response.body().getMessage());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }













    Handler mHandler = new Handler() {

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.obj.toString().equals("成都市锁人") || msg.obj.toString().equals("其他省份锁人")){
                lockType1.setVisibility(View.VISIBLE);
                lockType2.setVisibility(View.GONE);
            }else {
                lockType2.setVisibility(View.VISIBLE);
                lockType1.setVisibility(View.GONE);
            }
        }
    };


    //获取详情
    private void getData(ProcessListBean bean){
        Api.getGbkApiserver().testApi(getType, bean.getParameters()).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
//                mView.dismissProgress();
                Log.i("TAG", "onResponse: " + response.body().getData());
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    Map data = (Map) response.body().getData();

                    // 信息
                    projectName.getTvContent().setText(data.get("projectName").toString()); // 项目名称
                    userName.getEtContent().setText(data.get("userName").toString());//姓名/锁人
                    phone.getEtContent().setText(data.get("phone").toString());//联系方式
                    person.getEtContent().setText(data.get("person").toString());//负责人
                    tell.getEtContent().setText(data.get("tell").toString());//负责人联系方式
                    area.getTvContent().setText(data.get("area").toString());//省市区/县
                    type.getSpinnerText().setText(data.get("type").toString());//锁人类型


                    //需要数据传递，用下面方法；
                    Message msg =new Message();
                    msg.obj = data.get("type").toString();
                    mHandler.sendMessage(msg);


                    // 成都市锁人
                    cardName.getEtContent().setText(data.get("cardName").toString()); // 证件名称
                    dateLock.getTvContent().setText(data.get("dateLock").toString()); // 锁定日期
                    bePerson.getSpinnerText().setText(data.get("bePerson").toString()); // 人员归属
                    dateUnlock.getTvContent().setText(data.get("dateUnlock").toString()); // 计划解锁日期
                    dateInfactUnlock.getTvContent().setText(data.get("dateInfactUnlock").toString()); // 实际解锁日期
                    unlockRequiredFile.getEtContent().setText(data.get("unlockRequiredFile").toString()); // 解锁必备资料
                    process.getEtContent().setText(data.get("process").toString()); // 办理进度


                    //省建厅在建锁人
                    String pLockTypeIdx = data.get("pLockType").toString();
                    if (!StringUtil.isEmpty(pLockTypeIdx)){
                        int idx = Integer.parseInt(pLockTypeIdx);
                        if (idx < pLockTypeList.size()){
                            pLockType.getSpinnerText().setText(pLockTypeList.get(idx)); // 锁人类型
                        }
                    }


                    personType.getSpinnerText().setText(data.get("personType").toString()); // 人员类别
                    post.getSpinnerText().setText(data.get("post").toString()); // 职务
                    dateOutnetLock.getTvContent().setText(data.get("dateOutnetLock").toString()); // 外网锁定日期
                    dateOutnetUnlock.getTvContent().setText(data.get("dateOutnetUnlock").toString()); // 外网解锁日期
                    dateInnetLock.getTvContent().setText(data.get("dateInnetLock").toString()); // 内网锁定日期
                    dateInnetUnlock.getTvContent().setText(data.get("dateInnetUnlock").toString()); // 内网解锁日期
                    remark.getEtContent().setText(data.get("remark").toString()); // 备注

                    getFileList(data.get("id").toString());
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }


    /**
     * 展示日期选择对话框
     */
    private void showDatePickerDialog(YSelectDateCell tv, String key) {
        Calendar c = Calendar.getInstance();
        new DatePickerDialog(mContext, new DatePickerDialog.OnDateSetListener() {
            @Override
            public void onDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                // TODO Auto-generated method stub
                c.set(year, monthOfYear, dayOfMonth);
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                Log.i("TAG", "onDateSet: " + format.format(c.getTime()));

                int m = monthOfYear + 1;
                String month = m >= 10 ? m + "" : "0" + m;
                String day = dayOfMonth >= 10 ? dayOfMonth + "" : "0" + dayOfMonth;
                String dateStr = year + "-" + month + "-" + day;
                tv.getTvContent().setText(dateStr);
                mDataMap.put(key, dateStr);
                Log.i("mDataMap", "showDatePickerDialog: ---->" + mDataMap);
            }
        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)).show();
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }


    private ArrayList<EnclosureListDto> mFileList = new ArrayList<>();
    private boolean isTakePhoto;
    private BGAPhotoHelper mPhotoHelper;

    private void fileMethod() {
        mFileView.setApply(mIsApply);
        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);
        // 获取主键
        getPrimarykey(new GetPrimarykeyContract() {
            @Override
            public void getPrimarykey(String str) {
                formKey = str;
            }
        });
        // 点击添加附件
        mFileView.getAddFileBtn().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showFileSelect();
            }
        });
        // 删除附件
        mFileView.setFileOperateContract(new YFileListView.FileOperateContract() {
            @Override
            public void openFile(int index) {
                mFileList.remove(index);
                mFileView.setList(mFileList);
            }
        });
        // 打开附件
        mFileView.setOpenFileContract(new YFileListView.OpenFileContract() {
            @Override
            public void openFile(EnclosureListDto dto) {
                WebActivity.launch(getActivity(), dto);
            }
        });
    }

    private void showFileSelect() {
        // 空照片 ，添加
        String[] str = new String[]{"系统相机", "手机相册", "手机文件"};
        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
        ab.setCancelable(true);
        ab.setItems(str, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                switch (which) {
                    case 0://相机
                        isTakePhoto = true;
                        takePhoto();
                        break;
                    case 1://相册
                        isTakePhoto = false;
                        getPhoto();
                        break;
                    case 2://手机文件
                        getFile();
                        break;
                }
            }
        });
        ab.show();
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), Constant.REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), Constant.REQUEST_CODE_TAKE_PHOTO);
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    /**
     * 打开文件选择器
     */
    private void getFile() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("*/*");// 任意类型文件
        this.startActivityForResult(intent, Constant.REQUEST_CODE_FILE_SELECT);
    }

    BGAPhotoHelper1 mPhotoHelper1 = new BGAPhotoHelper1();

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQUEST_CODE_CHOOSE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_TAKE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800, 800),
                            Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_CROP) {
            if (resultCode == Activity.RESULT_OK) {
                // getCropIntent  获取裁剪完图片的路径
                String photoPath = mPhotoHelper1.getCropFilePath();
//                showToast(photoPath);
                File file = new File(mPhotoHelper1.getCropFilePath());
                double fileSize = FileUtil.getFileOrFilesSize(photoPath, 3);
                Log.i("fileSize", fileSize + "");

                //showToast(photoPath);
                if (fileSize > 1) {
                    ImageUtil.compressImageByLuban(photoPath);
                    String s = ImageUtil.compressImage(photoPath);
                    mFileList.add(new EnclosureListDto(file.getName(), s));
                } else {
                    mFileList.add(new EnclosureListDto(file.getName(), photoPath));
                }
                mFileView.setList(mFileList);
            } else {
                mPhotoHelper.deleteCameraFile();
                mPhotoHelper.deleteCropFile();
            }
        } else if (requestCode == Constant.REQUEST_CODE_FILE_SELECT) {
            String path = "";
            if (data == null) {
                // 用户未选择任何文件，直接返回
                return;
            }
            path = FileUtil.getRealPath(this.getContext(), data.getData());
            File file = new File(path);
            mFileList.add(new EnclosureListDto(file.getName(), path));
            mFileView.setList(mFileList);
//            showToast(path);
        }else if (requestCode == TRANSFERUSERNAME_CODE) {
            // 姓名/锁人
            if (data != null) {
                List<String> ids = new ArrayList<>();
                List<String> names = new ArrayList<>();
                ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                if (names != null && names.size() > 0) {
                    mDataMap.put("userName", names.get(0));
                    mDataMap.put("userNameId", ids.get(0));
                    userName.getEtContent().setText(names.get(0));
                }
            }

        }else if (requestCode == RECEIVEUSERNAME_CODE) {
            //负责人：
            if (data != null) {
                List<String> ids = new ArrayList<>();
                List<String> names = new ArrayList<>();
                ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                if (names != null && names.size() > 0) {
                    mDataMap.put("person", names.get(0));
                    mDataMap.put("personId", ids.get(0));
                    person.getEtContent().setText(names.get(0));
                }

            }
        }
    }

    private void uploadMulFile(){
        if (mFileList.isEmpty()) {
            sendData(mDataMap);
        } else {
            if (formKey.isEmpty()) {
                getPrimarykey(new GetPrimarykeyContract() {
                    @Override
                    public void getPrimarykey(String str) {
                        formKey = str;
                    }
                });
                showToast("未获取到主键");
                return;
            }
            mDataMap.put("id",formKey);
            showProgress();
            String s = DateUtil.dateToString(new Date(), DateUtil.YM1);
            HashMap<String, String> paras = new HashMap<>();
            paras.put("filePath", "/roa/oa/" + s);
            paras.put("businessId", formKey);
            paras.put("businessType", "T_PROJ_LOCK_PERSON");
            OkHttpRequestManager.getInstance().uploadMulFile(mFileList, paras, new IRequestCallback() {
                @Override
                public void onSuccess(String response) {
                    Log.i("upload", "onSuccess: ");
                    sendData(mDataMap);
                }

                @Override
                public void onFailure(Throwable throwable) {
                    Log.i("upload", "onFailure: ");
                    dismissProgress();
                }
            });
        }
    }
    // 获取附件列表
    private void getFileList(String bt) {
        HashMap<String, Object> paras = new HashMap<>();
        paras.put("businessId", bt);
        paras.put("businessType", "T_PROJ_LOCK_PERSON");
        Api.getGbkApiserver().getEnclosureList(Constant.HTTP_GET_ENCLOSURE_LIST, paras).enqueue(new Callback<NetworkResponse<List<EnclosureListDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<EnclosureListDto>>> call,
                                   Response<NetworkResponse<List<EnclosureListDto>>> response) {
                if (response.body().getData() != null) {
                    mFileList.clear();
                    mFileList.addAll(response.body().getData());
                    mFileView.setList(mFileList);
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<List<EnclosureListDto>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}


