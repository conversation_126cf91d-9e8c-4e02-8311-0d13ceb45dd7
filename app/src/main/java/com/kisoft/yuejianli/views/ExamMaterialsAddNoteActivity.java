package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.ui.YButtonSelectCell;
import com.kisoft.yuejianli.ui.YSubmitCell;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.GsonUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ExamMaterialsAddNoteActivity extends BaseActivity {
    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.etNote)
    EditText etNote;

    @BindView(R.id.y_btnsel)
    YButtonSelectCell mBtnSelView;

    @BindView(R.id.submit_cell)
    YSubmitCell submitCell;

    Map<String,String> mMap;
    private int mType;
    private SubmitListener listener;

    public static void launch(Activity activity, Map map, int type) {
        Intent intent = new Intent(activity, ExamMaterialsAddNoteActivity.class);
        String s = GsonUtil.GsonString(map);
        intent.putExtra("data",s);
        intent.putExtra("type",type);
        activity.startActivity(intent);
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_exam_materials_add_note;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mType = getIntent().getIntExtra("type", 0);
        String mapStr = getIntent().getStringExtra("data");
        mMap = GsonUtil.GsonToMaps(mapStr);
        initView();
    }

    private void initView() {
        // //是否保密	0:是;1:否
        if (mType == 1){
            mBtnSelView.setVisibility(View.VISIBLE);
            tvTitle.setText("我的笔记");
        }else {
            mBtnSelView.setVisibility(View.GONE);
            tvTitle.setText("我的评论");
        }
        mBtnSelView.getBtnselTitle().setText("是否保密");
        mBtnSelView.getRbBtnselYes().setText("保密");
        mBtnSelView.getRbBtnselNo().setText("不保密");
        mBtnSelView.setOnSelBtnListener(new YButtonSelectCell.OnSelBtnListener() {
            @Override
            public void onBtnSel(String i) {
                mMap.put("secret",i);
            }
        });

        submitCell.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                if (etNote.getText() == null || etNote.getText().length() < 1) {
                    showToast("请填写内容");
                    return;
                }
                if (mMap == null) {
                    showToast("请选择播放视频");
                    return;
                }
                if (mType == 1){
                    submitNoteAction();
                }else {
                    submitCommentAction();
                }

            }
        });
    }

    void submitNoteAction(){
        Map<String, Object> parameters = new HashMap<>();
        mMap.put("noteContent",etNote.getText().toString());
        mMap.put("createName",SettingManager.getInstance().getUserInfo().getName());
        mMap.put("createId",SettingManager.getInstance().getUserInfo().getId());
        mMap.put("createTime",DateUtil.dateToString(new Date(), DateUtil.YMD));

        parameters.put("notesObject", StringUtil.objectToJson(mMap));

//        try {
//            parameters.put("notesObject" , URLEncoder.encode(StringUtil.objectToJson(mMap).toString(),"UTF-8"));
//            parameters.put("notesObject" , URLEncoder.encode(URLEncoder.encode(StringUtil.objectToJson(mMap).toString(),"UTF-8"),"UTF-8"));
//        } catch (UnsupportedEncodingException e) {
//            e.printStackTrace();
//        }

        Api.getGbkApiserver().saveExamCourseNote ("saveExamCourseNote_APP", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    showToast("提交成功");
                    setResultIntent(1);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    void submitCommentAction(){
        Map<String, Object> parameters = new HashMap<>();
        mMap.put("commentContent",etNote.getText().toString());
        mMap.put("createName",SettingManager.getInstance().getUserInfo().getName());
        mMap.put("createId",SettingManager.getInstance().getUserInfo().getId());
        mMap.put("createTime",DateUtil.dateToString(new Date(), DateUtil.YMD));

        parameters.put("commentObject", StringUtil.objectToJson(mMap));

//        try {
//            parameters.put("commentObject" , URLEncoder.encode(StringUtil.objectToJson(mMap).toString(),"UTF-8"));
//            parameters.put("commentObject" , URLEncoder.encode(URLEncoder.encode(StringUtil.objectToJson(mMap).toString(),"UTF-8"),"UTF-8"));
//        } catch (UnsupportedEncodingException e) {
//            e.printStackTrace();
//        }

        Api.getGbkApiserver().saveExamCourseComment("saveExamCourseComment_APP", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    showToast("提交成功");
                    setResultIntent(0);
                    //finish();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    @OnClick({R.id.iv_back})
    void buttonClick(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
        }
    }

    void setResultIntent(int type){
        Intent intent = new Intent();
        intent.putExtra("data", type);
        setResult(Activity.RESULT_OK, intent);
        finish();
    }

    public void setSubmitListener(SubmitListener listener){
        this.listener = listener;
    }

    public interface SubmitListener{
        void submit(int type);
    }
}