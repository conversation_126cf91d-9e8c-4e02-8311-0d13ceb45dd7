package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.adpter.ContorlContentAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.ContorlContent;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/11.
 */

public class SceneActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    public static final int CONTORL_1 = 1;   //现场巡视
    public static final int CONTORL_2 = 2;   //检查验收
    public static final int CONTORL_3 = 3;   //材料验收
    public static final int CONTORL_4 = 4;   //施工旁站
    public static final int CONTORL_5 = 5;   //隐蔽工程
    public static final int CONTORL_6 = 6;   //监理日志
    public static final int CONTORL_7 = 7;   // 现场安全检测

    private List<ContorlContent> contents = new ArrayList<>();
    private ContorlContentAdapter mAdapter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initData();
        initView();
    }

    private void initView(){
        tvTitle.setText("现场作业");
        if(rvContent.getLayoutManager() == null){
            GridLayoutManager manager = new GridLayoutManager(this, 3);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new ContorlContentAdapter(R.layout.item_project_content, contents);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                ContorlContent content = contents.get(position);
                onClickContorl(content);
            }
        });
        rvContent.setAdapter(mAdapter);
    }

    private void initData(){
        contents.clear();
        contents.add(new ContorlContent(CONTORL_1, "现场巡视记录"));
        contents.add(new ContorlContent(CONTORL_2, "检查验收记录"));
        contents.add(new ContorlContent(CONTORL_3, "材料验收记录"));
        contents.add(new ContorlContent(CONTORL_4, "施工旁站记录"));
        contents.add(new ContorlContent(CONTORL_5, "隐蔽工程记录"));
        contents.add(new ContorlContent(CONTORL_6, "监理日志"));
        contents.add(new ContorlContent(CONTORL_7, "现场安全检测"));
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_scene;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    private void onClickContorl(ContorlContent content){
        switch (content.getId()){
            case CONTORL_1:
                goProcessQualityCheck();
                break;
            case CONTORL_2:
                goTaskQualityCheck();
                break;
            case CONTORL_3:
                goMeterialQualityCheck();
                break;
            case CONTORL_4:
                goOnsideCheck();
                break;
            case CONTORL_5:
                // todosd
                goShelteredCheck();
                break;
            case CONTORL_6:
                goSupervisionLog();
                break;
            case CONTORL_7:

                goSafeInspect();
                break;
            default:
                break;
        }
    }

    private void goMeterialQualityCheck(){
        Intent intent = new Intent();
        intent.setClass(this,MaterialAddListActivity.class);
        startActivity(intent);
    }

    private void goTaskQualityCheck(){
        Intent intent = new Intent();
        intent.setClass(this,QualityCheckListActivity.class);
        startActivity(intent);
    }

    private void goProcessQualityCheck(){
        Intent intent  =new Intent();
        intent.setClass(this, QualityInspectionListActivity.class);
        startActivity(intent);
    }

    private void goOnsideCheck(){
        Intent intent =new Intent();
        intent.setClass(this, OnsideListActivity.class);
        startActivity(intent);
    }

    private void goShelteredCheck(){
        Intent intent = new Intent();
        intent.setClass(this, QualityInvisibilityListActivity.class);
        startActivity(intent);
    }

    private void goSupervisionLog(){
        Intent intent = new Intent();
        intent.setClass(this, SupervisionLogListActivity.class);
        startActivity(intent);
    }

    private void goSafeInspect(){
        Intent intent = new Intent();
        intent.setClass(this, SceneSafeInspectActivity.class);
        startActivity(intent);
    }
}
