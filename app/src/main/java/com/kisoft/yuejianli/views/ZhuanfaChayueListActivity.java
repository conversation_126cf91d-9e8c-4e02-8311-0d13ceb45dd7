package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ZhuanfaChayueListActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.ptrl_content)
    PullToRefreshLayout ptrlContent;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    private int page = 1;
    private int pageSize = 10;

    private LcDaiYueAdapter mDaiYueAdapter;
    private List<Map> mDaiYueData = new ArrayList<>();


    private View empotyView;
    private int mType;
    private String psBusinessType;

    @Override
    public int getLayoutId() {
        return R.layout.activity_zhuanfa_chayue_list;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    // 跳转已阅
    @OnClick(R.id.tv_submit)
    public void submit() {
        ZhuanfaChayueListActivity.launch(this,1);
    }

    public static void launch(Activity activity, int type) {
        Intent intent = new Intent(activity, ZhuanfaChayueListActivity.class);
        intent.putExtra("type", type);
        activity.startActivity(intent);
    }

    public static void launch(Activity activity, int type,String psBusinessType) {
        Intent intent = new Intent(activity, ZhuanfaChayueListActivity.class);
        intent.putExtra("type", type);
        intent.putExtra("psBusinessType", psBusinessType);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //阅读状态 0：未阅；1:已阅
        mType = getIntent().getIntExtra("type",0);
        psBusinessType = getIntent().getStringExtra("psBusinessType");
        if (mType == 0){
            daiYueData("0");
            tvTitle.setText("转发待阅文件");
            tvSubmit.setVisibility(View.VISIBLE);
            tvSubmit.setText("已阅记录");
        }else {
            daiYueData("1");
            tvTitle.setText("转发已阅文件");
            tvSubmit.setVisibility(View.GONE);
        }

        ptrlContent.setRefreshListener(new BaseRefreshListener() {
            @Override
            public void refresh() {
                page = 0;
                mDaiYueData.clear();
                if (mType == 0){
                    daiYueData("0");
                }else {
                    daiYueData("1");
                }
            }

            @Override
            public void loadMore() {
                page++;
                if (mType == 0){
                    daiYueData("0");
                }else {
                    daiYueData("1");
                }
            }
        });
        initView();
    }

    private void initView() {

        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(this);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(layoutManager);
        }

        mDaiYueAdapter = new LcDaiYueAdapter(mDaiYueData);
        mDaiYueAdapter.setEmptyView(empotyView);
        rvContent.setAdapter(mDaiYueAdapter);

        mDaiYueAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                Object object = adapter.getData().get(position);
                if(object instanceof Map){
                    Map map = (Map) object;
                    Intent intent = new Intent(mContext, ApplyActivity.class);

                    String businessId = map.get("businessId").toString();
                    String businessType = map.get("businessType").toString();
                    String wfId = map.get("wfId").toString();
                    String flowTaskId = map.get("businessType").toString();
                    String flowId = map.get("businessType").toString();

                    // 跳转申请审批详情页面
                    ProcessListBean listBean = new ProcessListBean();
                    listBean.setBusinessId(businessId);
                    listBean.setBusinessType(businessType);
                    listBean.setFlowTaskState("");
                    listBean.setFlowTaskId("");
                    listBean.setFlowId(wfId);
                    listBean.setFlowStateName("");
                    listBean.setWfType("1");


                    intent.putExtra("isApply", false);
                    intent.putExtra("bean", listBean);
                    intent.putExtra("workType", "3");
                    intent.putExtra("businessType", map.get("businessType").toString());
                    intent.putExtra("flowStateName", "");
                    intent.putExtra("pageState", "1");
                    startActivity(intent);

                    // 已阅
                    Map<String, Object> parameters = new HashMap<>();
                    parameters.put("id",map.get("id"));
                    Api.getGbkApiserver().updateViewTime("updateViewTime", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
                        @Override
                        public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                            if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                            }
                        }
                        @Override
                        public void onFailure(Call<NetworkResponse<Object>> call, Throwable throwable) {
                            throwable.printStackTrace();
                        }
                    });

                }
            }
        });
    }


    // 查阅记录
    private void daiYueData(String state) {
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("orgId", userInfo.getId());
        parameters.put("page", page + "");
        parameters.put("limit", pageSize+"");
        parameters.put("state", state); //阅读状态 0：未阅；1:已阅

        if (!StringUtil.isEmpty(psBusinessType)){
            parameters.put("businessType",psBusinessType);
        }
        Api.getGbkApiserver().getSysForwardRecordDetailList("getSysForwardRecordDetailList", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    Map data = (Map) response.body().getData();
                    List<Map> list = (List<Map>) data.get("data");
                    if (list.size() < pageSize){
                        ptrlContent.setCanLoadMore(false);
                    }
                    mDaiYueData.addAll(list);
                    mDaiYueAdapter.notifyDataSetChanged();
                    ptrlContent.finishRefresh();
                    ptrlContent.finishLoadMore();
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable throwable) {
                throwable.printStackTrace();
            }
        });
    }


    // 待阅记录
    public class LcDaiYueAdapter extends BaseQuickAdapter<Map, BaseViewHolder> {

        public LcDaiYueAdapter(List<Map> data) {
            super(R.layout.item_process, data);
        }

        @Override
        protected void convert(BaseViewHolder helper, Map item) {

            TextView ivProgressTitle = helper.itemView.findViewById(R.id.iv_progress_title);

            TextView ivProgressStatusTitle = helper.itemView.findViewById(R.id.iv_progress_status_title);
            ivProgressStatusTitle.setVisibility(View.GONE);
            TextView ivProgressStatus = helper.itemView.findViewById(R.id.iv_progress_status);


            TextView ivProgressBeginDateTitle = helper.itemView.findViewById(R.id.iv_progress_begin_date_title);
            ivProgressBeginDateTitle.setText("转发时间");
            TextView ivProgressBeginDate = helper.itemView.findViewById(R.id.iv_progress_begin_date);

            TextView ivProgressUsernameTitle = helper.itemView.findViewById(R.id.iv_progress_username_title);
            ivProgressUsernameTitle.setText("转发人");
            TextView ivProgressUsername = helper.itemView.findViewById(R.id.iv_progress_username);

            TextView ivProgressDate = helper.itemView.findViewById(R.id.iv_progress_date);


            ivProgressTitle.setText(item.get("fileName").toString());
            ivProgressStatus.setText(item.get("remark").toString());
            ivProgressBeginDate.setText(item.get("forwardTime").toString());
            ivProgressUsername.setText(item.get("createName").toString());

            if (StringUtil.isEqual(item.get("state").toString(),"0")){
                ivProgressDate.setText("未阅");
            }else {
                ivProgressDate.setText("已阅");
            }
        }
    }
}