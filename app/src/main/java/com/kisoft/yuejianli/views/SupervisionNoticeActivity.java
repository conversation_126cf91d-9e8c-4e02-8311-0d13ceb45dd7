package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.SupervisionNoticeContract;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.ProjectSafeInspection;
import com.kisoft.yuejianli.entity.ProjectWsInfo;
import com.kisoft.yuejianli.entity.SurpervisionNotice;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.SupervisionNoticeModel;
import com.kisoft.yuejianli.presenter.SupervisionNoticePresenter;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/5/7.
 */

public class SupervisionNoticeActivity extends BaseActivity<SupervisionNoticeModel, SupervisionNoticePresenter>
        implements SupervisionNoticeContract.SupervisionNoticeViewContract {

    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_project)
    TextView tvProject;
    @BindView(R.id.tv_construction)
    TextView tvConstraction;
    @BindView(R.id.et_reason)
    EditText etReason;
    @BindView(R.id.et_content)
    EditText etContent;
    @BindView(R.id.tv_sup_org)
    TextView tvSupOrg;
    @BindView(R.id.tv_sup)
    EditText tvSup;
    @BindView(R.id.tv_year)
    TextView tvYear;
    @BindView(R.id.tv_month)
    TextView tvMonth;
    @BindView(R.id.tv_day)
    TextView tvDay;


    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    private SurpervisionNotice notice;
    private ProjectSafeInspection inspection;
    private List<ProjectWsInfo> wsInfos;
    private String approveId = "";

    private SupervisionNoticeModel model;
    private SupervisionNoticePresenter presenter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        model = new SupervisionNoticeModel(this);
        presenter = new SupervisionNoticePresenter(this, model);
        initMVP(model, presenter);
        initView();
        initData();

    }


    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        wsInfos = SettingManager.getInstance().getProjectWsInfo();
        tvProject.setText(projectInfo.getProjectName());
        Intent intent = getIntent();
        if(intent != null){
            inspection = (ProjectSafeInspection) intent.getSerializableExtra(Constant.INTENT_KEY_SAFE_INSPECTION);
            approveId = intent.getStringExtra(Constant.INTENT_KEY_APPROVE_ID);
        }
        if(inspection != null){
            tvConstraction.setText(inspection.getConstrCompany());
            etReason.setText(inspection.getTaskName());
            etContent.setText(inspection.getProblemStatement());
            notice = new SurpervisionNotice();
            notice.setProjectId(inspection.getProjectId());
            notice.setProjectName(projectInfo.getProjectName());
            notice.setCreateId(userInfo.getId());
            notice.setSnEnclosure(inspection.getProblemPhoto());
            notice.setRenson(inspection.getTaskName());
            notice.setContent(inspection.getProblemStatement());
            notice.setCompany(inspection.getConstrCompany());
            notice.setCreateName(userInfo.getName());
            notice.setSuperUnit(projectInfo.getPjJldw());
            tvSupOrg.setText(projectInfo.getPjJldw());

            if(wsInfos != null){
                for(ProjectWsInfo s: wsInfos){
                    if(StringUtil.isEqual(inspection.getModuleId(), s.getPwiId())){
                        tvSup.setText(s.getSupPersonName());
                    }

                }
            }

        }
    }


    private void initView() {
        tvTitle.setText("监理通知单");
        ivAction.setVisibility(View.VISIBLE);
        ivAction.setImageResource(R.drawable.ic_send);
        tvDay.setText(DateUtil.getTimeStr(3));
        tvMonth.setText(DateUtil.getTimeStr(2));
        tvYear.setText(DateUtil.getTimeStr(1));

    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_supervision_notice;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @OnClick(R.id.iv_action)
    public void sendSupervisionNotice(){
        notice.setContent(etContent.getText().toString().trim());
        notice.setRenson(etReason.getText().toString().trim());
        if(mPresenter != null){
            mPresenter.commitSupNotice(userInfo.getId(), projectInfo.getProjectId(), approveId,notice);
        }
    }

    @Override
    public void showCommitResult(boolean isOk) {
        if(isOk){
            showToast("监理通知单已下发");
            finish();
        }else {
            showToast("通知单生成失败！");
        }
    }
}
