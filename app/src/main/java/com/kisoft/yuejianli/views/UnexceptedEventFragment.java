package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.adpter.UnexpectedEventAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.UnexpectedEventContract;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UnexceptedEventInfo;
import com.kisoft.yuejianli.entity.UnexpectedEvent;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.UnexpectedEventModel;
import com.kisoft.yuejianli.presenter.UnexpectedEventPresenter;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/16.
 */

public class UnexceptedEventFragment extends BaseFragment<UnexpectedEventModel ,UnexpectedEventPresenter> implements
        UnexpectedEventContract.UnexpectedEventViewContract{

    private RecyclerView rvContent;
    private View emptyView;

    private List<UnexpectedEvent> events = new ArrayList<>();
    private UnexpectedEventAdapter mAdapter;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private int count = 0;
    private int page = 0;
    private int pageSize = 15;

    private UnexpectedEventModel model;
    private UnexpectedEventPresenter presenter;


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new UnexpectedEventModel(getContext());
        presenter = new UnexpectedEventPresenter(this, model);
    }


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);

        initView(inflater);
        initData();
        return mRootView;
    }

    private void initView(LayoutInflater inflater){
        mRootView = inflater.inflate(getRootView(), null);
        emptyView = inflater.inflate(R.layout.page_no_data, null);
        rvContent = mRootView.findViewById(R.id.rv_content);
        if(rvContent.getLayoutManager() == null){
            LinearLayoutManager manager = new LinearLayoutManager(getContext());
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new UnexpectedEventAdapter(R.layout.item_unexpected_event, events);
        mAdapter.setEmptyView(emptyView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                goUnexceptedDetail(events.get(position));

            }
        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                rvContent.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if(events.size() >= count){
                            mAdapter.loadMoreEnd();
                        }else {
                            getData();
                        }
                    }
                }, 1000);
            }
        }, rvContent);
        rvContent.setAdapter(mAdapter);
    }

    private void initData(){
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        if(projectInfo != null){
            getData();
        }
    }

    private void getData(){
        String c = String.valueOf(count);
        String p = String.valueOf(page);
        String z = String.valueOf(pageSize);
        presenter.getUnexceptedEvents(userInfo.getId(), projectInfo.getProjectId(), c,p,z);

    }


    @Override
    public int getRootView() {
        return R.layout.fragment_unexcepted_event;
    }

    private void goUnexceptedDetail(UnexpectedEvent event){
        Intent intent = new Intent();
        intent.setClass(getContext(), UnexceptedEventDetailActivity.class);
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constant.INTENT_KEY_UNEXCEPTED_EVENT, event);
        intent.putExtras(bundle);
        startActivity(intent);
    }

    @Override
    public void showUnexceptedEvents(UnexceptedEventInfo info, int type) {
        if(info != null){
            count = info.getCount();
            if(type == 0){
                page++;
                events.clear();
                events.addAll(info.getList());
            }else {
                page++;
                events.addAll(info.getList());
                mAdapter.loadMoreComplete();
            }
            mAdapter.notifyDataSetChanged();
        }
    }
}
