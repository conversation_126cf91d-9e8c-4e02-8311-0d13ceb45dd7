package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.adpter.ContractListAdapter;
import com.kisoft.yuejianli.adpter.ZxListAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.ContractInfo;
import com.kisoft.yuejianli.entity.ContractInformationDto;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.entity.ZxDto;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/6/7.
 */

public class ZxListActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    private List<ZxDto> listData = new ArrayList<>();
    private ZxListAdapter mAdapter;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    boolean isSelectModel;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    private void initView() {
        isSelectModel = getIntent().getBooleanExtra("isSelectModel", true);
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        tvTitle.setText("专项选择");
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new ZxListAdapter(listData);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (isSelectModel) {
                    Intent intent = new Intent();
                    intent.putExtra("data", listData.get(position));
                    setResult(Activity.RESULT_OK, intent);
                    finish();
                }
            }
        });
        rvContent.setAdapter(mAdapter);

        getList();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_zx_list;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    private void getList() {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("userId", userInfo.getId());
        pramaras.put("projectId", projectInfo.getProjectId());
        listData.clear();
        Api.getGbkApiserver().getZxList("getSpeCompList", pramaras).enqueue(new Callback<NetworkResponse<List<ZxDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ZxDto>>> call, Response<NetworkResponse<List<ZxDto>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    if (response.body().getData() != null)
                        listData.addAll(response.body().getData());
                    mAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ZxDto>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

}
