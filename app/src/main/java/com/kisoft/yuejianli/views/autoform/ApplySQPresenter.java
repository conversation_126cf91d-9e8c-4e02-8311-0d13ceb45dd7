package com.kisoft.yuejianli.views.autoform;

import android.util.Log;

import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BasePresenter;
import com.kisoft.yuejianli.contract.ApplySPContract;
import com.kisoft.yuejianli.entity.ApproveInfo;
import com.kisoft.yuejianli.entity.JDData;
import com.kisoft.yuejianli.model.ApplySPModel;
import com.kisoft.yuejianli.views.ApplySPFragment;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ApplySQPresenter extends BasePresenter<ApplySQFragment,ApplySPModel>
        implements ApplySPContract.ApplySPPresenterContract{
    public ApplySQPresenter(ApplySQFragment mView, ApplySPModel mModel) {
        super(mView, mModel);
    }

    @Override
    public void create() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void getProcessList(String businessType, String callBackName) {
        mView.showProgress();
        mModel.getProcessList(businessType, callBackName).enqueue(new Callback<NetworkResponse<List<JDData>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<JDData>>> call, Response<NetworkResponse<List<JDData>>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.processListBack(response.body().getData());
                }else {
                    mView.processListBack(null);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<JDData>>> call, Throwable t) {
                mView.dismissProgress();
                t.printStackTrace();
                mView.processListBack(null);
            }
        });
    }

    @Override
    public void getNextList(String businessType, String callBackName, String wfTaskId) {
        mView.showProgress();
        mModel.getNextList(businessType, callBackName, wfTaskId).enqueue(new Callback<NetworkResponse<List<JDData>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<JDData>>> call, Response<NetworkResponse<List<JDData>>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.nextListBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<JDData>>> call, Throwable t) {
                mView.dismissProgress();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void initWf(String businessId, String wftId ,String wfId, String wfName, String businessType, String callBackName) {
        mView.showProgress();
        Log.i("TAG", "initWf: " + wfId);
        mModel.initWf(businessId, wftId, wfId, wfName, businessType, callBackName).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() ==NetworkResponse.OK){
                    mView.initWfBack(response.body().getData());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                mView.dismissProgress();
                t.printStackTrace();
            }
        });
    }

    @Override
    public void submitApplySP(ApproveInfo info) {
        mModel.submitApplySP(info).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                mView.dismissProgress();
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    mView.submitApplySPBack(response.body().getData());
                }else {
                    mView.submitApplySPBack(response.body().getMessage());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                mView.dismissProgress();
                t.printStackTrace();
            }
        });
    }
}
