package com.kisoft.yuejianli.views;

import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.content.Intent;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.appcompat.widget.Toolbar;
import android.view.View;
import android.widget.AdapterView;
import android.widget.CompoundButton;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import com.bigkoo.pickerview.OptionsPickerView;
import com.bigkoo.pickerview.TimePickerView;
import com.bigkoo.pickerview.lib.WheelView;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProgrammeAddContract;
import com.kisoft.yuejianli.entity.ObjectItemInfo;
import com.kisoft.yuejianli.entity.ProgrammeInfo;
import com.kisoft.yuejianli.entity.ProgrammeInfoList;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ProgrammeAddModel;
import com.kisoft.yuejianli.presenter.ProgrammeAddPresenter;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.PhoneUtil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import butterknife.BindView;
import butterknife.OnClick;

public class ProgrammeAddActivity extends BaseActivity<ProgrammeAddModel,
        ProgrammeAddPresenter> implements ProgrammeAddContract.ProgrammeAddViewContract {

    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.tool_bar)
    Toolbar toolBar;
    @BindView(R.id.et_topic)
    EditText etTopic;
    @BindView(R.id.tv_creatorname)
    TextView tvCreatorname;

    @BindView(R.id.et_content)
    EditText etContent;
    @BindView(R.id.tv_clddate)
    TextView tvClddate;
    @BindView(R.id.tv_begintime)
    TextView tvBegintime;
    @BindView(R.id.tv_endtime)
    TextView tvEndtime;
    @BindView(R.id.sp_cldType)
    Spinner spCldType;
    @BindView(R.id.tv_cldType)
    TextView tvCldType;
    @BindView(R.id.tv_username)
    TextView tvUsername;
    @BindView(R.id.iv_today_quality1)
    ImageView ivTodayQuality1;
    @BindView(R.id.s_isalter)
    Switch sIsalter;
    @BindView(R.id.tv_altertime)
    TextView tvAltertime;
    @BindView(R.id.sp_repeattimes)
    Spinner spRepeattimes;
    @BindView(R.id.tv_repeattimes)
    TextView tvRepeattimes;
    @BindView(R.id.tv_save)
    TextView tvSave;
    @BindView(R.id.tv_sub)
    TextView tvSub;
    @BindView(R.id.ll_answer)
    LinearLayout llAnswer;
    @BindView(R.id.ll_username)
    LinearLayout llUsername;
    @BindView(R.id.v_view)
    View v_View;

    @BindView(R.id.ll_isalter)
    LinearLayout llIsalter;


    private ProgrammeAddPresenter presenter;
    private ProgrammeAddModel model;

    //知会人Id
    private String userGuid;

    private String cldGuid = "";

    //知会人name
    private String userName = "";
    private ProgrammeInfo programmeInfo = new ProgrammeInfo();
    private String beginTime;
    private String endTime;

    private int timeType = 1;
    private static final int TIME_TYPE_INVISIBLE = 1;
    private static final int TIME_TYPE_CHECK = 2;
    private TimePickerView tpv;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    //工作日程  or   私人日程
    private String cldType = "0";

    private int hour;
    private int minute;
    private String time1;
    private String time2;
    private String dateStr;
    private AlertDialog dialog;


    private int programType=1;

    List<String> food_s = new ArrayList<>();
    List<String> clothes_s = new ArrayList<>();
    List<String> computer_s = new ArrayList<>();


    @Override
    public int getLayoutId() {
        return R.layout.activity_programme_add;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        model = new ProgrammeAddModel(this);
        presenter = new ProgrammeAddPresenter(this, model);
        initMVP(model, presenter);
        getData();
        initView();
    }

    private void getData() {
        cldGuid = getIntent().getStringExtra("cldGuid");
        programType = getIntent().getIntExtra("programType",1);
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        if (projectInfo != null) {
            presenter.getObjectItemInfoList("108.002");
            presenter.getObjectItemInfoList("108.003");
        }
    }

    @Override
    public void showObjectItemInfoList(List<ObjectItemInfo.DataBean> infos) {
        if (infos != null) {
            for (int i = 0; i < infos.size(); i++) {
                if (infos.get(i).getTypecode().equals("108.002")) {//小时
                    food_s.add(infos.get(i).getItemname());
                } else if (infos.get(i).getTypecode().equals("108.003")) {//分钟
                    computer_s.add(infos.get(i).getItemname());
                }
            }
        }
    }

    @Override
    public void showProgrammeInfoBack(ProgrammeInfoList.DataBean info) {
        if (info != null) {
            if (programType==3) {
                cldGuid = info.getCldGuid();
                etTopic.setText(info.getTopic());

                programmeInfo.setCreator(info.getCreator());
                programmeInfo.setCreatorName(info.getCreatorName());
                tvCreatorname.setText(info.getCreatorName());
                tvCreatorname.setEnabled(false);
                etContent.setText(info.getContent());
                tvClddate.setText(info.getCldDateTempStr());
                tvBegintime.setText(info.getBeginTimeTempStr());
                tvEndtime.setText(info.getEndTimeTempStr());
                if (info.getCldType().equals("0")) {
                    //tvCldType.setText("工作日程");
                    //tvCldType.setVisibility(View.VISIBLE);
                    //spCldType.setVisibility(View.GONE);
                    spCldType.setSelection(0, true);
                    tvUsername.setText(info.getUserName());
                    programmeInfo.setCldType("0");
                    llUsername.setVisibility(View.VISIBLE);

                } else {
                    spCldType.setSelection(1, true);
                    programmeInfo.setCldType("1");

                    llUsername.setVisibility(View.GONE);
                    v_View.setVisibility(View.GONE);

                }
                //不提醒0  提醒1
                if (info.getIsalter().equals("1")) {
                    sIsalter.setChecked(true);
                    programmeInfo.setIsalter("1");
                    //sIsalter.setEnabled(false);
                    tvAltertime.setText(info.getAlterTime());
                    //tvAltertime.setEnabled(false);
                    //tvRepeattimes.setText(info.getRepeatTimes());
                    tvRepeattimes.setVisibility(View.INVISIBLE);
                    //tvRepeattimes.setEnabled(false);
                    if (info.getRepeatTimes().equals("1")) {
                        spRepeattimes.setSelection(0, true);
                        programmeInfo.setRepeatTimes("1");
                    } else if (info.getRepeatTimes().equals("3")) {
                        spRepeattimes.setSelection(1, true);
                        programmeInfo.setRepeatTimes("3");
                    } else if (info.getRepeatTimes().equals("5")) {
                        spRepeattimes.setSelection(2, true);
                        programmeInfo.setRepeatTimes("5");
                    }
                    //spRepeattimes.setVisibility(View.GONE);
                } else {
                    sIsalter.setChecked(false);
                    programmeInfo.setIsalter("0");
                    //sIsalter.setEnabled(false);
                    llIsalter.setVisibility(View.GONE);
                }
                programmeInfo.setUserGuid(info.getUserGuid());
                programmeInfo.setUserName(info.getUserName());
                tvSub.setText("修改");

            } else if(programType==2) {//详情
                etTopic.setText(info.getTopic());
                etTopic.setEnabled(false);
                tvCreatorname.setText(info.getCreatorName());
                tvCreatorname.setEnabled(false);
                etContent.setText(info.getContent());
                etContent.setEnabled(false);
                tvClddate.setText(info.getCldDateTempStr());
                tvClddate.setEnabled(false);
                tvBegintime.setText(info.getBeginTimeTempStr());
                tvBegintime.setEnabled(false);
                tvEndtime.setText(info.getEndTimeTempStr());
                tvEndtime.setEnabled(false);
                if (info.getCldType().equals("0")) {
                    tvCldType.setText("工作日程");
                    tvCldType.setVisibility(View.VISIBLE);
                    spCldType.setVisibility(View.GONE);
                    tvUsername.setText(info.getUserName());
                    tvUsername.setEnabled(false);
                    llUsername.setVisibility(View.VISIBLE);
                } else {

                    spCldType.setSelection(1, true);
                    spCldType.setEnabled(false);
                    spCldType.setVisibility(View.GONE);
                    llUsername.setVisibility(View.GONE);
                    tvCldType.setText("个人日程");
                    tvCldType.setVisibility(View.VISIBLE);
                    v_View.setVisibility(View.GONE);
                   /* llUsername.setVisibility(View.GONE);
                    tvCldType.setText("个人日程");
                    tvCldType.setVisibility(View.VISIBLE);
                    spCldType.setVisibility(View.GONE);*/

                }
                //不提醒0  提醒1
                if (info.getIsalter().equals("1")) {
                    sIsalter.setChecked(true);
                    sIsalter.setEnabled(false);
                    tvAltertime.setText(info.getAlterTime());
                    tvAltertime.setEnabled(false);
                    tvRepeattimes.setText(info.getRepeatTimes());
                    tvRepeattimes.setVisibility(View.VISIBLE);
                    tvRepeattimes.setEnabled(false);
                    spRepeattimes.setVisibility(View.GONE);
                } else {
                    sIsalter.setChecked(false);
                    sIsalter.setEnabled(false);
                    llIsalter.setVisibility(View.GONE);
                }
                tvSub.setVisibility(View.INVISIBLE);
            }

        }
    }

    @Override
    public void showProgrammeAddResult(boolean isok) {
        if (isok) {
            if (programType==1) {
//                  成功，返回上个页面
                showToast("提交成功");
                finish();
            } else if(programType==3){
//                  成功，返回上个页面
                showToast("修改成功");
                finish();
            }
        } else {
            showToast("系统繁忙！");
        }
    }


    private void initView() {
        spCldType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                if (i == 0) {//工作日程
                    programmeInfo.setCldType("0");
                    cldType = "0";
                    llUsername.setVisibility(View.VISIBLE);
                } else if (i == 1) {//私人日程
                    programmeInfo.setCldType("1");
                    cldType = "1";
                    llUsername.setVisibility(View.GONE);
                    v_View.setVisibility(View.GONE);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {
            }
        });

        sIsalter.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                //控制开关字体颜色
                if (b) {
                    programmeInfo.setIsalter("1");
                    sIsalter.setSwitchTextAppearance(ProgrammeAddActivity.this, R.style.s_true);
                    llIsalter.setVisibility(View.VISIBLE);
                } else {
                    programmeInfo.setIsalter("0");
                    sIsalter.setSwitchTextAppearance(ProgrammeAddActivity.this, R.style.s_false);
                    llIsalter.setVisibility(View.GONE);
                }

            }
        });

        initDatePick();
        spRepeattimes.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                switch (i) {
                    case 0:
                        programmeInfo.setRepeatTimes("1");
                        tvRepeattimes.setText("1");
                        break;
                    case 1:
                        programmeInfo.setRepeatTimes("3");
                        tvRepeattimes.setText("3");
                        break;
                    case 2:
                        programmeInfo.setRepeatTimes("5");
                        tvRepeattimes.setText("5");
                        break;

                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {
            }
        });


        if (programType==1) {//增加
            tvTitle.setText("新增日程");
            tvCreatorname.setText(userInfo.getName());
            programmeInfo.setCreator(userInfo.getId());
            programmeInfo.setCreatorName(userInfo.getName());

            sIsalter.setChecked(false);
            sIsalter.setChecked(true);
            programmeInfo.setIsalter("1");
            sIsalter.setSwitchTextAppearance(ProgrammeAddActivity.this, R.style.s_true);
        } else {
            if (programType==3) {//修改
                tvTitle.setText("日程修改");
                tvCreatorname.setText(userInfo.getName());
                presenter.getProgrammeInfo(cldGuid);
            } else if(programType==2){//详情
                tvTitle.setText("日程详情");
                tvCreatorname.setText(userInfo.getName());
                presenter.getProgrammeInfo(cldGuid);
            }

        }
    }

    private void initDatePick() {
        Calendar selectedDate = Calendar.getInstance();
        Calendar startDate = Calendar.getInstance();
        startDate.set(1950, 0, 1, 0, 0);
        Calendar endDate = Calendar.getInstance();
        endDate.set(2033, 11, 30, 23, 30);
        //时间选择器
        tpv = new TimePickerView.Builder(this, new TimePickerView.OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {//选中事件回调
                // 这里回调过来的v,就是show()方法里面所添加的 View 参数，如果show的时候没有添加参数，v则为null
                TextView tvDate = (TextView) v;
                tvDate.setText(DateUtil.dateToString(date, DateUtil.YMD_HM));
                switch (timeType) {
                    case TIME_TYPE_INVISIBLE:
                        tvAltertime.setText(DateUtil.dateToString(date, DateUtil.YMD_HM));
                        programmeInfo.setAlterTime(DateUtil.dateToString(date, DateUtil.YMD_HM));
                        break;
                }
            }
        })
                //年月日时分秒 的显示与否，不设置则默认全部显示
                .setType(new boolean[]{true, true, true, true, true, false})
                .setLabel("年", "月", "日", "时", "分", "")
                .isCenterLabel(false)
                .setDividerType(WheelView.DividerType.WRAP)
                .setDividerColor(ContextCompat.getColor(YueApplacation.mContext, R.color.colorAccent))
                .setContentSize(PhoneUtil.dpTopx(YueApplacation.mContext, 8))
                .setDate(selectedDate)
                .setRangDate(startDate, endDate)
                .setDecorView(null)
                .build();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 440) {
            if (resultCode == Constant.REQEST_CODE_SELECT_COMPANY_ORG_INFO) {
                if (data != null) {
                    List<String> ids = new ArrayList<>();
                    String id_s = "";
                    List<String> names = new ArrayList<>();
                    String name_s = "";
                    ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                    names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                    if (names != null && names.size() > 0) {
                        //completeCheckInfo.setWorkPersonId(ids.get(0));
                        for (int i = 0; i < names.size(); i++) {
                            if (i < names.size() - 1) {
                                name_s += names.get(i) + ",";
                                id_s += ids.get(i) + ",";
                            } else {
                                name_s += names.get(i);
                                id_s += ids.get(i);

                            }
                        }
                        userGuid = id_s;
                        userName = name_s;
                        tvUsername.setText(name_s);
                        programmeInfo.setUserGuid(id_s);
                        programmeInfo.setUserName(name_s);
                    }
                }

            }

        }


        if (requestCode == 450) {
            if (resultCode == Constant.REQEST_CODE_SELECT_COMPANY_ORG_INFO) {
                if (data != null) {
                    List<String> ids = new ArrayList<>();
                    String id_s = "";
                    List<String> names = new ArrayList<>();
                    String name_s = "";
                    ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                    names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                    if (names != null && names.size() > 0) {
                        //completeCheckInfo.setWorkPersonId(ids.get(0));
                        for (int i = 0; i < names.size(); i++) {
                            tvCreatorname.setText(names.get(0));
                            programmeInfo.setCreator(ids.get(0));
                            programmeInfo.setCreatorName(names.get(0));
                        }

                    }
                }

            }

        }

    }

    @OnClick({R.id.iv_back, R.id.tv_submit, R.id.tv_creatorname, R.id.tv_clddate, R.id.tv_begintime, R.id.tv_endtime, R.id.tv_cldType, R.id.tv_username, R.id.tv_altertime, R.id.tv_sub})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.tv_submit:
                break;
                //选择创建人
            case R.id.tv_creatorname:
                Intent intent2 = new Intent();
                intent2.putExtra("isSingle", true);
                intent2.setClass(this, CompanyOrgInfoActivity.class);
                startActivityForResult(intent2, 450);
                break;
            case R.id.tv_clddate:
                showDatePickerDialog(tvClddate);
                break;
            case R.id.tv_altertime:
                timeType = TIME_TYPE_INVISIBLE;
                tpv.show(tvAltertime);
                break;
            case R.id.tv_begintime://选择开始时间
                Collections.sort(food_s);
                Collections.sort(computer_s);
                //条件选择器
                OptionsPickerView pvOptions = new OptionsPickerView.Builder(ProgrammeAddActivity.this, new OptionsPickerView.OnOptionsSelectListener() {
                    @Override
                    public void onOptionsSelect(int options1, int options2, int options3, View v) {

                        beginTime = food_s.get(options1) + ":" + computer_s.get(options3);
                        tvBegintime.setText(food_s.get(options1) + ":" + computer_s.get(options3));
                        Toast.makeText(ProgrammeAddActivity.this, food_s.get(options1) + "时" + computer_s.get(options3) + "分", Toast.LENGTH_SHORT).show();
                    }
                }).setLabels("时", "", "分")//设置选择的单位名称
                        .build();
                pvOptions.setNPicker(food_s, clothes_s, computer_s);
                pvOptions.show();
                break;
            case R.id.tv_endtime:
                Collections.sort(food_s);
                Collections.sort(computer_s);
                //条件选择器
                OptionsPickerView pvOptions1 = new OptionsPickerView.Builder(ProgrammeAddActivity.this, new OptionsPickerView.OnOptionsSelectListener() {
                    @Override
                    public void onOptionsSelect(int options1, int options2, int options3, View v) {
                        endTime = food_s.get(options1) + ":" + computer_s.get(options3);
                        tvEndtime.setText(food_s.get(options1) + ":" + computer_s.get(options3));
                        Toast.makeText(ProgrammeAddActivity.this, food_s.get(options1) + "时" + computer_s.get(options3) + "分", Toast.LENGTH_SHORT).show();
                    }
                }).setLabels("时", "", "分")//设置选择的单位名称
                        .build();
                pvOptions1.setNPicker(food_s, clothes_s, computer_s);
                pvOptions1.show();
                break;
            case R.id.tv_cldType:
                break;
            case R.id.tv_username:
                Intent intent1 = new Intent();
                intent1.setClass(this, CompanyOrgInfoActivity.class);
                startActivityForResult(intent1, 440);
                break;
            case R.id.tv_sub:
                if (etTopic.getText().toString().equals("") || etTopic.getText().toString() == null) {
                    showToast("请输入标题");
                    return;
                }
                if (tvClddate.getText().toString().equals("") || tvClddate.getText().toString() == null) {
                    showToast("请选择日期");
                    return;
                }
                if (tvBegintime.getText().toString().equals("") || tvBegintime.getText().toString() == null) {
                    showToast("请选择开始时间");
                    return;
                }
                if (tvEndtime.getText().toString().equals("") || tvEndtime.getText().toString() == null) {
                    showToast("请选择结束时间");
                    return;
                }

                int result = tvBegintime.getText().toString().compareTo(tvEndtime.getText().toString());

                if ((0 - result) < 0) {
                    showToast("开始时间不能大于结束时间");
                    return;
                }


                if (cldType.equals("0")) {
                    if (tvUsername.getText().toString().equals("") || tvUsername.getText().toString() == null) {
                        showToast("请选择知会人");
                        return;
                    }
                }
                if (sIsalter.isChecked()) {
                    if (tvAltertime.getText().toString().equals("") || tvAltertime.getText().toString() == null) {
                        showToast("请选择提醒时间");
                        return;
                    }
                }

                programmeInfo.setCldGuid(cldGuid);
                programmeInfo.setTopic(etTopic.getText().toString());


                programmeInfo.setCldDate(tvClddate.getText().toString());
                programmeInfo.setBeginTime(tvClddate.getText().toString() + " " + tvBegintime.getText().toString() + ":00");
                programmeInfo.setEndTime(tvClddate.getText().toString() + " " + tvEndtime.getText().toString() + ":00");
                programmeInfo.setContent(etContent.getText().toString());
                programmeInfo.setAlterTime(tvAltertime.getText().toString() + ":00");
                //创建日期
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
                Date date = new Date();
                String format = dateFormat.format(date);
                programmeInfo.setCreateDate(format);

                programmeInfo.setRepeatTimes(tvRepeattimes.getText().toString());

                programmeInfo.setCldDateTempStr(tvClddate.getText().toString());
                programmeInfo.setBeginTimeTempStr(tvClddate.getText().toString() + " " + tvBegintime.getText().toString() + ":00");
                programmeInfo.setEndTimeTempStr(tvClddate.getText().toString() + " " + tvEndtime.getText().toString() + ":00");
                if (programType==1) {
                    presenter.addProgrammeAdd(programmeInfo, false);
                } else if(programType==3){
                    presenter.addProgrammeAdd(programmeInfo, true);
                }
                break;
        }
    }

    //日期选择 2019-01-01
    private void showDatePickerDialog(final TextView tv) {
        Calendar c = Calendar.getInstance();
        new DatePickerDialog(mContext, new DatePickerDialog.OnDateSetListener() {
            @Override
            public void onDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                // TODO Auto-generated method stub
                int m = monthOfYear + 1;
                String month = m >= 10 ? m + "" : "0" + m;
                String day = dayOfMonth >= 10 ? dayOfMonth + "" : "0" + dayOfMonth;
                String dateStr = year + "-" + month + "-" + day;
                tv.setText(dateStr);
            }
        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)).show();

    }
}
