package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bigkoo.pickerview.OptionsPickerView;
import com.bigkoo.pickerview.TimePickerView;
import com.bigkoo.pickerview.lib.WheelView;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.adpter.RowTableAdapter;
import com.kisoft.yuejianli.adpter.common.SimpleTextWatcher;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ApplyYGQJContract;
import com.kisoft.yuejianli.data.TimeData;
import com.kisoft.yuejianli.entity.ApplyLeaveInfo;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.LeaveInfo;
import com.kisoft.yuejianli.entity.ObjectItemInfo;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ApplyYGQJModel;
import com.kisoft.yuejianli.presenter.ApplyYGQJPresenter;
import com.kisoft.yuejianli.ui.YFileListView;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.PhoneUtil;
import com.kisoft.yuejianli.utils.ScreenUtils;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.utils.recyclerview.YBaseViewHolder;
import com.kisoft.yuejianli.views.watermark.ImageUtil;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Description: 员工请假
 * Author     : bhd119
 * Date       : 2019/5/5
 */
public class ApplyYGQJHdmhFragment extends BaseFragment<ApplyYGQJModel, ApplyYGQJPresenter>
        implements ApplyYGQJContract.ApplyYGQJViewContract, AdapterView.OnItemSelectedListener {

    public View mRootView;
    Unbinder unbinder;

    @BindView(R.id.et_leave_title)
    EditText etLeaveTitle;
    @BindView(R.id.sp_leave_type)
    Spinner spLeaveType;
    @BindView(R.id.tv_leave_type)
    TextView tvLeaveType;
    @BindView(R.id.tv_leave_time_begin)
    TextView tvLeaveTimeBegin;
    @BindView(R.id.tv_leave_time_end)
    TextView tvLeaveTimeEnd;
    @BindView(R.id.tv_sub)
    TextView tvSub;
    @BindView(R.id.et_leave_reason)
    EditText etLeaveReason;

    @BindView(R.id.et_location)
    EditText etLocation;
    

    @BindView(R.id.tv_starttime)
    TextView tvStarttime;
    @BindView(R.id.tv_endtime)
    TextView tvEndtime;
    @BindView(R.id.et_leavedays)
    EditText et_leavedays;
    @BindView(R.id.tv_applydate)
    TextView tvApplydate;

    @BindView(R.id.y_file_list)
    YFileListView mFileView;

    /**
     * 请假类型
     */
    private List<ApplyType> leaveTypes = new ArrayList<>();

    /**
     * 时间列表  小时，分钟
     */
    List<ObjectItemInfo.DataBean> timeInfos_h = new ArrayList<>();
    List<ObjectItemInfo.DataBean> timeInfos_m = new ArrayList<>();

    private ArrayAdapter<ApplyType> adapter;

    ApplyLeaveInfo applyLeaveInfo = new ApplyLeaveInfo();
    LeaveInfo leaveInfo = new LeaveInfo();

//    private ApplyYGQJModel model;
//    private ApplyYGQJPresenter presenter;

    ProcessListBean bean;

    List<TimeData> timeDataList = new ArrayList<>();
    List<TimeData> timeDataList1 = new ArrayList<>();

    List<String> food_s = new ArrayList<>();
    List<String> food_s_id = new ArrayList<>();

    List<String> clothes_s = new ArrayList<>();

    List<String> computer_s = new ArrayList<>();
    List<String> computer_s_id = new ArrayList<>();
    private String startTime;
    private String endTime;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private String startTimeHour;
    private String startTimeMin;
    private String endTimeHour;
    private String endTimeMin;
    private boolean mIsApply;

    // 请假类型与天数
    @BindView(R.id.tv_view_title)
    TextView textViewTitle;

    @BindView(R.id.recycleViewDetail)
    RecyclerView recyclerView;

//    @BindView(R.id.tvAddDetail)
//    TextView tvAddDetail;
//    @BindView(R.id.tvDeleteDetail)
//    TextView tvDeleteDetail;
//    @BindView(R.id.llOpDetail)
//    LinearLayout llOpDetail;
//    @BindView(R.id.title_container)
//    LinearLayout rowTileContainer;

    LeaveRowTableAdapter mRowTableAdapter;

    //    private String c1;//病假
//    private String c2;//哺乳假
//    private String c3;//产假
//    private String c4;//公假
//    private String c5;//护理假
//    private String c6;//婚假
//    private String c7;//计划生育手术假
//    private String c8;//年休假
//    private String c9;//女工假
//    private String c10;//其他
//    private String c11;//丧假
//    private String c12;//生育假
//    private String c13;//事假
//    private String c14;//探亲假
//    private String c15;//停工留薪期
//    private String c16;//育儿假
//    private String c17;//路程假
//    private String c18;//法定节日
//    private String c19;//休息日
    List<Map> leaveTypeMap = new ArrayList() {{
        add(new HashMap() {{
            put("key1", "c1");
            put("key2", "d1");
            put("name", "病假");
        }});
        add(new HashMap() {{
            put("key1", "c2");
            put("key2", "d2");
            put("name", "哺乳假");
        }});
        add(new HashMap() {{
            put("key1", "c3");
            put("key2", "d3");
            put("name", "产假");
        }});
        add(new HashMap() {{
            put("key1", "c4");
            put("key2", "d4");
            put("name", "公假");
        }});
        add(new HashMap() {{
            put("key1", "c5");
            put("key2", "d5");
            put("name", "护理假");
        }});
        add(new HashMap() {{
            put("key1", "c6");
            put("key2", "d6");
            put("name", "婚假");
        }});
        add(new HashMap() {{
            put("key1", "c7");

            put("key2", "d7");
            put("name", "计划生育手术假");
        }});
        add(new HashMap() {{
            put("key1", "c8");
            put("key2", "d8");
            put("name", "年休假");
        }});
        add(new HashMap() {{
            put("key1", "c9");
            put("key2", "d9");
            put("name", "女工假");
        }});
        add(new HashMap() {{
            put("key1", "c10");
            put("key2", "d10");
            put("name", "其他");
        }});
        add(new HashMap() {{
            put("key1", "c11");
            put("key2", "d11");
            put("name", "丧假");
        }});
        add(new HashMap() {{
            put("key1", "c12");
            put("key2", "d12");
            put("name", "生育假");
        }});
        add(new HashMap() {{
            put("key1", "c13");
            put("key2", "d13");
            put("name", "事假");
        }});
        add(new HashMap() {{
            put("key1", "c14");
            put("key2", "d14");
            put("name", "探亲假");
        }});
        add(new HashMap() {{
            put("key1", "c15");
            put("key2", "d15");
            put("name", "停工留薪期");
        }});
        add(new HashMap() {{
            put("key1", "c16");
            put("key2", "d16");
            put("name", "育儿假");
        }});
        add(new HashMap() {{
            put("key1", "c17");
            put("key2", "d17");
            put("name", "路程假");
        }});
        add(new HashMap() {{
            put("key1", "c18");
            put("key2", "d18");
            put("name", "法定节日");
        }});
        add(new HashMap() {{
            put("key1", "c19");
            put("key2", "d19");
            put("name", "休息日");
        }});
    }};

    Map<String, Object> mDataMap = new HashMap<>();


    @Override
    public int getRootView() {
        return R.layout.fragment_apply_ygqjhdmh;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        model = new ApplyYGQJModel(mContext);
//        presenter = new ApplyYGQJPresenter(this, model);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initData();
        initView();
        initDetailView();
        filMethod();
        return mRootView;
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
//        presenter.getLeaveType();
        if (SettingManager.getInstance().getAllApplyTypeInfos() != null && SettingManager.getInstance().getAllApplyTypeInfos().size() > 0) {
            leaveTypes = SettingManager.getInstance().getAllApplyTypeInfos();
        }
        /*if (projectInfo != null) {
            presenter.getObjectItemInfoList("108.002");
        }
        if (projectInfo != null) {
            presenter.getObjectItemInfoList("108.003");
        }*/
        timeInfos_h = SettingManager.getInstance().getAllTimeInfos_h();
        timeInfos_m = SettingManager.getInstance().getAllTimeInfos_m();
        if (SettingManager.getInstance().getAllTimeInfos_h() != null && SettingManager.getInstance().getAllTimeInfos_h().size() > 0) {
            timeInfos_h = SettingManager.getInstance().getAllTimeInfos_h();
        }
        if (SettingManager.getInstance().getAllTimeInfos_m() != null && SettingManager.getInstance().getAllTimeInfos_m().size() > 0) {
            timeInfos_m = SettingManager.getInstance().getAllTimeInfos_m();
        }
        TimeData timeData;
        TimeData timeData1;
        for (int i = 0; i < timeInfos_h.size(); i++) {
            //小时
            timeData = new TimeData();
            food_s.add(timeInfos_h.get(i).getItemname());
            food_s_id.add(timeInfos_h.get(i).getItemguid());
            timeData.setTimeId(timeInfos_h.get(i).getItemguid());
            timeData.setTimeName(timeInfos_h.get(i).getItemname());
            timeDataList.add(i, timeData);
        }
        for (int i = 0; i < timeInfos_m.size(); i++) {
            //分钟
            timeData1 = new TimeData();
            computer_s.add(timeInfos_m.get(i).getItemname());
            computer_s_id.add(timeInfos_m.get(i).getItemguid());
            timeData1.setTimeId(timeInfos_m.get(i).getItemguid());
            timeData1.setTimeName(timeInfos_m.get(i).getItemname());
            timeDataList1.add(i, timeData1);
        }
    }

    private void initView() {

        //请假类型
        adapter = new ArrayAdapter<>(mContext, android.R.layout.simple_list_item_1, leaveTypes);
        spLeaveType.setAdapter(adapter);
        spLeaveType.setOnItemSelectedListener(this);
        applyLeaveInfo.setUserId(SettingManager.getInstance().getUserInfo().getId());
        applyLeaveInfo.setUsername(SettingManager.getInstance().getUserInfo().getName());

        adapter.notifyDataSetChanged();
        applyLeaveInfo.setLeavetype(leaveTypes.get(0).getItemguid());

        mIsApply = ((ApplyActivity) getActivity()).isApply;
        if (mIsApply) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            //SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
            Date date = new Date();
            String format = dateFormat.format(date);
            tvApplydate.setText(format);//创建时间

//            initDatePick();
        } else {
            tvLeaveType.setVisibility(View.VISIBLE);
            spLeaveType.setVisibility(View.GONE);

            etLeaveTitle.setEnabled(false);
            tvLeaveTimeBegin.setEnabled(false);
            tvLeaveTimeEnd.setEnabled(false);
            etLeaveReason.setEnabled(false);
            etLocation.setEnabled(false);

            tvSub.setVisibility(View.GONE);

            bean = ((ApplyActivity) getActivity()).bean;
            getData(bean);

        }
    }






    @OnClick({R.id.tv_sub, R.id.tv_leave_time_begin, R.id.tv_leave_time_end})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tv_sub:
                String start = tvLeaveTimeBegin.getText().toString().trim();
                String end = tvLeaveTimeEnd.getText().toString().trim();
                String title = etLeaveTitle.getText().toString().trim();
                String reason = etLeaveReason.getText().toString().trim();
                String location = etLocation.getText().toString().trim();
                String startTime = tvStarttime.getText().toString().trim();
                String endTime = tvEndtime.getText().toString().trim();
                String leaveDays = et_leavedays.getText().toString().trim();
                String applyDate = tvApplydate.getText().toString().trim();

                if (StringUtil.isEmpty(title)) {
                    showToast("标题不能为空");
                    return;
                }

                if (StringUtil.isEmpty(start)) {
                    showToast("请选择开始日期");
                    return;
                }

                if (StringUtil.isEmpty(end)) {
                    showToast("请选择截止日期");
                    return;
                }
                if (StringUtil.isEmpty(startTime)) {
                    showToast("请选择请假开始时间");
                    return;
                }
                if (StringUtil.isEmpty(endTime)) {
                    showToast("请选择请假结束时间");
                    return;
                }

                String startDateTime = start + " " + startTime;
                String endDateTime = end + " " + endTime;
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                try {
                    Date startDate = dateFormat.parse(startDateTime);
                    Date endDate = dateFormat.parse(endDateTime);
                    if (endDate.getTime() <= startDate.getTime()) {
                        showToast("开始时间必须小于结束时间");
                        return;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isEmpty(leaveDays)) {
                    showToast("请输入请假天数");
                    return;
                }
//                if (StringUtil.isEmpty(reason)) {
//                    showToast("请假原因不能为空");
//                    return;
//                }
                applyLeaveInfo.setLeavetitle(title);
                applyLeaveInfo.setCause(reason);
                applyLeaveInfo.setStart(start);
                applyLeaveInfo.setEnd(end);
                applyLeaveInfo.setStartTime(startTime);
                applyLeaveInfo.setEndTime(endTime);
                applyLeaveInfo.setLeaveDays(leaveDays);
                applyLeaveInfo.setApplyDate(applyDate);

                applyLeaveInfo.setStartTimeHour(startTimeHour);
                applyLeaveInfo.setStartTimeMin(startTimeMin);
                applyLeaveInfo.setEndTimeHour(endTimeHour);
                applyLeaveInfo.setEndTimeMin(endTimeMin);

                mDataMap.put("leavetitle",title);
                mDataMap.put("start",start);
                mDataMap.put("end",end);
                mDataMap.put("cause",reason);
                mDataMap.put("location",location);
                mDataMap.put("empguid",SettingManager.getInstance().getUserInfo().getId());
                mDataMap.put("empname",SettingManager.getInstance().getUserInfo().getName());
                mDataMap.put("startdate",start);
                mDataMap.put("enddate",end);
                mDataMap.put("leaveDays",leaveDays);
                mDataMap.put("applyDate",applyDate);
                mDataMap.put("startTimeHour",startTimeHour);
                mDataMap.put("startTimeMin",startTimeMin);
                mDataMap.put("endTimeHour",endTimeHour);
                mDataMap.put("endTimeMin",endTimeMin);


                Log.i("applyLeaveInfo", mDataMap.toString());

                uploadMulFile();
                break;
            case R.id.tv_leave_time_begin:
//                tpv.show(tvLeaveTimeBegin);
                showDatePickerDialog(tvLeaveTimeBegin);
                break;
            case R.id.tv_leave_time_end:
//                tpv.show(tvLeaveTimeEnd);
                showDatePickerDialog(tvLeaveTimeEnd);
                break;
        }
    }


    /**
     * 展示日期选择对话框
     */
    private void showDatePickerDialog(final TextView tv) {
        Calendar c = Calendar.getInstance();
        new DatePickerDialog(mContext, new DatePickerDialog.OnDateSetListener() {

            @Override
            public void onDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                // TODO Auto-generated method stub
                int m = monthOfYear + 1;
                String month = m >= 10 ? m + "" : "0" + m;
                String day = dayOfMonth >= 10 ? dayOfMonth + "" : "0" + dayOfMonth;
                String dateStr = year + "-" + month + "-" + day;
                tv.setText(dateStr);
                switch (view.getId()) {
                    case R.id.tv_leave_time_begin:
                        applyLeaveInfo.setStart(dateStr);
                        break;
                    case R.id.tv_leave_time_end:
                        applyLeaveInfo.setEnd(dateStr);
                        break;
                }
            }
        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)).show();
    }

//    private TimePickerView tpv;
//    /**
//     * 展示日期选择对话框
//     */

//    private void initDatePick() {
//        Calendar selectedDate = Calendar.getInstance();
//        Calendar startDate = Calendar.getInstance();
//        startDate.set(1950, 0, 1, 0, 0);
//        Calendar endDate = Calendar.getInstance();
//        endDate.set(2033, 11, 30, 23, 30);
//        //时间选择器
//        tpv = new TimePickerView.Builder(mContext, new TimePickerView.OnTimeSelectListener() {
//            @Override
//            public void onTimeSelect(Date date, View v) {//选中事件回调
//                // 这里回调过来的v,就是show()方法里面所添加的 View 参数，如果show的时候没有添加参数，v则为null
//                ((TextView) v).setText(DateUtil.dateToString(date, DateUtil.YMD_HM));
//                switch (v.getId()) {
//                    case R.id.tv_leave_time_begin:
//                        applyLeaveInfo.setStart(DateUtil.dateToString(date, DateUtil.YMD_HM));
//                        break;
//                    case R.id.tv_leave_time_end:
//                        applyLeaveInfo.setEnd(DateUtil.dateToString(date, DateUtil.YMD_HM));
//                        break;
//                }
//            }
//        })
//                //年月日时分秒 的显示与否，不设置则默认全部显示
//                .setType(new boolean[]{true, true, true, true, true, false})
//                .setLabel("年", "月", "日", "时", "分", "")
//                .isCenterLabel(false)
//                .setDividerType(WheelView.DividerType.WRAP)
//                .setDividerColor(ContextCompat.getColor(YueApplacation.mContext, R.color.colorAccent))
//                .setContentSize(PhoneUtil.dpTopx(YueApplacation.mContext, 8))
//                .setDate(selectedDate)
//                .setRangDate(startDate, endDate)
//                .setDecorView(null)
//                .build();
//    }

    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        applyLeaveInfo.setLeavetype(leaveTypes.get(position).getItemguid());
//        mDataMap.put("leavetype",leaveTypes.get(position).getItemname());
//        mDataMap.put("leaveGuid",leaveTypes.get(position).getItemguid());
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }

    @Override
    public void leaveTypeBack(List<ApplyType> leaveTypesList) {
        this.leaveTypes.clear();
        if (leaveTypesList != null && !leaveTypesList.isEmpty()) {
            this.leaveTypes.addAll(leaveTypesList);
            adapter.notifyDataSetChanged();
            applyLeaveInfo.setLeavetype(this.leaveTypes.get(0).getItemguid());
        }
    }

    //获取员工请假时间数组
    @Override
    public void showObjectItemInfoList(List<ObjectItemInfo.DataBean> infos) {
        TimeData timeData;
        TimeData timeData1;
        if (infos != null) {
            for (int i = 0; i < infos.size(); i++) {
                if (infos.get(i).getTypecode().equals("108.002")) {//小时
                    timeData = new TimeData();
                    food_s.add(infos.get(i).getItemname());
                    food_s_id.add(infos.get(i).getItemguid());

                    timeData.setTimeId(infos.get(i).getItemguid());
                    timeData.setTimeName(infos.get(i).getItemname());
                    timeDataList.add(i, timeData);

                } else if (infos.get(i).getTypecode().equals("108.003")) {//分钟
                    timeData1 = new TimeData();
                    computer_s.add(infos.get(i).getItemname());
                    computer_s_id.add(infos.get(i).getItemguid());
                    timeData1.setTimeId(infos.get(i).getItemguid());
                    timeData1.setTimeName(infos.get(i).getItemname());
                    timeDataList1.add(i, timeData1);

                }
            }
        }
    }

    @Override
    public void applyBack(String str) {
        String[] backs = str.split(",");
        if (backs != null && backs.length == 3) {
            ((ApplyActivity) getContext()).setBack(str);
        }
    }

    @Override
    public void infoBack(LeaveInfo info) {
        if (info != null) {

//            this.leaveInfo = info;


            etLeaveTitle.setText(info.getLeavetitle());
            tvLeaveType.setVisibility(View.VISIBLE);

            for (int i = 0; i < leaveTypes.size(); i++) {
                if (info.getLeavetype().equals(leaveTypes.get(i).getItemguid())) {
                    tvLeaveType.setText(leaveTypes.get(i).getItemname());
                }
            }

            tvLeaveTimeBegin.setText(info.getStartdate());
            tvLeaveTimeEnd.setText(info.getEnddate());
/*
            DataByTime dataByTime = new DataByTime();

            for (int i = 0; i < timeDataList.size(); i++) {
                if (info.getStartTimeHour().equals(timeDataList.get(i).getTimeId())) {
                    dataByTime.setStartHour(timeDataList.get(i).getTimeName());
                }
            }

            for (int i = 0; i < timeDataList.size(); i++) {
                if (info.getEndTimeHour().equals(timeDataList.get(i).getTimeId())) {
                    dataByTime.setEndHour(timeDataList.get(i).getTimeName());
                }
            }

            for (int i = 0; i < timeDataList1.size(); i++) {
                if (info.getStartTimeMin().equals(timeDataList1.get(i).getTimeId())) {
                    dataByTime.setStartMin(timeDataList1.get(i).getTimeName());
                }
            }

            for (int i = 0; i < timeDataList1.size(); i++) {
                if (info.getEndTimeMin().equals(timeDataList1.get(i).getTimeId())) {
                    dataByTime.setEndMin(timeDataList1.get(i).getTimeName());
                }
            }
            */

            tvStarttime.setText(info.getStartTimeHour() + ":" + info.getStartTimeMin());
            tvStarttime.setEnabled(false);
            tvEndtime.setText(info.getEndTimeHour() + ":" + info.getEndTimeMin());
            //tvEndtime.setText(dataByTime.getEndHour() + ":" + dataByTime.getEndMin());
            tvEndtime.setEnabled(false);
            et_leavedays.setText(info.getLeaveDays());
            tvApplydate.setText(info.getApplyDate());
            etLeaveReason.setText(info.getCause());
            bean.setFlowId(info.getWfId());

            getFileList(info.getLeaveguid());
        }
    }

    //获取详情
    private void getData(ProcessListBean bean) {
        Api.getGbkApiserver().testApi(Constant.HTTP_GET_YGQJ_INFO, bean.getParameters()).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
//                mView.dismissProgress();
                Log.i("TAG", "onResponse: " + response.body().getData());
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    Map data = (Map) response.body().getData();

                    mDataMap = data;

                    try {
                        etLeaveTitle.setText(data.get("leavetitle").toString());

                        tvLeaveTimeBegin.setText(data.get("startdate").toString());
                        tvLeaveTimeEnd.setText(data.get("enddate").toString());

                        tvStarttime.setText(data.get("startTimeHour").toString() + ":" + data.get("startTimeMin").toString());
                        tvEndtime.setText(data.get("endTimeHour").toString() + ":" + data.get("endTimeMin").toString());
                        tvStarttime.setEnabled(false);
//                        tvEndtime.setText(info.getEndTimeHour() + ":" + info.getEndTimeMin());
                        tvEndtime.setEnabled(false);
                        et_leavedays.setText(data.get("leaveDays").toString());
//                        tvApplydate.setText(info.getApplyDate());
                        etLeaveReason.setText(data.get("cause").toString());
                        etLocation.setText(data.get("location").toString());

                        bean.setFlowId(data.get("wfid").toString());
                    }catch (Exception e){
                        e.printStackTrace();
                    }


                    mRowTableAdapter.notifyDataSetChanged();

//                    mDataMap.put("leavetitle",title);
//                    mDataMap.put("start",start);
//                    mDataMap.put("end",end);
//                    mDataMap.put("cause",reason);
//                    mDataMap.put("userId",SettingManager.getInstance().getUserInfo().getId());
//                    mDataMap.put("username",SettingManager.getInstance().getUserInfo().getName());
//                    mDataMap.put("startdate",startTime);
//                    mDataMap.put("enddate",endTime);
//                    mDataMap.put("leaveDays",leaveDays);
//                    mDataMap.put("applyDate",applyDate);
//                    mDataMap.put("startTimeHour",startTimeHour);
//                    mDataMap.put("startTimeMin",startTimeMin);
//                    mDataMap.put("endTimeHour",endTimeHour);
//                    mDataMap.put("endTimeMin",endTimeMin);







//                    for (Map map : mData) {
//                        String key = map.get("key").toString();
//                        String str = (data.get(key) != null) ? data.get(key).toString() : "";
//                        mDataMap.put(key, str);
//
//                        if (map.containsKey("showKey")) {
//                            String showKey = map.get("showKey").toString();
//                            String str1 = (data.get(showKey) != null) ? data.get(showKey).toString() : "";
//                            mDataMap.put(showKey, str1);
//                        }
//                    }
//                    mAdapter.setData(mDataMap);
//                    mAdapter.notifyDataSetChanged();









                    getFileList(data.get("leaveguid").toString());

                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }



    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    @OnClick({R.id.tv_starttime, R.id.tv_endtime})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_starttime:
                Collections.sort(food_s);
                Collections.sort(computer_s);
                //条件选择器
                OptionsPickerView pvOptions = new OptionsPickerView.Builder(mContext,
                        new OptionsPickerView.OnOptionsSelectListener() {
                    @Override
                    public void onOptionsSelect(int options1, int options2, int options3, View v) {

                        startTime = food_s.get(options1) + ":" + computer_s.get(options3);
                        tvStarttime.setText(food_s.get(options1) + ":" + computer_s.get(options3));
                        startTimeHour = food_s.get(options1);
                        startTimeMin = computer_s.get(options3);
//                        for (int i = 0; i < timeDataList.size(); i++) {
//                            if (timeDataList.get(i).getTimeName().equals(food_s.get(options1))) {
//                                applyLeaveInfo.setStartTimeHour(timeDataList.get(i).getTimeId());
//                            }
//                        }
//                        for (int i = 0; i < timeDataList1.size(); i++) {
//                            if (timeDataList1.get(i).getTimeName().equals(computer_s.get(options3))) {
//                                applyLeaveInfo.setStartTimeMin(timeDataList1.get(i).getTimeId());
//                            }
//                        }

                        applyLeaveInfo.setStartTimeHour(food_s_id.get(options1));
                        applyLeaveInfo.setStartTimeMin(computer_s_id.get(options3));

                        Toast.makeText(mContext, food_s.get(options1) + "时" + computer_s.get(options3) + "分",
                                Toast.LENGTH_SHORT).show();
                    }
                }).setLabels("时", "", "分")//设置选择的单位名称
                        .build();

                pvOptions.setNPicker(food_s, clothes_s, computer_s);
                pvOptions.show();
                break;
            case R.id.tv_endtime:
                Collections.sort(food_s);
                Collections.sort(computer_s);
                //条件选择器
                OptionsPickerView pvOptions1 = new OptionsPickerView.Builder(mContext,
                        new OptionsPickerView.OnOptionsSelectListener() {
                    @Override
                    public void onOptionsSelect(int options1, int options2, int options3, View v) {
                        endTime = food_s.get(options1) + ":" + computer_s.get(options3);
                        tvEndtime.setText(food_s.get(options1) + ":" + computer_s.get(options3));
                        endTimeHour = food_s.get(options1);
                        endTimeMin = computer_s.get(options3);

//                        for (int i = 0; i < timeDataList.size(); i++) {
//                            if (timeDataList.get(i).getTimeName().equals(food_s.get(options1))) {
//                                applyLeaveInfo.setEndTimeHour(timeDataList.get(i).getTimeId());
//                            }
//                        }
//                        for (int i = 0; i < timeDataList1.size(); i++) {
//                            if (timeDataList1.get(i).getTimeName().equals(computer_s.get(options3))) {
//                                applyLeaveInfo.setEndTimeMin(timeDataList1.get(i).getTimeId());
//                            }
//                        }

                        applyLeaveInfo.setEndTimeHour(food_s_id.get(options1));
                        applyLeaveInfo.setEndTimeMin(computer_s_id.get(options3));
                        Toast.makeText(mContext, food_s.get(options1) + "时" + computer_s.get(options3) + "分",
                                Toast.LENGTH_SHORT).show();
                    }
                }).setLabels("时", "", "分")//设置选择的单位名称
                        .build();
                pvOptions1.setNPicker(food_s, clothes_s, computer_s);
                pvOptions1.show();
                break;
        }
    }

    private ArrayList<EnclosureListDto> mFileList = new ArrayList<>();
    private boolean isTakePhoto;
    private BGAPhotoHelper mPhotoHelper;
    private String formKey = "";

    private void filMethod() {
        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);
        mFileView.setApply(mIsApply);
        // 点击添加附件
        mFileView.getAddFileBtn().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showFileSelect();
            }
        });
        mFileView.setFileOperateContract(new YFileListView.FileOperateContract() {
            @Override
            public void openFile(int index) {
                mFileList.remove(index);
                mFileView.setList(mFileList);
            }
        });
        // 打开附件
        mFileView.setOpenFileContract(new YFileListView.OpenFileContract() {
            @Override
            public void openFile(EnclosureListDto dto) {
                WebActivity.launch(getActivity(), dto);
            }
        });
        getPrimarykey(new GetPrimarykeyContract() {
            @Override
            public void getPrimarykey(String str) {
                formKey = str;
                applyLeaveInfo.setLeaveGuid(str);
            }
        });
    }

    private void showFileSelect() {
        // 空照片 ，添加
        String[] str = new String[]{"系统相机", "手机相册", "手机文件"};
        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
        ab.setItems(str, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                switch (which) {
                    case 0://相机
                        isTakePhoto = true;
                        takePhoto();
                        break;
                    case 1://相册
                        isTakePhoto = false;
                        getPhoto();
                        break;
                    case 2://手机文件
                        getFile();
                        break;
                }
            }
        });
        ab.show();
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), Constant.REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), Constant.REQUEST_CODE_TAKE_PHOTO);
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    /**
     * 打开文件选择器
     */
    private void getFile() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("*/*");// 任意类型文件
        this.startActivityForResult(intent, Constant.REQUEST_CODE_FILE_SELECT);
    }

    BGAPhotoHelper1 mPhotoHelper1 = new BGAPhotoHelper1();

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQUEST_CODE_CHOOSE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_TAKE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800, 800),
                            Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_CROP) {
            if (resultCode == Activity.RESULT_OK) {
                // getCropIntent  获取裁剪完图片的路径
                String photoPath = mPhotoHelper1.getCropFilePath();
//                showToast(photoPath);
                File file = new File(mPhotoHelper1.getCropFilePath());
                double fileSize = FileUtil.getFileOrFilesSize(photoPath, 3);
                Log.i("fileSize", fileSize + "");

                //showToast(photoPath);
                if (fileSize > 1) {
                    ImageUtil.compressImageByLuban(photoPath);
                    String s = ImageUtil.compressImage(photoPath);
                    mFileList.add(new EnclosureListDto(file.getName(), s));
                } else {
                    mFileList.add(new EnclosureListDto(file.getName(), photoPath));
                }
                mFileView.setList(mFileList);
            } else {
                mPhotoHelper.deleteCameraFile();
                mPhotoHelper.deleteCropFile();
            }
        } else if (requestCode == Constant.REQUEST_CODE_FILE_SELECT) {
            String path = "";
            if (data == null) {
                // 用户未选择任何文件，直接返回
                return;
            }
            path = FileUtil.getRealPath(this.getContext(), data.getData());
            File file = new File(path);
            mFileList.add(new EnclosureListDto(file.getName(), path));
            mFileView.setList(mFileList);
            //showToast(path);
        }
    }

    private void uploadMulFile() {
        if (mFileList.isEmpty()) {
            //presenter.submitApplyLeave(applyLeaveInfo);
            // 提交
            sendData();

        } else {
            if (applyLeaveInfo.getLeaveGuid().isEmpty()) {
                getPrimarykey(new GetPrimarykeyContract() {
                    @Override
                    public void getPrimarykey(String str) {
                        applyLeaveInfo.setLeaveGuid(str);
                    }
                });
                showToast("未获取到主键");
                return;
            }

            showProgress();
            String s = DateUtil.dateToString(new Date(), DateUtil.YM1);
            HashMap<String, String> paras = new HashMap<>();
            paras.put("filePath", "/hr/emp" + s);
            paras.put("businessId", applyLeaveInfo.getLeaveGuid());
            paras.put("businessType", "EMPLOYEEINFO");
            OkHttpRequestManager.getInstance().uploadMulFile(mFileList, paras, new IRequestCallback() {
                @Override
                public void onSuccess(String response) {
                    Log.i("upload", "onSuccess: ");
                    dismissProgress();
//                    presenter.submitApplyLeave(applyLeaveInfo);
                    // 提交
                    sendData();
                }

                @Override
                public void onFailure(Throwable throwable) {
                    Log.i("upload", "onFailure: ");
                    dismissProgress();
                }
            });
        }
    }


    private void sendData() {
//        showProgress();
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("data", StringUtil.objectToJson(mDataMap));
        Api.getGbkApiserver().submitApply1(Constant.HTTP_SUBMIT_APPLY_LEAVE, pramaras).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                // dismissProgress();
                Log.i("TAG", "onResponse: " + response.body().getData());
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    //mView.applyBack(response.body().getData());
                    String str = response.body().getData();
                    String[] backs = str.split(",");
                    if (backs != null && backs.length == 3) {
                        ((ApplyActivity) getContext()).setBack(str);
                    }
                } else {
//                    mView.applyBack(response.body().getMessage());
                    showToast(response.body().getMessage());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                // dismissProgress();
                Log.i("TAG", "onResponse: fail");
                if (StringUtil.isEmpty(t.getMessage())){
                    showToast("提交失败，请重试");
                }else {
                    showToast(t.getMessage());
                }
                t.printStackTrace();
            }
        });
    }


    // 获取附件列表
    private void getFileList(String bt) {
        HashMap<String, Object> paras = new HashMap<>();
        paras.put("businessId", bt);
        paras.put("businessType", "EMPLOYEEINFO");
        Api.getGbkApiserver().getEnclosureList(Constant.HTTP_GET_ENCLOSURE_LIST, paras).enqueue(new Callback<NetworkResponse<List<EnclosureListDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<EnclosureListDto>>> call,
                                   Response<NetworkResponse<List<EnclosureListDto>>> response) {
                if (response.body().getData() != null) {
                    mFileList.clear();
                    mFileList.addAll(response.body().getData());
                    mFileView.setList(mFileList);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<EnclosureListDto>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }


    private void initDetailView() {
        if (recyclerView.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(mContext);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            recyclerView.setLayoutManager(manager);
        }
        mRowTableAdapter = new LeaveRowTableAdapter(mIsApply);
        mRowTableAdapter.setNewData(leaveTypeMap);
        recyclerView.setAdapter(mRowTableAdapter);

//        tvAddDetail.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                mRowTableAdapter.addData(new HashMap());
//            }
//        });
//        tvDeleteDetail.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if (mRowTableAdapter.getData().size() > 0) {
//                    mRowTableAdapter.remove(mRowTableAdapter.getData().size() - 1);
//                }
//            }
//        });
    }

    public class LeaveRowTableAdapter extends BaseQuickAdapter<Map, YBaseViewHolder> {

        private boolean isApply = true;

        public LeaveRowTableAdapter(boolean isApply) {
            super(R.layout.hdmh_ygqj_apply_detail_cell);
            this.isApply = isApply;
        }

        @Override
        protected void convert(@NonNull YBaseViewHolder helper, Map item) {
            helper.setIsRecyclable(false);
            EditText leaveType = helper.getView(R.id.leaveType);
            EditText leaveNum = helper.getView(R.id.leaveNum);
//            leaveNum.setInputType(InputType.TYPE_CLASS_NUMBER);
            if (isApply) {
                //清除焦点
//                etGangWei.clearFocus();
                leaveNum.clearFocus();
                //先清除之前的文本改变监听
                if (leaveNum.getTag() instanceof TextWatcher) {
                    leaveNum.removeTextChangedListener((TextWatcher) leaveNum.getTag());
                }
            } else {
                leaveNum.setEnabled(false);
            }
            //设置数据
            String type = item.get("name").toString();
            String key1 = item.get("key1").toString();
            String key2 = item.get("key2").toString();
            leaveType.setText(StringUtil.isEmpty(type) ? "" : type);
            if (mDataMap.get(key1) != null) {
                leaveNum.setText(mDataMap.get(key2).toString().trim());
            }
            if (isApply) {
                final TextWatcher leaveNumWatcher = new SimpleTextWatcher() {
                    @Override
                    public void afterTextChanged(Editable editable) {
                        if (TextUtils.isEmpty(editable)) {
                            mDataMap.put(key2, "");
                        } else {
                            mDataMap.put(key2, String.valueOf(editable));
                            mDataMap.put(key1, key1);
                        }
                    }
                };

                //监听设置到不同的EditText上
                leaveNum.addTextChangedListener(leaveNumWatcher);
                leaveNum.setTag(leaveNumWatcher);
            }
        }
    }
}