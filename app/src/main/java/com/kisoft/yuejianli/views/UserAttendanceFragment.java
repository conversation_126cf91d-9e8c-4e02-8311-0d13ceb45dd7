package com.kisoft.yuejianli.views;

import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kisoft.yuejianli.adpter.CalendarAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.contract.UserAttendanceContract;
import com.kisoft.yuejianli.entity.CalendarData;
import com.kisoft.yuejianli.entity.PunchCardInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.UserAttendanceModel;
import com.kisoft.yuejianli.presenter.UserAttendancePresenter;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.TimeZone;

import butterknife.BindView;
import butterknife.BindViews;
import butterknife.ButterKnife;
import butterknife.Unbinder;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/4/3.
 */

public class UserAttendanceFragment extends BaseFragment<UserAttendanceModel, UserAttendancePresenter>
        implements UserAttendanceContract.UserAttendanceViewContract, View.OnClickListener {

    @BindView(R.id.iv_left)
    ImageView ivLeft;
    @BindView(R.id.tv_month)
    TextView tvTime;
    @BindView(R.id.iv_right)
    ImageView ivRight;

    Unbinder unbinder;
    @BindView(R.id.tv_selscted_date)
    TextView tvSelsctedDate;
    Unbinder unbinder1;
    @BindView(R.id.tv_punch_time1)
    TextView tvPunchTime1;
    @BindView(R.id.tv_status1)
    TextView tvStatus1;
    @BindView(R.id.ll_punch_card1)
    LinearLayout llPunchCard1;
    @BindView(R.id.tv_punch_time2)
    TextView tvPunchTime2;
    @BindView(R.id.tv_status2)
    TextView tvStatus2;
    @BindView(R.id.ll_punch_card2)
    LinearLayout llPunchCard2;
    @BindView(R.id.tv_punch_time3)
    TextView tvPunchTime3;
    @BindView(R.id.tv_status3)
    TextView tvStatus3;
    @BindView(R.id.ll_punch_card3)
    LinearLayout llPunchCard3;
    @BindView(R.id.tv_punch_time4)
    TextView tvPunchTime4;
    @BindView(R.id.tv_status4)
    TextView tvStatus4;
    @BindView(R.id.ll_punch_card4)
    LinearLayout llPunchCard4;
    @BindView(R.id.tv_time_diff)
    TextView tvTimeDiff;

    @BindViews({R.id.tv_status1, R.id.tv_status2, R.id.tv_status3, R.id.tv_status4})
    List<TextView> tvStatuses;

    private String userId = "";
    private String date = "";
    private UserAttendanceModel mModel;
    private UserAttendancePresenter mPresenter;

    /**
     * 日历列表
     **/
    @BindView(R.id.gv_calendar_list)
    GridView gvCalendarList;
    private List<CalendarData> list_calendar = new ArrayList<>();
    private CalendarAdapter adapter_calendar;
    /**
     * 年，月
     **/
    private int year, month;
    /**
     * 当前的日历
     **/
    private Calendar currentCalendar;

    /**
     * 排班方式
     *   // 0:固定四次班制 1:自由打卡 2：固定两次班制
     * true 自由排班，false 固定排班
     */
    private boolean scheduleWay = true;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mModel = new UserAttendanceModel(getActivity());
        mPresenter = new UserAttendancePresenter(this, mModel);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        initView(inflater);
        initData();
        initCalendar();
        unbinder1 = ButterKnife.bind(this, mRootView);
        return mRootView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    private void initView(LayoutInflater inflater) {
        mRootView = inflater.inflate(getRootView(), null);
        unbinder = ButterKnife.bind(this, mRootView);
        ivLeft.setOnClickListener(this);
        ivRight.setOnClickListener(this);
        date = DateUtil.getMonthDate();
        tvTime.setText(date);

    }

    private void initData() {
        scheduleWay = "0".equals(SettingManager.getInstance().getUserInfo().getScheduleWay());
        if (StringUtil.isEmpty(userId)) {
            userId = SettingManager.getInstance().getUserInfo().getId();
        }

        mPresenter.getRecord(userId, date, SettingManager.getInstance().getProject().getProjectId());
    }

    /**
     * 初始化日历控件
     */
    private void initCalendar() {
        adapter_calendar = new CalendarAdapter(mContext, list_calendar);
        gvCalendarList.setAdapter(adapter_calendar);
        currentCalendar = Calendar.getInstance();
        //设置当前年月
        String[] dateArray = date.split("-");
        year = Integer.parseInt(dateArray[0]);
        month = Integer.parseInt(dateArray[1]);
        getCalendarData();
        adapter_calendar.notifyDataSetChanged();

        //日历选择
        gvCalendarList.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                CalendarData calendarData = (CalendarData) parent.getItemAtPosition(position);
                adapter_calendar.setCheckedFirstDay(false);
                if (!TextUtils.isEmpty(calendarData.getDay())) {
                    adapter_calendar.setSelectPosition(position);
                }
                setPunchCardList(list_calendar.get(position));
            }
        });

    }


    @Override
    public int getRootView() {
        return R.layout.fragment_user_attendance;
    }

    @Override
    public void showRecord(List<PunchCardInfo> infos) {
        adapter_calendar.info.clear();
        adapter_calendar.info.addAll(infos);   // punchTime1 = 2018-12-6 10:37:08
        adapter_calendar.notifyDataSetChanged();

        CalendarData calendarData = (CalendarData) adapter_calendar.getItem(curDayPos);
        adapter_calendar.setCheckedFirstDay(false);
        if (!TextUtils.isEmpty(calendarData.getDay())) {
            adapter_calendar.setSelectPosition(curDayPos);
        }

        setPunchCardList(list_calendar.get(curDayPos));
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.iv_left:
                getLastMonth();
                break;

            case R.id.iv_right:
                getAfterMonth();
                break;
            default:
                break;
        }
    }


    private void getLastMonth() {
        String time = this.date;
        date = DateUtil.getLsatMonth(time);
        tvTime.setText(date);
        initData();
        //设置当前年月
        String[] dateArray = date.split("-");
        year = Integer.parseInt(dateArray[0]);
        month = Integer.parseInt(dateArray[1]);
        getCalendarData();
        adapter_calendar.notifyDataSetChanged();
    }

    private void getAfterMonth() {
        String time = this.date;
        date = DateUtil.getAfterMonth(time);
        tvTime.setText(date);
        initData();
        //设置当前年月
        String[] dateArray = date.split("-");
        year = Integer.parseInt(dateArray[0]);
        month = Integer.parseInt(dateArray[1]);
        getCalendarData();
        adapter_calendar.notifyDataSetChanged();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    int curDayPos = 0;

    /**
     * 获取日历数据
     */
    private void getCalendarData() {
        list_calendar.clear();
        //初始化日历
        Calendar calendar = Calendar.getInstance();

        //月数从0开始，所以-1
        //天数从1开始
        calendar.set(year, month - 1, 1);

        //获取当月1号是一周的第几天
        int weekDay = calendar.get(Calendar.DAY_OF_WEEK);

        //为了显示上个月余的天数
        calendar.add(Calendar.DAY_OF_MONTH, -(weekDay - 1));

        for (int i = 0; i < 42; i++) {
            CalendarData calendarData = new CalendarData();
            //不是当前月份，设置空
            if (calendar.get(Calendar.MONTH) != month - 1) {
                calendarData.setDay("");
                calendarData.setToday(false);
                list_calendar.add(calendarData);
            } else {
                calendarData.setDay("" + calendar.get(Calendar.DAY_OF_MONTH));
                calendarData.setMonth("" + (calendar.get(calendar.MONTH) + 1));
                calendarData.setYear("" + calendar.get(Calendar.YEAR));
                //是否为今天
                if (calendar.get(Calendar.YEAR) == currentCalendar.get(Calendar.YEAR) &&
                        calendar.get(Calendar.MONTH) == currentCalendar.get(Calendar.MONTH) &&
                        calendar.get(Calendar.DAY_OF_MONTH) == currentCalendar.get(Calendar.DAY_OF_MONTH)
                        ) {
                    curDayPos = i;
                    calendarData.setToday(true);
                } else {
                    calendarData.setToday(false);
                }

                //是否为本月第一天
                if (1 == calendar.get(Calendar.DAY_OF_MONTH)) {
                    calendarData.setFirstDay(true);
                    //calendarData.setDay((calendar.get(Calendar.DAY_OF_MONTH)+1)+"");
                } else {
                    calendarData.setFirstDay(false);
                }

                list_calendar.add(calendarData);
            }

            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        adapter_calendar.setCheckedFirstDay(true);
        adapter_calendar.setSelectPosition(-1);
    }


    /**
     * 根据日期设置签到列表
     *
     * @param calendarData
     */
    private void setPunchCardList(CalendarData calendarData) {
        String year = calendarData.getYear();
        String month = calendarData.getMonth();
        String day = calendarData.getDay();
        tvSelsctedDate.setText(year + "-" + month + "-" + day);
        String punchTime1 = "", punchTime2 = "", punchTime3 = "", punchTime4 = "";
        String state = "";
        for (int i = 0; i < adapter_calendar.info.size(); i++) {
            PunchCardInfo info = adapter_calendar.info.get(i);
            if (info.getYear().equals(calendarData.getYear()) &&
                    info.getMonth().equals(calendarData.getMonth()) &&
                    info.getDay().equals(calendarData.getDay())) {
                punchTime1 = info.getPunchTime1();
                punchTime2 = info.getPunchTime2();
                punchTime3 = info.getPunchTime3();
                punchTime4 = info.getPunchTime4();
                state = info.getState();
            }
        }
        if (TextUtils.isEmpty(punchTime1)) {
            llPunchCard1.setVisibility(View.GONE);

        } else {
            llPunchCard1.setVisibility(View.VISIBLE);
            tvPunchTime1.setText(punchTime1);
        }
        if (TextUtils.isEmpty(punchTime2)) {
            llPunchCard2.setVisibility(View.GONE);
        } else {
            llPunchCard2.setVisibility(View.VISIBLE);
            tvPunchTime2.setText(punchTime2);
        }
        if (TextUtils.isEmpty(punchTime3)) {
            llPunchCard3.setVisibility(View.GONE);
        } else {
            llPunchCard3.setVisibility(View.VISIBLE);
            tvPunchTime3.setText(punchTime3);
        }
        if (TextUtils.isEmpty(punchTime4)) {
            llPunchCard4.setVisibility(View.GONE);
        } else {
            llPunchCard4.setVisibility(View.VISIBLE);
            tvPunchTime4.setText(punchTime4);
        }

        initStatus(state);

        //工作时长
        try {
            if (!scheduleWay) {
                if (TextUtils.isEmpty(punchTime1) || TextUtils.isEmpty(punchTime4)) {
                    tvTimeDiff.setText("0小时0分钟");
                    return;
                }
                long downTime = DateUtil.stringToDate(punchTime4, DateUtil.YMD_HMS).getTime();
                long upTime = DateUtil.stringToDate(punchTime1, DateUtil.YMD_HMS).getTime();
                tvTimeDiff.setText(generateTime(downTime - upTime));
            } else {
                if (TextUtils.isEmpty(punchTime1) || TextUtils.isEmpty(punchTime2)) {
                    tvTimeDiff.setText("0小时0分钟");
                } else if (TextUtils.isEmpty(punchTime3) || TextUtils.isEmpty(punchTime4)) {
                    long downTime = DateUtil.stringToDate(punchTime2, DateUtil.YMD_HMS).getTime();
                    long upTime = DateUtil.stringToDate(punchTime1, DateUtil.YMD_HMS).getTime();
                    tvTimeDiff.setText(generateTime(downTime - upTime));
                } else {
                    long downTime = DateUtil.stringToDate(punchTime2, DateUtil.YMD_HMS).getTime();
                    long upTime = DateUtil.stringToDate(punchTime1, DateUtil.YMD_HMS).getTime();
                    long downTime1 = DateUtil.stringToDate(punchTime4, DateUtil.YMD_HMS).getTime();
                    long upTime1 = DateUtil.stringToDate(punchTime3, DateUtil.YMD_HMS).getTime();
                    tvTimeDiff.setText(generateTime((downTime - upTime) + (downTime1 - upTime1)));
                }

            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

    }

    void initStatus1(int index, String state) {
        /**
         * 打卡状态
         *  1：正常  2：迟到    3：早退  4：旷工  5.请假  6：外勤  7：补卡
         */
        switch (state) {
            case "1":
                tvStatuses.get(index).setText("正常");
                tvStatuses.get(index).setTextColor(getResources().getColor(R.color.sign_ok));
                break;
            case "2":
                tvStatuses.get(index).setText("迟到");
                tvStatuses.get(index).setTextColor(getResources().getColor(R.color.sign_fieldwork));
                break;
            case "3":
                tvStatuses.get(index).setText("早退");
                tvStatuses.get(index).setTextColor(getResources().getColor(R.color.sign_fieldwork));
                break;
            case "4":
                tvStatuses.get(index).setText("旷工");
                tvStatuses.get(index).setTextColor(getResources().getColor(R.color.sign_not));
                break;
           /* case "5":
                tvStatuses.get(index).setText("请假");
                tvStatuses.get(index).setTextColor(getResources().getColor(R.color.sign_leave));
                break;*/
            case "6":
                tvStatuses.get(index).setText("外勤");
                tvStatuses.get(index).setTextColor(getResources().getColor(R.color.sign_fieldwork));
                break;
           /* case "7":
                tvStatuses.get(index).setText("补卡");
                tvStatuses.get(index).setTextColor(getResources().getColor(R.color.sign_fieldwork));
                break;*/

        }
    }
    private void initStatus(String state) {
        if (StringUtil.isEmpty(state))
            return;
        if (state.length() >= 7) {
            String[] pcState = state.split(",");

            for (int i = 0; i < pcState.length; i++)
                initStatus1(i, pcState[i]);
        } else {
            /**
             * 打卡状态
             * 1：正常  2：迟到    3：早退  4：旷工  5.请假  6：外勤  7：补卡
             */
            switch (state) {
                case "1":
                    if (scheduleWay) {  // 自由打卡
                        tvStatus1.setText("正常");
                        tvStatus4.setText("正常");
                    } else {
                        tvStatus1.setText("正常");
                        tvStatus2.setText("正常");
                        tvStatus3.setText("正常");
                        tvStatus4.setText("正常");
                    }
                    break;
                case "2":
                    if (scheduleWay) {
                        tvStatus1.setText("迟到");
                        tvStatus4.setText("正常");
                    } else {
                        tvStatus1.setText("迟到");
                        tvStatus2.setText("正常");
                        tvStatus3.setText("正常");
                        tvStatus4.setText("正常");
                    }
                    tvStatus1.setTextColor(Color.RED);
                    break;
                case "3":
                    if (scheduleWay) {
                        tvStatus1.setText("正常");
                        tvStatus4.setText("早退");
                    } else {
                        tvStatus1.setText("正常");
                        tvStatus2.setText("早退");
                        tvStatus3.setText("正常");
                        tvStatus4.setText("正常");
                    }
                    tvStatus1.setTextColor(Color.RED);
                    break;
                case "4":
                    if (scheduleWay) {
                        tvStatus1.setText("旷工");
                        tvStatus4.setText("正常");
                    } else {
                        tvStatus1.setText("旷工");
                        tvStatus2.setText("正常");
                        tvStatus3.setText("正常");
                        tvStatus4.setText("正常");
                    }
                    tvStatus1.setTextColor(Color.RED);
                    break;
               /* case "5":
                    if (scheduleWay) {
                        tvStatus1.setText("正常");
                        tvStatus4.setText("请假");
                    } else {
                        tvStatus1.setText("正常");
                        tvStatus2.setText("请假");
                        tvStatus3.setText("正常");
                        tvStatus4.setText("正常");
                    }
                    tvStatus2.setTextColor(Color.RED);*/
                case "6":
                    if (scheduleWay) {
                        tvStatus1.setText("外勤");
                        tvStatus4.setText("外勤");
                    } else {
                        tvStatus1.setText("外勤");
                        tvStatus2.setText("外勤");
                        tvStatus3.setText("正常");
                        tvStatus4.setText("正常");
                    }
                    tvStatus1.setTextColor(getResources().getColor(R.color.sign_fieldwork));
                    tvStatus2.setTextColor(getResources().getColor(R.color.sign_fieldwork));
                    break;
            }
        }
    }

    /**
     * 将毫秒转时分秒
     *
     * @param time
     * @return
     */
    public static String generateTime(long time) {

//        SimpleDateFormat format = new SimpleDateFormat()
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
        formatter.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
        String hms = formatter.format(time);
        return hms;




//        int minutes=0;
//        int hours = (int)time / 3600000;
//        if(hours>0){
//            minutes= (int)time%3600000/60000;
//        }else{
//            minutes = (int)(time / 60000);
//        }
//        return hours > 0 ? hours + "小时" + minutes + "分钟" : hours + "小时"+minutes + "分钟";
    }
}
