package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.appcompat.widget.Toolbar;
import android.widget.ImageView;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ExamQuestBankAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ExamClassifyContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ExamQuestBank;
import com.kisoft.yuejianli.entity.ExamSubmitInfo;
import com.kisoft.yuejianli.model.ExamClassifyModel;
import com.kisoft.yuejianli.presenter.ExamClassifyPresenter;
import com.kisoft.yuejianli.utils.ScreenUtils;
import com.kisoft.yuejianli.utils.recyclerview.HorizontalDividerItemDecoration;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 分类练习
 */
public class ExamClassifyTestActivity extends BaseActivity<ExamClassifyModel, ExamClassifyPresenter> implements ExamClassifyContract.View {

    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.tool_bar)
    Toolbar toolBar;
    @BindView(R.id.mRecyclerView)
    RecyclerView mRecyclerView;
    private ExamQuestBankAdapter examQuestBankAdapter;

    public static void launch(Activity activity) {
        Intent intent = new Intent(activity, ExamClassifyTestActivity.class);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        init();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_exam_classify_test;
    }

    private void init(){
        mModel=new ExamClassifyModel(this);
        mPresenter=new ExamClassifyPresenter(this,mModel);
        tvTitle.setText("分类练习");
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this,LinearLayoutManager.VERTICAL,false));
        mRecyclerView.addItemDecoration(new HorizontalDividerItemDecoration.Builder(this)
                .drawable(R.color.line_space)
                .size(ScreenUtils.dip2px(this, 1))
                .build());
        examQuestBankAdapter=new ExamQuestBankAdapter();
        mRecyclerView.setAdapter(examQuestBankAdapter);
        mPresenter.getQuestbankList(0, Constant.LOAD_DATA_NORMAL);
        examQuestBankAdapter.setOnTypeSelectClick(new ExamQuestBankAdapter.OnTypeSelectClick() {
            @Override
            public void onClick(ExamQuestBank.QuestbankTypesBean item) {
                ExamSubmitInfo examSubmitInfo=new ExamSubmitInfo();
                examSubmitInfo.setEqtId(item.getId());
                ExamIngActivity.launch(ExamClassifyTestActivity.this,item.getTypeName(),ExamIngActivity.EXAM_TYPE_TYPE_PRACTICE,examSubmitInfo,null);
            }
        });
    }

    @OnClick(R.id.iv_back)
    void back(){
        finish();
    }

    @Override
    public void showQuestbankList(BaseList<ExamQuestBank> questBankBaseList, int type) {
        examQuestBankAdapter.setNewData(questBankBaseList.getList());
    }

    @Override
    public void finishRefresh() {

    }

    @Override
    public void showError() {
        showToast(getString(R.string.net_error));
    }
}
