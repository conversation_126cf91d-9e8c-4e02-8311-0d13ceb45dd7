package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.adpter.ContractListAdapter;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ContractListModel;
import com.kisoft.yuejianli.presenter.ContractListPresnter;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.contract.ContractListContract;
import com.kisoft.yuejianli.entity.ContractInfo;

/**
 * Created by tudou on 2018/6/7.
 */

public class ContractListActivity extends BaseActivity<ContractListModel, ContractListPresnter> implements
        ContractListContract.ContractListViewContract{

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_month)
    TextView tvTime;

    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    private View empotyView;
    private String typeName = "";
    private String month = "";


    private List<ContractInfo> contractInfos = new ArrayList<>();
    private ContractListAdapter mAdapter;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    private ContractListModel model;
    private ContractListPresnter presnter;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new ContractListModel(this);
        presnter = new ContractListPresnter(this ,model);
        initMVP(model, presnter);
        Intent intent = getIntent();
        if(intent != null){
            typeName = intent.getStringExtra(Constant.INTENT_KEY_CONTRACT_TYPE);
        }
        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);

        initView();
        initData();
    }

    private void initView(){
        tvTitle.setText(typeName);
        month = DateUtil.getMonthDate();
        tvTime.setText(month);
        if(rvContent.getLayoutManager() == null){
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new ContractListAdapter(R.layout.item_contract_list, contractInfos);
        mAdapter.setEmptyView(empotyView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                goContractDetail(contractInfos.get(position));
            }
        });
        rvContent.setAdapter(mAdapter);
    }

    private void initData(){
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        if(projectInfo!= null){
            mPresenter.getContracts(userInfo.getId(), projectInfo.getProjectId(), null, typeName);
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_contract_list;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    @OnClick(R.id.iv_left)
    public void getLastMonth() {
        String time = this.month;
        month = DateUtil.getLsatMonth(time);
        tvTime.setText(month);
        initData();
        mAdapter.notifyDataSetChanged();
    }

    @OnClick(R.id.iv_right)
    public void getAfterMonth() {
        String time = this.month;
        month = DateUtil.getAfterMonth(time);
        tvTime.setText(month);
        initData();
        mAdapter.notifyDataSetChanged();
    }

    private void goContractDetail(ContractInfo contractInfo){
        Intent intent = new Intent();
        intent.setClass(this, ContractInfoActivity.class);
        intent.putExtra(Constant.INTENT_KEY_CONTRACT_INFO ,contractInfo);
        startActivity(intent);
    }

    @Override
    public void showContracts(List<ContractInfo> infos) {
        if(infos!= null){
            this.contractInfos.clear();
            this.contractInfos.addAll(infos);
        }
        mAdapter.notifyDataSetChanged();
    }
}
