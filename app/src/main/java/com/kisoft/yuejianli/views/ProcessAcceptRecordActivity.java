package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ProcessAcceptDetailBean;
import com.kisoft.yuejianli.ui.TipsQuestionView;
import com.kisoft.yuejianli.ui.UnitChildListActivity;
import com.kisoft.yuejianli.ui.YSubmitCell;
import com.kisoft.yuejianli.ui.YTextViewCell;

import java.io.Serializable;

import butterknife.BindView;
import butterknife.OnClick;

public class ProcessAcceptRecordActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.subName)
    TipsQuestionView subName;// 分部工程

    @BindView(R.id.keyProName)
    TipsQuestionView keyProName;// 关键工序

    @BindView(R.id.checkProject)
    YTextViewCell checkProject;// 检查项目

    @BindView(R.id.range)
    YTextViewCell range;// 验收范围

    @BindView(R.id.checkDetail)
    YTextViewCell checkDetail;// 检查情况

    @BindView(R.id.submit_cell)
    YSubmitCell submitCell;// 检查情况

//    private List<UnitListBean.ListDataBean.ChildListDataBean> mFBDatas = new ArrayList<>(); // 分部
    private ProcessAcceptDetailBean.AddRowFiledDataBean mBean = new ProcessAcceptDetailBean.AddRowFiledDataBean();
    private Serializable mFBData;
    private boolean mIsShow = false;

    @Override
    public int getLayoutId() {
        return R.layout.activity_process_accept_record;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getIntent() != null){
            mFBData = getIntent().getSerializableExtra("data");
            mIsShow = getIntent().getBooleanExtra("isShow", false);
            if (mIsShow){
                mBean = (ProcessAcceptDetailBean.AddRowFiledDataBean) getIntent().getSerializableExtra(
                        "AddRowFiledDataBean");
            }
        }
        initData();
        initView();
    }

    private void initView() {
        tvTitle.setText("验收记录");

        // 分部工程
        subName.getTitle().setText("分部工程");
        subName.getQuestEdit().setEnabled(false);
        subName.getSelectView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(mContext, UnitChildListActivity.class);
                intent.putExtra("mType", 2);//分部工程2
                intent.putExtra("mEndType", 2);
                intent.putExtra("data", mFBData);
                startActivityForResult(intent, Constant.REQUEST_CODE_UNIT_FENBU);
            }
        });
        if (mBean != null){
            subName.getQuestEdit().setText(mBean.getSubName());
        }
        subName.getDocBtn().setVisibility(View.GONE);

        // 关键工序
        keyProName.getTitle().setText("关键工序");
        keyProName.getQuestEdit().setEnabled(false);
        keyProName.getSelectView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(mContext, KeyProcessListActivity.class);
                startActivityForResult(intent, Constant.REQUEST_CODE_KEYPROCESS_LIST);
            }
        });
        if (mBean != null) {
            keyProName.getQuestEdit().setText(mBean.getKeyProName());
        }
        keyProName.getDocBtn().setVisibility(View.GONE);

        if (mBean != null) {
            checkProject.getEtContent().setText(mBean.getCheckProject());// 检查项目
        }
        checkProject.getEtContent().setEnabled(false);

        if (mBean != null) {
            range.getEtContent().setText(mBean.getRange());// 验收范围
        }
        range.getEtContent().setEnabled(!mIsShow);

        if (mBean != null) {
            checkDetail.getEtContent().setText(mBean.getCheckDetail());// 检查情况
        }
        checkDetail.getEtContent().setEnabled(!mIsShow);

        submitCell.setVisibility(mIsShow ? View.GONE : View.VISIBLE);

        submitCell.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                mBean.setSubName(subName.getQuestEdit().getText().toString());// 分部工程
                mBean.setKeyProName(keyProName.getQuestEdit().getText().toString());// 关键工序
                mBean.setCheckProject(checkProject.getEtContent().getText().toString());// 检查项目
                mBean.setRange(range.getEtContent().getText().toString());// 验收范围
                mBean.setCheckDetail(checkDetail.getEtContent().getText().toString());// 检查情况
                Intent intent = new Intent();
                intent.putExtra("data", mBean);
                setResult(Activity.RESULT_OK, intent);
                finish();
            }
        });
    }

    private void initData() {

    }



    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case Constant.REQUEST_CODE_UNIT_FENBU:
                // 分部工程
                if (resultCode == Activity.RESULT_OK) {
                    String unitNameStr = data.getStringExtra("data").toString();
                    String ids = data.getStringExtra("ids").toString();
                    subName.getQuestEdit().setText(unitNameStr);
                    mBean.setSubName(unitNameStr);
                }
                break;
            case Constant.REQUEST_CODE_KEYPROCESS_LIST:
                // 关键工序
                if (resultCode == Activity.RESULT_OK) {
                    String KeyProjectStr = data.getStringExtra("KeyProject");
                    String CheckProjectStr = data.getStringExtra("CheckProject");
                    keyProName.getQuestEdit().setText(KeyProjectStr);
                    checkProject.getEtContent().setText(CheckProjectStr);
                }
                break;
        }
    }

    @OnClick(R.id.iv_back)
    public void goBack(){

        finish();
    }
}