package com.kisoft.yuejianli.views;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.kisoft.yuejianli.adpter.ProjectAttendanceAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.presenter.ProjectAttendancePresenter;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.R;

import com.kisoft.yuejianli.contract.ProjectAttendanceContract;
import com.kisoft.yuejianli.entity.PunchCardInfo;
import com.kisoft.yuejianli.model.ProjectAttendanceModel;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/4/3.
 */

public class ProjectAttendanceFragment extends BaseFragment<ProjectAttendanceModel, ProjectAttendancePresenter>
        implements ProjectAttendanceContract.ProjectAttendanceViewContract, View.OnClickListener {

    private ImageView ivLeft;
    private ImageView ivRight;
    private TextView tvTime;
    private RecyclerView rvContent;
    private ProjectAttendanceAdapter mAdapter;
    private List<PunchCardInfo> infos = new ArrayList<>();
    private String date;
    private String projectId = "";

    private ProjectAttendanceModel mModel;
    private ProjectAttendancePresenter mPresenter;
    private View headView;

    /**
     * 排班方式
     * true 自由排班，false 固定排班
     */
    private boolean scheduleWay = false;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mModel = new ProjectAttendanceModel(getActivity());
        mPresenter = new ProjectAttendancePresenter(this, mModel);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        scheduleWay = "1".equals(SettingManager.getInstance().getUserInfo().getScheduleWay());
        initView(inflater);
        initData();

        return mRootView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    private void initView(LayoutInflater inflater) {
        mRootView = inflater.inflate(getRootView(), null);
        headView = inflater.inflate(R.layout.item_project_attendance1, null);
        tvTime = mRootView.findViewById(R.id.tv_month);
        date = DateUtil.getTodayDate();
        tvTime.setText(DateUtil.getFormatDate(date,DateUtil.YMD));
        ivLeft = mRootView.findViewById(R.id.iv_left);
        ivRight = mRootView.findViewById(R.id.iv_right);
        ivLeft.setOnClickListener(this);
        ivRight.setOnClickListener(this);
        rvContent = mRootView.findViewById(R.id.rv_project_attendance);
        if(rvContent.getLayoutManager() == null){
            LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(layoutManager);
        }
        mAdapter = new ProjectAttendanceAdapter(infos);
        mAdapter.addHeaderView(headView);
        rvContent.setAdapter(mAdapter);
    }

    private void initData() {
        if(StringUtil.isEmpty(projectId)){
            projectId = SettingManager.getInstance().getProject().getProjectId();
        }
        mPresenter.getRecord(projectId, date);
    }

    @Override
    public int getRootView() {
        return R.layout.fragment_project_attendance;
    }

    @Override
    public void showInfo(List<PunchCardInfo> infos) {
        this.infos.clear();
        this.infos.addAll(infos);
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.iv_left:
                getLastday();
                break;
            case R.id.iv_right:
                getnextday();
                break;
            default:
                break;
        }
    }


    private void getLastday(){
        String time = date;
        Log.i("000" , "------"+time);
        date = DateUtil.getLastDay(time);
        Log.i("000" , "------"+date);
        tvTime.setText(DateUtil.getFormatDate(date ,DateUtil.YMD));
        initData();
    }


    private void getnextday(){
        String time = date;
        Log.i("000","________________"+time);
        date = DateUtil.getNextDay(time);
        Log.i("000","________________"+date);
        tvTime.setText(DateUtil.getFormatDate(date, DateUtil.YMD));
        initData();
    }

}
