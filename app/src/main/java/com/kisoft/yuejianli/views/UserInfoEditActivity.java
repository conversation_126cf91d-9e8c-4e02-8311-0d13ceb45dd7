package com.kisoft.yuejianli.views;

import android.Manifest;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Paint;
import android.os.Bundle;
import android.os.Environment;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;

import com.facebook.drawee.view.SimpleDraweeView;

import com.king.zxing.Intents;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.HuaweiService;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.UserInfoEditContract;
import com.kisoft.yuejianli.entity.FunctionPermission;
import com.kisoft.yuejianli.entity.LogoutEvent;
import com.kisoft.yuejianli.entity.PhotoUploadResulte;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.entity.event.FaceImageEvent;
import com.kisoft.yuejianli.face.model.FaceAdd;
import com.kisoft.yuejianli.face.model.FaceBean;
import com.kisoft.yuejianli.face.model.FaceSearch;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.UserInfoEditModel;
import com.kisoft.yuejianli.presenter.UserInfoEditPresenter;
import com.kisoft.yuejianli.utils.ImageLoadUtil;
import com.kisoft.yuejianli.utils.PermissionsChecker;
import com.kisoft.yuejianli.utils.StringUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.common.util.LogUtil;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;
import pub.devrel.easypermissions.AfterPermissionGranted;
import pub.devrel.easypermissions.EasyPermissions;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import com.king.zxing.CaptureActivity;

/**
 * Created by tudou on 2018/6/14.
 */
public class UserInfoEditActivity extends BaseActivity<UserInfoEditModel, UserInfoEditPresenter>
        implements UserInfoEditContract.UserInfoEditViewContract {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;

    @BindView(R.id.iv_avatar)
    SimpleDraweeView ivAvatar;

    @BindView(R.id.tv_phone)
    EditText etPhone;
    @BindView(R.id.ll_user_faceAdd)
    LinearLayout llUserFaceAdd;

    @BindView(R.id.ll_user_scan)
    LinearLayout llUserScan;

    @BindView(R.id.tvProtocol)
    TextView tvProtocol;

    private UserInfoEditModel model;
    private UserInfoEditPresenter presenter;

    private UserInfo userInfo;

    private BGAPhotoHelper mPhotoHelper;

    private static final int REQUEST_CODE_CHOOSE_PHOTO = 1;
    private static final int REQUEST_CODE_TAKE_PHOTO = 2;
    private static final int REQUEST_CODE_CROP = 3;
    private static final int REQUEST_CODE_FACE = 51;
    private String mFaceId;
    public static final int RC_CAMERA = 0X01;
    public static final int REQUEST_CODE_SCAN = 0X1092;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new UserInfoEditModel(mContext);
        presenter = new UserInfoEditPresenter(this, model);
        initMVP(model, presenter);

        initData();
        initView();
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        BGAPhotoHelper.onSaveInstanceState(mPhotoHelper, outState);
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        BGAPhotoHelper.onRestoreInstanceState(mPhotoHelper, savedInstanceState);
    }

    private void initView() {
        tvTitle.setText("账号管理");
        tvProtocol.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG);
        ImageLoadUtil.loadIvatar(mContext, userInfo.getUserPotoPath(), ivAvatar);
        ivAction.setVisibility(View.VISIBLE);
        ivAction.setImageResource(R.drawable.ic_ti_jiao);
        FunctionPermission permissionFunction = SettingManager.getInstance().getPermissionFunction();
        if (permissionFunction != null && permissionFunction.isHasFace()) {
            llUserFaceAdd.setVisibility(View.VISIBLE);
        } else {
            llUserFaceAdd.setVisibility(View.GONE);
        }

//        if (permissionFunction != null && permissionFunction.isHasScan()) {
//            llUserScan.setVisibility(View.VISIBLE);
//        } else {
//            llUserScan.setVisibility(View.GONE);
//        }

    }

    private void initData() {
        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);

        permissionsChecker = new PermissionsChecker(this);
        userInfo = SettingManager.getInstance().getUserInfo();
        etPhone.setText(!StringUtil.isEmpty(userInfo.getUserTel()) ? userInfo.getUserTel() : "");
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_user_info_edit;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @OnClick(R.id.ll_avatar)
    public void avatarClicked() {
        selectDialog();
    }

    @OnClick(R.id.tvProtocol)
    void goProtocol() {
        if (!StringUtil.isEmpty(Constant.PROTOCOL_URL))
            WebActivity.launch(this, Constant.PROTOCOL_URL, "用户协议");
    }

    /**
     * 提交用户信息
     */
    @OnClick(R.id.iv_action)
    public void saveInfo() {
        if (!StringUtil.isEmpty(mPhotoHelper.getCropFilePath())) {
            try {
                uploadPhoto(mPhotoHelper.getCropFilePath());
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            presenter.uploadUserInfo(etPhone.getText().toString().trim(), userInfo.getUserPotoPath());
        }
    }

    @OnClick(R.id.ll_user_change)
    public void goLogin() {
        userInfo.setPassword("");
        userInfo.setFaceId("");
        SettingManager.getInstance().saveUserInfo(userInfo);
        EventBus.getDefault().post(new LogoutEvent(true));
        LoginActivity.launch(mContext, true);
        finish();
    }

    @OnClick(R.id.ll_user_faceAdd)
    void addUserFace() {
        if (!StringUtil.isEmpty(SettingManager.getInstance().getUserInfo().getFaceId())) {
            showToast("人脸已绑定");
            return;
        }
        if (permissionsChecker.lacksPermissions(PERMISSIONS)) {
            getPermissions(Constant.REQUEST_CODE_TAKE_POHO);
        } else {
            startActivityForResult(new Intent(this, FaceDetectExpActivity.class), REQUEST_CODE_FACE);
        }
    }

    // 扫码登录
    @OnClick(R.id.ll_user_scan)
    void userScan() {
        checkCameraPermissions();

    }

    /**
     * 检测拍摄权限
     */
    @AfterPermissionGranted(RC_CAMERA)
    private void checkCameraPermissions() {
        String[] perms = {Manifest.permission.CAMERA};
        if (EasyPermissions.hasPermissions(this, perms)) {//有权限
            startScan();
        } else {
            // Do not have permissions, request them now
            EasyPermissions.requestPermissions(this, getString(R.string.camera_permission_required),
                    RC_CAMERA, perms);
        }
    }

    /**
     * 扫码
     */
    private void startScan() {
        Intent intent = new Intent(this, CaptureActivity.class);
        startActivityForResult(intent, REQUEST_CODE_SCAN);
    }

    private void scanLoginWithCode(String code) {

        Map<String, Object> parameters = new HashMap<>();
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        parameters.put("code", code);
        parameters.put("type", "from");
        parameters.put("password", userInfo.getPassword());
        parameters.put("account", userInfo.getAccount());

        Api.getGbkApiserver().scanLoginWithCode("from", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {

                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }


    static final String[] PERMISSIONS = new String[]{
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA

    };
    private PermissionsChecker permissionsChecker;

    /**
     * 6.0之上权限
     */
    private void getPermissions(int code) {
        ActivityCompat.requestPermissions(this, PERMISSIONS, code);
    }

    public void selectDialog() {
        if (permissionsChecker.lacksPermissions(PERMISSIONS)) {
            getPermissions(Constant.REQUEST_CODE_TAKE_POHO);
        } else {
            String str[] = new String[]{"系统相机", "手机相册"};
            AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
            ab.setItems(str, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    switch (which) {
                        case 0://相机
                            takePhoto();
                            break;
                        case 1://相册
                            getPhoto();
                            break;
                    }
                }
            });
            ab.show();
        }
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), REQUEST_CODE_TAKE_PHOTO);
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    private void uploadPhoto(String photoPath) {
        showProgress();
        if (!StringUtil.isEmpty(photoPath)) {
            mPresenter.uploadPhotoImage(photoPath);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            if (requestCode == REQUEST_CODE_CHOOSE_PHOTO) {
                try {
                    LogUtil.e("REQUEST_CODE_CHOOSE_PHOTO " + mPhotoHelper.getFilePathFromUri(data.getData()));
                    startActivityForResult(mPhotoHelper.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData())
                            , 100, 100), REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            } else if (requestCode == REQUEST_CODE_TAKE_PHOTO) {
                try {
                    startActivityForResult(mPhotoHelper.getCropIntent(mPhotoHelper.getCameraFilePath(), 100, 100),
                            REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            } else if (requestCode == REQUEST_CODE_CROP) {
                LogUtil.e("REQUEST_CODE_CROP " + mPhotoHelper.getCropFilePath());
                ImageLoadUtil.loadIvatar(mContext, mPhotoHelper.getCropFilePath(), ivAvatar);
            } else if (requestCode == REQUEST_CODE_SCAN) {

                String code = data.getStringExtra(Intents.Scan.RESULT);
                if (code.contains("yuejianyun")) {
                    String replace = code.replace("yuejianyun:", "");
                    scanLoginWithCode(replace);
                } else {
                    showToast("请扫描悦监云登录二维码");
                }
                LogUtil.e("scandata: " + code);
            }
        } else if (requestCode == REQUEST_CODE_CROP) {
            mPhotoHelper.deleteCameraFile();
            mPhotoHelper.deleteCropFile();
        }
    }


    @Override
    public void uploadUserInfoBack(boolean success) {
        dismissProgress();
        if (success) {
            userInfo.setUserTel(etPhone.getText().toString().trim());
            SettingManager.getInstance().saveUserInfo(this.userInfo);
            showToast("提交成功");
            finish();
        }
    }

    @Override
    public void bindFaceToUserBack(boolean success) {
        if (success) {
            userInfo.setFaceId(mFaceId);
            SettingManager.getInstance().saveUserInfo(this.userInfo);
            showToast("绑定成功");
        } else {
            showToast("绑定失败");
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(PhotoUploadResulte resulte) {
        if (resulte.isIsok()) {
            // todo 图片上传成功
            userInfo.setUserPotoPath(resulte.getImageUrl());
            presenter.uploadUserInfo(etPhone.getText().toString().trim(), userInfo.getUserPotoPath());
        } else {
            userInfo.setUserPotoPath(resulte.getImageUrl());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void messageEvent(FaceImageEvent imageEvent) {
        LogUtil.e("FaceImageEvent");
        if (imageEvent != null) {
            LogUtil.e(imageEvent.getPath() + "  " + imageEvent.getBestImageBase64());
            showProgress();
            HuaweiService.getInstance().searchFaceUser(imageEvent.getBestImageBase64(),
                    new HuaweiService.FaceSearchListener() {
                        @Override
                        public void onSuccess(FaceSearch faceSearch) {
                            if (faceSearch == null || faceSearch.getFaces() == null || faceSearch.getFaces().size() == 0) {
                                huaweiAddUserFace(imageEvent.getBestImageBase64());
                                return;
                            }
                            FaceBean facesBean = faceSearch.getFaces().get(0);
                            if (facesBean.getSimilarity() > Constant.FACE_SIMILARITY) {
                                mFaceId = facesBean.getFace_id();
                                mPresenter.bindFaceToUser(mFaceId, imageEvent.getBestImageBase64());
                            } else {
                                huaweiAddUserFace(imageEvent.getBestImageBase64());
                            }
                            dismissProgress();
                        }

                        @Override
                        public void onFailure(Throwable throwable) {
                            LogUtil.e("searchFaceUser onError:" + throwable.getMessage());
                            showToast("人脸查询失败");
                            dismissProgress();
                        }
                    });
        }
    }

    private void huaweiAddUserFace(String imageBase64) {
        HuaweiService.getInstance().addUserFace(imageBase64, new HuaweiService.FaceAddListener() {
            @Override
            public void onSuccess(FaceAdd faceAdd) {
                if (StringUtil.isEmpty(faceAdd.getError_code())) {
                    List<FaceBean> faces = faceAdd.getFaces();
                    if (faces != null && faces.size() > 0) {
                        //lDOcuS1D
                        mFaceId = faces.get(0).getFace_id();
                        mPresenter.bindFaceToUser(mFaceId, imageBase64);
                    }
                } else {
                    showToast(faceAdd.getError_msg());
                }
                dismissProgress();
            }

            @Override
            public void onFailure(Throwable throwable) {
                showToast("人脸添加失败");
                dismissProgress();
            }
        });
    }

}
