package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.TenderDepositDetailContract;
import com.kisoft.yuejianli.entity.TenderDeposit;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.TenderDepositDetailModel;
import com.kisoft.yuejianli.presenter.TenderDepositDetailPresenter;
import com.kisoft.yuejianli.utils.StringUtil;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/7/6.
 */

public class TenderDepositDetailActivity extends BaseActivity<TenderDepositDetailModel ,TenderDepositDetailPresenter> implements
        TenderDepositDetailContract.TenderDepositDetailViewContract{

    @BindView(R.id.tv_title)
    TextView tvTitle;

    /** 保证金缴纳情况   */
    @BindView(R.id.tv_plan_name)
    TextView tvPlanName;

    @BindView(R.id.tv_company)
    TextView tvCompany;

    @BindView(R.id.tv_money)
    TextView tvAcceptMoney;

    @BindView(R.id.tv_accept_person)
    TextView tvAcceptPerson;

    @BindView(R.id.tv_accept_time)
    TextView tvAcceptTime;

    @BindView(R.id.tv_remark1)
    TextView tvRemark1;

    @BindView(R.id.iv_return_status)
    ImageView ivReturnStatus;

    @BindView(R.id.iv_see_return)
    ImageView ivSeeReturn;


    /** 保证金退回情况  */

    @BindView(R.id.ll_return_info)
    View returnView;

    @BindView(R.id.tv_return_money)
    TextView tvReturnMoney;

    @BindView(R.id.tv_return_person)
    TextView tvReturnPerson;

    @BindView(R.id.tv_return_time)
    TextView tvReturnTime;

    @BindView(R.id.tv_remark2)
    TextView tvRemark2;


    private TenderDeposit.DepositAccept depositAccept;
    private TenderDeposit.DepositReturn depositReturn;
    private boolean isOpen = false;
    private UserInfo userInfo;
    private TenderDepositDetailModel model;
    private TenderDepositDetailPresenter presenter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        model = new TenderDepositDetailModel(this);
        presenter = new TenderDepositDetailPresenter(this, model);
        initMVP(model, presenter);

        initData();
        initView();
    }

    private void initView(){
        tvTitle.setText("保函、保证金");
        if(depositAccept!= null){
            tvPlanName.setText(depositAccept.getPlanName());
            tvCompany.setText(depositAccept.getInvCompany());
            tvAcceptMoney.setText(depositAccept.getAccptAmount());
            tvAcceptPerson.setText(depositAccept.getUserName());
            tvAcceptTime.setText(depositAccept.getAccptDate());
            tvRemark1.setText(depositAccept.getRemark());
            if(depositAccept.isReTurn()){
                ivReturnStatus.setImageResource(R.drawable.ic_deposit_return);
                ivSeeReturn.setVisibility(View.VISIBLE);
            }else {
                ivReturnStatus.setImageResource(R.drawable.ic_deposit_un_return);
                ivSeeReturn.setVisibility(View.INVISIBLE);
            }
        }
    }

    private void initData(){
        userInfo = SettingManager.getInstance().getUserInfo();
        depositAccept = (TenderDeposit.DepositAccept) getIntent().getSerializableExtra(Constant.INTENT_KEY_DEPOSIT);
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_tender_deposit_detail;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    @OnClick(R.id.iv_see_return)
    public void seeReturn(){
        if(isOpen){
            isOpen = false;
            returnView.setVisibility(View.GONE);
        }else {
            isOpen = true;
            if(depositReturn!= null && !StringUtil.isEmpty(depositReturn.getIbrId())){
                returnView.setVisibility(View.VISIBLE);
            }else {
                if(depositAccept.isReTurn()  && !StringUtil.isEmpty(depositAccept.getIpId())){
                    returnView.setVisibility(View.VISIBLE);
                    mPresenter.getReturnInfo(userInfo.getId(), depositAccept.getIpId());
                }
            }

        }
    }

    @Override
    public void showReturnInfo(TenderDeposit.DepositReturn depositReturn) {
        this.depositReturn = depositReturn;
        tvReturnMoney.setText(this.depositReturn.getReturnAmount());
        tvReturnPerson.setText(this.depositReturn.getUserName());
        tvReturnTime.setText(this.depositReturn.getReturnDate());
        tvRemark2.setText(this.depositReturn.getRemark());
    }

    @OnClick(R.id.iv_return_status)
    public void addReturn(){
        Intent intent = new Intent();
        intent.setClass(this, TenderDepositReturnAddActivity.class);
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constant.INTENT_KEY_DEPOSIT, depositAccept);
        intent.putExtras(bundle);
        startActivity(intent);
        finish();
    }
}
