package com.kisoft.yuejianli.views;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.text.format.DateFormat;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.facebook.drawee.view.SimpleDraweeView;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.PunchCardContract;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.PunchCardInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.PunchCardModel;
import com.kisoft.yuejianli.presenter.PunchCardPresenter;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.ImageLoadUtil;
import com.kisoft.yuejianli.utils.PermissionsChecker;
import com.kisoft.yuejianli.utils.PhoneUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.net.URL;
import java.net.URLConnection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import butterknife.BindView;
import butterknife.BindViews;
import butterknife.OnClick;

/**
 * Created by tudou on 2018/3/19.
 */
public class PunchCardActivity extends BaseActivity<PunchCardModel, PunchCardPresenter> implements
        PunchCardContract.PunchCardViewContract, ActivityCompat.OnRequestPermissionsResultCallback {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.user_avatar)
    SimpleDraweeView ivAvatar;
    @BindView(R.id.tv_name)
    TextView tvName;
    @BindView(R.id.tv_date)
    TextView tvDate;

    @BindView(R.id.iv_action)
    ImageView ivAction;

    @BindViews({R.id.v_line1, R.id.v_line2, R.id.v_line3, R.id.v_line4})
    List<View> vLines;
    @BindViews({R.id.iv_point1, R.id.iv_point2, R.id.iv_point3, R.id.iv_point4})
    List<ImageView> ivPoints;
    @BindViews({R.id.tv_text1, R.id.tv_text2, R.id.tv_text3, R.id.tv_text4})
    List<TextView> tvTexts;
    @BindViews({R.id.ll_up, R.id.ll_up1, R.id.ll_down, R.id.ll_down1})
    List<View> llViews;
    @BindViews({R.id.tv_up_do, R.id.tv_up_do1, R.id.tv_down_do, R.id.tv_down_do1})
    List<TextView> tvViewTitles;
    @BindViews({R.id.tv_begin_time, R.id.tv_begin_time1, R.id.tv_end_time, R.id.tv_end_time1})
    List<TextView> tvTimes;
    @BindViews({R.id.tv_up_time, R.id.tv_up_time1, R.id.tv_down_time, R.id.tv_down_time1})
    List<TextView> tvViewTimes;

    private PunchCardModel model;
    private PunchCardPresenter mPresenter;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    private String today = DateUtil.getTodayDate();
    private PunchCardInfo punchCardInfo;

    private boolean isOk = true;

    /**
     * 排班方式  // 0:固定四次班制 1:自由打卡 2：固定两次班制
     * true 自由排班，false 固定排班
     */
    private String scheduleWay = "1";

    private String mAddress;
    private TimeThread timeThread;

    //Android各种访问权限
    //1.添加依赖'com.werb.permissionschecker:permissionschecker:0.0.1-beta2'

    //2.定义好需要检查的权限列表，定义PermissionChecker
//    static final String[] PERMISSIONS = new String[]{
//            Manifest.permission.ACCESS_FINE_LOCATION,
//            Manifest.permission.ACCESS_COARSE_LOCATION,
//            Manifest.permission.ACCESS_LOCATION_EXTRA_COMMANDS,
//            Manifest.permission.CHANGE_WIFI_STATE,
//
//    };


    static final String[] PERMISSIONSAmap = new String[]{
            Manifest.permission.ACCESS_COARSE_LOCATION,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.READ_PHONE_STATE

    };


    private PermissionsChecker permissionsChecker;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new PunchCardModel(this);

        //3.初始化
        mPresenter = new PunchCardPresenter(this, model);

        initMVP(model, mPresenter);
        initData();
        initView();
    }

    //4.处理我们的回调方法
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == Constant.REQUEST_CODE_PUCH_UPCARD) {
            //此处是我们获取权限后的业务逻辑
            takeUpCard();
        } else if (requestCode == Constant.REQUEST_CODE_PUCH_ENDCARD) {
            takeDownCard();
        }
    }

    private void initData() {
        permissionsChecker = new PermissionsChecker(this);
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();

        if (userInfo != null) {
            // 排班方式scheduleWay   自由排班 1   固定排班0（除固定排班0外其他都可视为自由排班）
            //scheduleWay = "1".equals(userInfo.getScheduleWay());
            if (userInfo.getScheduleWay() != null && !StringUtil.isEmpty(userInfo.getScheduleWay())) {
                scheduleWay = userInfo.getScheduleWay();
            }
            //加载用户信息
            initUserInfo();
            mPresenter.getTodayPunchCardInfo(userInfo.getId(), DateUtil.getTodayDate(), projectInfo.getProjectId());
            //排班方式view初始化
            initScheduleWay();

        } else {
            showToast("用户信息为空");
            finish();
        }

        timeThread = new TimeThread();
        timeThread.start();

//        getData();
    }

    private void initView() {
        tvTitle.setPadding(0, 0, PhoneUtil.dpTopx(this, 50), 0);
        if (SettingManager.getInstance().getProject() != null) {
            tvTitle.setText(SettingManager.getInstance().getProject().getProjectName());
        }
        ivAction.setVisibility(View.GONE);
        ivAction.setImageResource(R.drawable.ic_project_list);
        tvDate.setText(today);
        llViews.get(0).setBackground(getResources().getDrawable(R.drawable.take_card_bg_pressed));
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_punch_card;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        isOk = false;
        finish();
    }

    //项目选择
    @OnClick(R.id.iv_action)
    public void changeProject() {
        Intent intent = new Intent();
        intent.setClass(this, ProjectChoseActivity.class);
        startActivityForResult(intent, Constant.REQUEST_CODE_CHANGE_PEOJECT);
    }

    @OnClick(R.id.ll_up)
    public void takeUpCard() {
       /* if(new Date().getTime()!=getNetTime().getTime()){
            Toast.makeText(this,"当前时间和网络时间不一致，无法打卡",Toast.LENGTH_LONG).show();
            return;
        }*/
        if (permissionsChecker.lacksPermissions(PERMISSIONSAmap)) {   //缺少权限
            // todo 申请定位权限
            getPermissions(Constant.REQUEST_CODE_PUCH_UPCARD);
        } else {
            //PhoneUtil.getUniqueInstance().getLocation  获取当前位置信息
            mAddress = PhoneUtil.getUniqueInstance().getLocation(this);
            if (mAddress == null) {
                // todo 权限问题,申请权限
                getPermissions(Constant.REQUEST_CODE_PUCH_UPCARD);
            } else if (mAddress.isEmpty()) {
                showToast("请在设置中打开定位");
                // 跳转到设置界面
                Intent intent = new Intent(Settings.ACTION_SETTINGS);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intent);
            } else {
                // 定位成功
                if (projectInfo == null) {
                    showToast("您尚未加入任何项目");
                } else if (projectInfo != null && !StringUtil.isEmpty(mAddress)) {
                    Intent intent = new Intent(PunchCardActivity.this, PunchCardMapActivity.class);
//                    intent.putExtra("cardStatus", initViewStatus());
                    intent.putExtra("cardStatus", "1");
                    intent.putExtra("takeCard", "takeUpCard");
                    startActivityForResult(intent, 0);
                }
            }
        }
    }

    @OnClick(R.id.ll_up1)
    public void takeUpCard1() {
        /*if(new Date().getTime()!=getNetTime().getTime()){
            Toast.makeText(this,"当前时间和网络时间不一致，无法打卡",Toast.LENGTH_LONG).show();
            return;
        }*/
        if (permissionsChecker.lacksPermissions(PERMISSIONSAmap)) {   //缺少权限
            getPermissions(Constant.REQUEST_CODE_PUCH_UPCARD);
        } else {
            mAddress = PhoneUtil.getUniqueInstance().getLocation(this);
            if (mAddress == null) {
                // todo 权限问题,申请权限
                getPermissions(Constant.REQUEST_CODE_PUCH_UPCARD);
            } else if (mAddress.isEmpty()) {
                showToast("请在设置中打开定位");
                Intent intent = new Intent(Settings.ACTION_SETTINGS);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intent);
            } else {
                // 定位成功
                if (projectInfo == null) {
                    showToast("您尚未加入任何项目");
                } else if (projectInfo != null && !StringUtil.isEmpty(mAddress)) {
                    Intent intent = new Intent(PunchCardActivity.this, PunchCardMapActivity.class);
//                    intent.putExtra("cardStatus", initViewStatus());
                    intent.putExtra("cardStatus", "1");
                    intent.putExtra("takeCard", "takeUpCard1");
                    startActivityForResult(intent, 0);
                }
            }
        }
    }


    @OnClick(R.id.ll_down)
    public void takeDownCard() {
       /* Log.d("date==0===",""+(new Date()).getTime());
        Log.d("date==1===",""+getNetTime().getTime());
        if((new Date()).getTime()!=getNetTime().getTime()){
            Toast.makeText(this,"当前时间和网络时间不一致，无法打卡",Toast.LENGTH_LONG).show();
            return;
        }*/
        if (permissionsChecker.lacksPermissions(PERMISSIONSAmap)) {   //缺少权限
            getPermissions(Constant.REQUEST_CODE_PUCH_ENDCARD);
        } else {
            mAddress = PhoneUtil.getUniqueInstance().getLocation(this);
            if (mAddress == null) {
                // todo 权限问题,申请权限
                getPermissions(Constant.REQUEST_CODE_PUCH_ENDCARD);
            } else if (mAddress.isEmpty()) {
                showToast("请在设置中打开定位");
                Intent intent = new Intent(Settings.ACTION_SETTINGS);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intent);
            } else {
                // 定位成功
                if (projectInfo == null) {
                    showToast("您尚未加入任何项目");
                } else if (projectInfo != null && !StringUtil.isEmpty(mAddress)) {
                    Intent intent = new Intent(PunchCardActivity.this, PunchCardMapActivity.class);
//                    intent.putExtra("cardStatus", initViewStatus());
                    intent.putExtra("cardStatus", "1");
                    intent.putExtra("takeCard", "takeDownCard");
                    startActivityForResult(intent, 0);
                }
            }
        }
    }

    @OnClick(R.id.ll_down1)
    public void takeDownCard1() {
       /* if(new Date().getTime()!=getNetTime().getTime()){
            Toast.makeText(this,"当前时间和网络时间不一致，无法打卡",Toast.LENGTH_LONG).show();
            return;
        }*/
        if (permissionsChecker.lacksPermissions(PERMISSIONSAmap)) {   //缺少权限
            getPermissions(Constant.REQUEST_CODE_PUCH_ENDCARD);
        } else {
            mAddress = PhoneUtil.getUniqueInstance().getLocation(this);
            if (mAddress == null) {
                // todo 权限问题,申请权限
                getPermissions(Constant.REQUEST_CODE_PUCH_ENDCARD);
            } else if (mAddress.isEmpty()) {
                showToast("请在设置中打开定位");
                Intent intent = new Intent(Settings.ACTION_SETTINGS);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intent);
            } else {
                // 定位成功
                if (projectInfo == null) {
                    showToast("您尚未加入任何项目");
                } else if (!StringUtil.isEmpty(mAddress)) {

                    Intent intent = new Intent(PunchCardActivity.this, PunchCardMapActivity.class);
//                    Intent intent = new Intent(PunchCardActivity.this, PunchCardMapNewActivity.class);
//                    intent.putExtra("cardStatus", initViewStatus());
                    intent.putExtra("cardStatus", "1");
                    intent.putExtra("takeCard", "takeDownCard1");
                    startActivityForResult(intent, 0);
                }
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 0) {
            if (resultCode == RESULT_OK) {
                /**返回的备注**/
                String remarks = data.getStringExtra("remarks");
                /**签到时间**/
                long punchCardTime = data.getLongExtra("punchCardTime", System.currentTimeMillis());
                /**签到位置**/
                String myLatLng = data.getStringExtra("myLatLng");
                /**上班打卡  or  下班打卡
                 * takeUpCard   or  takeDownCard
                 **/
                String takeCard = data.getStringExtra("takeCard");
                /**
                 * 打卡状态
                 * 1：正常  2：迟到    3：早退    4：旷工 5:请假  6：外勤  7：补卡
                 */
                int punchCardState = data.getIntExtra("punchCardState", 0);
                punchCardInfo = new PunchCardInfo();
                punchCardInfo.setUserId(userInfo.getId());
                punchCardInfo.setUserName(userInfo.getName());
                if (takeCard.equals("takeUpCard")) {   //上午上班打卡
                    //打卡地点 1
                    punchCardInfo.setPunchPoint1(myLatLng);
                    //打卡时间 1
                    punchCardInfo.setPunchTime1(DateUtil.YMD_HMS.format(punchCardTime));
                    punchCardInfo.setRemark1(remarks);
                } else if (takeCard.equals("takeUpCard1")) {   //上午下班打卡
                    punchCardInfo.setPunchPoint2(myLatLng);
                    punchCardInfo.setPunchTime2(DateUtil.YMD_HMS.format(punchCardTime));
                    punchCardInfo.setRemark2(remarks);
                } else if (takeCard.equals("takeDownCard")) {   //下午上班打卡
                    punchCardInfo.setPunchPoint3(myLatLng);
                    punchCardInfo.setPunchTime3(DateUtil.YMD_HMS.format(punchCardTime));
                    punchCardInfo.setRemark3(remarks);
                } else if (takeCard.equals("takeDownCard1")) {   //下午下班打卡
                    punchCardInfo.setPunchPoint4(myLatLng);
                    punchCardInfo.setPunchTime4(DateUtil.YMD_HMS.format(punchCardTime));
                    punchCardInfo.setRemark4(remarks);
                }

                // State 打卡状态 1：正常  2：迟到    3：早退    4：旷工 5:请假  6：外勤  7：补卡
                punchCardInfo.setState(punchCardState + "");
                //punchCardInfo.setRemarks(remarks);
                punchCardInfo.setProjectName(projectInfo.getProjectName());
                punchCardInfo.setProjectId(projectInfo.getProjectId());

                //punchCardInfo.setCreateTime(sdf.format(new Date()));
                //打卡
                puchCard();
            }
        }

        //设置项目
        if (resultCode == Constant.REQUEST_CODE_CHANGE_PEOJECT) {
            initData();
            initView();
        }
    }

    private void puchCard() {
        mPresenter.punchStartCard(userInfo.getId(), projectInfo.getProjectId(), punchCardInfo);
    }

    private void initUserInfo() {
        //加载用户头像和名字
        ImageLoadUtil.loadIvatar(mContext, userInfo.getUserPotoPath(), ivAvatar);
        tvName.setText(userInfo.getName());
    }

    /**
     * 排班方式view初始化
     * // 0:固定四次班制 1:自由打卡 2：固定两次班制
     */
    private void initScheduleWay() {
        Log.d("打卡模式1", scheduleWay + "");

        if (scheduleWay.equals("0")) { //固定
            //beginTimeHou 上午上班时间小时    beginTimeMin上午上班时间分钟
            hideIndex(0);
            hideIndex(1);
            hideIndex(2);
            hideIndex(3);

//            if (judgeTimeLessEndTime()) {
//                // 小于缺勤时间，正常打卡
//            } else {
//                // 大于缺勤时间，禁止打卡
//                tvViewTitles.get(0).setText("上班打卡");
//                tvViewTimes.get(0).setText("缺勤");
//
//                tvViewTitles.get(1).setText("下班打卡");
//                tvViewTimes.get(1).setText("缺勤");
//            }

            tvTimes.get(0).setText(userInfo.getBeginTimeHou() + ":" + userInfo.getBeginTimeMin());
            tvTimes.get(1).setText(userInfo.getEndTimeHou() + ":" + userInfo.getEndTimeMin());
            tvTimes.get(2).setText(userInfo.getBeginTimeHou1() + ":" + userInfo.getBeginTimeMin1());
            tvTimes.get(3).setText(userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());


        } else if (scheduleWay.equals("1")) {  //自由

            tvTexts.get(0).setText("上班时间");
            tvTexts.get(3).setText("下班时间");
            tvTimes.get(0).setVisibility(View.GONE);
            tvTimes.get(1).setVisibility(View.GONE);
            tvTimes.get(2).setVisibility(View.GONE);
            tvTimes.get(3).setVisibility(View.GONE);
            tvViewTitles.get(0).setText("上班打卡");
            tvViewTimes.get(0).setText("上班打卡");
            tvViewTitles.get(3).setText("下班打卡");
            tvViewTimes.get(3).setText("下班打卡");

            hideIndex(1);
            hideIndex(2);
        } else if (scheduleWay.equals("2")) {  //固定两次打卡

            tvTexts.get(0).setText("上班时间");
            tvTexts.get(3).setText("下班时间");
            tvTimes.get(0).setText(userInfo.getBeginTimeHou() + ":" + userInfo.getBeginTimeMin());
            tvTimes.get(3).setText(userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());

//            tvTimes.get(0).setVisibility(View.VISIBLE);
//            tvTimes.get(1).setVisibility(View.GONE);
//            tvTimes.get(2).setVisibility(View.GONE);
//            tvTimes.get(3).setVisibility(View.VISIBLE);

//            if (judgeTimeLessEndTime()){
//                // 小于缺勤时间，正常打卡
//                tvViewTitles.get(0).setText("上班打卡");
//                tvViewTimes.get(0).setText(userInfo.getBeginTimeHou() + ":" + userInfo.getBeginTimeMin());
//            }else {
//                // 大于缺勤时间，禁止打卡
//                tvViewTitles.get(0).setText("上班打卡");
//                tvViewTimes.get(0).setText("缺勤");
//            }
//            tvViewTitles.get(0).setText("上班打卡");
//            tvViewTimes.get(0).setText("上班打卡");
            tvViewTitles.get(0).setText("上班打卡");
            tvViewTimes.get(0).setText(userInfo.getBeginTimeHou() + ":" + userInfo.getBeginTimeMin());
            tvViewTitles.get(3).setText("下班打卡");
            tvViewTimes.get(3).setText(userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());

            hideIndex(1);
            hideIndex(2);
        }
        /*

        if (scheduleWay.equals("1")) { //自由
            tvTexts.get(0).setText("上班时间");
            tvTexts.get(3).setText("下班时间");
            tvTimes.get(0).setVisibility(View.GONE);
            tvTimes.get(1).setVisibility(View.GONE);
            tvTimes.get(2).setVisibility(View.GONE);
            tvTimes.get(3).setVisibility(View.GONE);
            tvViewTitles.get(0).setText("上班打卡");
            tvViewTimes.get(0).setText("上班打卡");
            tvViewTitles.get(3).setText("下班打卡");
            tvViewTimes.get(3).setText("下班打卡");

            hideIndex(1);
            hideIndex(2);
        } else if (scheduleWay.equals("0")){  //固定
            //beginTimeHou 上午上班时间小时    beginTimeMin上午上班时间分钟
            hideIndex(0);
            hideIndex(1);
            hideIndex(2);
            hideIndex(3);
            tvTimes.get(0).setText(userInfo.getBeginTimeHou() + ":" + userInfo.getBeginTimeMin());
            tvTimes.get(1).setText(userInfo.getEndTimeHou() + ":" + userInfo.getEndTimeMin());
            tvTimes.get(2).setText(userInfo.getBeginTimeHou1() + ":" + userInfo.getBeginTimeMin1());
            tvTimes.get(3).setText(userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());
        }
        */

    }

    @Override
    public void initPunchCardStatus(PunchCardInfo info) {
        this.punchCardInfo = info;
        Log.d("打卡模式2", scheduleWay + "");
        // 0:固定四次班制 1:自由打卡 2：固定两次班制
        if (scheduleWay.equals("0")) { // 固定排班
            if (info != null) {
                //String punchTime4   打卡时间 4
                if (!StringUtil.isEmpty(info.getPunchTime4())) {
                    initViewStatus(4);
                } else if (!StringUtil.isEmpty(info.getPunchTime3())) {
                    initViewStatus(4);
                } else if (!StringUtil.isEmpty(info.getPunchTime2())) {
                    initViewStatus(3);
                } else if (!StringUtil.isEmpty(info.getPunchTime1())) {
                    initViewStatus(2);
                } else {
                    initViewStatus(1);
                }
            } else {
                // 没有打卡时，判断是否有缺勤
                if (judgeTimeLessEndTime()) {
                    // 小于缺勤时间，正常打卡
                    initViewStatus(1);
                } else {
                    // 大于缺勤时间，禁止打卡
                    initViewStatus(3);
                }
            }
        } else if (scheduleWay.equals("1")) { //1:自由打卡
            if (info != null) {
                if (!StringUtil.isEmpty(info.getPunchTime4())) {
                    initViewStatus(4);
                } else if (!StringUtil.isEmpty(info.getPunchTime1())) {
                    initViewStatus(4);
                } else {
                    initViewStatus(1);
                }
            } else {
                initViewStatus(1);
            }
        } else if (scheduleWay.equals("2")) { //2：固定两次班制
            if (info != null) {
                if (!StringUtil.isEmpty(info.getPunchTime4())) {
                    initViewStatus(4);
                } else if (!StringUtil.isEmpty(info.getPunchTime1())) {
                    initViewStatus(4);
                } else {
                    initViewStatus(1);
                }
            } else {
                // 没有打卡时，判断是否有缺勤
                if (judgeTimeLessEndTime()) {
                    // 小于缺勤时间，正常打卡
                    initViewStatus(1);
                } else {
                    // 大于缺勤时间，禁止打卡
                    initViewStatus(4);
                }
            }
        }

        /*
        if (scheduleWay == false) { // 固定排班
            if (info != null) {
                //String punchTime4   打卡时间 4
                if (!StringUtil.isEmpty(info.getPunchTime4())) {
                    initViewStatus(4);
                } else if (!StringUtil.isEmpty(info.getPunchTime3())) {
                    initViewStatus(4);
                } else if (!StringUtil.isEmpty(info.getPunchTime2())) {
                    initViewStatus(3);
                } else if (!StringUtil.isEmpty(info.getPunchTime1())) {
                    initViewStatus(2);
                } else {
                    initViewStatus(1);
                }
            } else {
                initViewStatus(1);
//                private String beginTimeHou;//上午上班时间小时
//                private String beginTimeMin//上午上班时间分钟
//                private String endTimeHou;//上午下班时间小时
//                private String endTimeMin; //上午下班时间分钟

                //比较获取的下班时间是否为null
//                if (StringUtil.isEmpty(SettingManager.getInstance().getUserInfo().getEndTimeHou()) ||
//                        StringUtil.isEmpty(SettingManager.getInstance().getUserInfo().getEndTimeMin())) {
//                    initViewStatus(1);
//
//                } else if (StringUtil.isEmpty(SettingManager.getInstance().getUserInfo().getEndTimeHou1()) ||
//                        StringUtil.isEmpty(SettingManager.getInstance().getUserInfo().getEndTimeMin1())) {
//
//                    try {
//                        // 当前时间
//                        Date curDate = new Date();
//                        //上午下班时间
//                        Date amDownDate = DateUtil.HMS.parse(SettingManager.getInstance().getUserInfo()
//                        .getEndTimeHou() + ":" + SettingManager.getInstance().getUserInfo().getEndTimeMin());
//                        if (curDate.getHours() > amDownDate.getHours() ||
//                                (curDate.getHours() == amDownDate.getHours() && curDate.getMinutes() >= amDownDate
//                                .getMinutes())) {
//                            initViewStatus(3);
//                        } else {
//                            initViewStatus(1);
//                        }
//                    } catch (ParseException e) {
//                        e.printStackTrace();
//                    }
//                } else {
//                    try {
//                        // 当前时间
//                        Date curDate = new Date();
//                        //上午下班时间
//                        Date amDownDate = DateUtil.HMS.parse(SettingManager.getInstance().getUserInfo()
//                        .getEndTimeHou() + ":" + SettingManager.getInstance().getUserInfo().getEndTimeMin());
//                        //下午下班时间
//                        Date pmDownDate = DateUtil.HMS.parse(SettingManager.getInstance().getUserInfo()
//                        .getEndTimeHou1() + ":" + SettingManager.getInstance().getUserInfo().getEndTimeMin1());
//                        Log.d("=时间上午下班时间==",SettingManager.getInstance().getUserInfo().getEndTimeHou() + ":" +
//                        SettingManager.getInstance().getUserInfo().getEndTimeMin());
//                        Log.d("=时间下午下班时间==",SettingManager.getInstance().getUserInfo().getEndTimeHou1() + ":" +
//                        SettingManager.getInstance().getUserInfo().getEndTimeMin1());
//                        Log.d("=时间当前时间小时==",curDate.getHours()+"");
//                        Log.d("=时间下午下班时间小时==",pmDownDate.getHours()+"");
//                        Log.d("=时间当前时间分钟==",curDate.getMinutes()+"");
//                        Log.d("=时间下午下班时间分钟==",pmDownDate.getMinutes()+"");
//                        Log.d("=时间上午下班时间小时==",amDownDate.getHours()+"");
//                        Log.d("=时间上午下班时间分==",amDownDate.getMinutes()+"");
//
//                        if (curDate.getHours() > pmDownDate.getHours() ||
//                                (curDate.getHours() == pmDownDate.getHours() && curDate.getMinutes() >= pmDownDate
//                                .getMinutes())) {
//                            initViewStatus(1);
//                        }else if (curDate.getHours() > amDownDate.getHours() ||
//                                (curDate.getHours() == amDownDate.getHours() && curDate.getMinutes() >= amDownDate
//                                .getMinutes())) {
//
//                            initViewStatus(3);
//                        } else {
//                            initViewStatus(1);
//                        }
//                    } catch (ParseException e) {
//                        e.printStackTrace();
//                    }
//                }
            }
        } else {
            if (info != null) {
                if (!StringUtil.isEmpty(info.getPunchTime4())) {
                    initViewStatus(4);
                } else if (!StringUtil.isEmpty(info.getPunchTime1())) {
                    initViewStatus(4);
                } else {
                    initViewStatus(1);
                }
            } else {
                initViewStatus(1);
            }
        }
        */
    }

    int currIndex;

    private void initViewStatus(int currCanClickedItemIndex) {
        hideIndexMore(currCanClickedItemIndex);
        for (int i = 0; i < llViews.size(); i++) {
            llViews.get(i).setEnabled(true);
            if (i + 1 < currCanClickedItemIndex) {
                showIndex(i, false);
                initViewTime(i);
            } else if (i + 1 == currCanClickedItemIndex) {
                showIndex(i, true);
                initViewTime(i);
            } else {
                hideIndex(i);
            }
        }
    }

    /**
     * 显示下标index的打卡条目
     * <p>
     * 排班方式  // 0:固定四次班制 1:自由打卡 2：固定两次班制
     * true 自由排班，false 固定排班
     *
     * @param index
     * @param isCurrIndex 是否为当前条目
     */
    private void showIndex(int index, boolean isCurrIndex) {
        /*
        if (scheduleWay.equals("0")){//0:固定四次班制
            if (isCurrIndex) currIndex = index;
            ivPoints.get(index).setVisibility(View.VISIBLE);
            ivPoints.get(index).setImageDrawable(mContext.getResources().getDrawable(isCurrIndex ?
                    R.drawable.ic_point_fill : R.drawable.ic_point_unfill));
            vLines.get(index).setVisibility(isCurrIndex ? View.INVISIBLE : View.VISIBLE);
            tvTexts.get(index).setVisibility(View.VISIBLE);
            tvTimes.get(index).setVisibility(View.GONE);
            llViews.get(index).setVisibility(View.VISIBLE);
            llViews.get(index).setBackground(mContext.getResources().getDrawable(isCurrIndex ?
                    R.drawable.take_card_bg_normal : R.drawable.take_card_bg_pressed));
            llViews.get(index).setEnabled(isCurrIndex);

        }

        if (scheduleWay.equals("1")){//1:自由打卡
            if (index != 1 || index != 2) {
                tvTimes.get(index).setVisibility(View.VISIBLE);
            }
        }

        if (scheduleWay.equals("2")){//2：固定两次班制
            if (index != 1 || index != 2) {
                tvTimes.get(index).setVisibility(View.VISIBLE);
            }
        }
*/

        //scheduleWay = "1".equals(userInfo.getScheduleWay());
        boolean scheduleWayBool = !"0".equals(userInfo.getScheduleWay());
        if (scheduleWayBool && (index == 1 || index == 2)) {
            return;
        }
        if (isCurrIndex) currIndex = index;
        ivPoints.get(index).setVisibility(View.VISIBLE);
        ivPoints.get(index).setImageDrawable(mContext.getResources().getDrawable(isCurrIndex ?
                R.drawable.ic_point_fill : R.drawable.ic_point_unfill));
        vLines.get(index).setVisibility(isCurrIndex ? View.INVISIBLE : View.VISIBLE);
        tvTexts.get(index).setVisibility(View.VISIBLE);
        tvTimes.get(index).setVisibility(scheduleWayBool ? View.GONE : View.VISIBLE);
        llViews.get(index).setVisibility(View.VISIBLE);
        llViews.get(index).setBackground(mContext.getResources().getDrawable(isCurrIndex ?
                R.drawable.take_card_bg_normal : R.drawable.take_card_bg_pressed));
        llViews.get(index).setEnabled(isCurrIndex);


        /*
        if (scheduleWay && (index == 1 || index == 2)) {
            return;
        }
        if (isCurrIndex) currIndex = index;
        ivPoints.get(index).setVisibility(View.VISIBLE);
        ivPoints.get(index).setImageDrawable(mContext.getResources().getDrawable(isCurrIndex ?
                R.drawable.ic_point_fill : R.drawable.ic_point_unfill));
        vLines.get(index).setVisibility(isCurrIndex ? View.INVISIBLE : View.VISIBLE);
        tvTexts.get(index).setVisibility(View.VISIBLE);
        tvTimes.get(index).setVisibility(scheduleWay ? View.GONE : View.VISIBLE);
        llViews.get(index).setVisibility(View.VISIBLE);
        llViews.get(index).setBackground(mContext.getResources().getDrawable(isCurrIndex ?
                R.drawable.take_card_bg_normal : R.drawable.take_card_bg_pressed));
        llViews.get(index).setEnabled(isCurrIndex);
        */
    }

    /**
     * 隐藏下标index的打卡条目
     *
     * @param index
     */
    private void hideIndex(int index) {
        ivPoints.get(index).setVisibility(View.GONE);
        vLines.get(index).setVisibility(View.GONE);
        tvTexts.get(index).setVisibility(View.GONE);
        tvTimes.get(index).setVisibility(View.GONE);
        llViews.get(index).setVisibility(View.GONE);
    }

    /**
     * 隐藏下标为index以后的所有打卡条目
     *
     * @param index
     */
    private void hideIndexMore(int index) {
        for (int i = index + 1; i < llViews.size(); i++) {
            ivPoints.get(i).setVisibility(View.GONE);
            vLines.get(i).setVisibility(View.GONE);

            tvTexts.get(i).setVisibility(View.GONE);
            tvTimes.get(i).setVisibility(View.GONE);
            llViews.get(i).setVisibility(View.GONE);
        }
    }

    /**
     * 固定排班，显示所有打卡条目,全部为未打卡
     */
//    private void showAllItem() {
//        timeThread.interrupt();
//        for (int i = 0; i < llViews.size(); i++) {
//            ivPoints.get(i).setVisibility(View.VISIBLE);
//            ivPoints.get(i).setImageDrawable(mContext.getResources().getDrawable(R.drawable.ic_point_unfill));
//            vLines.get(i).setVisibility(View.VISIBLE);
//
//            tvTexts.get(i).setVisibility(View.VISIBLE);
//            tvViewTimes.get(i).setText("未打卡");
//            tvTimes.get(i).setVisibility(View.VISIBLE);
//            llViews.get(i).setVisibility(View.VISIBLE);
//            llViews.get(i).setBackground(mContext.getResources().getDrawable(R.drawable.take_card_bg_pressed));
//            llViews.get(i).setEnabled(false);
//        }
//    }
    public void initViewTime(int i) {
//        Toast.makeText(this,i+"",Toast.LENGTH_LONG).show();
        switch (i) {
            case 0:
                if (punchCardInfo == null) {
                    // 未打卡
                    if (judgeTimeLessEndTime()) {
                        // 小于缺勤时间，正常打卡
                    } else {
                        // 大于缺勤时间，禁止打卡
                        tvViewTitles.get(i).setText("上班打卡");
                        tvViewTimes.get(i).setText("缺勤");

                        tvViewTitles.get(1).setText("下班打卡");
                        tvViewTimes.get(1).setText("缺勤");
                    }
                } else {
                    if (StringUtil.isEmpty(punchCardInfo.getPunchTime1()) == false) {
                        tvTexts.get(i).setText("上午上班时间：" + today + " " + DateUtil.YmdToHmTime(punchCardInfo.getPunchTime1()));
                        tvViewTimes.get(i).setText("已打卡");
                    } else {
                        tvViewTitles.get(i).setText("上班打卡");
                        tvViewTimes.get(i).setText("缺勤");
                    }
                }
                break;
            case 1:
                if (punchCardInfo == null) {
                } else {
                    if (StringUtil.isEmpty(punchCardInfo.getPunchTime2()) == false) {
                        tvTexts.get(i).setText("上午下班时间：" + today + " " + DateUtil.YmdToHmTime(punchCardInfo.getPunchTime2()));
                        tvViewTimes.get(i).setText("已打卡");
                    } else {
                        tvViewTitles.get(i).setText("下班打卡");
                        tvViewTimes.get(i).setText("缺勤");
                    }
                }
                break;
            case 2:
                if (punchCardInfo == null) {
                } else {
                    if (StringUtil.isEmpty(punchCardInfo.getPunchTime3()) == false) {
                        tvTexts.get(i).setText("下午上班时间：" + today + " " + DateUtil.YmdToHmTime(punchCardInfo.getPunchTime3()));
                        tvViewTimes.get(i).setText("已打卡");
                    } else {
                        if (scheduleWay.equals("2")) {
                            tvViewTitles.get(i).setText("上班打卡");
                            tvViewTimes.get(i).setText("缺勤");
                        }
                    }
                }
                break;
            case 3:
                if (punchCardInfo == null) {
                } else if (StringUtil.isEmpty(punchCardInfo.getPunchTime4()) == false) {
                    tvTexts.get(i).setText("下午下班时间：" + today + " " + DateUtil.YmdToHmTime(punchCardInfo.getPunchTime4()));
                }
                break;
        }
    }

    class TimeThread extends Thread {
        @Override
        public void run() {
            do {
                try {
                    Thread.sleep(999);
                    Message msg = new Message();
                    msg.what = 1;  //消息(一个整型值)
                    mHandler.sendMessage(msg);// 每隔1秒发送一个msg给mHandler
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } while (isOk);
        }
    }

    //在主线程里面处理消息并更新UI界面
    // 0:固定四次班制 1:自由打卡 2：固定两次班制
    private Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case 1:
                    long sysTime = System.currentTimeMillis();
                    CharSequence sysTimeStr = DateFormat.format("HH:mm", sysTime);
                    // todo 根据当前状态，设置时钟现实的位置
                    if (currIndex == 4) {
                        currIndex = 3;
                    }

                    tvViewTimes.get(currIndex).setText(sysTimeStr);
                    /*
                    if (judgeTimeLessEndTime()){
                        // 小于缺勤时间，正常打卡
                    }else {
                        if (scheduleWay.equals("0")){
                            // 大于缺勤时间，禁止打卡
                            tvViewTitles.get(0).setText("上班打卡");
                            tvViewTimes.get(0).setText("缺勤");
                            llViews.get(0).setBackground(mContext.getResources().getDrawable(R.drawable
                            .take_card_bg_pressed));
                            llViews.get(0).setEnabled(false);

                            tvViewTitles.get(1).setText("下班打卡");
                            tvViewTimes.get(1).setText("缺勤");
                            llViews.get(1).setBackground(mContext.getResources().getDrawable(R.drawable
                            .take_card_bg_pressed));
                            llViews.get(1).setEnabled(false);
                        }
                        if (scheduleWay.equals("2")){
                            // 大于缺勤时间，禁止打卡
                            tvViewTitles.get(0).setText("上班打卡");
                            tvViewTimes.get(0).setText("缺勤");
                            llViews.get(0).setBackground(mContext.getResources().getDrawable(R.drawable
                            .take_card_bg_pressed));
                            llViews.get(0).setEnabled(false);
                        }

                    }
                    */

                    break;

                default:
                    break;
            }
        }
    };

    /**
     * 6.0之上权限申请
     */
    private void getPermissions(int code) {
        ActivityCompat.requestPermissions(this, PERMISSIONSAmap, code);
    }

    private Date getNetTime() {
        URL url = null;//取得资源对象
        Date date = null;
        try {
            url = new URL("http://www.baidu.com");
            //url = new URL("http://www.ntsc.ac.cn");//中国科学院国家授时中心
            //url = new URL("http://www.bjtime.cn");
            URLConnection uc = url.openConnection();//生成连接对象
            uc.connect(); //发出连接
            long ld = uc.getDate(); //取得网站日期时间
            java.text.DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(ld);
            final String format = formatter.format(calendar.getTime());
            date = calendar.getTime();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return date;
    }

    // 比较时间[是否小于缺勤时间点]
    private boolean judgeTimeLessEndTime() {

        String endTime = "";
        // 0:固定四次班制 1:自由打卡 2：固定两次班制
        if (scheduleWay.equals("0")) {
            endTime = userInfo.getEndTimeHou() + ":" + userInfo.getEndTimeMin();
        }

        if (scheduleWay.equals("2")) {
            endTime = userInfo.getCriticalTime();
            if (StringUtil.isEmpty(endTime)) {
                return true;
            }
        }

        String startTime = DateUtil.dateToString(new Date(), DateUtil.HMS);
        SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm");//年-月-日 时-分
        try {
            Date startDate = dateFormat.parse(startTime);//开始时间
            Date endDate = dateFormat.parse(endTime);//结束时间
            if (startDate.getTime() < endDate.getTime()) {
                //小于缺勤时间
                return true;
            } else {
                return false;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return true;
    }

    /*
    //获取详情
    private void getData(){
        Map<String ,Object> paramars = new HashMap<>();
        paramars.put("userId", userInfo.getId());
        paramars.put("nowTime" ,DateUtil.getTodayDate());
        paramars.put("projectId",projectInfo.getProjectId());
        Api.getGbkApiserver().testApi1("viewAttendanceSheet", null).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
//                mView.dismissProgress();
                Log.i("TAG", "onResponse: " + response.body().getData());
                if(response.body()!= null && response.body().getCode() == NetworkResponse.OK){
                    Map data = (Map) response.body().getData();
//
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
    */
}
