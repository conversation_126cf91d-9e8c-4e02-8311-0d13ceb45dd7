package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse2;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.GsonUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * Created by tudou on 2018/6/22.
 */
public class ProjectEnvPLanListActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    private View empotyView;
    private ProjectPreparePlanListAdapter mAdapter;
    private List<Map<String, Object>> mList = new ArrayList<>();
    private String bType = "";
    private String url = "";

    private int page = 1;
    private int pageSize = 20;
    private int total = 0;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getIntent() != null) {
            bType = getIntent().getStringExtra("ProjectPrepareInfoNewActivityData");
        }
        initView();
        initData();
    }

    private void initView() {
        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new ProjectPreparePlanListAdapter(mList);
        mAdapter.setEmptyView(empotyView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                goDetail(mList.get(position));
            }
        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                rvContent.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        page++;
                        if(mList.size() >= total){
                            mAdapter.loadMoreEnd();
                        }else {
                            getData();
                        }
                    }
                }, 1000);
            }
        }, rvContent);
        rvContent.setAdapter(mAdapter);
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();

        if (StringUtil.isEqual(bType, "T_PDS_ENVIRONMENTAL_DETAIL")) {
            // 环保细则,
            tvTitle.setText("环保细则");
            url = "pds/PdsEnvironmentalDetailAction.do?method=getPdsEnvironmentalDetailList";
        } else if (StringUtil.isEqual(bType, "T_PDS_ENVIRONMENTAL_PLAN")) {
            // 环保计划,
            tvTitle.setText("环保计划");
            url = "pds/PdsEnvironmentalPlanAction.do?method=getPdsEnvironmentalPlanList";
        } else if (StringUtil.isEqual(bType, "T_PDS_SPECIAL_SUP_PLAN")) {
            // 监理专项方案,
            tvTitle.setText("监理专项方案");
            url = "pds/PdsSpecialSupPlanAction.do?method=getPdsSpecialSupPlanList";
        } else if (StringUtil.isEqual(bType, "T_PDS_DANGEROUS_PROJECT_PLAN")) {
            // 危大工程方案,
            tvTitle.setText("危大工程方案");
            url = "pds/PdsDangerousProjectPlanAction.do?method=getPdsDangerousProjectPlanList";
        }

        getData();
    }

    private void getData() {
        String p = Integer.toString(page);
        String z = Integer.toString(pageSize);
        Map<String, Object> pram = new HashMap<>();
        pram.put("projectId", projectInfo.getProjectId());
        pram.put("page", p);
        pram.put("limit", z);
        url = SettingManager.getInstance().getBaseUrl() + url;
        OkHttpRequestManager.getInstance().post(url, pram, new IRequestCallback() {
            @Override
            public void onSuccess(String response) {
                Log.i("TAG", "onSuccess: " + response);
//                Map<String, Object> map = GsonUtil.GsonToMaps(response);
//                Map<String, Object> map1 = (Map<String, Object>) map.get("data");
//                mList = (List<Map<String, Object>>) map1.get("data");
                NetworkResponse2 response2 = GsonUtil.GsonToBean(response, NetworkResponse2.class);
                NetworkResponse2.NetworkResponse2Data data = response2.getData();
                total = data.getTotal();
                if (page == 1) {
                    mList = data.getData();
                } else {
                    mList.addAll(data.getData());
                }
                mAdapter.setNewData(mList);
            }
            @Override
            public void onFailure(Throwable throwable) {
             throwable.printStackTrace();
            }
        });
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_project_sup_plan_list;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    private void goDetail(Map<String,Object> map) {
        Intent intent = new Intent();
        intent.setClass(this, ProjectModelCommonDetailActivity.class);
        intent.putExtra("ProjectModelCommonDetailActivityData", (Serializable) map);
        intent.putExtra("btype",bType);
        intent.putExtra("bId",map.get("id").toString());
        intent.putExtra("isAdd",false);


//        Bundle bundle = new Bundle();
//        bundle.putString("btype",bType);
//        bundle.putSerializable("ProjectModelCommonDetailActivityData", (Serializable) map);
//        intent.putExtras(bundle);
        startActivity(intent);
    }

    // 准备阶段--计划列表
    public class ProjectPreparePlanListAdapter extends BaseQuickAdapter<Map<String, Object>, BaseViewHolder> {
        public ProjectPreparePlanListAdapter(List<Map<String, Object>> data) {
            super(R.layout.item_project_sup_plan, data);
        }

        @Override
        protected void convert(BaseViewHolder helper, Map item) {

            TextView tvPlanTitle = helper.itemView.findViewById(R.id.tv_plan_title);
            TextView tvCharger = helper.itemView.findViewById(R.id.tv_charger);

            tvPlanTitle.setText(item.get("title").toString());
            tvCharger.setText(item.get("createName").toString());
        }
    }


}
