package com.kisoft.yuejianli.views;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.QualityAccident;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.QualityAccidentAdapter;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectQualityProblemContract;
import com.kisoft.yuejianli.entity.AccidentInfos;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.model.ProjectQualityProblemModel;
import com.kisoft.yuejianli.presenter.ProjectQualityProblemPresenter;

/**
 * Created by tudou on 2018/4/18.
 */

public class ProjectQualityProblemFragment extends BaseFragment<ProjectQualityProblemModel, ProjectQualityProblemPresenter>
        implements ProjectQualityProblemContract.ProjectQualityProblemViewContract {

    private RecyclerView rvQualityProblem;
    private QualityAccidentAdapter mAdapter;
    private List<QualityAccident> accidents = new ArrayList<>();

    private int count = 0;
    private int page = 1;
    private int pageSize = 15;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private ProjectQualityProblemModel mModel;
    private ProjectQualityProblemPresenter mPresenter;

    private View empotyView;



    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mModel = new ProjectQualityProblemModel(getActivity());
        mPresenter = new ProjectQualityProblemPresenter(this, mModel);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        initView(inflater);
        initData();
        return mRootView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }


    private void initView(LayoutInflater inflater) {
        empotyView = inflater.inflate(R.layout.page_no_data,null);
        mRootView = inflater.inflate(getRootView(), null);
        rvQualityProblem = mRootView.findViewById(R.id.rv_quality_problem);
        if (rvQualityProblem.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvQualityProblem.setLayoutManager(layoutManager);
        }
        mAdapter = new QualityAccidentAdapter(R.layout.item_quality_accident, accidents);
        mAdapter.setEmptyView(empotyView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {

            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                //todo
            }
        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                // todo
                rvQualityProblem.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (accidents.size() >= count) {
                            mAdapter.loadMoreEnd();
                        } else {
                            getData();
                        }
                    }
                }, Constant.LOAD_MORE_DELAY);
            }

        },rvQualityProblem);
        rvQualityProblem.setAdapter(mAdapter);
    }


    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        getData();
    }

    private void getData(){
        String c = Integer.toString(count);
        String s = Integer.toString(pageSize);
        String p = Integer.toString(page);
        mPresenter.getQualityAccident(userInfo.getId(),projectInfo.getProjectId(),c,s,p);
    }

    @Override
    public int getRootView() {
        return R.layout.fragment_project_quality_problem;
    }

    @Override
    public void showQualityAccident(AccidentInfos infos, int type) {
        if (infos != null) {
            count = infos.getCount();
            if (infos.getList() != null && infos.getList().size() > 0) {
                if (type == 0) {
                    page++;
                    this.accidents.clear();
                    this.accidents.addAll(infos.getList());
                } else {
                    this.accidents.addAll(infos.getList());
                    page++;
                    mAdapter.loadMoreComplete();
                }
            }
        }
        mAdapter.notifyDataSetChanged();
    }
}
