package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.widget.Toolbar;
import android.widget.ImageView;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.ExamSubmitResult;
import com.kisoft.yuejianli.utils.ActivityController;
import com.kisoft.yuejianli.utils.DateUtil;

import java.text.MessageFormat;
import java.util.Date;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 答题结果
 */
public class ExamResultActivity extends BaseActivity {

    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.tool_bar)
    Toolbar toolBar;
    @BindView(R.id.tvScore)
    TextView tvScore;
    @BindView(R.id.tvElapsedTime)
    TextView tvElapsedTime;
    @BindView(R.id.tvTotalNum)
    TextView tvTotalNum;
    @BindView(R.id.tvErrorNum)
    TextView tvErrorNum;
    @BindView(R.id.tvAccuracy)
    TextView tvAccuracy;
    @BindView(R.id.tvLookBack)
    TextView tvLookBack;
    private String onlineState;

    public static void launch(Activity activity, ExamSubmitResult examSubmitResult,String onlineState){
        Intent intent=new Intent(activity,ExamResultActivity.class);
        intent.putExtra("examSubmitResult",examSubmitResult);
        intent.putExtra("onlineState",onlineState);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        init();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_exam_result;
    }

    private void init(){
        tvTitle.setText("答题结果");
        onlineState=getIntent().getStringExtra("onlineState");
        ExamSubmitResult examSubmitResult = (ExamSubmitResult) getIntent().getSerializableExtra("examSubmitResult");
        if(examSubmitResult!=null){
            tvScore.setText(String.valueOf(examSubmitResult.getTaskScore()));
            tvElapsedTime.setText(DateUtil.showCountTime(examSubmitResult.getTaskTime()*1000));
            tvTotalNum.setText(String.valueOf(examSubmitResult.getQuestiionSum()));
            tvErrorNum.setText(String.valueOf(examSubmitResult.getWrong()+examSubmitResult.getNotNum()));
            tvAccuracy.setText(String.valueOf(examSubmitResult.getRight()));
        }
    }

    @Override
    public void onBackPressed() {
        doBack();
    }

    @OnClick(R.id.iv_back)
    void back(){
        doBack();
    }

    private void doBack() {
        ActivityController.finishTempActivity();
        finish();
    }

    @OnClick(R.id.tvLookBack)
    void lookBack(){
        ExamRecordActivity.launch(this,onlineState);
        finish();
    }

}
