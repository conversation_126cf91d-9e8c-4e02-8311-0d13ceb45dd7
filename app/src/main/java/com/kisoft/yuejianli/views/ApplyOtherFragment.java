package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ApplyOtherAdapter;
import com.kisoft.yuejianli.adpter.interf.GlobalListener;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ApplyType;
import com.kisoft.yuejianli.entity.ComPanyOrgInfo;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.entity.event.BeanEvent;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.ui.YFileListView;
import com.kisoft.yuejianli.ui.YSubmitCell;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.views.watermark.ImageUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ApplyOtherFragment extends BaseFragment {
    public View mRootView;
    Unbinder unbinder;

    @BindView(R.id.rv_content)
    RecyclerView mRecyclerView;

    @BindView(R.id.y_file_list)
    YFileListView mFileView;

    @BindView(R.id.submit_cell)
    YSubmitCell submitCell;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private String formKey = "";

    public String saveType;// 保存
    public String getType;// 获取
    public Map<String, Object> extraParam; // 额外参数
    boolean noFile;
    public String businessType;// 获取
    Map<String, String> systemItemMap;// 获取
    List<Map> mData;
//    private String orgNameKey;
//    private String orgIdKey;

    private String selectNameKey;
    private String selectIdKey;

    private boolean mDateFormat = false;

    public List<Map> getData() {
        return mData;
    }

    public void setData(List<Map> data) {
        mData = data;
    }

    Map<String, Object> mDataMap = new HashMap<>();

    boolean mIsApply = false;
    private ApplyOtherAdapter mAdapter;

    Handler handler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case 1:
                    int pos = msg.arg1;
                    //局部更新recyclerView,不用刷新整个list
                    mAdapter.notifyItemChanged(pos);
                    break;
            }
        }
    };

    @Override
    public int getRootView() {
        return R.layout.fragment_apply_other;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EventBus.getDefault().register(this);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initData();
        Log.i("TAG", "saveType== " + saveType + "getType===" + getType + "mData===" + mData);
        initView();
        if (noFile) {

        } else {
            fileMethod();
        }
        return mRootView;
    }

    private void initView() {
        if (mRecyclerView.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(getContext());
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            mRecyclerView.setLayoutManager(manager);
        }

        mAdapter = new ApplyOtherAdapter(mData, getContext(), handler, mIsApply, mDataMap, new GlobalListener() {
            @Override
            public void onViewClick(int id, int position, Object model) {
                Log.i("TAG", "onViewClick: " + "id==" + id + "position ==" + position + "model ==" + model);
            }

            @Override
            public void onRootViewClick(View view, int position, Object model) {
                mDataMap = (Map) model;
                Log.i("TAG", "onViewClick: " + "position ==" + position + "model ==" + model);
            }
        });
        mRecyclerView.setAdapter(mAdapter);
        mAdapter.setSelectTextViewListener(new ApplyOtherAdapter.SelectTextViewListener() {
            @Override
            public void skipType(String type, String key, String extra) {
                if (type.equals("Depart")) {
                    selectDepartment(key, extra);
                } else if (type.equals("singlePerson")) {
                    selectPerson(key, extra,true);
                }  else if (type.equals("Person")) {
                    selectPerson(key, extra,false);
                } else if (type.equals("Project")) {
                    selectProject(key, extra);
                }
            }
        });

        submitCell.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                uploadMulFile();
            }
        });
    }

    private void selectDepartment(String key, String extra) {
        this.selectNameKey = key;
        this.selectIdKey = extra;

        Intent intent = new Intent(getContext(), CompanyOrgInfoActivity.class);
        intent.putExtra(CompanyOrgInfoActivity.KEY_SELECT_DEPARTMENT, true);
        startActivity(intent);
    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void selectOrg(BeanEvent beanEvent) {
//        if (beanEvent != null && BeanEvent.TYPE_COMPANY_ORG.equals(beanEvent.getDataType())) {
//            ComPanyOrgInfo comPanyOrgInfo = (ComPanyOrgInfo) beanEvent.getData();
//            Log.i("Subscribe", "selectOrg: " + comPanyOrgInfo);
//            mDataMap.put(orgNameKey, comPanyOrgInfo.getName());
//            mDataMap.put(orgIdKey, comPanyOrgInfo.getId());
//            mAdapter.notifyDataSetChanged();
//
//        }
//    }

    private void selectPerson(String key, String extra,Boolean isSingle) {
        this.selectNameKey = key;
        this.selectIdKey = extra;

        Intent intent = new Intent();
        intent.putExtra("isSingle", isSingle);
        intent.setClass(getContext(), CompanyOrgInfoActivity.class);
        startActivityForResult(intent, Constant.REQEST_CODE_SELECT_COMPANY_ORG_INFO);
    }

    private void selectProject(String key, String extra) {
        this.selectNameKey = key;
        this.selectIdKey = extra;

        Intent intent = new Intent(mContext, ProjectChoseActivity.class);
        intent.putExtra("selectModel", true);
        intent.putExtra("autoForm", key);
        startActivityForResult(intent, Constant.REQUEST_CODE_CHOSE_PEOJECT);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void selectOrg(BeanEvent beanEvent) {
        if (beanEvent != null && BeanEvent.TYPE_COMPANY_ORG.equals(beanEvent.getDataType())) {
            ComPanyOrgInfo comPanyOrgInfo = (ComPanyOrgInfo) beanEvent.getData();
            Log.i("Subscribe", "selectOrg: " + comPanyOrgInfo);
            mDataMap.put(selectNameKey, comPanyOrgInfo.getName());
            mDataMap.put(selectIdKey, comPanyOrgInfo.getId());
            mAdapter.notifyDataSetChanged();
        } else if (beanEvent != null && BeanEvent.TYPE_COMPANY_PERSON.equals(beanEvent.getDataType())) {
            List<ComPanyOrgInfo> comPanyOrgInfo = (List<ComPanyOrgInfo>) beanEvent.getData();
            Log.i("TAG", "selectOrg: " + comPanyOrgInfo);
        }
    }

    private void initData() {
        mIsApply = ((ApplyActivity) getActivity()).isApply;
        for (Map map : mData) {
            String key = map.get("key").toString();
            mDataMap.put(key, "");
//            if (StringUtil.isEqual(map.get("key").toString(),"createTime") && map.containsKey("dateFormat")){
//                mDateFormat = true;
//            }
        }
        if (mIsApply) {
            // 申请页面
//            if (mDateFormat){
//            }else {
//                mDataMap.put("createTime", DateUtil.dateToString(new Date(), DateUtil.YMD));
//            }
            userInfo = SettingManager.getInstance().getUserInfo();
            projectInfo = SettingManager.getInstance().getProject();

            mDataMap.put("createTime", DateUtil.dateToString(new Date(), DateUtil.YMD_HMS));
            mDataMap.put("projectName", projectInfo.getProjectName());
            mDataMap.put("projectId", projectInfo.getProjectId());
            mDataMap.put("createId", userInfo.getId());
            mDataMap.put("createName", userInfo.getName());
            mDataMap.put("deptName", userInfo.getFatherName());
            mDataMap.put("deptId", userInfo.getFather());
            if (systemItemMap != null) {
                String spkey = systemItemMap.get("spkey");
                int index = Integer.parseInt(systemItemMap.get("index"));
                if (SettingManager.getInstance().getFormParamType(spkey) != null && SettingManager.getInstance().getFormParamType(spkey).size() > 0) {
                    List<ApplyType> infos = SettingManager.getInstance().getFormParamType(spkey);
                    mData.get(index).put("SystemItemModel", infos);
                }
            }
        } else {
            // 查看详情
            submitCell.setVisibility(View.GONE);
            ProcessListBean bean = ((ApplyActivity) getActivity()).bean;
            getData(bean);
        }
    }

    //提交
    private void sendData(Map param) {
//      showProgress();
        // 合并额外参数
        if (extraParam.size() > 0) {
            for (String key : extraParam.keySet()) {
                param.put(key, extraParam.get(key));
            }
        }
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("data", StringUtil.objectToJson(param));
        Api.getGbkApiserver().submitApply1(saveType, pramaras).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                // dismissProgress();
                Log.i("TAG", "onResponse: " + response.body().getData());
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    //mView.applyBack(response.body().getData());
                    String str = response.body().getData();
                    String[] backs = str.split(",");
                    if (backs != null && backs.length == 3) {
                        ((ApplyActivity) getContext()).setBack(str);
                    }
                } else {
//                    mView.applyBack(response.body().getMessage());
                    showToast(response.body().getMessage());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                // dismissProgress();
                Log.i("TAG", "onResponse: fail");
                if (StringUtil.isEmpty(t.getMessage())){
                    showToast("提交失败，请重试");
                }else {
                    showToast(t.getMessage());
                }
                t.printStackTrace();
            }
        });
    }

    //获取详情
    private void getData(ProcessListBean bean) {
        Api.getGbkApiserver().testApi(getType, bean.getParameters()).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
//                mView.dismissProgress();
                Log.i("TAG", "onResponse: " + response.body().getData());
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    Map data = (Map) response.body().getData();
                    for (Map map : mData) {
                        String key = map.get("key").toString();
                        String str = (data.get(key) != null) ? data.get(key).toString() : "";
                        mDataMap.put(key, str);

                        if (map.containsKey("showKey")) {
                            String showKey = map.get("showKey").toString();
                            String str1 = (data.get(showKey) != null) ? data.get(showKey).toString() : "";
                            mDataMap.put(showKey, str1);
                        }
                    }
                    mAdapter.setData(mDataMap);
//                    mAdapter.notifyDataSetChanged();
                    if (noFile) {
                    } else {
                        getFileList(data.get("id").toString());
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    private ArrayList<EnclosureListDto> mFileList = new ArrayList<>();
    private boolean isTakePhoto;
    private BGAPhotoHelper mPhotoHelper;

    private void fileMethod() {
        mFileView.setApply(mIsApply);
        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);
        // 获取主键
        getPrimarykey(new GetPrimarykeyContract() {
            @Override
            public void getPrimarykey(String str) {
                formKey = str;
            }
        });
        // 点击添加附件
        mFileView.getAddFileBtn().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showFileSelect();
            }
        });
        // 删除附件
        mFileView.setFileOperateContract(new YFileListView.FileOperateContract() {
            @Override
            public void openFile(int index) {
                mFileList.remove(index);
                mFileView.setList(mFileList);
            }
        });
        // 打开附件
        mFileView.setOpenFileContract(new YFileListView.OpenFileContract() {
            @Override
            public void openFile(EnclosureListDto dto) {
                WebActivity.launch(getActivity(), dto);
            }
        });
    }

    private void showFileSelect() {
        // 空照片 ，添加
        String[] str = new String[]{"系统相机", "手机相册", "手机文件"};
        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
        ab.setCancelable(true);
        ab.setItems(str, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                switch (which) {
                    case 0://相机
                        isTakePhoto = true;
                        takePhoto();
                        break;
                    case 1://相册
                        isTakePhoto = false;
                        getPhoto();
                        break;
                    case 2://手机文件
                        getFile();
                        break;
                }
            }
        });
        ab.show();
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), Constant.REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), Constant.REQUEST_CODE_TAKE_PHOTO);
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    /**
     * 打开文件选择器
     */
    private void getFile() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("*/*");// 任意类型文件
        this.startActivityForResult(intent, Constant.REQUEST_CODE_FILE_SELECT);
    }

    BGAPhotoHelper1 mPhotoHelper1 = new BGAPhotoHelper1();

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQUEST_CODE_CHOOSE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_TAKE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800, 800),
                            Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_CROP) {
            if (resultCode == Activity.RESULT_OK) {
                // getCropIntent  获取裁剪完图片的路径
                String photoPath = mPhotoHelper1.getCropFilePath();
//                showToast(photoPath);
                File file = new File(mPhotoHelper1.getCropFilePath());
                double fileSize = FileUtil.getFileOrFilesSize(photoPath, 3);
                Log.i("fileSize", fileSize + "");

                //showToast(photoPath);
                if (fileSize > 1) {
                    ImageUtil.compressImageByLuban(photoPath);
                    String s = ImageUtil.compressImage(photoPath);
                    mFileList.add(new EnclosureListDto(file.getName(), s));
                } else {
                    mFileList.add(new EnclosureListDto(file.getName(), photoPath));
                }
                mFileView.setList(mFileList);
            } else {
                mPhotoHelper.deleteCameraFile();
                mPhotoHelper.deleteCropFile();
            }
        } else if (requestCode == Constant.REQUEST_CODE_FILE_SELECT) {
            String path = "";
            if (data == null) {
                // 用户未选择任何文件，直接返回
                return;
            }
            path = FileUtil.getRealPath(this.getContext(), data.getData());
            File file = new File(path);
            mFileList.add(new EnclosureListDto(file.getName(), path));
            mFileView.setList(mFileList);
//            showToast(path);
        } else if (requestCode == Constant.REQEST_CODE_SELECT_COMPANY_ORG_INFO) {
            if (data != null) {
                List<String> ids = new ArrayList<>();
                List<String> names = new ArrayList<>();
                ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                if (names != null && names.size() > 0) {
                    String idStr = TextUtils.join(",", ids);
                    String nameStr = TextUtils.join(",", names);
                    mDataMap.put(selectNameKey, nameStr);
                    mDataMap.put(selectIdKey, idStr);
                    mAdapter.notifyDataSetChanged();
                }
            }
        }else if (requestCode == Constant.REQUEST_CODE_CHOSE_PEOJECT) {
            if (resultCode == Activity.RESULT_OK && data != null) {
                ProjectInfo info = (ProjectInfo) data.getSerializableExtra("data");
                mDataMap.put(selectNameKey, info.getProjectName());
                mDataMap.put(selectIdKey, info.getProjectId());
                mAdapter.notifyDataSetChanged();
            }
        }
    }

    private void uploadMulFile() {
        if (mFileList.isEmpty()) {
            sendData(mDataMap);
        } else {
            if (formKey.isEmpty()) {
                getPrimarykey(new GetPrimarykeyContract() {
                    @Override
                    public void getPrimarykey(String str) {
                        formKey = str;
                    }
                });
                showToast("未获取到主键");
                return;
            }
            mDataMap.put("id", formKey);
            showProgress();
            String s = DateUtil.dateToString(new Date(), DateUtil.YM1);
            HashMap<String, String> paras = new HashMap<>();
            paras.put("filePath", "/roa/oa/" + s);
            paras.put("businessId", formKey);
            paras.put("businessType", businessType);
            OkHttpRequestManager.getInstance().uploadMulFile(mFileList, paras, new IRequestCallback() {
                @Override
                public void onSuccess(String response) {
                    Log.i("upload", "onSuccess: ");
                    dismissProgress();
                    sendData(mDataMap);
                }

                @Override
                public void onFailure(Throwable throwable) {
                    Log.i("upload", "onFailure: ");
                    dismissProgress();
                }
            });
        }
    }

    // 获取附件列表
    private void getFileList(String bt) {
        HashMap<String, Object> paras = new HashMap<>();
        paras.put("businessId", bt);
        paras.put("businessType", businessType);
        Api.getGbkApiserver().getEnclosureList(Constant.HTTP_GET_ENCLOSURE_LIST, paras).enqueue(new Callback<NetworkResponse<List<EnclosureListDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<EnclosureListDto>>> call,
                                   Response<NetworkResponse<List<EnclosureListDto>>> response) {
                Log.i("getFileList", response.toString());
                if (response.body().getData() != null) {
                    mFileList.clear();
                    mFileList.addAll(response.body().getData());
                    mFileView.setList(mFileList);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<EnclosureListDto>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }


    private void getSystemItemWithTypeCode(String spkey, Integer index) {

        /*if (SettingManager.getInstance().getFormParamType(spkey) != null && SettingManager.getInstance()
        .getFormParamType(spkey).size() > 0) {
            List<ApplyType> infos = SettingManager.getInstance().getFormParamType(spkey);
            mData.get(index).put("SystemItemModel", infos);
            mAdapter.setData(mData);
        }*/
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("typeCode", code);
//        Api.getGbkApiserver().getLeaveType(Constant.HTTP_GET_LEAVE_TYPE, parameters).enqueue(new
//        Callback<NetworkResponse<List<ApplyType>>>() {
//            @Override
//            public void onResponse(Call<NetworkResponse<List<ApplyType>>> call,
//                                   Response<NetworkResponse<List<ApplyType>>> response) {
//                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
//                    List<ApplyType> data = response.body().getData();
//                    mData.get(index).put("SystemItemModel", data);
//                    mAdapter.setData(mData);
////                    }
//                }
//            }

//            @Override
//            public void onFailure(Call<NetworkResponse<List<ApplyType>>> call, Throwable t) {
//
//            }
//        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
        EventBus.getDefault().unregister(this);
    }
}