package com.kisoft.yuejianli.views;
import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.QualityReportAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.QualityReportContract;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.QualityReportInfo;
import com.kisoft.yuejianli.entity.QualityReportList;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.QualityReportModel;
import com.kisoft.yuejianli.presenter.QualityReportPresenter;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class QualityReportActivity extends BaseActivity<QualityReportModel,
        QualityReportPresenter> implements QualityReportContract.QualityReportViewContract, BaseRefreshListener {

    @BindView(R.id.tv_submit)
    TextView textViewAdd;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    @BindView(R.id.ptrl_content)
    PullToRefreshLayout ptrlContent;
    private int count = 0;
    private int page = 1;
    private int pageSize = 10;
    private int pageCount = 0;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private View empotyView;
    private QualityReportAdapter mAdapter;
    private List<QualityReportInfo> qualityReportList=new ArrayList<>();
    private QualityReportPresenter presenter;
    private QualityReportModel model;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new QualityReportModel(this);
        presenter = new QualityReportPresenter(this, model);
        initMVP(model, presenter);
        initData();
        initView();
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        textViewAdd.setVisibility(View.VISIBLE);
        textViewAdd.setText("新增");
        tvTitle.setText("质量评估报告");

        empotyView=getLayoutInflater().inflate(R.layout.activity_quality_report, null);
        if (rvContent.getLayoutManager() == null){
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new QualityReportAdapter(R.layout.item_quality_report_info, qualityReportList);
        mAdapter.setOnItemClickListener(new QualityReportAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                seeDetail(qualityReportList.get(position));
            }
        });
        rvContent.setAdapter(mAdapter);
    }

    private void getData(){
        if(projectInfo != null){
            String c = Integer.toString(count);
            String p = Integer.toString(page);
            String z = Integer.toString(pageSize);
            presenter.getQualityReportList(userInfo.getId(), projectInfo.getProjectId(), c, p, z);
        }
    }

    private void initView() {
        ptrlContent.setRefreshListener(this);
        refresh();
    }

    @Override
    public void finshRefresh() {
        ptrlContent.finishRefresh();
        ptrlContent.finishLoadMore();
    }

    @Override
    public void refresh() {
        qualityReportList.clear();
        page = 1;
        count=0;
        pageCount = 0;
        getData();
    }

    @Override
    public void loadMore() {
        if (qualityReportList.size() >= count) {
            showToast("没有更多数据了");
            ptrlContent.finishLoadMore();
        } else {
            page++;
            pageCount = qualityReportList.size() + pageSize;
            getData();
        }
    }

    private void seeDetail(QualityReportInfo qualityReportInfo) {
        Intent intent = new Intent();
        intent.setClass(this, QualityReportAddActivity.class);
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constant.INTENT_KEY_QUALITYREPORT_INFO , qualityReportInfo);
        intent.putExtras(bundle);
        startActivity(intent);
    }

    @Override
    public void showQualityReportList(QualityReportList infos, int type) {
        if (infos != null) {
            if (pageCount == 0)
                count = infos.count;
            this.qualityReportList.addAll(infos.list);
            mAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_quality_report;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    @OnClick(R.id.tv_submit)
    public void goAdd(){
        Intent intent = new Intent();
        intent.setClass(this, QualityReportAddActivity.class);
        /*Bundle bundle = new Bundle();
        bundle.putSerializable(Constant.INTENT_KEY_TENDER_INFO , preAcceptInfo);
        intent.putExtras(bundle);*/
        startActivity(intent);
    }

}