package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.adpter.MarketInfoAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.SelectMarketInfoContract;
import com.kisoft.yuejianli.entity.MarketInfo;
import com.kisoft.yuejianli.entity.MarketInfoInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.SelectMarketInfoModel;
import com.kisoft.yuejianli.presenter.SelectMarketInfoPresenter;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/7/23.
 */

public class SelectMarketInfoActivity extends BaseActivity<SelectMarketInfoModel, SelectMarketInfoPresenter> implements
        SelectMarketInfoContract.SelectMarketInfoViewContract {

    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    private View empotyView;

    private List<MarketInfo> infos = new ArrayList<>();
    private MarketInfoAdapter mAdapter;

    private int count = 0;
    private int page = 1;
    private int pageSize = 10;

    private UserInfo userInfo;
    private SelectMarketInfoModel model;
    private SelectMarketInfoPresenter presenter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new SelectMarketInfoModel(this);
        presenter = new SelectMarketInfoPresenter(this, model);
        initMVP(model, presenter);
        initView();
        initData();
    }

    private void initView() {
        tvTitle.setText("请选择投标项目");
        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new MarketInfoAdapter(R.layout.item_market_info, infos);
        mAdapter.setEmptyView(empotyView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                getSelectMarketInfo(infos.get(position));
            }
        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                rvContent.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (infos.size() >= count) {
                            mAdapter.loadMoreEnd();
                        } else {
                            getData();
                        }

                    }
                }, Constant.LOAD_MORE_DELAY);
            }
        }, rvContent);
        rvContent.setAdapter(mAdapter);
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        if (count == 0) {
            getData();
        }
    }

    private void getData() {
        String c = infos.isEmpty() ? "0" : (infos.size() + pageSize) + "";
        String p = String.valueOf(page);
        String z = String.valueOf(pageSize);
        mPresenter.getMarketInfo(userInfo.getId(), p, z, c);
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_makert_info_selecter;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    /**
     * 获得选择的市场信息
     *
     * @param marketInfo
     */
    private void getSelectMarketInfo(MarketInfo marketInfo) {
        Intent intent = new Intent();
        intent.putExtra(Constant.INTENT_KEY_MARKET_INFO_ID, marketInfo.getIaGuid());
        intent.putExtra(Constant.INTENT_KEY_MARKET_INFO_NAME, marketInfo.getProjectName());
        this.setResult(Constant.REQEST_CODE_SELECT_MARKET_INFO, intent);
        finish();
    }

    @Override
    public void showMarketInfo(MarketInfoInfo info, int type) {
        if (info != null) {
            if (count == 0)
                count = info.getCount();
            if (type == 0) {
                page++;
                infos.clear();
                infos.addAll(info.getList());
            } else if (type == 1) {
                page++;
                infos.addAll(info.getList());
                mAdapter.loadMoreComplete();
            }
            mAdapter.notifyDataSetChanged();
        }
    }
}
