package com.kisoft.yuejianli.views;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.widget.TextView;

import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.contract.AnnNoticeDetailContract;
import com.kisoft.yuejianli.entity.InfoIssueDto;
import com.kisoft.yuejianli.model.AnnNoticeDetailModel;
import com.kisoft.yuejianli.presenter.AnnNoticeDetailPresenter;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;
import com.kisoft.yuejianli.R;

/**
 * Description: 公告通知详细信息
 * Author     : bhd
 * qq         : 602394773
 */
public class AnnNoticeDetailFragment extends BaseFragment<AnnNoticeDetailModel, AnnNoticeDetailPresenter> implements AnnNoticeDetailContract.AnnNoticeDetailViewContract {

    public View mRootView;
    Unbinder unbinder;

    @BindView(R.id.tv_page_title)
    TextView tvPageTitle;
    @BindView(R.id.tv_under)
    TextView tvUnder;
    @BindView(R.id.webview)
    WebView webView;

    InfoIssueDto.InfoIssueBean bean;

    AnnNoticeDetailModel model;
    AnnNoticeDetailPresenter presenter;

    @Override
    public int getRootView() {
        return R.layout.fragment_ann_notice_detail;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        bean = (InfoIssueDto.InfoIssueBean) getArguments().getSerializable("data");
        model = new AnnNoticeDetailModel(mContext);
        presenter = new AnnNoticeDetailPresenter(this, model);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initView();
        return mRootView;
    }

    public void initView() {
        if (bean != null) {
            tvPageTitle.setText(bean.getTitle());
            tvUnder.setText(bean.getName()+"　　"+bean.getFcreatorName()+"　　"+bean.getFcreatedate());
            presenter.requestData(bean.getId());
        }
    }

    @Override
    public void dataBack(InfoIssueDto.InfoIssueBean bean) {
        if (bean != null) {
            Document parse = Jsoup.parse(bean.getFbody());
//        Elements imgs = parse.getElementsByTag("img");
//        if (!imgs.isEmpty()) {
//            for (Element e : imgs) {
//                imgs.attr("width", "100%");
//                imgs.attr("height", "auto");
//            }
//        }
            String content = parse.toString();
            webView.loadDataWithBaseURL(null, content, "text/html", "utf-8", null);
        }
    }
}
