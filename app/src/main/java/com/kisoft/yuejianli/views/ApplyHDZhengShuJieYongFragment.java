package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.DatePicker;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.HDZhengShuMingXiAdapter;
import com.kisoft.yuejianli.adpter.PresonRequireAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.ui.YFileListView;
import com.kisoft.yuejianli.ui.YLabelCell;
import com.kisoft.yuejianli.ui.YSelectDateCell;
import com.kisoft.yuejianli.ui.YSubmitCell;
import com.kisoft.yuejianli.ui.YTextFieldCell;
import com.kisoft.yuejianli.ui.YTextViewCell;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.views.watermark.ImageUtil;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ApplyHDZhengShuJieYongFragment extends BaseFragment {

    public View mRootView;
    Unbinder unbinder;
    boolean mIsApply = false;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    String saveType;// 保存
    String getType;// 获取
    String businessType;//

    @BindView(R.id.deptName)
    YLabelCell deptName;//申请部门
    @BindView(R.id.userName)
    YLabelCell userName;//申请人
    @BindView(R.id.departmentLeader)
    YTextFieldCell departmentLeader;//部门领导
    @BindView(R.id.cause)
    YTextViewCell cause;//申请原因
    @BindView(R.id.validity)
    YTextViewCell validity;//有效期(复印件)
    @BindView(R.id.remark)
    YTextViewCell remark;//备注


//    @{@"title":@"申请部门:",@"key":@"deptName",@"cellClass":@"YTextViewCell"},
//    @{@"title":@"申请人:",@"key":@"userName",@"cellClass":@"YLabelCell"},
//    @{@"title":@"部门领导:",@"key":@"departmentLeader",@"cellClass":@"YTextFieldCell"},
//    @{@"title":@"申请原因:",@"key":@"cause",@"cellClass":@"YTextViewCell"},
//    @{@"title":@"有效期(复印件):",@"key":@"validity",@"cellClass":@"YTextViewCell"},
//    @{@"title":@"备注:",@"key":@"remark",@"cellClass":@"YTextViewCell"},


    @BindView(R.id.submit_cell)
    YSubmitCell tvSub;

    @BindView(R.id.recycleViewDetail)
    RecyclerView mRecyclerView;
    @BindView(R.id.tvAddDetail)
    TextView tvAddDetail;
    @BindView(R.id.tvDeleteDetail)
    TextView tvDeleteDetail;
    @BindView(R.id.llOpDetail)
    LinearLayout llOpDetail;
    @BindView(R.id.y_file_list)
    YFileListView mFileView;
    Map<String, Object> mDataMap = new HashMap<>();

    private HDZhengShuMingXiAdapter mAdapter;
    private String formKey = "";

    @Override
    public int getRootView() {
        return R.layout.fragment_apply_hd_zhengshujieyong;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initData();
        initView();
        fileMethod();
        initListener();
        return mRootView;
    }

    private void initView() {
        if (mRecyclerView.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(getContext());
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            mRecyclerView.setLayoutManager(manager);
        }

        mAdapter = new HDZhengShuMingXiAdapter(mIsApply);
        mRecyclerView.setAdapter(mAdapter);
//        mAdapter.setOnItemChildClickListener(this);
    }

    private void initListener() {

    }

    private void initData() {

        saveType = "saveOaCertificateBorrowing";
        getType = "getOaCertificateBorrowingById";
        businessType = "T_OA_CERTIFICATE_BORROWING";

        mIsApply = ((ApplyActivity) getActivity()).isApply;
        // 申请页面
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        mDataMap.put("createTime", DateUtil.dateToString(new Date(), DateUtil.YMD));
        mDataMap.put("projectName", projectInfo.getProjectName());
        mDataMap.put("projectId", projectInfo.getProjectId());
        mDataMap.put("createId", userInfo.getId());
        mDataMap.put("createName", userInfo.getName());
        mDataMap.put("userName", userInfo.getName());
        mDataMap.put("deptName", userInfo.getFatherName());
        mDataMap.put("deptId", userInfo.getFather());

        deptName.setContent(mDataMap.get("deptName").toString());
        userName.setContent(mDataMap.get("userName").toString());


        //    @{@"title":@"申请部门:",@"key":@"deptName",@"cellClass":@"YTextViewCell"},
//    @{@"title":@"申请人:",@"key":@"userName",@"cellClass":@"YLabelCell"},
//    @{@"title":@"部门领导:",@"key":@"departmentLeader",@"cellClass":@"YTextFieldCell"},
//    @{@"title":@"申请原因:",@"key":@"cause",@"cellClass":@"YTextViewCell"},
//    @{@"title":@"有效期(复印件):",@"key":@"validity",@"cellClass":@"YTextViewCell"},
//    @{@"title":@"备注:",@"key":@"remark",@"cellClass":@"YTextViewCell"},

        //部门领导
        departmentLeader.getEtContent().setEnabled(mIsApply);
        //申请原因
        cause.getEtContent().setEnabled(mIsApply);
        //有效期
        validity.getEtContent().setEnabled(mIsApply);
        //备注
        remark.getEtContent().setEnabled(mIsApply);


        if (mIsApply) {
            // 申请页面
            tvSub.setVisibility(View.VISIBLE);
            llOpDetail.setVisibility(View.VISIBLE);

        } else {
            // 查看详情
            tvSub.setVisibility(View.GONE);
            llOpDetail.setVisibility(View.GONE);

            ProcessListBean bean = ((ApplyActivity) getActivity()).bean;
            getData(bean);
        }
    }

    @OnClick({R.id.tvAddDetail, R.id.tvDeleteDetail, R.id.submit_cell})
    void buttonClick(View view) {
        switch (view.getId()) {
            case R.id.tvAddDetail:
                Map map = new HashMap();
                map.put("certificateName", "");
                map.put("type", "");
                map.put("copies", "");
                mAdapter.addData(map);
                break;
            case R.id.tvDeleteDetail:
                if (mAdapter.getData().size() > 0) {
                    mAdapter.remove(mAdapter.getData().size() - 1);
                }
                break;

            case R.id.submit_cell:


                //部门领导
                mDataMap.put("departmentLeader", departmentLeader.getEtContent().getText().toString().trim());
                //申请原因
                mDataMap.put("cause", cause.getEtContent().getText().toString().trim());
                //有效期
                mDataMap.put("validity", validity.getEtContent().getText().toString().trim());
                //备注
                mDataMap.put("remark", remark.getEtContent().getText().toString().trim());

                List<Map> data = mAdapter.getData();

                mDataMap.put("oaCertificateDetailList", data);

                uploadMulFile();

                break;
        }
    }

    //提交
    private void sendData(Map param) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("data", StringUtil.objectToJson(param));
        Log.i("TAG", "saveType== " + saveType + "getType===" + getType + "mData===" + param);
        Api.getGbkApiserver().submitApply1(saveType, pramaras).enqueue(new Callback<NetworkResponse<String>>() {
            @Override
            public void onResponse(Call<NetworkResponse<String>> call, Response<NetworkResponse<String>> response) {
                Log.i("TAG", "onResponse: " + response.body().getData());
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    //mView.applyBack(response.body().getData());
                    String str = response.body().getData();
                    String[] backs = str.split(",");
                    if (backs != null && backs.length == 3) {
                        ((ApplyActivity) getContext()).setBack(str);
                    }
                } else {
//                    mView.applyBack(response.body().getMessage());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<String>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    //获取详情
    private void getData(ProcessListBean bean) {
        Api.getGbkApiserver().testApi(getType, bean.getParameters()).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
//                mView.dismissProgress();
                Log.i("TAG", "onResponse: " + response.body().getData());
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    Map data = (Map) response.body().getData();
                    deptName.getTvContent().setText(data.get("deptName").toString());
                    userName.getTvContent().setText(data.get("userName").toString());
                    departmentLeader.getEtContent().setText(data.get("departmentLeader").toString());
                    cause.getEtContent().setText(data.get("cause").toString());
                    validity.getEtContent().setText(data.get("validity").toString());
                    remark.getEtContent().setText(data.get("remark").toString());

                    List<Map> data1 = (List<Map>) data.get("oaCertificateDetailList");
                    mAdapter.setNewData(data1);

                    getFileList(data.get("id").toString());
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    /**
     * 展示日期选择对话框
     */
    private void showDatePickerDialog(YSelectDateCell tv, String key) {
        Calendar c = Calendar.getInstance();
        new DatePickerDialog(mContext, new DatePickerDialog.OnDateSetListener() {
            @Override
            public void onDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                // TODO Auto-generated method stub
                c.set(year, monthOfYear, dayOfMonth);
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                Log.i("TAG", "onDateSet: " + format.format(c.getTime()));

                int m = monthOfYear + 1;
                String month = m >= 10 ? m + "" : "0" + m;
                String day = dayOfMonth >= 10 ? dayOfMonth + "" : "0" + dayOfMonth;
                String dateStr = year + "-" + month + "-" + day;
                tv.getTvContent().setText(dateStr);
                mDataMap.put(key, dateStr);
                Log.i("mDataMap", "showDatePickerDialog: ---->" + mDataMap);
            }
        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)).show();
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    private ArrayList<EnclosureListDto> mFileList = new ArrayList<>();
    private boolean isTakePhoto;
    private BGAPhotoHelper mPhotoHelper;

    private void fileMethod() {
        mFileView.setApply(mIsApply);
        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);
        // 获取主键
        getPrimarykey(new GetPrimarykeyContract() {
            @Override
            public void getPrimarykey(String str) {
                formKey = str;
            }
        });
        // 点击添加附件
        mFileView.getAddFileBtn().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showFileSelect();
            }
        });
        // 删除附件
        mFileView.setFileOperateContract(new YFileListView.FileOperateContract() {
            @Override
            public void openFile(int index) {
                mFileList.remove(index);
                mFileView.setList(mFileList);
            }
        });
        // 打开附件
        mFileView.setOpenFileContract(new YFileListView.OpenFileContract() {
            @Override
            public void openFile(EnclosureListDto dto) {
                WebActivity.launch(getActivity(), dto);
            }
        });
    }

    private void showFileSelect() {
        // 空照片 ，添加
        String[] str = new String[]{"系统相机", "手机相册", "手机文件"};
        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
        ab.setCancelable(true);
        ab.setItems(str, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                switch (which) {
                    case 0://相机
                        isTakePhoto = true;
                        takePhoto();
                        break;
                    case 1://相册
                        isTakePhoto = false;
                        getPhoto();
                        break;
                    case 2://手机文件
                        getFile();
                        break;
                }
            }
        });
        ab.show();
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), Constant.REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), Constant.REQUEST_CODE_TAKE_PHOTO);
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    /**
     * 打开文件选择器
     */
    private void getFile() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("*/*");// 任意类型文件
        this.startActivityForResult(intent, Constant.REQUEST_CODE_FILE_SELECT);
    }

    BGAPhotoHelper1 mPhotoHelper1 = new BGAPhotoHelper1();

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQUEST_CODE_CHOOSE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_TAKE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800, 800),
                            Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_CROP) {
            if (resultCode == Activity.RESULT_OK) {
                // getCropIntent  获取裁剪完图片的路径
                String photoPath = mPhotoHelper1.getCropFilePath();
//                showToast(photoPath);
                File file = new File(mPhotoHelper1.getCropFilePath());
                double fileSize = FileUtil.getFileOrFilesSize(photoPath, 3);
                Log.i("fileSize", fileSize + "");

                //showToast(photoPath);
                if (fileSize > 1) {
                    ImageUtil.compressImageByLuban(photoPath);
                    String s = ImageUtil.compressImage(photoPath);
                    mFileList.add(new EnclosureListDto(file.getName(), s));
                } else {
                    mFileList.add(new EnclosureListDto(file.getName(), photoPath));
                }
                mFileView.setList(mFileList);
            } else {
                mPhotoHelper.deleteCameraFile();
                mPhotoHelper.deleteCropFile();
            }
        } else if (requestCode == Constant.REQUEST_CODE_FILE_SELECT) {
            String path = "";
            if (data == null) {
                // 用户未选择任何文件，直接返回
                return;
            }
            path = FileUtil.getRealPath(this.getContext(), data.getData());
            File file = new File(path);
            mFileList.add(new EnclosureListDto(file.getName(), path));
            mFileView.setList(mFileList);
//            showToast(path);
        }
    }

    private void uploadMulFile() {
        if (mFileList.isEmpty()) {
            sendData(mDataMap);
        } else {
            if (formKey.isEmpty()) {
                getPrimarykey(new GetPrimarykeyContract() {
                    @Override
                    public void getPrimarykey(String str) {
                        formKey = str;
                    }
                });
                showToast("未获取到主键");
                return;
            }
            mDataMap.put("id", formKey);
            showProgress();
            String s = DateUtil.dateToString(new Date(), DateUtil.YM1);
            HashMap<String, String> paras = new HashMap<>();
            paras.put("filePath", "/roa/oa/" + s);
            paras.put("businessId", formKey);
            paras.put("businessType", businessType);
            OkHttpRequestManager.getInstance().uploadMulFile(mFileList, paras, new IRequestCallback() {
                @Override
                public void onSuccess(String response) {
                    Log.i("upload", "onSuccess: ");
                    sendData(mDataMap);
                }

                @Override
                public void onFailure(Throwable throwable) {
                    Log.i("upload", "onFailure: ");
                    dismissProgress();
                }
            });
        }
    }

    // 获取附件列表
    private void getFileList(String bt) {
        HashMap<String, Object> paras = new HashMap<>();
        paras.put("businessId", bt);
        paras.put("businessType", businessType);
        Api.getGbkApiserver().getEnclosureList(Constant.HTTP_GET_ENCLOSURE_LIST, paras).enqueue(new Callback<NetworkResponse<List<EnclosureListDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<EnclosureListDto>>> call,
                                   Response<NetworkResponse<List<EnclosureListDto>>> response) {
                if (response.body().getData() != null) {
                    mFileList.clear();
                    mFileList.addAll(response.body().getData());
                    mFileView.setList(mFileList);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<EnclosureListDto>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }
}


