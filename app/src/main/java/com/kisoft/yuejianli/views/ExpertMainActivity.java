package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ExamButtonAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.ButtonBean;
import com.kisoft.yuejianli.utils.ScreenUtils;
import com.kisoft.yuejianli.utils.recyclerview.GridDividerItemDecoration;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 *
 */
public class ExpertMainActivity extends BaseActivity implements BaseQuickAdapter.OnItemClickListener {

    private static final int BUTTON_TYPE_TEST=0;
    private static final int BUTTON_TYPE_SIMULATION=1;
    private static final int BUTTON_TYPE_ONLINE=2;
    private static final int BUTTON_TYPE_WRONG=3;
    private static final int BUTTON_TYPE_COLLECT=4;
    private static final int BUTTON_TYPE_LEARN=5;
    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.tool_bar)
    Toolbar toolBar;
    @BindView(R.id.mRecyclerView)
    RecyclerView mRecyclerView;
    private boolean isLoading;


    public static void launch(Activity activity){
        Intent intent=new Intent(activity,ExpertMainActivity.class);
        activity.startActivity(intent);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_exam_main;
    }

    private void initView() {
        tvTitle.setText("专家诊断");
        mRecyclerView.setLayoutManager(new GridLayoutManager(this,3));
        mRecyclerView.addItemDecoration(new GridDividerItemDecoration.Builder(this)
                .setHorizontalSpan((float) ScreenUtils.dip2px(this,1))
                .setVerticalSpan((float)ScreenUtils.dip2px(this,1))
                .setColor(ContextCompat.getColor(this,R.color.line_space)).setShowLastLine(true).build());
        ExamButtonAdapter examButtonAdapter=new ExamButtonAdapter();
        mRecyclerView.setAdapter(examButtonAdapter);
        List<ButtonBean> buttonBeans=new ArrayList<>();
        buttonBeans.add(new ButtonBean("发起工单",R.drawable.icon_exam01,BUTTON_TYPE_TEST));
        buttonBeans.add(new ButtonBean("待处理工单",R.drawable.icon_exam01,BUTTON_TYPE_SIMULATION));
        buttonBeans.add(new ButtonBean("已完成工单",R.drawable.icon_exam01,BUTTON_TYPE_ONLINE));

        examButtonAdapter.setNewData(buttonBeans);
        examButtonAdapter.setOnItemClickListener(this);
    }

    @OnClick(R.id.iv_back)
    void back(){
        finish();
    }

    @OnClick(R.id.tv_title)
    void test(){
    }

    @Override
    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
        Object object=adapter.getData().get(position);
        if(object instanceof ButtonBean){
            ButtonBean buttonBean= (ButtonBean) object;
            switch (buttonBean.getType()){
                case BUTTON_TYPE_TEST:
                    WorkorderActivity.launch(this);
                    break;
                case BUTTON_TYPE_SIMULATION:
                    WorkorderViewStartedActivity.launch(ExpertMainActivity.this,1);
                    break;
                case BUTTON_TYPE_ONLINE:
                    WorkorderViewStartedActivity.launch(ExpertMainActivity.this,2);
                    break;
            }
        }
    }
}