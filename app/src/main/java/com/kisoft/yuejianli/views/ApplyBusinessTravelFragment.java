package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.EnclosureListAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ApplyBusinessTravelContract;
import com.kisoft.yuejianli.entity.ApplyBusinessTravelInfo;
import com.kisoft.yuejianli.entity.BusinessTravelType;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ApplyBusinessTravelModel;
import com.kisoft.yuejianli.presenter.ApplyBusinessTravelPresenter;
import com.kisoft.yuejianli.ui.ImageDialog;
import com.kisoft.yuejianli.ui.YLabelCell;
import com.kisoft.yuejianli.ui.YTextFieldCell;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.UpdateAppHttpUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.views.watermark.ImageUtil;
import com.vector.update_app.HttpManager;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 出差申请
 */
public class ApplyBusinessTravelFragment extends BaseFragment<ApplyBusinessTravelModel, ApplyBusinessTravelPresenter> implements ApplyBusinessTravelContract.View {

    @BindView(R.id.tvProjectName)
    TextView tvProjectName;
    @BindView(R.id.llProject)
    LinearLayout llProject;
    @BindView(R.id.tvName)
    TextView tvName;
    @BindView(R.id.tvOrgName)
    TextView tvOrgName;
    @BindView(R.id.tvApplyTime)
    TextView tvApplyTime;
    @BindView(R.id.llApplyTime)
    LinearLayout llApplyTime;
    @BindView(R.id.spBusinessType)
    Spinner spBusinessType;
    @BindView(R.id.tv_file_type)
    TextView tvFileType;
    @BindView(R.id.etReasons)
    EditText etReasons;
    @BindView(R.id.etTargetLocation)
    EditText etTargetLocation;
    @BindView(R.id.tvWithPerson)
    TextView tvWithPerson;
    @BindView(R.id.llWithPerson)
    LinearLayout llWithPerson;
    @BindView(R.id.tvPlanStartTime)
    TextView tvPlanStartTime;
    @BindView(R.id.tvPlanEndTime)
    TextView tvPlanEndTime;
    @BindView(R.id.etVehicle)
    EditText etVehicle;
    @BindView(R.id.etDays)
    EditText etDays;
    @BindView(R.id.etRemark)
    EditText etRemark;

    @BindView(R.id.zhiwu)
    YTextFieldCell zhiwu;

    @BindView(R.id.txbm)
    YTextFieldCell txbm;

    @BindView(R.id.yusuandaxie)
    YLabelCell yusuandaxie;

    @BindView(R.id.yusuanxiaoxie)
    YTextFieldCell yusuanxiaoxie;

    @BindView(R.id.tv_add_item)
    TextView tvAddItem;

    @BindView(R.id.rv_file_list)
    RecyclerView rvContent;

    @BindView(R.id.tv_sub)
    TextView tvSub;
    Unbinder unbinder;
    @BindView(R.id.ivWithPerson)
    ImageView ivWithPerson;
    private ApplyBusinessTravelInfo info = new ApplyBusinessTravelInfo();
    private List<BusinessTravelType> process = new ArrayList<>();
    private ArrayAdapter<BusinessTravelType> processAdapter;
    private BusinessTravelType mBusinessType;
    private ApplyBusinessTravelPresenter presenter;
    private ProjectInfo projectInfo;
    private boolean isApply;
    private boolean isTakePhoto;
    private BGAPhotoHelper mPhotoHelper;
    private static final int REQUEST_CODE_CHOOSE_PHOTO = 1;
    private static final int REQUEST_CODE_TAKE_PHOTO = 2;
    private static final int REQUEST_CODE_CROP = 3;
    private static final int REQUEST_CONSTRUCTIONUNITLIST_ACTIVITY = 4;

    private static final int REQUEST_CODE_FILE_SELECT = 120;
    //    private ArrayList<String> filePaths = new ArrayList<>();
    private ArrayList<EnclosureListDto> fileList = new ArrayList<>();
    private String btid;
    private EnclosureListAdapter mAdapter;
    private ImageDialog mImageDialog;

    public ApplyBusinessTravelFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ApplyBusinessTravelModel model = new ApplyBusinessTravelModel(mContext);
        presenter = new ApplyBusinessTravelPresenter(this, model);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        return mRootView;
    }

    @Override
    public int getRootView() {
        return R.layout.fragment_apply_business_travel;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        isApply = ((ApplyActivity) getActivity()).isApply;
        processAdapter = new ArrayAdapter<>(mContext, android.R.layout.simple_list_item_1, process);
        spBusinessType.setAdapter(processAdapter);
        if (isApply) {
            getUUID();
            UserInfo userInfo = SettingManager.getInstance().getUserInfo();
            if (userInfo != null) {
                tvName.setText(userInfo.getName());
                tvOrgName.setText(userInfo.getFatherName());
                info.setCreateId(userInfo.getId());
                info.setCreateName(userInfo.getName());
                info.setOrgId(userInfo.getFather());
                info.setOrgName(userInfo.getFatherName());
            }
            projectInfo = SettingManager.getInstance().getProject();
            tvProjectName.setText(projectInfo.getProjectName());
            tvApplyTime.setText(DateUtil.dateToString(new Date(), DateUtil.YMD));

            yusuanxiaoxie.getEtContent().setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL);
            yusuanxiaoxie.getEtContent().addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void afterTextChanged(Editable editable) {
                    String num = editable.toString();
                    if (!StringUtil.isEmpty(num)) {
                        double value = Double.valueOf(num.toString());
                        String format = String.format("%.2f", value);
                        info.setTotalFeesCapital(StringUtil.getChinessMoney(num));
                        yusuandaxie.setContent(StringUtil.getChinessMoney(num));
                    }
                }
            });

            spBusinessType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> adapterView, View view, int position, long id) {
                    mBusinessType = process.get(position);
                }

                @Override
                public void onNothingSelected(AdapterView<?> adapterView) {

                }
            });
            presenter.getBusinessTravelType();

            // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
            File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
            mPhotoHelper = new BGAPhotoHelper(takePhotoDir);
            // 点击附件选择
            tvAddItem.setVisibility(View.VISIBLE);
            tvAddItem.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    showFileSelect();
                }
            });
        } else {

            zhiwu.setEnabled(false);
            txbm.setEnabled(false);
            yusuanxiaoxie.setEnabled(false);

            spBusinessType.setEnabled(false);
            spBusinessType.setVisibility(View.GONE);
            tvFileType.setVisibility(View.VISIBLE);
            etReasons.setEnabled(false);
            etReasons.setHint(null);
            etTargetLocation.setEnabled(false);
            etTargetLocation.setHint(null);
            llWithPerson.setEnabled(false);
            tvWithPerson.setHint(null);
            ivWithPerson.setVisibility(View.GONE);
            tvPlanStartTime.setEnabled(false);
            tvPlanEndTime.setEnabled(false);
            etVehicle.setEnabled(false);
            etVehicle.setHint(null);
            etDays.setEnabled(false);
            etDays.setHint(null);
            etRemark.setEnabled(false);
            etRemark.setHint(null);
            tvSub.setVisibility(View.GONE);
            ProcessListBean bean = ((ApplyActivity) getActivity()).bean;
            presenter.getInfo(bean);
        }
        initView();
    }

    private void initView() {
        mImageDialog = new ImageDialog();
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            layoutManager.setSmoothScrollbarEnabled(false);
            rvContent.setLayoutManager(layoutManager);
            rvContent.setNestedScrollingEnabled(false);
        }
        mAdapter = new EnclosureListAdapter(fileList);
        rvContent.setAdapter(mAdapter);
        if (isApply) {
            mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseQuickAdapter adapter, View view, int position) {

                    showFileOperate(position);
                }
            });
        }
        if (!isApply) {
            rvContent.addOnItemTouchListener(new OnItemClickListener() {
                @Override
                public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int position) {
                    String webUrl = fileList.get(position).getViewUrl();
                    if (webUrl != null && !webUrl.isEmpty()) {
//                        WebActivity.launch(getActivity(),Constant.PROTOCOL_URL,"用户协议");
//                        WebActivity.launch(getActivity(),webUrl,"附件");
                        WebActivity.launch(getActivity(), fileList.get(position));
//                        Intent intent = new Intent();
//                        intent.setClass(getContext(), PdfViewActivity.class);
//                        intent.putExtra(Constant.INTENT_KEY_FILE_URL, fileList.get(position).getViewUrl());
//                        startActivity(intent);

//                        Intent intent = new Intent();
//                        intent.setAction(Intent.ACTION_VIEW);
//                        intent.setData(Uri.parse(StringUtil.handlerUrl(fileList.get(position).getFilePath() +
//                        "&filename=" + fileList.get(position).getFileName())));
//                        mContext.startActivity(intent);
//                        downloadFile(fileList.get(position).getFileName(), fileList.get(position).getFilePath());
                    } else {
                        showToast("附件地址为空");
                    }
                }
            });
        }
    }


    private void showFileOperate(int position) {

        // 空照片 ，添加
        String[] str = new String[]{"删除", "取消"};
        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
        ab.setTitle("确认删除附件吗？");
        ab.setNegativeButton("取消", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
               // Toast.makeText(mContext, "你点击了取消按钮~", Toast.LENGTH_SHORT).show();
            }
        });
        ab.setPositiveButton("确定", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                //Toast.makeText(mContext, "你点击了确定按钮~", Toast.LENGTH_SHORT).show();
                fileList.remove(position);
                mAdapter.notifyDataSetChanged();
            }
        });

        ab.show();
    }

    // 附件下载
    public void downloadFile(final String fileName, String fileUrl) {

        String path = "";
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED) || !Environment.isExternalStorageRemovable()) {
            try {
                path = this.getActivity().getExternalCacheDir().getAbsolutePath();
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (TextUtils.isEmpty(path)) {
                path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getAbsolutePath();
            }
        } else {
            path = this.getActivity().getCacheDir().getAbsolutePath();
        }
        File file1 = new File(path + "/" + fileName);
        if (file1.exists()) {
            showToast("可以直接打开");
            Log.i("TAG", "onStart: 文件已存在");
            Uri uri = Uri.fromFile(file1);

//            Intent intent = new Intent("android.intent.action.VIEW");
//            intent.addCategory("android.intent.category.DEFAULT");
//            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//            intent.setDataAndType(uri, "image/*");
            showFileDetail(file1, fileUrl);
            return;
        }
        String finalPath = path;
        UpdateAppHttpUtil httpUtil = new UpdateAppHttpUtil();
        httpUtil.download(fileUrl, finalPath, fileName, new HttpManager.FileCallback() {
            @Override
            public void onProgress(float progress, long total) {
                Log.i("TAG", "onStart: 下载进度 = " + progress);
            }

            @Override
            public void onError(String error) {
                Log.i("TAG", "onStart: 下载失败");
            }

            @Override
            public void onResponse(File file) {
                Log.i("TAG", "onStart: 下载完成");
                file.renameTo(file1);
                // 打开文件
//                Intent intent = new Intent();
//                intent.setClass(getContext(), PdfViewActivity.class);
//                intent.putExtra(Constant.INTENT_KEY_FILE_URL, finalPath);
//                startActivity(intent);
                showFileDetail(file, fileUrl);
            }

            @Override
            public void onBefore() {
                Log.i("TAG", "onStart: 开始下载");
            }
        });

    }

    /**
     * 通过url，展示照片
     *
     * @param url
     */
    private void showImage(String url) {
        if (mImageDialog != null) {
            mImageDialog.showImageDialog(getActivity().getFragmentManager(), url);
        }
    }

    private Uri getUri(Intent intent, File file) {
        Uri uri = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            //判断版本是否在7.0以上
            uri =
                    FileProvider.getUriForFile(this.getContext(),
                            getContext().getPackageName() + ".fileprovider",
                            file);
            //添加这一句表示对目标应用临时授权该Uri所代表的文件
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        } else {
            uri = Uri.fromFile(file);
        }
        return uri;
    }

    public void showFileDetail(File file, String fileUrl) {

        if (!file.exists()) return;
        /* 取得扩展名 */
        String end =
                file.getName().substring(file.getName().lastIndexOf(".") + 1, file.getName().length()).toLowerCase();
        /* 依扩展名的类型决定MimeType */
        if (end.equals("jpg") || end.equals("gif") || end.equals("png") ||
                end.equals("jpeg") || end.equals("bmp")) {
            showImage(fileUrl);
        } else {
            Uri uri = Uri.fromFile(file);
            Intent intent = new Intent("android.intent.action.VIEW");
            intent.addCategory("android.intent.category.DEFAULT");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.setDataAndType(uri, "application/msword");
            startActivity(intent);
        }
    }


    @OnClick({R.id.llWithPerson, R.id.tvPlanStartTime, R.id.tvPlanEndTime, R.id.tv_sub})
//R.id.llProject,
    void buttonClick(View view) {
        switch (view.getId()) {
            case R.id.llProject:
                Intent intent = new Intent();
                intent.setClass(mContext, ProjectChoseActivity.class);
                startActivityForResult(intent, Constant.REQUEST_CODE_CHANGE_PEOJECT);
                break;
            case R.id.llWithPerson:
                Intent intent2 = new Intent();
                intent2.setClass(mContext, CompanyOrgInfoActivity.class);
                startActivityForResult(intent2, Constant.REQUEST_CODE_INCOME_PEOPLE);
                break;
            case R.id.tvPlanStartTime:
            case R.id.tvPlanEndTime:
                showDatePickerDialog((TextView) view);
                break;
            case R.id.tv_sub:
                if (mBusinessType == null) {
                    showToast("请选择出差类别");
                    return;
                }
                String reasons = etReasons.getText().toString().trim();
                if (StringUtil.isEmpty(reasons)) {
                    showToast("请输入出差事由");
                    return;
                }

                String location = etTargetLocation.getText().toString().trim();
                if (StringUtil.isEmpty(location)) {
                    showToast("请输入出差地点");
                    return;
                }

                String startTime = tvPlanStartTime.getText().toString().trim();
                if (StringUtil.isEmpty(startTime)) {
                    showToast("请输选择出差日期");
                    return;
                }

                String endTime = tvPlanEndTime.getText().toString().trim();
                if (StringUtil.isEmpty(endTime)) {
                    showToast("请输选择返回日期");
                    return;
                }

                String vehicle = etVehicle.getText().toString().trim();
                if (StringUtil.isEmpty(vehicle)) {
                    showToast("请输入交通工具");
                    return;
                }

                String days = etDays.getText().toString().trim();
                if (StringUtil.isEmpty(days)) {
                    showToast("请输入出差天数");
                    return;
                }

                info.setProjectId(projectInfo.getProjectId());
                info.setProjectName(projectInfo.getProjectName());
                info.setType(mBusinessType.getItemguid());
                info.setReasons(reasons);
                info.setLocation(location);
                info.setDepartureDate(startTime);
                info.setReturnDate(endTime);
                info.setVehicle(vehicle);
                info.setDays(days);
                info.setRemark(etRemark.getText().toString().trim());
                info.setCreateTime(DateUtil.dateToString(new Date(), DateUtil.YMD));
                info.setPost(zhiwu.getEtContent().getText().toString());
                info.setDept(txbm.getEtContent().getText().toString());
                info.setTotalFees(yusuanxiaoxie.getEtContent().getText().toString());

                uploadMulFile();
                break;
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    @Override
    public void typeListBack(List<BusinessTravelType> leaveTypes) {
        this.process.clear();
        if (leaveTypes != null && !leaveTypes.isEmpty()) {
            if (isApply) {
                this.process.addAll(leaveTypes);
            } else {
                for (BusinessTravelType leaveType : leaveTypes) {
                    if (leaveType.getItemguid().equals(info.getType())) {
                        process.add(leaveType);
                        tvFileType.setText(leaveType.getItemname());
                        break;
                    }
                }
            }
            processAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void applyBack(String str) {
        String[] backs = str.split(",");
        if (backs.length == 3) {
            ((ApplyActivity) getContext()).setBack(str);
        }
    }

    @Override
    public void infoBack(ApplyBusinessTravelInfo info) {
        if (info != null) {
            this.info = info;
            presenter.getBusinessTravelType();

            tvProjectName.setText(info.getProjectName());
            tvName.setText(info.getCreateName());
            tvOrgName.setText(info.getOrgName());
            tvApplyTime.setText(info.getCreateTime());
            etReasons.setText(info.getReasons());
            etTargetLocation.setText(info.getLocation());
            tvWithPerson.setText(info.getPengikut());
            tvPlanStartTime.setText(info.getDepartureDate());
            tvPlanEndTime.setText(info.getReturnDate());
            etVehicle.setText(info.getVehicle());
            tvFileType.setText(info.getTypeName());
            etDays.setText(info.getDays());
            etRemark.setText(info.getRemark());
            zhiwu.getEtContent().setText(info.getPost());
            txbm.getEtContent().setText(info.getDept());
            yusuanxiaoxie.getEtContent().setText(info.getTotalFees());
            yusuandaxie.setContent(info.getTotalFeesCapital());

            // 获取附件
            getFileList(info.getBtId());

        }
    }

    @Override
    public void showError() {
        showToast(getString(R.string.request_error));
    }

    /**
     * 展示日期选择对话框
     */
    private void showDatePickerDialog(final TextView tv) {
        Calendar c = Calendar.getInstance();
        new DatePickerDialog(mContext, new DatePickerDialog.OnDateSetListener() {

            @Override
            public void onDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                int m = monthOfYear + 1;
                String month = m >= 10 ? m + "" : "0" + m;
                String day = dayOfMonth >= 10 ? dayOfMonth + "" : "0" + dayOfMonth;
                String dateStr = year + "-" + month + "-" + day;
                Log.e("dateStr", "showDatePickerDialog:" + dateStr);
                switch (tv.getId()) {
                    case R.id.tvPlanStartTime:
                        info.setDepartureDate(dateStr);
                        break;
                    case R.id.tvPlanEndTime:      // 计划回收日期
                        info.setReturnDate(dateStr);
                        break;
                }
                tv.setText(dateStr);
            }
        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)).show();
    }


    private void showFileSelect() {
        // 空照片 ，添加
        String[] str = new String[]{"系统相机", "手机相册", "手机文件"};
        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
        ab.setItems(str, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                switch (which) {
                    case 0://相机
                        isTakePhoto = true;
                        takePhoto();
                        break;
                    case 1://相册
                        isTakePhoto = false;
                        getPhoto();
                        break;
                    case 2://手机文件
                        getFile();
                        break;
                }
            }
        });
        ab.show();
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), REQUEST_CODE_TAKE_PHOTO);
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    /**
     * 打开文件选择器
     */
    private void getFile() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("*/*");// 任意类型文件
        this.startActivityForResult(intent, REQUEST_CODE_FILE_SELECT);
    }

    BGAPhotoHelper1 mPhotoHelper1 = new BGAPhotoHelper1();

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQUEST_CODE_CHANGE_PEOJECT) {
            projectInfo = SettingManager.getInstance().getProject();
        } else if (requestCode == Constant.REQUEST_CODE_INCOME_PEOPLE) {
            if (data != null) {
                StringBuilder name = new StringBuilder();
                StringBuilder id = new StringBuilder();
                List<String> ids = new ArrayList<>(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                List<String> names = new ArrayList<>(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                if (names.size() > 0) {
                    for (int i = 0; i < names.size() && i < 1; i++) {
                        if (i != 0) {
                            name.append(",");
                            id.append(",");
                        }
                        name.append(names.get(i));
                        id.append(ids.get(i));
                    }
                    tvWithPerson.setText(name.toString());
                    info.setPengikut(name.toString());
                    info.setPengikutId(id.toString());
                } else {
                    tvWithPerson.setText(null);
                    info.setPengikut(null);
                    info.setPengikutId(null);
                }
            }
        } else if (requestCode == REQUEST_CODE_CHOOSE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == REQUEST_CODE_TAKE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800, 800),
                            REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == REQUEST_CODE_CROP) {
            if (resultCode == Activity.RESULT_OK) {
                // getCropIntent  获取裁剪完图片的路径
                String photoPath = mPhotoHelper1.getCropFilePath();
                //showToast(photoPath);
                File file = new File(mPhotoHelper1.getCropFilePath());
                double fileSize = FileUtil.getFileOrFilesSize(photoPath, 3);
                Log.i("fileSize", fileSize + "");

                //showToast(photoPath);
                if (fileSize > 1) {
                    ImageUtil.compressImageByLuban(photoPath);
                    String s = ImageUtil.compressImage(photoPath);
                    fileList.add(new EnclosureListDto("图片", s));
                    mAdapter.notifyDataSetChanged();
                } else {
                    fileList.add(new EnclosureListDto("图片", photoPath));
                    mAdapter.notifyDataSetChanged();
//                    uploadCorpPhoto(file);
                }
            } else {
                mPhotoHelper.deleteCameraFile();
                mPhotoHelper.deleteCropFile();
            }

        } else if (requestCode == REQUEST_CODE_FILE_SELECT) {
            String path = "";
            if (data == null) {
                // 用户未选择任何文件，直接返回
                return;
            }
            path = handleSingleDocument(data);
            File file = new File(path);
            fileList.add(new EnclosureListDto(file.getName(), path));
            mAdapter.notifyDataSetChanged();
            //showToast(path);
        }
    }

    //将uri转换为我们需要的path,多选类似
    private String handleSingleDocument(Intent data) {
        Uri uri = data.getData();
        return FileUtil.getRealPath(this.getContext(), uri);
    }

    // 获取UUID
    private void getUUID() {
        Api.getGbkApiserver().testApi("getUUID", new HashMap<>()).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
//                mView.dismissProgress();
                Log.i("TAG", "onResponse: " + response.body().getData());
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    btid = response.body().getData().toString();
                    info.setBtId(btid);
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    // 获取附件列表
    private void getFileList(String bt) {
        HashMap<String, Object> paras = new HashMap<>();
        paras.put("businessId", bt);
        paras.put("businessType", "T_BUSINESS_TRAVEL");
        Api.getGbkApiserver().getEnclosureList(Constant.HTTP_GET_ENCLOSURE_LIST, paras).enqueue(new Callback<NetworkResponse<List<EnclosureListDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<EnclosureListDto>>> call,
                                   Response<NetworkResponse<List<EnclosureListDto>>> response) {
                if (response.body().getData() != null) {
                    fileList.clear();
                    fileList.addAll(response.body().getData());
                    mAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<EnclosureListDto>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    private void uploadMulFile() {
        showProgress();
        if (fileList.isEmpty()) {
            presenter.submitApply(info);
        } else {
            if ( btid.isEmpty()) {
                getPrimarykey(new GetPrimarykeyContract() {
                    @Override
                    public void getPrimarykey(String str) {
                        btid = str;
                        info.setBtId(btid);
                    }
                });
                showToast("未获取到主键");
                return;
            }
            String s = DateUtil.dateToString(new Date(), DateUtil.YM1);
            HashMap<String, String> paras = new HashMap<>();
            paras.put("filePath", "/roa/oa/" + s);
            paras.put("businessId", btid);
            paras.put("businessType", "T_BUSINESS_TRAVEL");
            OkHttpRequestManager.getInstance().uploadMulFile(fileList, paras, new IRequestCallback() {
                @Override
                public void onSuccess(String response) {
                    Log.i("upload", "onSuccess: ");
                    presenter.submitApply(info);
                }
                @Override
                public void onFailure(Throwable throwable) {
                    Log.i("upload", "onFailure: ");
                }
            });
        }
    }

    /*
        // 上传附件
        public void uploadMulFile() {

            int count = fileList.size();
            List<MultipartBody.Part> parts = new ArrayList<>(count);
            Map<String, List<MultipartBody.Part>> partss = new HashMap<>();

            UserInfo userInfo = SettingManager.getInstance().getUserInfo();
            String s = DateUtil.dateToString(new Date(), DateUtil.YM1);
            HashMap<String, String> paras = new HashMap<>();
            paras.put("filePath", "/roa/oa/" + s);
            paras.put("type", "fileUpload");
            paras.put("businessId", btid);
            paras.put("businessType", "T_BUSINESS_TRAVEL");
            paras.put("userId", userInfo.getId());
            paras.put("projectId", projectInfo.getProjectId());


            MultipartBody.Builder builder = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM);
            //注意，file是后台约定的参数，如果是多图，file[]，如果是单张图片，file就行
            for (int i = 0; i < count; i++) {
                EnclosureListDto dto = fileList.get(i);
                File file = new File(dto.getFilePath());
                //这里上传的是多图
                builder.addFormDataPart("file", file.getName(), RequestBody.create(MediaType.parse("application/octet" +
                        "-stream"), file));
            }
            RequestBody requestBody = builder.build();

            String url = SettingManager.getInstance().getBaseUrl() + Constant.HTTP_APP_SERVLET + "?" + asUrlParams
            (paras);
            Log.i("asUrlParams", "getFileApiserver: " + url);
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("multipart/form-data");

            Request request = new Request.Builder()
                    .url(url)
                    .method("POST", requestBody)
                    .addHeader("Content-Type", "multipart/form-data")
                    .addHeader("Cookie", "JSESSIONID=********************************")
                    .build();

            client.newCall(request).enqueue(new okhttp3.Callback() {
                @Override
                public void onFailure(okhttp3.Call call, IOException e) {
    //                showToast("失败");
                    Log.i("", "onFailure: ");
                    e.printStackTrace();
                }

                @Override
                public void onResponse(okhttp3.Call call, okhttp3.Response response) throws IOException {
                    Log.i("uploadMulFile", "onResponse: " + response.body());

    //                showToast("成功");
                }
            });
            presenter.submitApply(info);

        }
    */
    private void tetttt() {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("text/plain");
        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("file", "西安市危险性较大的分部分项工程安全管理实施细则.pdf",
                        RequestBody.create(MediaType.parse("application/octet-stream"),
                                new File(fileList.get(0).getFilePath())))
                .addFormDataPart("appId", "yozowBiOkiKy7381")
                .addFormDataPart("sign", "95920C5DB8E0AFA0871E989EB8448954123DA0D0CBF9E18F1D7B77127E0C19CB")
                .build();
        Request request = new Request.Builder()
                .url("http://dmc.yozocloud.cn/api/file/upload")
                .method("POST", body)
                .addHeader("Cookie", "JSESSIONID=85DEE01551B99AD4B6633DAF5D37F098")
                .build();
        client.newCall(request).enqueue(new okhttp3.Callback() {
            @Override
            public void onFailure(okhttp3.Call call, IOException e) {
                Log.i("TAG", "onFailure: " + "失败");
            }

            @Override
            public void onResponse(okhttp3.Call call, okhttp3.Response response) throws IOException {
                String string = response.body().string();
                Log.i("TAG", "onResponse: " + string);
            }
        });
    }
}
