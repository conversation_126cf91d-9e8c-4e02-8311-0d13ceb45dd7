package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.CustomerPleasedSurveyDetailContract;
import com.kisoft.yuejianli.entity.CustomerPleasedSurvey;
import com.kisoft.yuejianli.entity.CustomerVisit;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.CustomerPleasedSurveyDetailModel;
import com.kisoft.yuejianli.presenter.CustomerPleasedSurveyDetailPresenter;
import com.kisoft.yuejianli.utils.StringUtil;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * Created by tudou on 2018/6/19.
 */

public class CustomerPleasedSurveyDetailActivity extends BaseActivity<CustomerPleasedSurveyDetailModel, CustomerPleasedSurveyDetailPresenter>
        implements CustomerPleasedSurveyDetailContract.CustomerPleasedSurveyDetailViewContract, RadioGroup.OnCheckedChangeListener {

    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.tv_project_name)
    TextView tvProjectName;

    @BindView(R.id.tv_visit_unit)
    TextView tvVisitUnit;

    @BindView(R.id.tv_visiter)
    TextView tvVisiter;

    @BindView(R.id.tv_content)
    EditText etContent;

    @BindView(R.id.tv_rg1)
    TextView tvRg1;

    @BindView(R.id.rg_1)
    RadioGroup rg1;

    @BindView(R.id.tv_rg2)
    TextView tvRg2;

    @BindView(R.id.rg_2)
    RadioGroup rg2;

    @BindView(R.id.tv_rg3)
    TextView tvRg3;

    @BindView(R.id.rg_3)
    RadioGroup rg3;

    @BindView(R.id.tv_rg4)
    TextView tvRg4;

    @BindView(R.id.rg_4)
    RadioGroup rg4;

    @BindView(R.id.tv_sub)
    TextView tvSubmit;


    private boolean canEdit = false;

    private UserInfo userInfo;
    private CustomerVisit visit;
    private CustomerPleasedSurvey survey;

    private CustomerPleasedSurveyDetailModel model;
    private CustomerPleasedSurveyDetailPresenter presenter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new CustomerPleasedSurveyDetailModel(this);
        presenter = new CustomerPleasedSurveyDetailPresenter(this, model);
        initMVP(model, presenter);
        initData();
        initView();
    }

    private void initView() {
        tvRg1.setText(CustomerPleasedSurvey.SURVEY_ITEM_1);
        tvRg2.setText(CustomerPleasedSurvey.SURVEY_ITEM_2);
        tvRg3.setText(CustomerPleasedSurvey.SURVEY_ITEM_3);
        tvRg4.setText(CustomerPleasedSurvey.SURVEY_ITEM_4);
        tvSubmit.setVisibility(canEdit ? View.VISIBLE : View.GONE);
        etContent.setEnabled(canEdit);
        rg1.setEnabled(canEdit);
        rg2.setEnabled(canEdit);
        rg3.setEnabled(canEdit);
        rg4.setEnabled(canEdit);
        if (!canEdit && survey != null) {
            tvProjectName.setText(survey.getProjectName());
            tvVisitUnit.setText(survey.getCusName());
            tvVisiter.setText(survey.getRetbackMan());
            etContent.setText(survey.getTotalReview());
            if (!StringUtil.isEmpty(survey.getStatisRes1())) {
                switch (survey.getStatisRes1()) {
                    case CustomerPleasedSurvey.JUDGE_LEVEL_1:
                        rg1.check(R.id.rb1_pleased_4);
                        break;
                    case CustomerPleasedSurvey.JUDGE_LEVEL_2:
                        rg1.check(R.id.rb1_pleased_3);
                        break;
                    case CustomerPleasedSurvey.JUDGE_LEVEL_3:
                        rg1.check(R.id.rb1_pleased_2);
                        break;
                    case CustomerPleasedSurvey.JUDGE_LEVEL_4:
                        rg1.check(R.id.rb1_pleased_1);
                        break;
                    default:
                        break;
                }
            } else {
                rg1.check(R.id.rb1_pleased_1);
            }
            if (!StringUtil.isEmpty(survey.getStatisRes2())) {
                switch (survey.getStatisRes2()) {
                    case CustomerPleasedSurvey.JUDGE_LEVEL_1:
                        rg2.check(R.id.rb2_pleased_4);
                        break;
                    case CustomerPleasedSurvey.JUDGE_LEVEL_2:
                        rg2.check(R.id.rb2_pleased_3);
                        break;
                    case CustomerPleasedSurvey.JUDGE_LEVEL_3:
                        rg2.check(R.id.rb2_pleased_2);
                        break;
                    case CustomerPleasedSurvey.JUDGE_LEVEL_4:
                        rg2.check(R.id.rb2_pleased_1);
                        break;
                    default:
                        break;
                }

            } else {
                rg2.check(R.id.rb2_pleased_1);
            }

            if (!StringUtil.isEmpty(survey.getStatisRes3())) {
                switch (survey.getStatisRes3()) {
                    case CustomerPleasedSurvey.JUDGE_LEVEL_1:
                        rg3.check(R.id.rb3_pleased_4);
                        break;
                    case CustomerPleasedSurvey.JUDGE_LEVEL_2:
                        rg3.check(R.id.rb3_pleased_3);
                        break;
                    case CustomerPleasedSurvey.JUDGE_LEVEL_3:
                        rg3.check(R.id.rb3_pleased_2);
                        break;
                    case CustomerPleasedSurvey.JUDGE_LEVEL_4:
                        rg3.check(R.id.rb3_pleased_1);
                        break;
                    default:
                        break;
                }
            } else {
                rg3.check(R.id.rb3_pleased_1);
            }

            if (!StringUtil.isEmpty(survey.getStatisRes4())) {
                switch (survey.getStatisRes4()) {
                    case CustomerPleasedSurvey.JUDGE_LEVEL_1:
                        rg4.check(R.id.rb4_pleased_4);
                        break;
                    case CustomerPleasedSurvey.JUDGE_LEVEL_2:
                        rg4.check(R.id.rb4_pleased_3);
                        break;
                    case CustomerPleasedSurvey.JUDGE_LEVEL_3:
                        rg4.check(R.id.rb4_pleased_2);
                        break;
                    case CustomerPleasedSurvey.JUDGE_LEVEL_4:
                        rg4.check(R.id.rb4_pleased_1);
                        break;
                    default:
                        break;
                }
            } else {
                rg4.check(R.id.rb4_pleased_1);
            }
        } else if (canEdit && visit != null) {
            tvProjectName.setText(visit.getProjectName());
            tvVisitUnit.setText(visit.getCusName());
            tvVisiter.setText(visit.getRetbackMan());
            rg1.setOnCheckedChangeListener(this);
            rg2.setOnCheckedChangeListener(this);
            rg3.setOnCheckedChangeListener(this);
            rg4.setOnCheckedChangeListener(this);
            etContent.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void afterTextChanged(Editable editable) {

                }
            });
        }
    }


    private void initData() {
        tvTitle.setText("客户满意度调查");
        userInfo = SettingManager.getInstance().getUserInfo();
        Intent intent = getIntent();
        if (intent != null) {
            survey = (CustomerPleasedSurvey) intent.getSerializableExtra(Constant.INTENT_KEY_CUSTOMER_PLEASED_SURVEY);
            if (survey == null) {
                canEdit = intent.getBooleanExtra(Constant.INTENT_KEY_CAN_EDIT, false);
                visit = (CustomerVisit) intent.getSerializableExtra(Constant.INTENT_KEY_CUSTOMER_VISIT);
                survey = new CustomerPleasedSurvey();
                survey.setProjectId(visit.getProjectId());
                survey.setProjectName(visit.getProjectName());
                survey.setCrpGuid(visit.getCrpGuid());
                survey.setCraGuid(visit.getCraGuid());
                survey.setCusGuid(visit.getCusGuid());
                survey.setCusName(visit.getCusName());
                survey.setCreateId(userInfo.getId());
                survey.setCreateName(userInfo.getName());
            } else {
                canEdit = false;
                presenter.getPleasedSurvey(userInfo.getId(), survey.getCrpGuid());
            }
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_customer_pleased_survey_detail;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @OnClick(R.id.tv_sub)
    public void submit() {
        mPresenter.submitpleasedSurvey(userInfo.getId(), survey);
    }

    @Override
    public void onCheckedChanged(RadioGroup radioGroup, int i) {
        switch (radioGroup.getId()) {
            case R.id.rg_1:
                switch (i) {
                    case R.id.rb1_pleased_1:
                        survey.setStatisRes1(CustomerPleasedSurvey.JUDGE_LEVEL_4);
                        survey.setResName1(CustomerPleasedSurvey.JUDGE_LEVEL_4_NAME);
                        break;
                    case R.id.rb1_pleased_2:
                        survey.setStatisRes1(CustomerPleasedSurvey.JUDGE_LEVEL_3);
                        survey.setResName1(CustomerPleasedSurvey.JUDGE_LEVEL_3_NAME);
                        break;

                    case R.id.rb1_pleased_3:
                        survey.setStatisRes1(CustomerPleasedSurvey.JUDGE_LEVEL_2);
                        survey.setResName1(CustomerPleasedSurvey.JUDGE_LEVEL_2_NAME);
                        break;

                    case R.id.rb1_pleased_4:
                        survey.setStatisRes1(CustomerPleasedSurvey.JUDGE_LEVEL_1);
                        survey.setResName1(CustomerPleasedSurvey.JUDGE_LEVEL_1_NAME);
                        break;
                    default:
                        break;
                }

                break;

            case R.id.rg_2:
                switch (i) {
                    case R.id.rb2_pleased_1:
                        survey.setStatisRes2(CustomerPleasedSurvey.JUDGE_LEVEL_4);
                        survey.setResName2(CustomerPleasedSurvey.JUDGE_LEVEL_4_NAME);
                        break;
                    case R.id.rb2_pleased_2:
                        survey.setStatisRes2(CustomerPleasedSurvey.JUDGE_LEVEL_3);
                        survey.setResName2(CustomerPleasedSurvey.JUDGE_LEVEL_3_NAME);
                        break;

                    case R.id.rb2_pleased_3:
                        survey.setStatisRes2(CustomerPleasedSurvey.JUDGE_LEVEL_2);
                        survey.setResName2(CustomerPleasedSurvey.JUDGE_LEVEL_2_NAME);
                        break;

                    case R.id.rb2_pleased_4:
                        survey.setStatisRes2(CustomerPleasedSurvey.JUDGE_LEVEL_1);
                        survey.setResName2(CustomerPleasedSurvey.JUDGE_LEVEL_1_NAME);
                        break;
                    default:
                        break;
                }
                break;


            case R.id.rg_3:
                switch (i) {
                    case R.id.rb3_pleased_1:
                        survey.setStatisRes3(CustomerPleasedSurvey.JUDGE_LEVEL_4);
                        survey.setResName3(CustomerPleasedSurvey.JUDGE_LEVEL_4_NAME);
                        break;
                    case R.id.rb3_pleased_2:
                        survey.setStatisRes3(CustomerPleasedSurvey.JUDGE_LEVEL_3);
                        survey.setResName3(CustomerPleasedSurvey.JUDGE_LEVEL_3_NAME);
                        break;

                    case R.id.rb3_pleased_3:
                        survey.setStatisRes3(CustomerPleasedSurvey.JUDGE_LEVEL_2);
                        survey.setResName3(CustomerPleasedSurvey.JUDGE_LEVEL_2_NAME);
                        break;

                    case R.id.rb3_pleased_4:
                        survey.setStatisRes3(CustomerPleasedSurvey.JUDGE_LEVEL_1);
                        survey.setResName3(CustomerPleasedSurvey.JUDGE_LEVEL_1_NAME);
                        break;
                    default:
                        break;
                }
                break;


            case R.id.rg_4:
                switch (i) {
                    case R.id.rb4_pleased_1:
                        survey.setStatisRes4(CustomerPleasedSurvey.JUDGE_LEVEL_4);
                        survey.setResName4(CustomerPleasedSurvey.JUDGE_LEVEL_4_NAME);
                        break;
                    case R.id.rb4_pleased_2:
                        survey.setStatisRes4(CustomerPleasedSurvey.JUDGE_LEVEL_3);
                        survey.setResName4(CustomerPleasedSurvey.JUDGE_LEVEL_3_NAME);
                        break;

                    case R.id.rb4_pleased_3:
                        survey.setStatisRes4(CustomerPleasedSurvey.JUDGE_LEVEL_2);
                        survey.setResName4(CustomerPleasedSurvey.JUDGE_LEVEL_2_NAME);
                        break;

                    case R.id.rb4_pleased_4:
                        survey.setStatisRes4(CustomerPleasedSurvey.JUDGE_LEVEL_1);
                        survey.setResName4(CustomerPleasedSurvey.JUDGE_LEVEL_1_NAME);
                        break;
                    default:
                        break;
                }
                break;

            default:

                break;
        }
    }

    @Override
    public void showResult(boolean isOk) {
        if (isOk) {
            Intent intent = new Intent();
            intent.getBooleanExtra(Constant.INTENT_KEY_COMPLETE_PLEASED_RESULT, true);
            this.setResult(Constant.REQEST_CODE_COMPLETE_PLEASED, intent);
            finish();
        }
    }

    private boolean isComplete() {
        boolean isComplete = true;
        if (StringUtil.isEmpty(survey.getTotalReview())) {
            showToast("请填写满意度总结");
            return false;
        } else if (StringUtil.isEmpty(survey.getStatisRes1())) {
            showToast("请完成各项调查结果");
            return false;
        } else if (StringUtil.isEmpty(survey.getStatisRes2())) {
            showToast("请完成各项调查结果");
            return false;
        } else if (StringUtil.isEmpty(survey.getStatisRes3())) {
            showToast("请完成各项调查结果");
            return false;
        } else if (StringUtil.isEmpty(survey.getStatisRes4())) {
            showToast("请完成各项调查结果");
            return false;
        }
        return true;
    }

    @Override
    public void dataBack(CustomerPleasedSurvey data) {
        survey = data;
        initView();
    }
}
