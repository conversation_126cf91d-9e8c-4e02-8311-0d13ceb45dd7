package com.kisoft.yuejianli.views;

import android.Manifest;
import android.graphics.Color;
import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.os.Handler;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.api.HuaweiService;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.AsrBean;
import com.kisoft.yuejianli.entity.FaceData;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.PermissionsChecker;
import com.kisoft.yuejianli.utils.PhotoUtil;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.VoiceRecordUtil;
import com.kisoft.yuejianli.utils.office.EncryptionUtils;
import com.ntko.app.support.DocumentsLibrary;
import com.ntko.app.support.Params;
import com.ntko.app.support.RhLogger;
import com.ntko.app.support.appcompat.DownloadResponse;
import com.ntko.app.support.appcompat.NetworkMetrics;
import com.ntko.app.support.appcompat.ProgressiveData;
import com.ntko.app.support.appcompat.ResponseType;
import com.ntko.app.support.appcompat.RhDocumentApi;
import com.ntko.app.support.appcompat.UploadResponse;
import com.ntko.app.support.callback.LifecycleListener;
import com.ntko.app.support.callback.SimpleLifecycleListener;
import com.ntko.app.support.callback.WPSWordParametersCallback;
import com.ntko.app.wps.params.WPSAppLicenseVersion;
import com.ntko.app.wps.params.WPSDisallowedActionList;
import com.ntko.app.wps.params.WPSDocumentParameters;
import com.ntko.app.wps.params.WPSMenuBarViewDisabledList;
import com.ntko.app.wps.params.WPSMenuBarViewHiddenList;
import com.ntko.app.wps.params.WPSWordParameters;

import org.xutils.common.util.LogUtil;

import java.io.File;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

import butterknife.BindView;
import butterknife.OnClick;

public class RecordTestActivity extends BaseActivity implements VoiceRecordUtil.VoiceRecordListener {

    @BindView(R.id.tvRecordContent)
    TextView tvRecordContent;
    @BindView(R.id.tvRecord)
    TextView tvRecord;
    @BindView(R.id.tvPlay)
    TextView tvPlay;
    @BindView(R.id.ivRecord)
    ImageView ivRecord;
    private boolean isRecording;
    private String soundFilePath;
    private long startRecordTime;
    static final String[] PERMISSIONS = new String[]{
            Manifest.permission.MODIFY_AUDIO_SETTINGS,
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
    };
    private PermissionsChecker permissionsChecker;
    private Handler handler=new Handler();
    private Runnable mLongPressed=new Runnable() {
        @Override
        public void run() {
            if(!isRecording){
                if (permissionsChecker.lacksPermissions(PERMISSIONS)) {
                    getPermissions(52);
                    showToast("使用需获取录音权限");
                    return;
                }
                voiceRecordUtil.startRecording(2);
            }
        }
    };
    private AnimationDrawable animationRecord;
    private VoiceRecordUtil voiceRecordUtil;
    private Timer playTimer;

    private String wpsAppGetUrl = "http://192.168.3.28:8181/oa-statics/moffice_11.4.1_default_ProCn00110_multidex_c88969cc5ef.apk";
    private String fileServer = "http://demo.ntko.com:1986/mobileoffice/servlet/DownloadServlet?filename=";
    private String uploadServer = "http://demo.ntko.com:1986/mobileoffice/servlet/UploadServlet";

    private RhDocumentApi documentsSDK;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        FaceData faceData = SettingManager.getInstance().getFaceData();
        if(faceData!=null){
            wpsAppGetUrl=SettingManager.getInstance().getBaseUrl()+faceData.getWpsUrl();
        }
        animationRecord = (AnimationDrawable) ivRecord.getBackground();
        permissionsChecker = new PermissionsChecker(this);
        voiceRecordUtil=new VoiceRecordUtil(this,this);
        tvRecord.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if(event.getAction() == MotionEvent.ACTION_DOWN)
                    handler.postDelayed(mLongPressed,200);
                if(event.getAction() == MotionEvent.ACTION_UP) {
                    handler.removeCallbacks(mLongPressed);
                    // 放开处理
                    if(isRecording){
                        voiceRecordUtil.stopRecording();
                    }
                }
                return false;
            }
        });

        initSDK();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_record_test;
    }

    @OnClick(R.id.tvRecord)
    void check(){
//        if (permissionsChecker.lacksPermissions(PERMISSIONS)) {
//            getPermissions(52);
//            showToast("使用需获取录音权限");
//        }
    }

    @OnClick(R.id.tvPlay)
    void play(){
        voiceRecordUtil.startPlaying(soundFilePath,3);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if(animationRecord!=null&&animationRecord.isRunning()){
            animationRecord.stop();
        }
    }

    /**
     * 6.0之上权限生情
     */
    private void getPermissions(int code) {
        ActivityCompat.requestPermissions(this, PERMISSIONS, code);
    }

    //删除录音
    private void deleteSound() {
        File file = new File(soundFilePath);
        LogUtil.i("deleteSound "+"soundFilePath=" + soundFilePath);
        if (file.exists() && file.isFile()) {
            boolean delete = file.delete();
            LogUtil.i("delete=" + delete);
        }
        soundFilePath = "";
    }

    @Override
    public void startRecord(int recordCode) {
        animationRecord.start();
        ivRecord.setVisibility(View.VISIBLE);
        isRecording = true;
    }

    @Override
    public void recordSuccess(String recordPath, boolean timeOut, int recordCode) {
        isRecording = false;
        soundFilePath=recordPath;
        ivRecord.setVisibility(View.GONE);
        animationRecord.stop();
        String base64= PhotoUtil.imageToBase64(soundFilePath);
        LogUtil.e(timeOut+" "+recordCode);
        HuaweiService.getInstance().asrVoice(base64, new HuaweiService.AsrListener() {

            @Override
            public void onSuccess(AsrBean asrBean) {
                if(!StringUtil.isEmpty(asrBean.getError_code())&&asrBean.getResult()!=null){
                    tvRecordContent.setText(asrBean.getResult().getText());
                }
            }

            @Override
            public void onFailure(Throwable throwable) {
                showToast("未能识别语音");
//                deleteSound();
            }
        });
    }

    private int playTime;
    @Override
    public void startPlay(int code) {
        playTime=0;
        playTimer=new Timer();
        playTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                playTime+=1000;
                tvPlay.setText(MessageFormat.format("播放 {0}", StringUtil.getPlayTime(playTime)));
            }
        },0,1000);
    }

    @Override
    public void stopPlay(int code) {
        playTimer.cancel();
        tvPlay.setText("播放完成");
    }

    private void initSDK() {

        DocumentsLibrary library = DocumentsLibrary.getInstance();
        library.enableDebug();
        //  library.enableOfflineProvision("http://15.33.71.194:1987");
        documentsSDK = library.newSDKInstance(this)
                .addLifecycleListener(listener)
                //  .initialize("", "");
                .initialize("78530E3D71A93B61CDD292808343C19C", "964202142531");

        // 设定上传和下载拦截器，实现自定义我们在网络请求中的逻辑
        //    NetworkingUtils.setupDocumentApi(documentsSDK);

        // 如果服务器输出的文档流是加密的，我们可以注册一个文档加解密接口，
        // 当下载时对文档数据进行解密，当上传时对文档数据进行加密
        // 注意：服务器加密的算法要确保在设定的解密接口中可以正确的解密，
        //      同时，Android端的加密，服务器端也同时应该能够正确的解密
        EncryptionUtils.setupDocumentApi(this, library);
    }

    public void create(View view) {
        String uploadUrl = uploadServer + "?filename=sample.docx&filetype=docx";

        documentsSDK.withParameters(new WPSWordParametersCallback() {
            @Override
            public WPSWordParameters prepare() {
                WPSWordParameters wordParameters = new WPSWordParameters();
                wordParameters.setOpenInView(WPSWordParameters.OPEN_IN_NORMAL);
                wordParameters.setUsePenMode(true);
                wordParameters.setInkColor(Color.DKGRAY);
                wordParameters.setRevisionMode(true);
                wordParameters.setUsername("张大钊");

                wordParameters.setWatermark("重庆软航科技有限公司");
                wordParameters.setWatermarkColor(Color.LTGRAY);

                wordParameters.setWPSAppLicenseVersion(WPSAppLicenseVersion.DEPRECATED);

                // 对于没有安装WPS的设备，设定WPS下载地址后将自动安装WPS
                wordParameters.setWpsAppDownloadURL(wpsAppGetUrl);
                return wordParameters;
            }
        }).createNewWordDocument("新建word文档", uploadUrl);
    }

    public void open(View view) {
        // 文件下载地址
        String downloadUrl = fileServer + "sample.docx";
        String uploadUrl = uploadServer + "?filename=sample.docx&filetype=docx";

        // 打开服务器Word文档
        documentsSDK.withParameters(new WPSWordParametersCallback() {
            @Override
            public WPSWordParameters prepare() {
                WPSWordParameters wordParameters = new WPSWordParameters();
                // wordParameters.setOpenInView(WPSWordParameters.OPEN_IN_NORMAL);
                wordParameters.setUsePenMode(false);  //true 笔写
                wordParameters.setInkColor(Color.DKGRAY);
                wordParameters.setRevisionMode(true);
                wordParameters.setUsername("张大钊");
                wordParameters.setOpenInView(WPSDocumentParameters.OPEN_IN_HAND_SIGNATURE);
                wordParameters.setWatermark("重庆软航科技有限公司");
                wordParameters.setWatermarkColor(Color.LTGRAY);

                // 非3端合一版本WPS的激活设定
                wordParameters.setWPSAppLicenseVersion(WPSAppLicenseVersion.DEPRECATED);

                // 对于没有安装WPS的设备，设定WPS下载地址后将自动安装WPS
                wordParameters.setWpsAppDownloadURL(wpsAppGetUrl);

                //  WPS APP 截屏控制
                wordParameters.setScreenshotAllowed(true);

                // 设置WPS App的菜单项权限
                setupWPSAppMenus(wordParameters);

                // 加密的文件
                // 使用此类型时，文档在下载过程中会调用注册的DataProvider解密数据，上传时会再次调用DataProvider加密数据
                // 此种方式保证的是传输安全
                //wordParameters.setDataType(DataType.ENCRYPTED_FILE);

                // 加密的文档数据流
                // 使用此类型时，文档下载时会解密内存中的数据流，上传时会加密内存中文档数据，文件数据不会存储到磁盘
                // 此种方式保证的是传输和存储安全
                //wordParameters.setDataType(DataType.ENCRYPTED_STREAM);

                // 文档数据流
                // 使用此类型时，文档上传下载都在内存中进行，不会存储到磁盘
                // 此种方式保证的是存储安全
                //wordParameters.setDataType(DataType.STREAM);

                // 文档文件，默认的数据类型
                wordParameters.setDataType(Params.DataType.FILE);
                return wordParameters;
            }
        }).openServerWordDocument("通知", downloadUrl, uploadUrl);
    }

    /**
     * 设置 WPS Word 的菜单项权限
     */
    private void setupWPSAppMenus(WPSWordParameters parameters) {

        //
        // 设定不响应用户点击操作的菜单项
        //
        WPSDisallowedActionList list = new WPSDisallowedActionList();
        list.AT_COPY = true; // 不允许拷贝
        list.AT_CUT = true; // 不允许剪切
        list.AT_PASTE = true; // 不允许黏贴
        list.AT_EXPORT_AS_PDF = true;// 不允许导出PDF
        list.AT_PRINT = true;// 不允许打印
        list.AT_SHARE = true;// 不允许分享
        parameters.setDisallowedActionList(list);

        //
        // 设定禁用的菜单项
        //
        WPSMenuBarViewDisabledList disabledList = new WPSMenuBarViewDisabledList();
        // 通过禁用如下菜单可以实现强制修订功能
        disabledList.VT_REVIEW_ACCEPTALLREVISIONS = true; // 禁用接受所有修订的按钮
        disabledList.VT_REVIEW_REJECTALLREVISIONS = true;// 禁用拒绝所有修订的按钮
        disabledList.VT_REVIEW_COMMENTREVISE = true; // 禁用文字批注
        disabledList.VT_REVIEW_ENTERREVISEMODE = true; // 禁用进入审阅按钮
        disabledList.VT_REVIEW_EXITREVISEMODE = true;// 禁用退出审阅按钮
        disabledList.VT_REVIEW_MODIFY_USERNAME = true;// 禁用修改审阅用户按钮
        // 通过禁用如下菜单可以禁止文件外流
        disabledList.VT_FILE_SHARE = true;
        disabledList.VT_FILE_PRINT = true;
        disabledList.VT_FILE_SAVEAS = true;
        // 通过禁用如下菜单可以禁止插入图像和表格
        disabledList.VT_INSERT_PICTURE = true;
        disabledList.VT_INSERT_TABLE = true;
        disabledList.VT_INSERT_SHAPE = true;
        // 通过禁用如下菜单项可以禁止修改批注工具(手指或电容笔)、颜色、线条、模式等
        disabledList.VT_PEN_BYFINGER = true;
        disabledList.VT_PEN_PEN = true;
        disabledList.VT_PEN_HIGHLIGHTER = true;
        disabledList.VT_PEN_ERASER = true;
        disabledList.VT_PEN_COLOR = true;
        disabledList.VT_PEN_THICKNESS = true;
        parameters.setMenuBarViewDisabledList(disabledList);

        //
        // 设定隐藏菜单项
        //
        WPSMenuBarViewHiddenList hiddenList = new WPSMenuBarViewHiddenList();
        hiddenList.VT_REVIEW_SPELLCHECK = true; // 隐藏拼写检查菜单项
        hiddenList.VT_REVIEW_MODIFY_USERNAME = true;// 隐藏更改用户名菜单项
        hiddenList.VT_FILE_SHARE = true;// 隐藏分享菜单项
        hiddenList.VT_PEN_BYFINGER = true;// 隐藏使用手指批注菜单项
        hiddenList.VT_PEN_PEN = true;// 隐藏使用手写笔批注菜单项
        hiddenList.VT_PEN_HIGHLIGHTER = true;// 隐藏使用荧光笔书写菜单项
        hiddenList.VT_PEN_ERASER = true;// 隐藏橡皮擦菜单项
        hiddenList.VT_PEN_COLOR = true;// 隐藏修改笔的颜色的菜单项
        hiddenList.VT_PEN_THICKNESS = true;// 隐藏笔宽度的菜单项

        parameters.setMenuBarViewHiddenList(hiddenList);
    }
    /**
     * 文档事件回调
     */
    private LifecycleListener listener = new SimpleLifecycleListener() {
        @Override
        public void onStartOpenDocument(String filePath, boolean isEncrypted) {
            RhLogger.d("打开文档: " + filePath);
        }

        @Override
        public void onDocumentOpened(String filePath, int docType, Params.RawFileType fileType) {
            RhLogger.d("文档已打开: " + filePath);
  /*
            if (Params.RawFileType.WORD.equals(fileType) || Params.RawFileType.WORD_X.equals(fileType)) {
                try {
                    // 进入审阅模式
                    documentsSDK.enterRevisionMode();
                    // 退出审阅模式
                    documentsSDK.exitRevisionMode();
                    // 接受所有审阅-清稿
                    documentsSDK.acceptAllRevisions();
                    // 拒绝所有审阅
                    documentsSDK.denyAllRevision();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
   */
        }

        @Override
        public void onDocumentOpenFailed(String filePath, Throwable ex) {
            RhLogger.e("文档打开失败: " + filePath, ex);
        }

        @Override
        public void onDocumentClosed(String localPath, boolean isModified) {
            RhLogger.d("关闭文档。 文档" + (isModified ? "内容已经更改" : "内容未更改") + "，文件存储在：" + localPath);
        }

        @Override
        public void onSaveDocument(String localPath) {
            RhLogger.d("文档已保存: " + localPath);
        }

        @Override
        public void onSaveDocumentFailed(String localPath) {
            RhLogger.d("文档保存失败: " + localPath);
        }

        @Override
        public void onDownloadStart(String address) {
            RhLogger.d("开始下载: " + address);
        }

        @Override
        public void onDownloadFailed(int errorCode, String errorMessage, Throwable cause) {
            RhLogger.e("下载失败: " + errorMessage, cause);
        }

        @Override
        public void onDownloadComplete(DownloadResponse response) {
            RhLogger.d("下载成功: " + response.getFilePath());

            RhLogger.d("服务器返回的HEADER为: ");
            final Map<String, List<String>> headers = response.getResponseHeaders();
            if (headers != null && !headers.isEmpty()) {
                for (String s : headers.keySet()) {
                    List<String> headerValues = headers.get(s);
                    RhLogger.d(s + ": " +
                            (headerValues != null && headerValues.size() > 0 ?
                                    Arrays.toString(headerValues.toArray(new String[0])) : "")
                    );
                }
            } else {
                RhLogger.d("没有请求头！");
            }
        }

        @Override
        public void onDownloadProgress(@NonNull ProgressiveData progress) {
            final NetworkMetrics metrics = progress.getMetrics();
            if (metrics != null) {
                RhLogger.d(metrics.getStatus());
            } else {
                RhLogger.d(progress.getType() + "- " + progress.describeProgress());
            }
        }

        @Override
        public void onDownloadCanceled(String downloadUrl, Throwable throwable) {
            RhLogger.e("取消下载: " + downloadUrl, throwable);
        }

        @Override
        public void onStartUpload(String any, String path, String title, String uploadUrl) {
            RhLogger.d(("上传文档: " + path));
        }

        @Override
        public void onUploadProgress(final ProgressiveData progress) {
            final NetworkMetrics metrics = progress.getMetrics();
            if (metrics != null) {
                RhLogger.d(metrics.getStatus());
            } else {
                RhLogger.d(progress.getType() + "- " + progress.describeProgress());
            }
        }

        @Override
        public void onUploadFailed(String filePath, String uploadUrl, String failure, int code, Throwable cause) {
            RhLogger.e("上传失败: " + failure, cause);
        }

        @Override
        public void onUploadCanceled(String filePath, String uploadURL, String reason) {
            RhLogger.e("取消上传: " + reason);
        }

        @Override
        public void onUploadSucceed(UploadResponse response) {
            String str = response.string().trim();
            ResponseType type = response.type();
            switch (type) {
                case JSON:// 返回json
                    RhLogger.d("上传成功，服务器返回的json为：" + str);
                    break;
                case TEXT:// 返回string
                    RhLogger.d("上传成功，服务器返回的txt为：" + str);
                    break;
                case XML:// 返回XML
                    RhLogger.d("上传成功，服务器返回的xml为：" + str);
                    break;
            }

            RhLogger.d("服务器返回的HEADER为: ");
            final Map<String, List<String>> headers = response.getResponseHeaders();
            if (headers != null && !headers.isEmpty()) {
                for (String s : headers.keySet()) {
                    List<String> headerValues = headers.get(s);
                    RhLogger.d(s + ": " +
                            (headerValues != null && headerValues.size() > 0 ?
                                    Arrays.toString(headerValues.toArray(new String[0])) : "")
                    );
                }
            } else {
                RhLogger.d("没有请求头！");
            }
        }
    };
}
