package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.NoticeAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.NoticeListContract;
import com.kisoft.yuejianli.entity.NoticeInfo;
import com.kisoft.yuejianli.entity.ProjectUsInfoDto;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.NoticeListModel;
import com.kisoft.yuejianli.presenter.NoticeListPresenter;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;

/**
 * Description: 消息中心
 */

public class NoticeListFragment extends BaseFragment<NoticeListModel, NoticeListPresenter>
        implements NoticeListContract.NoticeListViewContract, BaseRefreshListener {

    Unbinder unbinder;

    @BindView(R.id.rv_content)
    RecyclerView rvContent;
    @BindView(R.id.ptrl_content)
    PullToRefreshLayout ptrlContent;

    private NoticeListModel model;
    private NoticeListPresenter presenter;

    private int count = 0;
    private int page = 1;
    private int pageCount = 0;
    private int pageSize = 10;
    private int type = 0;   // 0我发起的 1待我审批
    private int pageType;   // 页面类型     0全部
    private List<ProjectUsInfoDto> dtoList = new ArrayList<>();
    private NoticeAdapter adapter;

    /**
     * case "所有通知单":
     * snStatus = "";break;
     * case "未审批":
     * snStatus = "1";break;
     * case "未回复":
     * snStatus = "2";break;
     * case "已完成":
     * snStatus = "3";break;
     */
    private String snStatus;
    private String requestType = Constant.HTTP_GET_SUPERVISION_NOTICE_BY_MY;

    @Override
    public int getRootView() {
        return R.layout.fragment_refresh_list;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle != null) {
            pageType = bundle.getInt("pageType", 0);
            type = bundle.getInt("type", 0);
        }
        model = new NoticeListModel(mContext);
        presenter = new NoticeListPresenter(this, model);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initData();
        initView();
        return mRootView;
    }

    private void initData() {
        switch (type) {
            case 0:
                snStatus = "";
                break;
            case 1:
                snStatus = "1";
                break;
        }
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            layoutManager.setSmoothScrollbarEnabled(false);
            rvContent.setLayoutManager(layoutManager);
            rvContent.setNestedScrollingEnabled(false);
        }
        adapter = new NoticeAdapter(dtoList);
        adapter.setOnItemClickListener(new NoticeAdapter.OnItemClickListener<ProjectUsInfoDto>() {
            @Override
            public void onViewClick(View view, ProjectUsInfoDto data, int position) {
                Intent intent = new Intent();
                switch (data.getSnStatus()) {         //通知单状态；(1=待审批；2=待回复；3=已完成；
                    case "1":
                        intent.putExtra("snStatus", data.getSnStatus());
                        intent.setClass(mContext, PendingActivity.class);
                        break;
                    case "2":
                    case "3":
                        intent.setClass(mContext, NoticeDetailActivity.class);
                        break;
                }
                intent.putExtra("ProjectUsInfoDto", data);
                startActivity(intent);
            }

            @Override
            public void onRootViewClick(View view, ProjectUsInfoDto data, int position) {
                Intent intent = new Intent(mContext, NoticeDetailActivity.class);
                intent.putExtra("ProjectUsInfoDto", data);
                startActivity(intent);
            }
        });
        rvContent.setAdapter(adapter);
    }

    private void initView() {
        ptrlContent.setRefreshListener(this);
    }

    @Override
    public void toReferList(NoticeInfo dtoList) {
        if (dtoList != null) {
            if (pageCount == 0)
                count = dtoList.getCount();
            this.dtoList.addAll(dtoList.getList());
            adapter.notifyDataSetChanged();
        }
    }

    @Override
    public void finshRefresh() {
        ptrlContent.finishRefresh();
        ptrlContent.finishLoadMore();
    }

    @Override
    public void refresh() {
        dtoList.clear();
        page = 1;
        pageCount = 0;
        getData();
    }

    @Override
    public void loadMore() {
        if (dtoList.size() >= count) {
            showToast("没有更多数据了");
            ptrlContent.finishLoadMore();
        } else {
            page++;
            pageCount = dtoList.size() + pageSize;
            getData();
        }
    }

    private void getData() {
        presenter.toReferList(requestType, SettingManager.getInstance().getUserInfo().getId(), snStatus,
                DateUtil.getMonthDate(), SettingManager.getInstance().getProject().getProjectId(),
                count + "", pageSize + "", page + "");
    }

    @Override
    public void onResume() {
        super.onResume();
        refresh();
    }
}
