package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.widget.EditText;
import android.widget.TextView;

import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.PassWordEditContract;
import com.kisoft.yuejianli.entity.LogoutEvent;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.PassWordEditModel;
import com.kisoft.yuejianli.presenter.PassWordEditPresenter;
import com.kisoft.yuejianli.utils.GsonUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import butterknife.BindView;
import butterknife.OnClick;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import com.kisoft.yuejianli.R;

import org.greenrobot.eventbus.EventBus;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * Created by tudou on 2018/6/14.
 */

public class PassWordEditActivity extends BaseActivity<PassWordEditModel, PassWordEditPresenter> implements
        PassWordEditContract.PassWordEditViewContract{

    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.et_old_password)
    EditText etOldPassword;
    @BindView(R.id.et_new_password)
    EditText etNewPassword;

    @BindView(R.id.et_ensure_password)
    EditText etEnsurePassword;


    private UserInfo userInfo;
    private String oldPassWord = "";
    private String newPassWord = "";
    private String ensurePassWord = "";

    private PassWordEditModel model;
    private PassWordEditPresenter presenter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new PassWordEditModel(this);
        presenter = new PassWordEditPresenter(this, model);
        initMVP(model, presenter);
        initView();
        initData();
    }

    private void initView(){
        tvTitle.setText("修改密码");
        etOldPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if(editable != null){
                    oldPassWord = editable.toString().trim();
                }
            }
        });
        etNewPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if(editable != null){
                    newPassWord = editable.toString().trim();
                }
            }
        });
        etEnsurePassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                ensurePassWord = editable.toString().trim();
            }
        });
    }

    private void initData(){
        userInfo = SettingManager.getInstance().getUserInfo();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_password_edit;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    @OnClick(R.id.tv_save)
    public void saveChange(){
        if(newPasswordisOk()){

            mPresenter.changePassWord(userInfo.getId(), oldPassWord, newPassWord);
//            getData();
        }
    }

    //获取详情
    private void getData() {
        Map<String ,Object> pramaras = new HashMap<>();
        pramaras.put("userId" , userInfo.getId());
        pramaras.put("oldPassword" , oldPassWord);
        pramaras.put("newPassword" , newPassWord);
//        pramaras.put("type" , "changePassword");


        Api.getGbkApiserver().getCarbonCopyCount("changePassword", pramaras).enqueue(new retrofit2.Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(retrofit2.Call<NetworkResponse<Object>> call,
                                   retrofit2.Response<NetworkResponse<Object>> response) {
//                response.body().getData().toString()
//                GsonUtil.GsonToMaps(response.body().getData().toString());
                Map<String, Object> map = GsonUtil.GsonToMaps(response.body().getData().toString());
                Double count = Double.valueOf(map.get("count").toString());
                String isOk = count.intValue() + "";
                if("1".equals(isOk)){
                    showToast("密码更改成功！");
//            userInfo.setPassword(newPassWord);
//            SettingManager.getInstance().saveUserInfo(userInfo);
//            finish();
                    userInfo.setPassword("");
                    userInfo.setFaceId("");
                    SettingManager.getInstance().saveUserInfo(userInfo);
                    EventBus.getDefault().post(new LogoutEvent(true));
                    startActivity(new Intent(mContext, LoginActivity.class));
                    finish();
                } else if("2".equals(isOk)){
                    showToast("原始密码不正确");
                } else {
                    showToast("修改失败");
                }
            }

            @Override
            public void onFailure(retrofit2.Call<NetworkResponse<Object>> call, Throwable t) {
                showToast("修改失败");
            }
        });



        /*
        String url = SettingManager.getInstance().getBaseUrl();
        String ss = url + Constant.HTTP_APP_SERVLET + "?" + asUrlParams(pramaras);
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        Request request = new Request.Builder()
                .url(ss)
                .method("GET", null)
                .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                e.printStackTrace();
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String string = response.body().string();
                Map<String, Object> map = GsonUtil.GsonToMaps(string);
                Map<String,Object> data = (Map<String, Object>) map.get("data");
                Double count = Double.valueOf(data.get("flag").toString());
                String isOk = count.intValue() + "";
                if("1".equals(isOk)){
                    showToast("密码更改成功！");
//            userInfo.setPassword(newPassWord);
//            SettingManager.getInstance().saveUserInfo(userInfo);
//            finish();
                    userInfo.setPassword("");
                    userInfo.setFaceId("");
                    SettingManager.getInstance().saveUserInfo(userInfo);
                    EventBus.getDefault().post(new LogoutEvent(true));
                    startActivity(new Intent(mContext, LoginActivity.class));
                    finish();
                } else if("2".equals(isOk)){
                    showToast("原始密码不正确");
                } else {
                    showToast("修改失败");
                }
            }
        });
        */
    }

    /**
     * 只要确保你的编码输入是正确的,就可以忽略掉 UnsupportedEncodingException
     */
    public static String asUrlParams(Map<String, String> source) {
        Iterator<String> it = source.keySet().iterator();
        StringBuilder paramStr = new StringBuilder();
        while (it.hasNext()) {
            String key = it.next();
            String value = source.get(key);

            if (StringUtil.isEmpty(value)) {
                value = "";
            }
            try {
                // URL 编码
                value = URLEncoder.encode(value, "utf-8");
            } catch (UnsupportedEncodingException e) {
                // do nothing
            }
            paramStr.append("&").append(key).append("=").append(value);
        }
        // 去掉第一个&
        return paramStr.substring(1);
    }

    private boolean newPasswordisOk(){
        boolean isOk = false;
        if (StringUtil.isEmpty(oldPassWord)) {
            showToast("输入原密码");
        }
        if(StringUtil.isEmpty(newPassWord)){
            showToast("请输入新密码");
        }else {
            if(newPassWord.length() < 6){
                showToast("密码最少为6位");
            }else {
                if (StringUtil.isEqual(newPassWord ,ensurePassWord)){
                    isOk = true;
                }else {
                    showToast("确认密码和新密码不一致");
                }
            }
        }
        return isOk;
    }


    @Override
    public void showResult(String isOk) {
        if("1".equals(isOk)){
            showToast("密码更改成功！");
//            userInfo.setPassword(newPassWord);
//            SettingManager.getInstance().saveUserInfo(userInfo);
//            finish();
            userInfo.setPassword("");
            userInfo.setFaceId("");
            SettingManager.getInstance().saveUserInfo(userInfo);
            EventBus.getDefault().post(new LogoutEvent(true));
            startActivity(new Intent(mContext, LoginActivity.class));
            finish();
        } else if("2".equals(isOk)){
            showToast("原始密码不正确");
        } else {
            showToast("修改失败");
        }
    }
}
