package com.kisoft.yuejianli.views;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.kisoft.yuejianli.adpter.RecordReadListAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.contract.RecordReadListContract;
import com.kisoft.yuejianli.entity.InfoIssueDto;
import com.kisoft.yuejianli.entity.RecordReadListDto;
import com.kisoft.yuejianli.model.RecordReadListModel;
import com.kisoft.yuejianli.presenter.RecordReadListPresenter;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;
import com.kisoft.yuejianli.R;

/**
 * Description: 我发起的 列表
 * Author     : yanlu
 * Date       : 2019/1/16 17:59
 */
public class RecordReadListFragment extends BaseFragment<RecordReadListModel, RecordReadListPresenter> implements RecordReadListContract.RecordReadListViewContract {
    @BindView(R.id.rv_content)
    RecyclerView rvContent;
    Unbinder unbinder;

    InfoIssueDto.InfoIssueBean bean;

    private RecordReadListAdapter adapter;
    private List<RecordReadListDto> dtoList = new ArrayList<>();

    private RecordReadListModel model;
    private RecordReadListPresenter presenter;

    @Override
    public int getRootView() {
        return R.layout.fragment_enclosure_list;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        bean = (InfoIssueDto.InfoIssueBean) getArguments().getSerializable("data");
        model = new RecordReadListModel(mContext);
        presenter = new RecordReadListPresenter(this, model);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initView();
        return mRootView;
    }

    private void initView() {
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            layoutManager.setSmoothScrollbarEnabled(false);
            rvContent.setLayoutManager(layoutManager);
            rvContent.setNestedScrollingEnabled(false);
        }
        adapter = new RecordReadListAdapter(dtoList);
        rvContent.setAdapter(adapter);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    @Override
    public void RecordReadListBack(List<RecordReadListDto> dtoList) {
        this.dtoList.clear();
        if (dtoList != null) {
            this.dtoList.addAll(dtoList);
        }
        adapter.notifyDataSetChanged();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && presenter != null && bean != null) {
            presenter.getRecordReadList(bean.getId());
        }
    }
}
