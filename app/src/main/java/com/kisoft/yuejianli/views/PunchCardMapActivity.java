package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.format.DateFormat;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.amap.api.maps.AMap;
import com.amap.api.maps.AMapUtils;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.MapView;
import com.amap.api.maps.model.Circle;
import com.amap.api.maps.model.CircleOptions;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.MyLocationStyle;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.HuaweiService;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.PunchCardMapContract;
import com.kisoft.yuejianli.entity.AddressPoint;
import com.kisoft.yuejianli.entity.AddressPointTypeAdapter;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.FunctionPermission;
import com.kisoft.yuejianli.entity.InfoIssueDto;
import com.kisoft.yuejianli.entity.MailInfo;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.entity.event.FaceImageEvent;
import com.kisoft.yuejianli.face.model.FaceBean;
import com.kisoft.yuejianli.face.model.FaceSearch;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.AttendanceRulesModel;
import com.kisoft.yuejianli.model.PunchCardMapModel;
import com.kisoft.yuejianli.presenter.PunchCardMapPresenter;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.common.util.LogUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

import butterknife.BindView;
import butterknife.OnClick;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Description: 打卡地图界面
 * Author     : yanlu
 * Date       : 2018/12/13 17:15
 */

public class PunchCardMapActivity extends BaseActivity<PunchCardMapModel, PunchCardMapPresenter> implements
        PunchCardMapContract.PunchCardMapViewContract {


    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.mv_map)
    /**地图**/
            MapView mvMap;

    @BindView(R.id.tv_map_location)
    /**我的位置**/
            TextView tvMapLocation;

    @BindView(R.id.tv_map_status)
    /**状态**/
            TextView tvMapStatus;

    @BindView(R.id.tv_map_address)
    /**定位地址**/
            TextView tvMapAddress;

    @BindView(R.id.et_map_remarks)
    /**备注**/
            EditText etMapRemarks;
    @BindView(R.id.btn_map_punch)
    /**打卡按钮**/
            Button btnMapPunch;
    @BindView(R.id.btnFacePunch)
    Button btnFacePunch;


    private PunchCardMapModel model;
    private PunchCardMapPresenter mPresenter;
//    private UserInfo userInfo;

    private UserInfo userInfoCache;


    private AttendanceRulesModel userInfo;
    private ProjectInfo projectInfo;
    private String today = DateUtil.getTodayDate();
    private List<AttendanceRulesModel> ruleList;
    private boolean isfast = true;
    //初始化地图控制器对象
    AMap aMap;
    //当前位置
    MyLocationStyle myLocationStyle;
    //目标区域
    Circle circle;
    //目标坐标
    LatLng latLng;

    private MapHanlder mHanlder;
    /**
     * 是否打卡
     **/
    private String cardStatus;
    /**
     * 上班打卡  or  下班打卡
     * takeUpCard   or  takeDownCard
     **/
    private String takeCard;
    private Timer timer;
    /**
     * 我的当前位置
     **/
    private LatLng myLatLng;
    /**
     * 打卡状态
     * 1：正常  2：迟到    3：早退    4：旷工  5:请假   6：外勤   7：补卡
     */

    int punchCardState = 1;
    AMapLocation locationdata = null;

    /**
     * 排班方式
     * true 自由排班，false 固定排班
     * // 0:固定四次班制 1:自由打卡 2：固定两次班制
     */
    private String scheduleWay = "";

    private boolean isDrawCircle = true;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        cardStatus = getIntent().getStringExtra("cardStatus");
        takeCard = getIntent().getStringExtra("takeCard");
        model = new PunchCardMapModel(this);
        mPresenter = new PunchCardMapPresenter(this, model);
        initMVP(model, mPresenter);
        initData();

        //在activity执行onCreate时执行mMapView.onCreate(savedInstanceState)，创建地图
        mvMap.onCreate(savedInstanceState);
        mHanlder = new MapHanlder();
        timer = new Timer();
        initMap();
        FunctionPermission permissionFunction = SettingManager.getInstance().getPermissionFunction();
        if (permissionFunction != null && permissionFunction.isHasFace()) {
            btnFacePunch.setVisibility(View.VISIBLE);
        } else {
            btnFacePunch.setVisibility(View.GONE);
        }
//      initView();

    }

    private void initMap() {
        if (aMap == null) {
            aMap = mvMap.getMap();
        }
        myLocationStyle = new MyLocationStyle();//初始化定位蓝点样式类
        myLocationStyle.myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE);//定位一次，且将视角移动到地图中心点。
        // 定位精度圈
        myLocationStyle.strokeColor(Color.argb(0, 0, 0, 0));// 设置圆形的边框颜色
        myLocationStyle.radiusFillColor(Color.argb(0, 0, 0, 0));// 设置圆形的填充颜色
        // myLocationStyle.interval(10000); //设置连续定位模式下的定位间隔，只在连续定位模式下生效，单次定位模式下不会生效。单位为毫秒。
        aMap.setMyLocationStyle(myLocationStyle);//设置定位蓝点的Style
        aMap.getUiSettings().setMyLocationButtonEnabled(true);//设置默认定位按钮是否显示，非必需设置。
        aMap.getUiSettings().setLogoBottomMargin(-50);  //  隐藏logo
        aMap.setMyLocationEnabled(true);// 设置为true表示启动显示定位蓝点，false表示隐藏定位蓝点并不进行定位，默认是false

        //设置希望展示的地图缩放级别
        aMap.moveCamera(CameraUpdateFactory.zoomTo(13));
        aMap.getUiSettings().setCompassEnabled(true);
        initLocation();
    }

    private void initData() {
        userInfoCache = SettingManager.getInstance().getUserInfo();
//        scheduleWay = "1".equals(userInfo.getScheduleWay());
        projectInfo = SettingManager.getInstance().getProject();
        userInfo = new AttendanceRulesModel(this);

        FunctionPermission permissionFunction = SettingManager.getInstance().getPermissionFunction();
        if (permissionFunction != null && permissionFunction.isHasMoreAttendancePointsFunction()) {
            getAttendanceRulesData();
        }else{
            // 兼容服务端未更新版本
            userInfo.setLatAndLong(userInfoCache.getLatAndLong());
            userInfo.setEffectRange(userInfoCache.getEffectRange());
            userInfo.setBeginTimeStr(userInfoCache.getBeginTimeStr());
            userInfo.setEndTimeStr(userInfoCache.getEndTimeStr());
            userInfo.setWorkFlexTimes(userInfoCache.getWorkFlexTimes());
            userInfo.setLateTimes(userInfoCache.getLateTimes());
            userInfo.setAbsentTimes(userInfoCache.getAbsentTimes());
            userInfo.setFieldState(userInfoCache.getFieldState());
            userInfo.setScheduleWay(userInfoCache.getScheduleWay());
            userInfo.setWorkDay(userInfoCache.getWorkDay());
            userInfo.setUserTel(userInfoCache.getUserTel());
            userInfo.setUserPotoPath(userInfoCache.getUserPotoPath());
            userInfo.setBeginTimeHou(userInfoCache.getBeginTimeHou());
            userInfo.setBeginTimeMin(userInfoCache.getBeginTimeMin());
            userInfo.setEndTimeHou(userInfoCache.getEndTimeHou());
            userInfo.setEndTimeMin(userInfoCache.getEndTimeMin());
            userInfo.setBeginTimeHou1(userInfoCache.getBeginTimeHou1());
            userInfo.setBeginTimeMin1(userInfoCache.getBeginTimeMin1());
            userInfo.setEndTimeHou1(userInfoCache.getEndTimeHou1());
            userInfo.setEndTimeMin1(userInfoCache.getEndTimeMin1());

            getRuleLatLngOld();
        }

    }

    private void initView() {
        LogUtil.e("initView");
        tvTitle.setText("考勤打卡");
        LogUtil.e("initView 考勤打卡");
        setBtnStyleCanClick();
//        initUserInfo();

//      mHanlder.sendEmptyMessageAtTime(100, 3000);
        //1：今天未打卡  2: 上班已打卡  3. 今天打卡结束
//        if (cardStatus.equals("1")) {
//            setBtnStyleCanClick();
//        } else if (cardStatus.equals("2")) {
//            if (takeCard.equals("takeUpCard")) {     //上班打卡
//                setBtnStyleUnClick();
//            } else if (takeCard.equals("takeDownCard")) { //下班打卡
//                setBtnStyleCanClick();
//            }
//        } else if (cardStatus.equals("3")) {
//            setBtnStyleUnClick();
//        }
//        if (scheduleWay) {
//            //    tvMapStatus.setVisibility(View.GONE);
//        }
    }


    /**
     * 可点击样式
     **/
    private void setBtnStyleCanClick() {
        LogUtil.e("setBtnStyleCanClick");
        btnMapPunch.setBackgroundColor(getResources().getColor(R.color.sign_leave));
        btnMapPunch.setEnabled(true);
        btnFacePunch.setBackgroundColor(getResources().getColor(R.color.sign_leave));
        btnFacePunch.setEnabled(true);
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                LogUtil.e("timer.schedule");
                mHanlder.sendEmptyMessage(200);
            }
        }, 0, 1000);
    }

    /**
     * 不可点击样式
     **/
//    private void setBtnStyleUnClick(){
//        btnMapPunch.setBackgroundColor(getResources().getColor(R.color.nomal_btn_color));
//        timer.cancel();
//        btnMapPunch.setText("已打卡");
//        btnMapPunch.setEnabled(false);
//    }
    @Override
    public int getLayoutId() {
        return R.layout.activity_punch_card_map;
    }


    private void initUserInfo2() {

        FunctionPermission permissionFunction = SettingManager.getInstance().getPermissionFunction();
        if (permissionFunction != null && permissionFunction.isHasMoreAttendancePointsFunction()) {
            // 多考勤点

            if (userInfo != null && locationdata != null && ruleList != null) {

                if (isDrawCircle) {
                    isDrawCircle = false;
                    for (int i = 0; i < ruleList.size(); i++) {
                        AttendanceRulesModel rulesModel = ruleList.get(i);
                        String[] latAndLong = rulesModel.getLatAndLong().split(",");
                        LatLng rulesLatLng = new LatLng(0, 0);
                        if (latAndLong.length < 2) {
                            rulesLatLng = new LatLng(0, 0);
                        } else {
                            rulesLatLng = new LatLng(Double.parseDouble(latAndLong[1]), Double.parseDouble(latAndLong[0]));
                        }
                        aMap.addCircle(new CircleOptions().
                                center(rulesLatLng).
                                radius(StringUtil.isEmpty(rulesModel.getEffectRange()) ? 100 :
                                        Integer.parseInt(rulesModel.getEffectRange())).
                                fillColor(getResources().getColor(R.color.map_fill_color)).
                                strokeColor(getResources().getColor(R.color.chat_color_11)).
                                strokeWidth(15));
                    }

                    //当前时间
                    try {
                        Date nowDate = DateUtil.stringToDate(DateUtil.getHmTime(), DateUtil.HMS);

                        if (takeCard.equals("takeUpCard")) {     //上午上班打卡
                            beginState(nowDate, true);
                        } else if (takeCard.equals("takeUpCard1")) { //上午下班打卡
                            beginState(nowDate, true);
                        } else if (takeCard.equals("takeDownCard")) { //下午上班打卡
                            beginState(nowDate, false);
                        } else if (takeCard.equals("takeDownCard1")) { //下午下班打卡
                            beginState(nowDate, false);
                        }
                    } catch (ParseException e) {
                        Log.e("PunchCardMapActivity", "时间格式化异常：" + e);
                    }
                }
            }
        } else {
            // 单考勤点
            LogUtil.e("initUserInfo");
//            String[] latAndLong = userInfo.getLatAndLong().split(",");
//            if (latAndLong.length < 2) {
//                latLng = new LatLng(0, 0);
//            } else {
//                latLng = new LatLng(Double.parseDouble(latAndLong[1]), Double.parseDouble(latAndLong[0]));
//            }

            if (userInfo != null && locationdata != null) {
                if (circle != null) {
                    circle.remove();
                }
                circle = aMap.addCircle(new CircleOptions().
                        center(latLng).
                        radius(StringUtil.isEmpty(userInfo.getEffectRange()) ? 100 :
                                Integer.parseInt(userInfo.getEffectRange())).
                        fillColor(getResources().getColor(R.color.map_fill_color)).
                        strokeColor(getResources().getColor(R.color.chat_color_11)).
                        strokeWidth(15));
                //当前时间
                try {
                    Date nowDate = DateUtil.stringToDate(DateUtil.getHmTime(), DateUtil.HMS);

                    if (takeCard.equals("takeUpCard")) {     //上午上班打卡
                        beginState(nowDate, true);
                    } else if (takeCard.equals("takeUpCard1")) { //上午下班打卡
                        beginState(nowDate, true);
                    } else if (takeCard.equals("takeDownCard")) { //下午上班打卡
                        beginState(nowDate, false);
                    } else if (takeCard.equals("takeDownCard1")) { //下午下班打卡
                        beginState(nowDate, false);
                    }
                } catch (ParseException e) {
                    Log.e("PunchCardMapActivity", "时间格式化异常：" + e);
                }
            }
        }
    }

    private void initUserInfo() {
        initUserInfo2();
        /*
        LogUtil.e("initUserInfo");
        String[] latAndLong = userInfo.getLatAndLong().split(",");
        if (latAndLong.length < 2) {
            latLng = new LatLng(0, 0);
        } else {
            latLng = new LatLng(Double.parseDouble(latAndLong[1]), Double.parseDouble(latAndLong[0]));
        }
        if (circle != null) {
            circle.remove();
        }
        circle = aMap.addCircle(new CircleOptions().
                center(latLng).
                radius(StringUtil.isEmpty(userInfo.getEffectRange()) ? 100 :
                        Integer.parseInt(userInfo.getEffectRange())).
                fillColor(getResources().getColor(R.color.map_fill_color)).
                strokeColor(getResources().getColor(R.color.chat_color_11)).
                strokeWidth(15));

//        LatLng latLng1 = new LatLng(34.14432,108.913768);
//
//        LatLng latLng2 = new LatLng(34.137216,108.874286);
//
//        LatLng latLng3 = new LatLng(34.157105,108.942264);
//
//        LatLng latLng4 = new LatLng(34.165912,108.86536);
////        aMap.addCircle(new CircleOptions().
////                center(latLng).
////                radius(500).
////                fillColor(getResources().getColor(R.color.map_fill_color)).
////                strokeColor(getResources().getColor(R.color.chat_color_11)).
////                strokeWidth(15));
////        aMap.addCircle(new CircleOptions().
////                center(latLng1).
////                radius(1200).
////                fillColor(getResources().getColor(R.color.map_fill_color)).
////                strokeColor(getResources().getColor(R.color.chat_color_11)).
////                strokeWidth(15));
////
////        aMap.addCircle(new CircleOptions().
////                center(latLng2).
////                radius(1000).
////                fillColor(getResources().getColor(R.color.map_fill_color)).
////                strokeColor(getResources().getColor(R.color.chat_color_11)).
////                strokeWidth(15));
////
////        aMap.addCircle(new CircleOptions().
////                center(latLng3).
////                radius(2000).
////                fillColor(getResources().getColor(R.color.map_fill_color)).
////                strokeColor(getResources().getColor(R.color.chat_color_11)).
////                strokeWidth(15));
        //当前时间
        try {
            Date nowDate = DateUtil.stringToDate(DateUtil.getHmTime(), DateUtil.HMS);

            if (takeCard.equals("takeUpCard")) {     //上午上班打卡
                beginState(nowDate, true);
            } else if (takeCard.equals("takeUpCard1")) { //上午下班打卡
                beginState(nowDate, true);
            } else if (takeCard.equals("takeDownCard")) { //下午上班打卡
                beginState(nowDate, false);
            } else if (takeCard.equals("takeDownCard1")) { //下午下班打卡
                beginState(nowDate, false);
            }
        } catch (ParseException e) {
            Log.e("PunchCardMapActivity", "时间格式化异常：" + e);
        }
         */
    }

    /**
     * 下班打卡打卡状态判定
     */
    private void endState(Date nowDate, boolean isAm) throws ParseException {
        String endTimeStr = isAm ? (userInfo.getEndTimeHou() + ":" + userInfo.getEndTimeMin()) :
                (userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());//19:00
        Date endDate = DateUtil.HMS.parse(endTimeStr);
        if (nowDate.getTime() - endDate.getTime() < 0) {
            tvMapStatus.setText("早退");
            tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));
            punchCardState = 3;
        }
    }

    /**
     * 上班打卡状态判定
     */
    private void beginState(Date nowDate, boolean isAm) throws ParseException {
        if (locationdata == null || userInfo == null) {
            return;
        }
        LogUtil.e("Latitude=" + locationdata.getLatitude() + " Longitude=" + locationdata.getLongitude());
//        myLatLng = new LatLng(locationdata.getLatitude(), locationdata.getLongitude());
//        double d = AMapUtils.calculateLineDistance(latLng, myLatLng);
//        LogUtil.e("beginState distance=" + d + " getEffectRange=" + userInfo.getEffectRange());
        tvMapStatus.setVisibility(View.VISIBLE);
//        1：正常  2：迟到    3：早退    4：旷工 5:请假  6：外勤  7：补卡

        // 0:固定四次班制 1:自由打卡 2：固定两次班制
        if (scheduleWay.equals("0")) {
            //固定班制
            // 弹性时间
            String workFlexTimes = userInfo.getWorkFlexTimes();  //15
            // isAm ? 上午 : 下午
            String beginTimeStr = isAm ? (userInfo.getBeginTimeHou() + ":" + userInfo.getBeginTimeMin()) :
                    (userInfo.getBeginTimeHou1() + ":" + userInfo.getBeginTimeMin1());   //08:30
            //String lateTimes = userInfo.getLateTimes();//25
            // 旷工时间
            String absentTimes = userInfo.getAbsentTimes(); //30
            //上午上班时间 or 下午上班时间
            Date date = DateUtil.HMS.parse(beginTimeStr);
            SimpleDateFormat formatter0 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            //String dateString0 = formatter0.format(date);
            Log.d("==上午上班时间==", formatter0.format(date));
            //弹性时间
            Date FlexDate = DateUtil.M.parse(workFlexTimes);
            //严重迟到(弃用)   //Date lateDate = DateUtil.M.parse(lateTimes);
            //旷工
            Date absentDate = DateUtil.M.parse(absentTimes);
            /**时间差**/
            long data = 0;
            //固定打卡  允许外勤
            if (userInfo.getFieldState().equals("1")) {
                if (locationdata != null) {
                    myLatLng = new LatLng(locationdata.getLatitude(), locationdata.getLongitude());
                    double distance = AMapUtils.calculateLineDistance(latLng, myLatLng);
                    //EffectRange 有效范围
                    /*
                    if (StringUtil.isEmpty(userInfo.getEffectRange())) {
                        if (takeCard.equals("takeUpCard") || takeCard.equals("takeDownCard")) {   //上午上班打卡or下午上班打卡
                            Date nowdate = new Date();
                            long kuanggongshijian = Integer.valueOf(absentTimes) * 60 * 1000;
                            long tanxingshijan = Integer.valueOf(workFlexTimes) * 60 * 1000;
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowdate).split(" ");
                            String timesss = dateshijian[0] + " " + beginTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            Log.d("旷工时间=", nowdate.getTime() + "====" + nnn.getTime() + "==" + kuanggongshijian +
                                    "上班时间：" + dateshijian[0] + " " + beginTimeStr);
                            if (nowdate.getTime() <= nnn.getTime()) {
                                tvMapStatus.setText("正常");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                punchCardState = 1;
                            } else if (nowDate.getTime() - date.getTime() > kuanggongshijian) {
                                tvMapStatus.setText("旷工");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                punchCardState = 4;
                            } else if (nowDate.getTime() - date.getTime() <= tanxingshijan) {
                                tvMapStatus.setText("迟到");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                punchCardState = 2;
                            }
                        } else if (takeCard.equals("takeUpCard1") || takeCard.equals("takeDownCard1")) {   //上午下班打卡or
                            // 下午下班打卡
                            Date nowdate = new Date();
                            String endTimeStr = isAm ? (userInfo.getEndTimeHou() + ":" + userInfo.getEndTimeMin()) :
                                    (userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());//19:00
                            //Date endDate = DateUtil.HMS.parse(endTimeStr);
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowdate).split(" ");
                            String timesss = dateshijian[0] + " " + endTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            if (nowDate.getTime() < nnn.getTime()) {
                                tvMapStatus.setText("早退");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                punchCardState = 3;
                            } else {
                                tvMapStatus.setText("正常");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                punchCardState = 1;
                            }
                        }
                    } else {//有效范围不为空
                        */
                    Integer ran = 0;
                    if (StringUtil.isEmpty(userInfo.getEffectRange())) {
                        ran = 0;
                    } else {
                        ran = Integer.parseInt(userInfo.getEffectRange());
                    }
                    if (distance < ran) {//在考勤位置内 if (distance < ran) {//在考勤位置内
                        if (takeCard.equals("takeUpCard") || takeCard.equals("takeDownCard")) {   //上午上班打卡or下午上班打卡
                            Date nowdate = new Date();
                            long kuanggongshijian = Integer.valueOf(absentTimes) * 60 * 1000;
                            long tanxingshijan = Integer.valueOf(workFlexTimes) * 60 * 1000;
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowdate).split(" ");
                            String timesss = dateshijian[0] + " " + beginTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            Log.d("旷工时间=",
                                    nowdate.getTime() + "====" + nnn.getTime() + "==" + kuanggongshijian +
                                            "上班时间：" + dateshijian[0] + " " + beginTimeStr);
                            Log.d("==时间==",
                                    "当前时间=" + nowDate.getTime() + "上班时间=" + date.getTime() + "<=弹性时间" + tanxingshijan);
                            if (nowdate.getTime() <= nnn.getTime() + tanxingshijan) {//在考勤范围内
                                tvMapStatus.setText("正常");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                tvMapLocation.setText("（在考勤位置内）");
                                punchCardState = 1;
                            } else if (nowDate.getTime() - date.getTime() > kuanggongshijian) {
                                tvMapStatus.setText("旷工");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                tvMapLocation.setText("（在考勤位置内）");
                                punchCardState = 4;
                            } else if (nowDate.getTime() - date.getTime() <= tanxingshijan * 2) {
                                tvMapStatus.setText("迟到");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                tvMapLocation.setText("（在考勤位置内）");
                                punchCardState = 2;
                            }
                        } else if (takeCard.equals("takeUpCard1") || takeCard.equals("takeDownCard1")) {
                            //上午下班打卡or下午下班打卡
                            Date nowdate = new Date();
                            String endTimeStr = isAm ?
                                    (userInfo.getEndTimeHou() + ":" + userInfo.getEndTimeMin()) :
                                    (userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());//19:00
                            //Date endDate = DateUtil.HMS.parse(endTimeStr);
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowDate).split(" ");
                            String timesss = dateshijian[0] + " " + endTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            if (nowDate.getTime() < nnn.getTime()) {
                                tvMapStatus.setText("早退");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                tvMapLocation.setText("（在考勤位置内）");
                                punchCardState = 3;
                            } else {
                                tvMapStatus.setText("正常");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                tvMapLocation.setText("（在考勤位置内）");
                                punchCardState = 1;
                            }
                        }
                    } else {//不在考勤位置内
                        if (takeCard.equals("takeUpCard") || takeCard.equals("takeDownCard")) {   //上午上班打卡or下午上班打卡
                            Date nowdate = new Date();
                            long kuanggongshijian = Integer.valueOf(absentTimes) * 60 * 1000;
                            long tanxingshijan = Integer.valueOf(workFlexTimes) * 60 * 1000;
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowdate).split(" ");
                            String timesss = dateshijian[0] + " " + beginTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            Log.d("旷工时间=",
                                    nowdate.getTime() + "====" + nnn.getTime() + "==" + kuanggongshijian +
                                            "上班时间：" + dateshijian[0] + " " + beginTimeStr);
                            Log.d("==时间==",
                                    "当前时间=" + nowDate.getTime() + "上班时间=" + date.getTime() + "<=弹性时间" + tanxingshijan);
                            if (nowdate.getTime() <= nnn.getTime() + tanxingshijan) {//在考勤范围内
                                tvMapStatus.setText("正常");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                tvMapLocation.setText("（不在考勤位置内）");
                                punchCardState = 1;
                            } else if (nowDate.getTime() - date.getTime() > kuanggongshijian) {
                                tvMapStatus.setText("旷工");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                tvMapLocation.setText("（不在考勤位置内）");
                                punchCardState = 4;
                            } else if (nowDate.getTime() - date.getTime() <= tanxingshijan * 2) {
                                tvMapStatus.setText("迟到");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                tvMapLocation.setText("（不在考勤位置内）");
                                punchCardState = 2;
                            }
                        } else if (takeCard.equals("takeUpCard1") || takeCard.equals("takeDownCard1")) {
                            //上午下班打卡or下午下班打卡
                            Date nowdate = new Date();
                            String endTimeStr = isAm ?
                                    (userInfo.getEndTimeHou() + ":" + userInfo.getEndTimeMin()) :
                                    (userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());//19:00
                            //Date endDate = DateUtil.HMS.parse(endTimeStr);
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowDate).split(" ");
                            String timesss = dateshijian[0] + " " + endTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            if (nowDate.getTime() < nnn.getTime()) {
                                tvMapStatus.setText("早退");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                tvMapLocation.setText("（不在考勤位置内）");
                                punchCardState = 3;
                            } else {
                                tvMapStatus.setText("正常");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                tvMapLocation.setText("（不在考勤位置内）");
                                punchCardState = 1;
                            }
                        }
                    }
                    //}
                } else {
                    tvMapStatus.setVisibility(View.GONE);
                }
            } else {//固定班制  不允许外勤
                if (locationdata != null) {
                    myLatLng = new LatLng(locationdata.getLatitude(), locationdata.getLongitude());
                    double distance = AMapUtils.calculateLineDistance(latLng, myLatLng);
                    //EffectRange 有效范围
                    /*
                    if (StringUtil.isEmpty(userInfo.getEffectRange())) {
                        if (takeCard.equals("takeUpCard") || takeCard.equals("takeDownCard")) {   //上午上班打卡or下午上班打卡
                            Date nowdate = new Date();
                            long kuanggongshijian = Integer.valueOf(absentTimes) * 60 * 1000;
                            long tanxingshijan = Integer.valueOf(workFlexTimes) * 60 * 1000;
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowdate).split(" ");
                            String timesss = dateshijian[0] + " " + beginTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            Log.d("旷工时间=", nowdate.getTime() + "====" + nnn.getTime() + "==" + kuanggongshijian +
                                    "上班时间：" + dateshijian[0] + " " + beginTimeStr);
                            if (nowdate.getTime() <= nnn.getTime()) {
                                tvMapStatus.setText("正常");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                punchCardState = 1;
                            } else if (nowDate.getTime() - date.getTime() > kuanggongshijian) {
                                tvMapStatus.setText("旷工");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                punchCardState = 4;
                            } else if (nowDate.getTime() - date.getTime() <= tanxingshijan) {
                                tvMapStatus.setText("迟到");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                punchCardState = 2;
                            }
                        } else if (takeCard.equals("takeUpCard1") || takeCard.equals("takeDownCard1")) {   //上午下班打卡or
                            // 下午下班打卡
                            Date nowdate = new Date();
                            String endTimeStr = isAm ? (userInfo.getEndTimeHou() + ":" + userInfo.getEndTimeMin()) :
                                    (userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());//19:00
                            //Date endDate = DateUtil.HMS.parse(endTimeStr);
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowdate).split(" ");
                            String timesss = dateshijian[0] + " " + endTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            if (nowDate.getTime() < nnn.getTime()) {
                                tvMapStatus.setText("早退");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                punchCardState = 3;
                            } else {
                                tvMapStatus.setText("正常");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                punchCardState = 1;
                            }
                        }
                    } else {//有效范围不为空
                        */
                    Integer ran = 0;
                    if (StringUtil.isEmpty(userInfo.getEffectRange())) {
                        ran = 0;
                    } else {
                        ran = Integer.parseInt(userInfo.getEffectRange());
                    }
                    if (distance < ran) {//在考勤位置内
                        if (takeCard.equals("takeUpCard") || takeCard.equals("takeDownCard")) {   //上午上班打卡or下午上班打卡
                            Date nowdate = new Date();
                            long kuanggongshijian = Integer.valueOf(absentTimes) * 60 * 1000;
                            long tanxingshijan = Integer.valueOf(workFlexTimes) * 60 * 1000;
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowdate).split(" ");
                            String timesss = dateshijian[0] + " " + beginTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            Log.d("旷工时间=",
                                    nowdate.getTime() + "====" + nnn.getTime() + "==" + kuanggongshijian +
                                            "上班时间：" + dateshijian[0] + " " + beginTimeStr);
                            Log.d("==时间==",
                                    "当前时间=" + nowDate.getTime() + "上班时间=" + date.getTime() + "<=弹性时间" + tanxingshijan);
                            if (nowdate.getTime() <= nnn.getTime() + tanxingshijan) {
                                tvMapStatus.setText("正常");
                                tvMapLocation.setText("（在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                punchCardState = 1;
                            } else if (nowDate.getTime() - date.getTime() > kuanggongshijian) {
                                tvMapStatus.setText("旷工");
                                tvMapLocation.setText("（在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                punchCardState = 4;
                            } else if (nowDate.getTime() - date.getTime() <= tanxingshijan * 2) {
                                tvMapStatus.setText("迟到");
                                tvMapLocation.setText("（在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                punchCardState = 2;
                            }
                        } else if (takeCard.equals("takeUpCard1") || takeCard.equals("takeDownCard1")) {
                            //上午下班打卡or下午下班打卡
                            Date nowdate = new Date();
                            String endTimeStr = isAm ?
                                    (userInfo.getEndTimeHou() + ":" + userInfo.getEndTimeMin()) :
                                    (userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());//19:00
                            //Date endDate = DateUtil.HMS.parse(endTimeStr);
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowDate).split(" ");
                            String timesss = dateshijian[0] + " " + endTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            if (nowDate.getTime() < nnn.getTime()) {
                                tvMapStatus.setText("早退");
                                tvMapLocation.setText("（在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                punchCardState = 3;
                            } else {
                                tvMapStatus.setText("正常");
                                tvMapLocation.setText("（在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                punchCardState = 1;
                            }
                        }
                    } else {//不在考勤位置内
                        //不允许打卡
                        if (takeCard.equals("takeUpCard") || takeCard.equals("takeDownCard")) {   //上午上班打卡or下午上班打卡
                            Date nowdate = new Date();
                            long kuanggongshijian = Integer.valueOf(absentTimes) * 60 * 1000;
                            long tanxingshijan = Integer.valueOf(workFlexTimes) * 60 * 1000;
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowdate).split(" ");
                            String timesss = dateshijian[0] + " " + beginTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            Log.d("旷工时间=",
                                    nowdate.getTime() + "====" + nnn.getTime() + "==" + kuanggongshijian +
                                            "上班时间：" + dateshijian[0] + " " + beginTimeStr);
                            Log.d("==时间==",
                                    "当前时间=" + nowDate.getTime() + "上班时间=" + date.getTime() + "<=弹性时间" + tanxingshijan);
                            if (nowdate.getTime() <= nnn.getTime() + tanxingshijan) {
                                tvMapStatus.setText("正常");
                                tvMapLocation.setText("（不在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                btnMapPunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnMapPunch.setEnabled(false);
                                btnFacePunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnFacePunch.setEnabled(false);
                                punchCardState = 1;
                            } else if (nowDate.getTime() - date.getTime() > kuanggongshijian) {
                                tvMapStatus.setText("旷工");
                                tvMapLocation.setText("（不在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                btnMapPunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnMapPunch.setEnabled(false);
                                btnFacePunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnFacePunch.setEnabled(false);
                                punchCardState = 4;
                            } else if (nowDate.getTime() - date.getTime() <= tanxingshijan * 2) {
                                tvMapStatus.setText("迟到");
                                tvMapLocation.setText("（不在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                btnMapPunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnMapPunch.setEnabled(false);
                                btnFacePunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnFacePunch.setEnabled(false);
                                punchCardState = 2;
                            }
                        } else if (takeCard.equals("takeUpCard1") || takeCard.equals("takeDownCard1")) {
                            //上午下班打卡or下午下班打卡
                            Date nowdate = new Date();
                            String endTimeStr = isAm ?
                                    (userInfo.getEndTimeHou() + ":" + userInfo.getEndTimeMin()) :
                                    (userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());//19:00
                            //Date endDate = DateUtil.HMS.parse(endTimeStr);
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowDate).split(" ");
                            String timesss = dateshijian[0] + " " + endTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            if (nowDate.getTime() < nnn.getTime()) {
                                tvMapStatus.setText("早退");
                                tvMapLocation.setText("（不在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                btnMapPunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnMapPunch.setEnabled(false);
                                btnFacePunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnFacePunch.setEnabled(false);
                                punchCardState = 3;
                            } else {
                                tvMapStatus.setText("正常");
                                tvMapLocation.setText("（不在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                btnMapPunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnMapPunch.setEnabled(false);
                                btnFacePunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnFacePunch.setEnabled(false);
                                punchCardState = 1;
                            }
                        }
                    }
//                    }
                } else {
                    tvMapStatus.setVisibility(View.GONE);
                }
            }

        } else if (scheduleWay.equals("2")) {
            // 0:固定四次班制 1:自由打卡 2：固定两次班制
            //固定两次班制

            //固定打卡  允许外勤
            if (userInfo.getFieldState().equals("1")) {
                if (locationdata != null) {
                    myLatLng = new LatLng(locationdata.getLatitude(), locationdata.getLongitude());
                    double distance = AMapUtils.calculateLineDistance(latLng, myLatLng);
                    //EffectRange 有效范围
                    Integer ran = 0;
                    if (StringUtil.isEmpty(userInfo.getEffectRange())) {
                        ran = 0;
                    } else {
                        ran = Integer.parseInt(userInfo.getEffectRange());
                    }
                    if (distance < ran) {//在考勤位置内
                        if (takeCard.equals("takeUpCard") || takeCard.equals("takeDownCard")) {   //上午上班打卡or下午上班打卡

                            // 弹性时间
                            String workFlexTimes = userInfo.getWorkFlexTimes();  //15
                            // isAm ? 上午
                            // 上班
                            String beginTimeStr = userInfo.getBeginTimeHou() + ":" + userInfo.getBeginTimeMin();
                            //08:30
                            //String lateTimes = userInfo.getLateTimes();//25
                            // 旷工时间
                            String absentTimes = userInfo.getAbsentTimes(); //30
                            //上午上班时间 or 下午上班时间
                            Date date = DateUtil.HMS.parse(beginTimeStr);
                            SimpleDateFormat formatter0 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                            //String dateString0 = formatter0.format(date);
                            Log.d("==上午上班时间==", formatter0.format(date));
                            //弹性时间
                            Date FlexDate = DateUtil.M.parse(workFlexTimes);
                            //严重迟到(弃用)   //Date lateDate = DateUtil.M.parse(lateTimes);
                            //旷工
                            Date absentDate = DateUtil.M.parse(absentTimes);
                            /**时间差**/
                            long data = 0;

                            Date nowdate = new Date();
                            long kuanggongshijian = Integer.valueOf(absentTimes) * 60 * 1000;
                            long tanxingshijan = Integer.valueOf(workFlexTimes) * 60 * 1000;
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowdate).split(" ");
                            String timesss = dateshijian[0] + " " + beginTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            Log.d("旷工时间=",
                                    nowdate.getTime() + "====" + nnn.getTime() + "==" + kuanggongshijian +
                                            "上班时间：" + dateshijian[0] + " " + beginTimeStr);
                            Log.d("==时间==",
                                    "当前时间=" + nowDate.getTime() + "上班时间=" + date.getTime() + "<=弹性时间" + tanxingshijan);
                            if (nowdate.getTime() <= nnn.getTime() + tanxingshijan) {//在考勤范围内
                                tvMapStatus.setText("正常");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                tvMapLocation.setText("（在考勤位置内）");
                                punchCardState = 1;
                            } else if (nowDate.getTime() - date.getTime() > kuanggongshijian) {
                                tvMapStatus.setText("旷工");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                tvMapLocation.setText("（在考勤位置内）");
                                punchCardState = 4;
                            } else if (nowDate.getTime() - date.getTime() <= tanxingshijan * 2) {
                                tvMapStatus.setText("迟到");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                tvMapLocation.setText("（在考勤位置内）");
                                punchCardState = 2;
                            }
                        } else if (takeCard.equals("takeUpCard1") || takeCard.equals("takeDownCard1")) {
                            //上午下班打卡or下午下班打卡
                            Date nowdate = new Date();
                            String endTimeStr = isAm ?
                                    (userInfo.getEndTimeHou() + ":" + userInfo.getEndTimeMin()) :
                                    (userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());//19:00
                            //Date endDate = DateUtil.HMS.parse(endTimeStr);
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowDate).split(" ");
                            String timesss = dateshijian[0] + " " + endTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            if (nowDate.getTime() < nnn.getTime()) {
                                tvMapStatus.setText("早退");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                tvMapLocation.setText("（在考勤位置内）");
                                punchCardState = 3;
                            } else {
                                tvMapStatus.setText("正常");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                tvMapLocation.setText("（在考勤位置内）");
                                punchCardState = 1;
                            }
                        }
                    } else {//不在考勤位置内
                        if (takeCard.equals("takeUpCard") || takeCard.equals("takeDownCard")) {   //上午上班打卡or下午上班打卡

                            // 弹性时间
                            String workFlexTimes = userInfo.getWorkFlexTimes();  //15
                            // isAm ? 上午
                            // 上班
                            String beginTimeStr = userInfo.getBeginTimeHou() + ":" + userInfo.getBeginTimeMin();
                            //08:30
                            //String lateTimes = userInfo.getLateTimes();//25
                            // 旷工时间
                            String absentTimes = userInfo.getAbsentTimes(); //30
                            //上午上班时间 or 下午上班时间
                            Date date = DateUtil.HMS.parse(beginTimeStr);
                            SimpleDateFormat formatter0 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                            //String dateString0 = formatter0.format(date);
                            Log.d("==上午上班时间==", formatter0.format(date));
                            //弹性时间
                            Date FlexDate = DateUtil.M.parse(workFlexTimes);
                            //严重迟到(弃用)   //Date lateDate = DateUtil.M.parse(lateTimes);
                            //旷工
                            Date absentDate = DateUtil.M.parse(absentTimes);
                            /**时间差**/
                            long data = 0;


                            Date nowdate = new Date();
                            long kuanggongshijian = Integer.valueOf(absentTimes) * 60 * 1000;
                            long tanxingshijan = Integer.valueOf(workFlexTimes) * 60 * 1000;
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowdate).split(" ");
                            String timesss = dateshijian[0] + " " + beginTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            Log.d("旷工时间=",
                                    nowdate.getTime() + "====" + nnn.getTime() + "==" + kuanggongshijian +
                                            "上班时间：" + dateshijian[0] + " " + beginTimeStr);
                            Log.d("==时间==",
                                    "当前时间=" + nowDate.getTime() + "上班时间=" + date.getTime() + "<=弹性时间" + tanxingshijan);
                            if (nowdate.getTime() <= nnn.getTime() + tanxingshijan) {//在考勤范围内
                                tvMapStatus.setText("正常");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                tvMapLocation.setText("（不在考勤位置内）");
                                punchCardState = 1;
                            } else if (nowDate.getTime() - date.getTime() > kuanggongshijian) {
                                tvMapStatus.setText("旷工");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                tvMapLocation.setText("（不在考勤位置内）");
                                punchCardState = 4;
                            } else if (nowDate.getTime() - date.getTime() <= tanxingshijan * 2) {
                                tvMapStatus.setText("迟到");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                tvMapLocation.setText("（不在考勤位置内）");
                                punchCardState = 2;
                            }
                        } else if (takeCard.equals("takeUpCard1") || takeCard.equals("takeDownCard1")) {
                            //上午下班打卡or下午下班打卡
                            Date nowdate = new Date();
                            String endTimeStr = isAm ?
                                    (userInfo.getEndTimeHou() + ":" + userInfo.getEndTimeMin()) :
                                    (userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());//19:00
                            //Date endDate = DateUtil.HMS.parse(endTimeStr);
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowDate).split(" ");
                            String timesss = dateshijian[0] + " " + endTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            if (nowDate.getTime() < nnn.getTime()) {
                                tvMapStatus.setText("早退");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                tvMapLocation.setText("（不在考勤位置内）");
                                punchCardState = 3;
                            } else {
                                tvMapStatus.setText("正常");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                tvMapLocation.setText("（不在考勤位置内）");
                                punchCardState = 1;
                            }
                        }
                    }
                    //}
                } else {
                    tvMapStatus.setVisibility(View.GONE);
                }
            } else {//固定班制  不允许外勤
                if (locationdata != null) {
                    myLatLng = new LatLng(locationdata.getLatitude(), locationdata.getLongitude());
                    double distance = AMapUtils.calculateLineDistance(latLng, myLatLng);
                    //EffectRange 有效范围
                    Integer ran = 0;
                    if (StringUtil.isEmpty(userInfo.getEffectRange())) {
                        ran = 0;
                    } else {
                        ran = Integer.parseInt(userInfo.getEffectRange());
                    }
                    if (distance < ran) {//在考勤位置内
                        if (takeCard.equals("takeUpCard") || takeCard.equals("takeDownCard")) {   //上午上班打卡or下午上班打卡

                            // 弹性时间
                            String workFlexTimes = userInfo.getWorkFlexTimes();  //15
                            // isAm ? 上午
                            // 上班
                            String beginTimeStr = userInfo.getBeginTimeHou() + ":" + userInfo.getBeginTimeMin();
                            //08:30
                            //String lateTimes = userInfo.getLateTimes();//25
                            // 旷工时间
                            String absentTimes = userInfo.getAbsentTimes(); //30
                            //上午上班时间 or 下午上班时间
                            Date date = DateUtil.HMS.parse(beginTimeStr);
                            SimpleDateFormat formatter0 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                            //String dateString0 = formatter0.format(date);
                            Log.d("==上午上班时间==", formatter0.format(date));
                            //弹性时间
                            Date FlexDate = DateUtil.M.parse(workFlexTimes);
                            //严重迟到(弃用)   //Date lateDate = DateUtil.M.parse(lateTimes);
                            //旷工
                            Date absentDate = DateUtil.M.parse(absentTimes);
                            /**时间差**/
                            long data = 0;

                            Date nowdate = new Date();
                            long kuanggongshijian = Integer.valueOf(absentTimes) * 60 * 1000;
                            long tanxingshijan = Integer.valueOf(workFlexTimes) * 60 * 1000;
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowdate).split(" ");
                            String timesss = dateshijian[0] + " " + beginTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            Log.d("旷工时间=",
                                    nowdate.getTime() + "====" + nnn.getTime() + "==" + kuanggongshijian +
                                            "上班时间：" + dateshijian[0] + " " + beginTimeStr);
                            Log.d("==时间==",
                                    "当前时间=" + nowDate.getTime() + "上班时间=" + date.getTime() + "<=弹性时间" + tanxingshijan);
                            if (nowdate.getTime() <= nnn.getTime() + tanxingshijan) {
                                tvMapStatus.setText("正常");
                                tvMapLocation.setText("（在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                punchCardState = 1;
                            } else if (nowDate.getTime() - date.getTime() > kuanggongshijian) {
                                tvMapStatus.setText("旷工");
                                tvMapLocation.setText("（在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                punchCardState = 4;
                            } else if (nowDate.getTime() - date.getTime() <= tanxingshijan * 2) {
                                tvMapStatus.setText("迟到");
                                tvMapLocation.setText("（在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                punchCardState = 2;
                            }
                        } else if (takeCard.equals("takeUpCard1") || takeCard.equals("takeDownCard1")) {
                            //上午下班打卡or下午下班打卡
                            Date nowdate = new Date();
                            String endTimeStr = isAm ?
                                    (userInfo.getEndTimeHou() + ":" + userInfo.getEndTimeMin()) :
                                    (userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());//19:00
                            //Date endDate = DateUtil.HMS.parse(endTimeStr);
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowDate).split(" ");
                            String timesss = dateshijian[0] + " " + endTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            if (nowDate.getTime() < nnn.getTime()) {
                                tvMapStatus.setText("早退");
                                tvMapLocation.setText("（在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                punchCardState = 3;
                            } else {
                                tvMapStatus.setText("正常");
                                tvMapLocation.setText("（在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                punchCardState = 1;
                            }
                        }
                    } else {//不在考勤位置内
                        //不允许打卡
                        if (takeCard.equals("takeUpCard") || takeCard.equals("takeDownCard")) {   //上午上班打卡or下午上班打卡

                            // 弹性时间
                            String workFlexTimes = userInfo.getWorkFlexTimes();  //15
                            // isAm ? 上午
                            // 上班
                            String beginTimeStr = userInfo.getBeginTimeHou() + ":" + userInfo.getBeginTimeMin();
                            //08:30
                            //String lateTimes = userInfo.getLateTimes();//25
                            // 旷工时间
                            String absentTimes = userInfo.getAbsentTimes(); //30
                            //上午上班时间 or 下午上班时间
                            Date date = DateUtil.HMS.parse(beginTimeStr);
                            SimpleDateFormat formatter0 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                            //String dateString0 = formatter0.format(date);
                            Log.d("==上午上班时间==", formatter0.format(date));
                            //弹性时间
                            Date FlexDate = DateUtil.M.parse(workFlexTimes);
                            //严重迟到(弃用)   //Date lateDate = DateUtil.M.parse(lateTimes);
                            //旷工
                            Date absentDate = DateUtil.M.parse(absentTimes);
                            /**时间差**/
                            long data = 0;

                            Date nowdate = new Date();
                            long kuanggongshijian = Integer.valueOf(absentTimes) * 60 * 1000;
                            long tanxingshijan = Integer.valueOf(workFlexTimes) * 60 * 1000;
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowdate).split(" ");
                            String timesss = dateshijian[0] + " " + beginTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            Log.d("旷工时间=",
                                    nowdate.getTime() + "====" + nnn.getTime() + "==" + kuanggongshijian +
                                            "上班时间：" + dateshijian[0] + " " + beginTimeStr);
                            Log.d("==时间==",
                                    "当前时间=" + nowDate.getTime() + "上班时间=" + date.getTime() + "<=弹性时间" + tanxingshijan);
                            if (nowdate.getTime() <= nnn.getTime() + tanxingshijan) {
                                tvMapStatus.setText("正常");
                                tvMapLocation.setText("（不在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                btnMapPunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnMapPunch.setEnabled(false);
                                btnFacePunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnFacePunch.setEnabled(false);
                                punchCardState = 1;
                            } else if (nowDate.getTime() - date.getTime() > kuanggongshijian) {
                                tvMapStatus.setText("旷工");
                                tvMapLocation.setText("（不在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                btnMapPunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnMapPunch.setEnabled(false);
                                btnFacePunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnFacePunch.setEnabled(false);
                                punchCardState = 4;
                            } else if (nowDate.getTime() - date.getTime() <= tanxingshijan * 2) {
                                tvMapStatus.setText("迟到");
                                tvMapLocation.setText("（不在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                btnMapPunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnMapPunch.setEnabled(false);
                                btnFacePunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnFacePunch.setEnabled(false);
                                punchCardState = 2;
                            }
                        } else if (takeCard.equals("takeUpCard1") || takeCard.equals("takeDownCard1")) {
                            //上午下班打卡or下午下班打卡
                            Date nowdate = new Date();
                            String endTimeStr = isAm ?
                                    (userInfo.getEndTimeHou() + ":" + userInfo.getEndTimeMin()) :
                                    (userInfo.getEndTimeHou1() + ":" + userInfo.getEndTimeMin1());//19:00
                            //Date endDate = DateUtil.HMS.parse(endTimeStr);
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String[] dateshijian = formatter.format(nowDate).split(" ");
                            String timesss = dateshijian[0] + " " + endTimeStr;
                            Date nnn = DateUtil.YMD_HM.parse(timesss);
                            if (nowDate.getTime() < nnn.getTime()) {
                                tvMapStatus.setText("早退");
                                tvMapLocation.setText("（不在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_not_bg));//红色
                                btnMapPunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnMapPunch.setEnabled(false);
                                btnFacePunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnFacePunch.setEnabled(false);
                                punchCardState = 3;
                            } else {
                                tvMapStatus.setText("正常");
                                tvMapLocation.setText("（不在考勤位置内）");
                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                                btnMapPunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnMapPunch.setEnabled(false);
                                btnFacePunch.setBackground(getResources().getDrawable(R.drawable.sign_notpunchcard_bg)); //灰色
                                btnFacePunch.setEnabled(false);
                                punchCardState = 1;
                            }
                        }
                    }
//                    }
                } else {
                    tvMapStatus.setVisibility(View.GONE);
                }
            }
        } else {

//        }
//
//        // 0:固定四次班制 1:自由打卡 2：固定两次班制
//        if (scheduleWay.equals("1")) {
            //自由
            //地图数据是否为空
            if (locationdata != null) {
                myLatLng = new LatLng(locationdata.getLatitude(), locationdata.getLongitude());
                double distance = AMapUtils.calculateLineDistance(latLng, myLatLng);
                //EffectRange 有效范围
//                if (StringUtil.isEmpty(userInfo.getEffectRange())) {
//                    tvMapLocation.setText("（在考勤位置内）");
//                    tvMapStatus.setText("正常");
//                    tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
//                    punchCardState = 1;
//                } else {

                int aa = Integer.parseInt((StringUtil.isEmpty(userInfo.getEffectRange())) ? "0" :
                        userInfo.getEffectRange());
//                if (distance > Integer.parseInt((userInfo.getEffectRange() == null) ? "0" : userInfo.getEffectRange
//                ())) {
                if (distance > aa) {
                    tvMapLocation.setText("（不在考勤位置内）");
                    tvMapStatus.setText("外勤");
                    tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_fieldwork_bg));
                    punchCardState = 6;
                } else {
                    tvMapLocation.setText("（在考勤位置内）");
                    tvMapStatus.setText("正常");
                    tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_leave_bg));  //蓝色
                    punchCardState = 1;
                }
//                }
            } else {
                tvMapStatus.setVisibility(View.GONE);
            }
        }
    }

    @OnClick({R.id.iv_back, R.id.tv_map_feedback, R.id.btn_map_punch, R.id.btnFacePunch})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.tv_map_feedback:
                showToast("反馈位置不准确");
                break;
            case R.id.btn_map_punch:
                doPunch();
                break;
            case R.id.btnFacePunch:
                UserInfo userInfoChahe = SettingManager.getInstance().getUserInfo();
                if (userInfoChahe == null) {
                    showToast("请检查登录状态");
                    return;
                }
                if (StringUtil.isEmpty(userInfoChahe.getFaceId())) {
                    showToast("请先绑定人脸信息");
                    return;
                }
                btnFacePunch.setEnabled(false);
                startActivity(new Intent(this, FaceDetectExpActivity.class));
                break;
        }
    }

    //进行打卡
    private void doPunch() {
        Intent intent = getIntent();
        intent.putExtra("remarks", etMapRemarks.getText().toString().trim());
        intent.putExtra("punchCardTime", System.currentTimeMillis());
//                SimpleDateFormat sdf = new SimpleDateFormat();// 格式化时间
//                sdf.applyPattern("yyyy-MM-dd HH:mm:ss a");// a为am/pm的标记
//                Date date = new Date();// 获取当前时间
//                AllData.dakatime=sdf.format(date);
        if (myLatLng != null) {
            AddressPoint point = new AddressPoint();
            point.setLatitude(myLatLng.latitude);
            point.setLongitude(myLatLng.longitude);
            Gson gson =
                    new GsonBuilder().registerTypeAdapter(AddressPoint.class, new AddressPointTypeAdapter()).create();
            intent.putExtra("myLatLng", gson.toJson(point));
        }
        intent.putExtra("takeCard", takeCard);
        intent.putExtra("punchCardState", punchCardState);
        setResult(RESULT_OK, intent);
        finish();
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        //在activity执行onDestroy时执行mMapView.onDestroy()，销毁地图
        mvMap.onDestroy();
        mHanlder.removeCallbacksAndMessages(null);
        if (timer != null) {
            timer.cancel();
        }
        destroyLocation();
    }

    @Override
    protected void onResume() {
        super.onResume();
        //在activity执行onResume时执行mMapView.onResume ()，重新绘制加载地图
        mvMap.onResume();
        startLocation();
    }

    @Override
    protected void onPause() {
        super.onPause();
        //在activity执行onPause时执行mMapView.onPause ()，暂停地图的绘制
        mvMap.onPause();
        stopLocation();
    }

    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        //在activity执行onSaveInstanceState时执行mMapView.onSaveInstanceState (outState)，保存地图当前的状态
        mvMap.onSaveInstanceState(outState);
    }

    private class MapHanlder extends Handler {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
//                case 100:
//                    tvMapAddress.setText(PhoneUtil.getUniqueInstance().getAddress(mContext));
//                    if (userInfo != null) {
//                        try {
//                            initUserInfo();
//                        } catch (Exception e) {
//                            showToast("数据错误，请检查后台");
//                        }
//                    }
//                    break;
                case 200:
                    long sysTime = System.currentTimeMillis();
                    CharSequence sysTimeStr = DateFormat.format("HH:mm:ss", sysTime);
                    btnMapPunch.setText(sysTimeStr + " 打卡");
                    LogUtil.e("MapHanlder 200");
                    break;
            }
        }
    }


    private AMapLocationClient locationClient = null;
    private AMapLocationClientOption locationOption = null;

    /**
     * 初始化定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void initLocation() {
        //初始化client
        locationClient = new AMapLocationClient(this.getApplicationContext());
        locationOption = getDefaultOption();
        //设置定位参数
        locationClient.setLocationOption(locationOption);
//        // 设置定位监听
        locationClient.setLocationListener(locationListener);
    }

    /**
     * 默认的定位参数
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private AMapLocationClientOption getDefaultOption() {
        AMapLocationClientOption mOption = new AMapLocationClientOption();
        mOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
        //可选，设置定位模式，可选的模式有高精度、仅设备、仅网络。默认为高精度模式
        mOption.setGpsFirst(false);//可选，设置是否gps优先，只在高精度模式下有效。默认关闭
        mOption.setHttpTimeOut(30000);//可选，设置网络请求超时时间。默认为30秒。在仅设备模式下无效
//        mOption.setInterval(10000);//可选，设置定位间隔。默认为2秒
        mOption.setNeedAddress(true);//可选，设置是否返回逆地理地址信息。默认是true
        mOption.setOnceLocation(false);//可选，设置是否单次定位。默认是false
        mOption.setOnceLocationLatest(false);//可选，设置是否等待wifi刷新，默认为false.如果设置为true,会自动变为单次定位，持续定位时不要使用
        AMapLocationClientOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTP);//可选，
        // 设置网络请求的协议。可选HTTP或者HTTPS。默认为HTTP
        mOption.setSensorEnable(false);//可选，设置是否使用传感器。默认是false
        mOption.setWifiScan(true); //可选，设置是否开启wifi扫描。默认为true，如果设置为false会同时停止主动刷新，停止以后完全依赖于系统刷新，定位位置可能存在误差
        mOption.setLocationCacheEnable(true); //可选，设置是否使用缓存定位，默认为true
        mOption.setGeoLanguage(AMapLocationClientOption.GeoLanguage.DEFAULT);//可选，设置逆地理信息的语言，默认值为默认语言（根据所在地区选择语言）
        return mOption;
    }


    /**
     * 定位监听
     */
    AMapLocationListener locationListener = new AMapLocationListener() {
        @Override
        public void onLocationChanged(AMapLocation location) {
            if (isfast) {
                isfast = false;
                initView();
            }
            if (null != location) {

                locationdata = location;
                FunctionPermission permissionFunction = SettingManager.getInstance().getPermissionFunction();
                if (permissionFunction != null && permissionFunction.isHasMoreAttendancePointsFunction()) {
                    getCurrentRule();
                }

                initUserInfo();

                //errCode等于0代表定位成功，其他的为定位失败，具体的可以参照官网定位错误码说明
                if (location.getErrorCode() == 0) {
                    myLatLng = new LatLng(location.getLatitude(), location.getLongitude());
                    double distance = AMapUtils.calculateLineDistance(latLng, myLatLng);
                    tvMapAddress.setText(location.getAddress());

//                    if (scheduleWay) { //自由
//                        if (StringUtil.isEmpty(userInfo.getEffectRange())) {
//                            tvMapLocation.setText("（不在考勤位置内）");
//                            tvMapStatus.setText("外勤");
//                            tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_fieldwork_bg));
//                            punchCardState = 6;
//                        } else if (distance > Integer.parseInt(userInfo.getEffectRange())) {
//                            if (userInfo.getFieldState().equals("1")) {
//                                //允许外勤打卡
//                                tvMapLocation.setText("（不在考勤位置内）");
//                                tvMapStatus.setText("外勤");
//                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_fieldwork_bg));
//                                punchCardState = 6;
//                            }
//                        }
//                    }else {//固定
//                        if (StringUtil.isEmpty(userInfo.getEffectRange())) {
//                            tvMapLocation.setText("（不在考勤位置内）");
//                            tvMapStatus.setText("外勤");
//                            Log.d("时间戳","外勤1");
//                            tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_fieldwork_bg));
//                            punchCardState = 6;
//                        } else if (distance > Integer.parseInt(userInfo.getEffectRange())) {
//                            if (userInfo.getFieldState().equals("0")) {
//                                //不许外勤打卡
//                                tvMapLocation.setText("（不在考勤位置内）");
//                                tvMapLocation.setTextColor(Color.RED);
//                                btnMapPunch.setBackgroundColor(Color.parseColor("#AAAAAA"));
//                                btnMapPunch.setEnabled(false);
//                                Log.d("时间戳","外勤2");
//                            } else {
//                                //允许外勤打卡
//                                tvMapLocation.setText("（超出考勤位置）");
//                                tvMapStatus.setText("外勤");
//                                tvMapStatus.setBackground(getResources().getDrawable(R.drawable.sign_fieldwork_bg));
//                                punchCardState = 6;
//
//
//
//
//                            }
//                        }
//
//                    }
                }
            } else {
                showToast("定位失败");
            }
        }
    };


    /**
     * 开始定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void startLocation() {
        // 设置定位参数
        locationClient.setLocationOption(locationOption);
        // 启动定位
        locationClient.startLocation();
    }

    /**
     * 停止定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void stopLocation() {
        // 停止定位
        locationClient.stopLocation();
    }

    /**
     * 销毁定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void destroyLocation() {
        if (null != locationClient) {
            /**
             * 如果AMapLocationClient是在当前Activity实例化的，
             * 在Activity的onDestroy中一定要执行AMapLocationClient的onDestroy
             */
            locationClient.onDestroy();
            locationClient = null;
            locationOption = null;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void messageEvent(FaceImageEvent imageEvent) {
        if (imageEvent != null) {
            LogUtil.e(imageEvent.getPath() + "  " + imageEvent.getBestImageBase64());
            HuaweiService.getInstance().searchFaceUser(imageEvent.getBestImageBase64(),
                    new HuaweiService.FaceSearchListener() {
                        @Override
                        public void onSuccess(FaceSearch faceSearch) {
                            if (faceSearch == null || faceSearch.getFaces() == null || faceSearch.getFaces().size() == 0) {
                                showToast("人脸库未找到，检查是否已完成人脸注册");
                                btnFacePunch.setEnabled(true);
                                return;
                            }
                            FaceBean facesBean = faceSearch.getFaces().get(0);
                            if (facesBean.getSimilarity() > Constant.FACE_SIMILARITY) {
                                if (facesBean.getFace_id().equals(SettingManager.getInstance().getUserInfo().getFaceId())) {
                                    doPunch();
                                } else {
                                    showToast("检查非当前用户，请重新尝试");
                                }
                                btnFacePunch.setEnabled(true);
                            } else {
                                btnFacePunch.setEnabled(true);
                                showToast("人脸库未找到，请重新尝试");
                            }
                        }

                        @Override
                        public void onFailure(Throwable throwable) {
                            LogUtil.e("searchFaceUser onError:" + throwable.getMessage());
                            btnFacePunch.setEnabled(true);
                            showToast("人脸查询失败");
                        }
                    });
        }
    }


    private void getCurrentRule() {
        if (locationdata == null || ruleList == null) {
            return ;
        }
        LogUtil.e("Latitude=" + locationdata.getLatitude() + " Longitude=" + locationdata.getLongitude());
        // 定位点
        myLatLng = new LatLng(locationdata.getLatitude(), locationdata.getLongitude());
        AttendanceRulesModel rulesModelNew = null;

        for (int i = 0; i < ruleList.size(); i++) {
            AttendanceRulesModel rulesModel = ruleList.get(i);

            String[] latAndLong = rulesModel.getLatAndLong().split(",");
            // 考勤点
            LatLng ruleLatLng = new LatLng(0, 0);
            if (latAndLong.length < 2) {
                ruleLatLng = new LatLng(0, 0);
            } else {
                ruleLatLng = new LatLng(Double.parseDouble(latAndLong[1]), Double.parseDouble(latAndLong[0]));
            }
            double distance = AMapUtils.calculateLineDistance(ruleLatLng, myLatLng);

            // 考勤范围
            Integer ran = 0;
            if (StringUtil.isEmpty(rulesModel.getEffectRange())) {
                ran = 0;
            } else {
                ran = Integer.parseInt(rulesModel.getEffectRange());
            }

            if (distance < ran) {
                rulesModelNew = rulesModel;
                userInfo = rulesModel;
            }
        }

        if (rulesModelNew == null) {// 设置默认考勤规则

        }

        getRuleLatLng();
    }


    // 获取当前项目我的考勤规则列表
    private void getAttendanceRulesData() {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectInfo.getProjectId());
        param.put("userId", userInfoCache.getId());

        Api.getGbkApiserver().getAttendanceRulesData("getAttendanceRulesData", param).enqueue(new Callback<NetworkResponse<List<AttendanceRulesModel>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<AttendanceRulesModel>>> call,
                                   Response<NetworkResponse<List<AttendanceRulesModel>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    ruleList = response.body().getData();
                    getMyDefaultAttendanceRules();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<AttendanceRulesModel>>> call, Throwable throwable) {
                throwable.printStackTrace();
//                finish();
            }
        });
    }


    // 获取当前项目我的默认考勤规则（原逻辑优化）
    private void getMyDefaultAttendanceRules() {
//        FunctionPermission permissionFunction = SettingManager.getInstance().getPermissionFunction();
//        if (permissionFunction != null && permissionFunction.isHasMoreAttendancePointsFunction()) {
            Map<String, Object> param = new HashMap<>();
            param.put("projectId", projectInfo.getProjectId());
            param.put("userId", userInfoCache.getId());

            Api.getGbkApiserver().getMyDefaultAttendanceRules("getMyDefaultAttendanceRules", param).enqueue(new Callback<NetworkResponse<AttendanceRulesModel>>() {
                @Override
                public void onResponse(Call<NetworkResponse<AttendanceRulesModel>> call,
                                       Response<NetworkResponse<AttendanceRulesModel>> response) {
                    if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                        AttendanceRulesModel data = response.body().getData();
                        userInfo = data;
                        getCurrentRule();
                    }else{
                        showToast("请先设置考勤规则");
                        finish();
                    }
                }

                @Override
                public void onFailure(Call<NetworkResponse<AttendanceRulesModel>> call, Throwable throwable) {
                    throwable.printStackTrace();
                    showToast("考勤规则获取失败");
                    finish();
                }
            });
//        }


    }

    private void getRuleLatLng(){
        scheduleWay = userInfo.getScheduleWay();
        latLng = new LatLng(0, 0);
        String[] latAndLong = userInfo.getLatAndLong().split(",");
        if (latAndLong.length < 2) {
            latLng = new LatLng(0, 0);
        } else {
            latLng = new LatLng(Double.parseDouble(latAndLong[1]), Double.parseDouble(latAndLong[0]));
        }
    }

    private void getRuleLatLngOld(){
        scheduleWay = userInfoCache.getScheduleWay();
        latLng = new LatLng(0, 0);
        String[] latAndLong = userInfoCache.getLatAndLong().split(",");
        if (latAndLong.length < 2) {
            latLng = new LatLng(0, 0);
        } else {
            latLng = new LatLng(Double.parseDouble(latAndLong[1]), Double.parseDouble(latAndLong[0]));
        }
    }

}
