package com.kisoft.yuejianli.views;

import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by tudou on 2018/6/22.
 */
public class ProjectModelCommonDetailData {

   public static Map<String, Object> getPageData(String bType){
       List<Map<String, Object>> data = null;
       String titleKey = "";
       String listUrl = "";
       String saveUrl = "";
       String updateUrl = "";
       String filePath = "";
       String pageTitle = "";
       if (StringUtil.isEqual("T_PDS_ENVIRONMENTAL_DETAIL",bType)){
            //环保细则
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "标题:");
                   put("key", "title");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "内容:");
                   put("key", "content");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PDS_ENVIRONMENTAL_PLAN",bType)){
           //环保计划
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "标题:");
                   put("key", "title");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "内容:");
                   put("key", "content");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PDS_SPECIAL_SUP_PLAN",bType)){
           //监理专项方案
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "标题:");
                   put("key", "title");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "类别:");
                   put("key", "type");
                   put("viewType", Constant.VIEWTYPE_LABEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "内容:");
                   put("key", "content");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PDS_DANGEROUS_PROJECT_PLAN",bType)){
           //危大工程方案
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "标题:");
                   put("key", "title");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "内容:");
                   put("key", "content");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PDS_SECURITY_LOG_SCGL",bType)){
           // 安全监理日志
           pageTitle = "安全监理日志";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "pds/PdsSecurityLogScglAction.do?method=getPdsSecurityLogScglList";
           saveUrl = "pds/PdsSecurityLogScglAction.do?method=savePdsSecurityLogScgl_APP";
           updateUrl = "pds/PdsSecurityLogScglAction.do?method=updatePdsSecurityLogScgl_APP";

           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "监理机构:");
                   put("key", "supervisoryAuthority");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "施工合同号:");
                   put("key", "constructionContractNo");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "记录人:");
                   put("key", "recorder");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "记录日期:");
                   put("key", "recordDate");
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "审核人:");
                   put("key", "auditor");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "审核日期:");
                   put("key", "auditDate");
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "天气:");
                   put("key", "weather");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "各合同段主要施工项目简述:");
                   put("key", "projectDescription");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "监理机构主要安全监理工作简述:");
                   put("key", "safetyWork");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "安全问题处理情况简述:");
                   put("key", "safetyProblem");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PDS_ENVIRONMENTAL_LOG",bType)){
           // 环保监理日志
           pageTitle = "环保监理日志";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "/pds/PdsEnvironmentalLogAction.do?method=getPdsEnvironmentalLogList";
           saveUrl = "/pds/PdsEnvironmentalLogAction.do?method=savePdsEnvironmentalLog_APP";
           updateUrl = "/pds/PdsEnvironmentalLogAction.do?method=updatePdsEnvironmentalLog_APP";

           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "编号:");
                   put("key", "serialNumber");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "监理机构:");
                   put("key", "supervisorOrganization");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "记录人:");
                   put("key", "recorder");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "审核人:");
                   put("key", "auditor");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "日期:");
                   put("key", "dateTime");
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "天气:");
                   put("key", "weatherSituation");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "主要施工情况:");
                   put("key", "constructionSituation");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "监理主要工作:");
                   put("key", "supervisionWork");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "问题及处理情况:");
                   put("key", "problem");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PROD_PROMPT_NOTICE",bType)){
           // 生产经营工作提示单/整改单
           pageTitle = "工作提示单/整改单";
           filePath = "/pds/production/";
           titleKey = "workTasks";
           listUrl = "/pds/ProdPromptNoticeAction.do?method=getProdPromptNoticeList";
           saveUrl = "/pds/ProdPromptNoticeAction.do?method=saveProdPromptNotice_APP";
           updateUrl = "/pds/ProdPromptNoticeAction.do?method=updateProdPromptNotice_APP";
           List<String> list = new ArrayList() {{
               add("提示单");
               add("整改单");
           }};
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "资料名称:");
                   put("key", "dataName");
                   put("data", list);
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "编号:");
                   put("key", "serialNumber");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "内容:");
                   put("key", "content");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "生产部门:");
                   put("key", "productionDepartment");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "问题闭合情况，回复日期:");
                   put("key", "problemClosure");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "下发日期:");
                   put("key", "deliveryDate");
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "提示、通知人:");
                   put("key", "prompter");
                   put("extra", "prompterId");
                   put("type", "Person");
                   put("viewType", Constant.VIEWTYPE_SELECTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PROJ_CHECK",bType)){
           // 项目自查
           pageTitle = "项目自查";

           filePath = "/pds/subcompany/";
           titleKey = "workTasks";
           listUrl = "/pds/ProjCheckAction.do?method=getProjCheckAppList";
           saveUrl = "/pds/ProjCheckAction.do?method=saveProjCheck_APP";
           updateUrl = "/pds/ProjCheckAction.do?method=updateProjCheck_APP";

           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_LABELVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "自查内容:");
                   put("key", "checkContent");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "自查人:");
                   put("key", "checkPerson");
                   put("extra", "checkPersonId");
                   put("type", "Person");
                   put("viewType", Constant.VIEWTYPE_SELECTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "问题及处理措施:");
                   put("key", "problemAndSolve");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "自查日期:");
                   put("key", "checkDate");
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
               add(new HashMap() {{
                   put("title", "整改完成日期:");
                   put("key", "solveDate");
                   put("viewType", Constant.VIEWTYPE_DATESEL_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("T_PROD_WEEKLY_REPORT",bType)){
           // 项目周报
           pageTitle = "项目周报";
           filePath = "";
           titleKey = "workTasks";
           listUrl = "/pds/ProdWeeklyReportAction.do?method=getProdWeeklyReportList";
           saveUrl = "";
           data = new ArrayList<>();
       }else if (StringUtil.isEqual("T_PROJ_ASSESSMENT_SCORE",bType)){
           // 项目季度、年终考评
           pageTitle = "项目季度、年终考评";
           filePath = "";
           titleKey = "workTasks";
           listUrl = "/pds/ProjAssessmentScoreAction.do?method=getProjAssessmentScoreList";
           saveUrl = "";
           data = new ArrayList<>();
       }else if (StringUtil.isEqual("T_PROD_ENVIRONMENTAL_CHECK",bType)){
           // 环保检查记录
           pageTitle = "环保检查记录";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "/pds/ProdEnvironmentalCheckAction.do?method=getProdEnvironmentalCheckList";
           saveUrl = "/pds/ProdEnvironmentalCheckAction.do?method=saveProdEnvironmentalCheck_APP";
           data = new ArrayList<>();
       }else if (StringUtil.isEqual("T_PROD_WEEKLY_REPORT_SUMMARY",bType)){
           // 周报汇总上传
           pageTitle = "周报汇总上传";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "/pds/ProdWeeklyReportSummaryAction.do?method=getProdWeeklyReportSummaryList";
           saveUrl = "/pds/ProdWeeklyReportSummaryAction.do?method=saveProdWeeklyReportSummary_APP";
           List<String> yearList = new ArrayList() {{
               add("2022");
               add("2023");
               add("2024");
               add("2025");
               add("2026");
               add("2027");
               add("2028");
           }};
           List<String> monthsList = new ArrayList() {{
               add("1月");
               add("2月");
               add("3月");
               add("4月");
               add("5月");
               add("6月");
               add("7月");
               add("8月");
               add("9月");
               add("10月");
               add("11月");
               add("12月");
           }};
           List<String> weeksList = new ArrayList() {{
               add("第一周");
               add("第二周");
               add("第三周");
               add("第四周");
               add("第五周");
           }};
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "填报年份:");
                   put("key", "year");
                   put("data", yearList);
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});

               add(new HashMap() {{
                   put("title", "填报月份:");
                   put("key", "month");
                   put("data", monthsList);
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "填报周份:");
                   put("key", "week");
                   put("data", weeksList);
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
           }};

       }else if (StringUtil.isEqual("T_PROD_MONTHLY_REPORT",bType)){
           // 月报板块
           pageTitle = "月报板块";
           filePath = "/pds/production/";
           titleKey = "workTasks";
           listUrl = "/pds/ProdMonthlyReportAction.do?method=getProdMonthlyReportList";
           saveUrl = "/pds/ProdMonthlyReportAction.do?method=saveProdMonthlyReport_APP";

           List<String> yearList = new ArrayList() {{
               add("2022");
               add("2023");
               add("2024");
               add("2025");
               add("2026");
               add("2027");
               add("2028");
           }};
           List<String> monthsList = new ArrayList() {{
               add("1月");
               add("2月");
               add("3月");
               add("4月");
               add("5月");
               add("6月");
               add("7月");
               add("8月");
               add("9月");
               add("10月");
               add("11月");
               add("12月");
           }};
           data = new ArrayList() {{
               add(new HashMap() {{
                   put("title", "填报年份:");
                   put("key", "year");
                   put("data", yearList);
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});

               add(new HashMap() {{
                   put("title", "填报月份:");
                   put("key", "month");
                   put("data", monthsList);
                   put("extra", "notIndex");
                   put("viewType", Constant.VIEWTYPE_SELECTFIELD_CELL);
               }});
               add(new HashMap() {{
                   put("title", "项目名称:");
                   put("key", "projectName");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
               add(new HashMap() {{
                   put("title", "备注:");
                   put("key", "remark");
                   put("viewType", Constant.VIEWTYPE_TEXTVIEW_CELL);
               }});
           }};
       }else if (StringUtil.isEqual("testEngineeringApp",bType)){
           // 工程门户
           pageTitle = "工程门户";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "";
           saveUrl = "";
           data = new ArrayList<>();
       } else if (StringUtil.isEqual("testEngineeringApp",bType)){
           // 项目管理清单
           pageTitle = "项目管理清单";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "";
           saveUrl = "";
           data = new ArrayList<>();
       } else if (StringUtil.isEqual("testEngineeringApp",bType)){
           // 经营门户
           pageTitle = "经营门户";
           filePath = "/pds/icon/";
           titleKey = "workTasks";
           listUrl = "";
           saveUrl = "";
           data = new ArrayList<>();
       }

       Map<String, Object> map= new HashMap<>();
       map.put("pageTitle", pageTitle);
       map.put("listUrl", listUrl);
       map.put("saveUrl", saveUrl);
       map.put("updateUrl", updateUrl);
       map.put("filePath", filePath);
       map.put("data",data);
       return map;
   }
}
