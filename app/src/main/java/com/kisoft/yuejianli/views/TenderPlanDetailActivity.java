package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.adpter.TenderEmpAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.TenderPlanDetailContract;
import com.kisoft.yuejianli.entity.TenderPeopleInfo;
import com.kisoft.yuejianli.entity.TenderPlan;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.TenderPlanDetailModel;
import com.kisoft.yuejianli.presenter.TenderPlanDetailPresenter;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/27.
 */

public class TenderPlanDetailActivity extends BaseActivity<TenderPlanDetailModel, TenderPlanDetailPresenter> implements
        TenderPlanDetailContract.TenderPlanDetailViewContract{

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_number)
    TextView tvNumber;
    @BindView(R.id.tv_name)
    TextView tvName;
    @BindView(R.id.tv_person)
    TextView tvPerson;
    @BindView(R.id.tv_end_time)
    TextView tvEndTime;
    @BindView(R.id.rv_emp)
    RecyclerView rvEmp;

    @BindView(R.id.add_open_info)
    ImageView addOpenInfo;

    private View headerView;
    private View emptyView;
    private TextView tvEmpName;
    private TextView tvPer;
    private List<TenderPeopleInfo> emps = new ArrayList<>();
    private TenderEmpAdapter mAdapter;


    private boolean isGetEmp = false;
    private UserInfo userInfo;
    private TenderPlan plan;
    private TenderPlanDetailModel model;
    private TenderPlanDetailPresenter presenter;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new TenderPlanDetailModel(this);
        presenter = new TenderPlanDetailPresenter(this, model);
        initMVP(model,presenter);
        initData();
        initView();
    }

    private void initView(){
        tvTitle.setText("投标信息");
        if (plan!= null){
            tvNumber.setText(plan.getConNo());
            tvName.setText(plan.getProjecName());
            tvPerson.setText(plan.getConName());
            tvEndTime.setText(plan.getEndDate());
        }
        emptyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        headerView = getLayoutInflater().inflate(R.layout.item_tender_emp, null);
        tvEmpName = headerView.findViewById(R.id.tv_name);
        tvPer = headerView.findViewById(R.id.tv_per);
        tvEmpName.setTextColor(ContextCompat.getColor(YueApplacation.mContext, R.color.colorAccent));
        tvPer.setTextColor(ContextCompat.getColor(YueApplacation.mContext, R.color.colorAccent));
        if (rvEmp.getLayoutManager() == null){
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvEmp.setLayoutManager(manager);
        }
        mAdapter = new TenderEmpAdapter(R.layout.item_tender_emp, emps);
        mAdapter.setEmptyView(emptyView);
        mAdapter.setHeaderView(headerView);
        rvEmp.setAdapter(mAdapter);
    }

    private void initData(){
        userInfo = SettingManager.getInstance().getUserInfo();
        plan = (TenderPlan) getIntent().getSerializableExtra(Constant.INTENT_KEY_TENDER_PLAN);
    }

    private void getEmp(){
        mPresenter.getTenderEmps(userInfo.getId(), plan.getIpId());
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_tender_detail;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    @OnClick(R.id.tv_emp)
    public void showEmp(){
        rvEmp.setVisibility(View.VISIBLE);
        if (plan!= null && !StringUtil.isEmpty(plan.getIpId())){
            // 获得人员配置信息
            if (emps.size() == 0 && !isGetEmp){
                getEmp();
            }
        }
    }

    @OnClick(R.id.add_open_info)
    public void addOpenInfo(){
        Intent intent = new Intent();
        intent.setClass(this, TenderOpenInfoDetailActivity.class);
        intent.putExtra(Constant.INTENT_KEY_TENDER_PLAN_NAME, plan.getProjecName());
        intent.putExtra(Constant.INTENT_KEY_TENDER_PLAN_ID, plan.getIpId());
        startActivityForResult(intent, Constant.REQEST_CODE_ADD_TENDER_INFO);
    }

    @Override
    public void showEmps(List<TenderPeopleInfo> infos) {
        emps.clear();
        emps.addAll(infos);
        isGetEmp = true;
        mAdapter.notifyDataSetChanged();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode){
            case Constant.REQEST_CODE_ADD_TENDER_INFO:
                if (data!= null){
                    if (data.getBooleanExtra(Constant.INTENT_KEY_ISOk, false)){
                        addOpenInfo.setVisibility(View.GONE);
                    }
                }
                break;

                default:
                    break;
        }
    }
}
