package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.appcompat.widget.Toolbar;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 工程检查记录 发起
 */
public class ProjectCheckRecordListActivity extends BaseActivity {

    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.tool_bar)
    Toolbar toolBar;
    @BindView(R.id.fl_content)
    FrameLayout flContent;

    public static void launch(Activity activity){
        Intent intent=new Intent(activity,ProjectCheckRecordListActivity.class);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        init();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_project_check_record_list;
    }

    private void init() {
        tvTitle.setText("工程检查记录");
        tvSubmit.setVisibility(View.VISIBLE);
        tvSubmit.setText("添加");
        ivAction.setVisibility(View.GONE);
        FragmentManager fragmentManager = getSupportFragmentManager();
        FragmentTransaction fragmentTransaction = fragmentManager.beginTransaction();
        fragmentTransaction.add(R.id.fl_content, ProjectCheckRecordFragment.newInstance(ProjectCheckRecordFragment.TYPE_PROJECT_CHECK));// 或者fragmentTransaction.replace(ViewId,fragment);
        fragmentTransaction.commit();
    }

    @OnClick(R.id.iv_back)
    public void onClick() {
        finish();
    }

    @OnClick(R.id.tv_submit)
    public void add() {
        ProjectCheckActivity.launch(this,null,true);
    }

}
