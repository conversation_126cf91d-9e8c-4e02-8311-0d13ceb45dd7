package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.SuplogListAdapter;
import com.kisoft.yuejianli.adpter.interf.GlobalListener;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.SuplogListContract;
import com.kisoft.yuejianli.entity.MaterialInspect;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.ProjectSafeInspection;
import com.kisoft.yuejianli.entity.QualityAcceptance;
import com.kisoft.yuejianli.entity.QualityInspection;
import com.kisoft.yuejianli.entity.QualityInvisibility;
import com.kisoft.yuejianli.entity.SideReport;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.SuplogListModel;
import com.kisoft.yuejianli.presenter.SuplogListPresenter;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class SuplogListActivity extends BaseActivity<SuplogListModel, SuplogListPresenter>
        implements SuplogListContract.SuplogListViewContract {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;
    @BindView(R.id.iv_action)
    ImageView ivAction;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    List<Object> arrayList = new ArrayList<>();
    private SuplogListAdapter mAdapter;

    private SuplogListModel model;
    private SuplogListPresenter presenter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new SuplogListModel(this);
        presenter = new SuplogListPresenter(this, model);
        initMVP(model, presenter);
        initView();
        initData();
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        arrayList.clear();
        if (projectInfo != null) {
            presenter.setPramaras(userInfo.getId(), projectInfo.getProjectId(), getIntent().getStringExtra("date"));
            mPresenter.setListTypes(getIntent().getIntegerArrayListExtra("listTypes"));
        }
    }

    private void initView() {
        tvTitle.setText(getIntent().getStringExtra("title"));
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new SuplogListAdapter(mContext, arrayList, new GlobalListener() {
            @Override
            public void onViewClick(int id, int position, Object model) {

            }

            @Override
            public void onRootViewClick(View view, int position, Object item) {
                if (item == null)
                    return;
                if (item instanceof QualityInspection) {
                    Intent intent = new Intent();
                    intent.setClass(mContext, ProcessQualityCheckActivity.class);
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(Constant.INTENT_KEY_SCENE_INSPECTION, (QualityInspection) item);
                    intent.putExtras(bundle);
                    startActivity(intent);
                } else if (item instanceof QualityAcceptance) {
                    Intent intent = new Intent();
                    intent.setClass(mContext, TaskQualityCheckActivity.class);
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(Constant.INTENT_KEY_QUALITY_CKECK, (QualityAcceptance) item);
                    intent.putExtras(bundle);
                    startActivity(intent);
                } else if (item instanceof SideReport) {
                    Intent intent = new Intent();
                    intent.setClass(mContext, OnSideCheckActivity.class);
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(Constant.INTENT_KEY_ONSIDE_INSPECTION, (SideReport) item);
                    intent.putExtras(bundle);
                    startActivity(intent);
                } else if (item instanceof MaterialInspect) {
                    Intent intent = new Intent();
                    intent.setClass(mContext, MaterialQualityCheckActivity.class);
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(Constant.INTENT_KEY_MATERAIL_CHECK, (MaterialInspect) item);
                    intent.putExtras(bundle);
                    startActivity(intent);
                } else if (item instanceof ProjectSafeInspection) {
                    Intent intent = new Intent();
                    intent.setClass(mContext, SceneSafeInspectActivity.class);
                    intent.putExtra("data", (ProjectSafeInspection) item);
                    startActivity(intent);
                } else if (item instanceof QualityInvisibility) {
                    Intent intent = new Intent();
                    intent.setClass(mContext, ShelteredCheckActivity.class);
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(Constant.INTENT_KEY_INVISIBILITY, (QualityInvisibility) item);
                    intent.replaceExtras(bundle);
                    startActivity(intent);
                }
            }
        });

        rvContent.setAdapter(mAdapter);
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_suplog_quality_inspction_select;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @Override
    public void listDataBack(List<Object> list) {
        if (list != null && !list.isEmpty()) {
            arrayList.addAll(list);
            mAdapter.notifyDataSetChanged();
        }
    }
}
