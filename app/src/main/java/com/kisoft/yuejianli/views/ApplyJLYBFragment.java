package com.kisoft.yuejianli.views;

import android.Manifest;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bigkoo.pickerview.TimePickerView;
import com.bigkoo.pickerview.lib.WheelView;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.adpter.EnclosureListAdapter;
import com.kisoft.yuejianli.adpter.ImageAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.MonthlySupContract;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.FunctionPermission;
import com.kisoft.yuejianli.entity.MonthlySupervision;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.MonthlySupModel;
import com.kisoft.yuejianli.presenter.MonthlySupPresenter;
import com.kisoft.yuejianli.ui.ImageDialog;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.PhoneUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import butterknife.BindView;
import butterknife.BindViews;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;

/**
 * 监理月报
 * Created by tudou on 2018/7/4.
 */

public class ApplyJLYBFragment extends BaseFragment<MonthlySupModel, MonthlySupPresenter> implements
        MonthlySupContract.MonthlySupViewContract {

    Unbinder unbinder;
    @BindViews({R.id.iv_item1, R.id.iv_item2, R.id.iv_item3, R.id.iv_item4, R.id.iv_item5, R.id.iv_item6, R.id.iv_item7, R.id.iv_item8})
    List<ImageView> ivItems;
    @BindViews({R.id.ll_content1, R.id.ll_content2, R.id.ll_content3, R.id.ll_content4, R.id.ll_content5, R.id.ll_content6})
    List<LinearLayout> llContents;

    @BindView(R.id.tv_project_name)
    TextView tvProjectName;
    @BindView(R.id.tv_project_manager)
    TextView tvProjectManager;
    @BindView(R.id.tv_creater)
    TextView tvCreater;
    @BindView(R.id.tv_create_date)
    TextView tvCreateDate;
    @BindView(R.id.tv_section)
    EditText etSection;
    @BindView(R.id.tv_cons_company)
    EditText etConsCompany;

    // 月报内容
    @BindView(R.id.et_content)
    EditText etContent;

    // 进度
    @BindView(R.id.et_syjhjd)
    EditText etSyjhjd;
    @BindView(R.id.et_xyjhjd)
    EditText etXyjhjd;
    @BindView(R.id.et_ljjd)
    EditText etLjjd;
    @BindView(R.id.et_jdms)
    EditText etJdms;
    @BindView(R.id.et_czwt)
    EditText etCzwt;

    // 计量支付情况
    @BindView(R.id.et_sywc)
    EditText etSywc;
    @BindView(R.id.et_byzf)
    EditText etByzf;
    @BindView(R.id.et_ljwc)
    EditText etLjwc;
    @BindView(R.id.et_ljzf)
    EditText etLjzf;
    @BindView(R.id.et_remark)
    EditText etRemark;

    // 质量情况
    @BindView(R.id.et_ztxw)
    EditText etZtxw;
    @BindView(R.id.et_sgxczlqk)
    EditText etSgxczlqk;
    @BindView(R.id.et_zlqxyhqk)
    EditText etZlqxyhqk;
    @BindView(R.id.et_jcqk)
    EditText etJcqk;
    @BindView(R.id.et_qtqk)
    EditText etQtqk;

    // 安全情况
    @BindView(R.id.et_byaqqk)
    EditText etByaqqk;

    @BindView(R.id.tv_time)
    TextView tvTime;
    @BindView(R.id.tv_sub)
    TextView tvSubmit;
    @BindView(R.id.iv_time_more)
    ImageView ivMoreTime;
    @BindView(R.id.ll_time)
    LinearLayout llTime;
    @BindView(R.id.iv_item7)
    ImageView ivItem7;
    @BindView(R.id.recycleViewPhoto)
    RecyclerView recycleViewPhoto;
    @BindView(R.id.iv_item8)
    ImageView ivItem8;
    @BindView(R.id.recycleViewAttachment)
    RecyclerView recycleViewAttachment;

    private TimePickerView tpv;

    private boolean canEdit = false;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    private MonthlySupervision monthlySupervision = new MonthlySupervision();
    private MonthlySupModel model;
    private MonthlySupPresenter presenter;

    static final String[] PERMISSIONS = new String[]{
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
    };
    private boolean isUpdate = false;
    private String date;
    private String wfId;
    ProcessListBean bean;
    private List<EnclosureListDto> dtoList=new ArrayList<>();
    private EnclosureListAdapter enclosureAdapter;
    private List<String> images = new ArrayList<>();
    private ImageDialog imageDialog;
    private ImageAdapter imageAdapter;

    @Override
    public int getRootView() {
        return R.layout.activity_monthly_sup;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        // TODO: inflate a fragment view
        View rootView = super.onCreateView(inflater, container, savedInstanceState);
        model = new MonthlySupModel(mContext);
        presenter = new MonthlySupPresenter(this, model);
        unbinder = ButterKnife.bind(this, rootView);
        initListView();
        initData();
        initView();
        return rootView;


    }

    /*@Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new MonthlySupModel(this);
        presenter = new MonthlySupPresenter(this, model);
        initMVP(model, presenter);
        initData();
        initView();
    }*/

    private void initListView() {
        imageDialog = new ImageDialog();
        imageAdapter = new ImageAdapter(R.layout.item_iamge_show, images);
        imageAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                showImage(images.get(position));
            }
        });
        recycleViewPhoto.setLayoutManager(new LinearLayoutManager(getContext(),LinearLayoutManager.HORIZONTAL,false));
        recycleViewPhoto.setAdapter(imageAdapter);
        LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
        layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        layoutManager.setSmoothScrollbarEnabled(false);
        recycleViewAttachment.setLayoutManager(layoutManager);
        recycleViewAttachment.setNestedScrollingEnabled(false);
        enclosureAdapter = new EnclosureListAdapter(dtoList);
        recycleViewAttachment.setAdapter(enclosureAdapter);
        recycleViewAttachment.addOnItemTouchListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (!StringUtil.isEmpty(dtoList.get(position).getFilePath())) {
//                    presenter.downFile(dtoList.get(position).getFileName(), dtoList.get(position).getFilePath());
                    Intent intent = new Intent();
                    intent.setAction(Intent.ACTION_VIEW);
                    intent.setData(Uri.parse(StringUtil.handlerUrl(dtoList.get(position).getFilePath()+"&filename="+dtoList.get(position).getFileName())));
                    mContext.startActivity(intent);
                }else {
                    showToast("附件地址为空");
                }
            }
        });
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        date = getActivity().getIntent().getStringExtra("date");
        wfId = getActivity().getIntent().getStringExtra("wfId");
        isUpdate = getActivity().getIntent().getBooleanExtra("isUpdate", false);

        if (isUpdate) {
            if (!StringUtil.isEmpty(wfId) && !StringUtil.isEmpty(date)) {
                presenter.getMonthlyInfo(date, userInfo.getId(), projectInfo.getProjectId(), wfId);
                canEdit = false;
            }
        } else {
            monthlySupervision = (MonthlySupervision) getActivity().getIntent().getSerializableExtra(Constant.INTENT_KEY_MONTHLY_SUP);
            if (monthlySupervision != null) {
                canEdit = false;
            } else {
                monthlySupervision = new MonthlySupervision();
                monthlySupervision.setCreateId(userInfo.getId());
                monthlySupervision.setCreateName(userInfo.getName());
                canEdit = true;
                if (projectInfo != null) {
                    monthlySupervision.setPjDirId(projectInfo.getPjDirId());
                    monthlySupervision.setPjDirName(projectInfo.getPjDirName());
                    monthlySupervision.setProjectId(projectInfo.getProjectId());
                    monthlySupervision.setProjectName(projectInfo.getProjectName());

                    bean = ((ApplyActivity) getActivity()).bean;
                    presenter.getMonthlySupervision("", bean.getFlowTaskId(), bean.getWfType(), bean.getFlowTaskState(), bean.getBusinessId(), SettingManager.getInstance().getUserId());
//                    presenter.getEnclosureList("200927134432400e979e23ef674ce0be");
                    presenter.getEnclosureList(bean.getBusinessId());
                    //获得现场的月统计信息
                    presenter.getMonthCountInfo(userInfo.getId(), projectInfo.getProjectId(), DateUtil.getMonthDate());
                }
            }
        }
    }

    private void initView() {
        if (canEdit) {            // 新增
            tvSubmit.setVisibility(View.VISIBLE);
            tvProjectName.setText(projectInfo != null ? projectInfo.getProjectName() : "");
            tvProjectManager.setText(projectInfo != null ? projectInfo.getPjDirName() : "");
            tvCreater.setText(userInfo.getName());
            String creatDate = DateUtil.dateToString(new Date(), DateUtil.YMD);
            tvCreateDate.setText(creatDate);
            monthlySupervision.setCreateTime(creatDate);
        } else {                 // 预览
            if (!isUpdate) {
                tvSubmit.setVisibility(View.GONE);
                tvTime.setEnabled(false);
                llTime.setEnabled(false);
                tvProjectName.setText(StringUtil.isEmpty(monthlySupervision.getProjectName()) ? projectInfo.getProjectName() :
                        monthlySupervision.getProjectName());
                tvCreater.setText(monthlySupervision.getCreateName());
                tvProjectManager.setText(monthlySupervision.getPjDirName());
                etSection.setText(monthlySupervision.getSupervisionSection());
                etSection.setHint("");
                etSection.setEnabled(false);
                etConsCompany.setText(monthlySupervision.getConstructionUnit());
                etConsCompany.setHint("");
                etConsCompany.setEnabled(false);
                ivMoreTime.setVisibility(View.GONE);
                tvTime.setText(DateUtil.getYmdByTime(monthlySupervision.getMonthlyReportDate()));
                tvCreateDate.setText(monthlySupervision.getCreateTime());
                etContent.setText(monthlySupervision.getMonthlyText());
                etContent.setHint("");
                etContent.setEnabled(false);
                etSyjhjd.setText(monthlySupervision.getLastRatio());
                etSyjhjd.setHint("");
                etSyjhjd.setEnabled(false);
                etXyjhjd.setText(monthlySupervision.getNextRatio());
                etXyjhjd.setHint("");
                etXyjhjd.setEnabled(false);
                etLjjd.setText(monthlySupervision.getCurrRatio());
                etLjjd.setHint("");
                etLjjd.setEnabled(false);
                etJdms.setText(monthlySupervision.getProgRemark());
                etJdms.setHint("");
                etJdms.setEnabled(false);
                etCzwt.setText(monthlySupervision.getProbMeasure());
                etCzwt.setHint("");
                etCzwt.setEnabled(false);

                etSywc.setText(monthlySupervision.getLastCompamount());
                etSywc.setHint("");
                etSywc.setEnabled(false);
                etByzf.setText(monthlySupervision.getCurrPayamount());
                etByzf.setHint("");
                etByzf.setEnabled(false);
                etLjwc.setText(monthlySupervision.getTotalCompamount());
                etLjwc.setHint("");
                etLjwc.setEnabled(false);
                etLjzf.setText(monthlySupervision.getToatlPayamount());
                etLjzf.setHint("");
                etLjzf.setEnabled(false);
                etRemark.setText(monthlySupervision.getPayAmountremark());
                etRemark.setHint("");
                etRemark.setEnabled(false);

                etZtxw.setText(monthlySupervision.getSafRemark1());
                etZtxw.setHint("");
                etZtxw.setEnabled(false);
                etSgxczlqk.setText(monthlySupervision.getSafRemark2());
                etSgxczlqk.setHint("");
                etSgxczlqk.setEnabled(false);
                etZlqxyhqk.setText(monthlySupervision.getSafRemark3());
                etZlqxyhqk.setHint("");
                etZlqxyhqk.setEnabled(false);
                etJcqk.setText(monthlySupervision.getSafRemark4());
                etJcqk.setHint("");
                etJcqk.setEnabled(false);
                etQtqk.setText(monthlySupervision.getSafRemark5());
                etQtqk.setHint("");
                etQtqk.setEnabled(false);

                etByaqqk.setText(monthlySupervision.getQuaRemark());
                etByaqqk.setHint("");
                etByaqqk.setEnabled(false);
            }
        }
        contentOpenClose(0);
        initDatePick();
    }

    private void initDatePick() {
        Calendar selectedDate = Calendar.getInstance();
        Calendar startDate = Calendar.getInstance();
        startDate.set(2018, 0, 1, 0, 0);
        Calendar endDate = Calendar.getInstance();
        //时间选择器
        tpv = new TimePickerView.Builder(mContext, new TimePickerView.OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {//选中事件回调
                // 这里回调过来的v,就是show()方法里面所添加的 View 参数，如果show的时候没有添加参数，v则为null
                TextView tvDate = (TextView) v;
                String month = DateUtil.dateToString(date, DateUtil.YM);
                tvDate.setText(month);
                monthlySupervision.setMonthlyReportDate(month);
                presenter.checkMonthlySup(projectInfo.getProjectId(), month);
            }
        })
                //年月日时分秒 的显示与否，不设置则默认全部显示
                .setType(new boolean[]{true, true, false, false, false, false})
                .setLabel("年", "月", "", "", "", "")
                .isCenterLabel(false)
                .setDividerType(WheelView.DividerType.WRAP)
                .setDividerColor(ContextCompat.getColor(YueApplacation.mContext, R.color.colorAccent))
                .setContentSize(PhoneUtil.dpTopx(YueApplacation.mContext, 8))
                .setDate(selectedDate)
                .setRangDate(startDate, endDate)
                .setDecorView(null)
                .build();
    }

    @OnClick(R.id.ll_time)
    public void getMoreTime() {
        tpv.show(tvTime);
    }

    @OnClick(R.id.tv_sub)
    public void submit() {
        if (projectInfo != null) {
            monthlySupervision.setConstructionUnit(etConsCompany.getText().toString());
            monthlySupervision.setCreateName(userInfo.getName());
            monthlySupervision.setMonthlyReportDate(tvTime.getText().toString());
            monthlySupervision.setSupervisionSection(etSection.getText().toString());
            // 月报内容
            monthlySupervision.setMonthlyText(etContent.getText().toString());
            // 进度
            monthlySupervision.setLastRatio(etSyjhjd.getText().toString());
            monthlySupervision.setNextRatio(etXyjhjd.getText().toString());
            monthlySupervision.setCurrRatio(etLjjd.getText().toString());
            monthlySupervision.setProgRemark(etJdms.getText().toString());
            monthlySupervision.setProbMeasure(etCzwt.getText().toString());

            // 计量支付情况
            monthlySupervision.setLastCompamount(etSywc.getText().toString());
            monthlySupervision.setCurrPayamount(etByzf.getText().toString());
            monthlySupervision.setTotalCompamount(etLjwc.getText().toString());
            monthlySupervision.setToatlPayamount(etLjzf.getText().toString());
            monthlySupervision.setPayAmountremark(etRemark.getText().toString());

            // 质量情况
            monthlySupervision.setSafRemark1(etZtxw.getText().toString());
            monthlySupervision.setSafRemark2(etSgxczlqk.getText().toString());
            monthlySupervision.setSafRemark3(etZlqxyhqk.getText().toString());
            monthlySupervision.setSafRemark4(etJcqk.getText().toString());
            monthlySupervision.setSafRemark5(etQtqk.getText().toString());

            // 安全情况
            monthlySupervision.setQuaRemark(etByaqqk.getText().toString());

            if (isComplete()) {
                if (isUpdate) {
                    monthlySupervision.setWfId(wfId);
                    presenter.updateMonthlyInfo(monthlySupervision);
                } else {
                    presenter.commit(userInfo.getId(), projectInfo.getProjectId(), monthlySupervision);
                }

            }
        }
    }

    @OnClick({R.id.ll_item1, R.id.ll_item2, R.id.ll_item3, R.id.ll_item4, R.id.ll_item5, R.id.ll_item6, R.id.ll_item7, R.id.ll_item8})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ll_item1:
                contentOpenClose(0);
                break;
            case R.id.ll_item2:
                contentOpenClose(1);
                break;
            case R.id.ll_item3:
                contentOpenClose(2);
                break;
            case R.id.ll_item4:
                contentOpenClose(3);
                break;
            case R.id.ll_item5:
                contentOpenClose(4);
                break;
            case R.id.ll_item6:
                contentOpenClose(5);
                break;
            case R.id.ll_item7:
                contentOpenClose(6);
                break;
            case R.id.ll_item8:
                contentOpenClose(7);
                break;
        }
    }

    private void contentOpenClose(int index) {
        if (index < 0 || index >= ivItems.size()) {
            return;
        }
        boolean selected = ivItems.get(index).isSelected();
        if(index==6){
            recycleViewPhoto.setVisibility(selected ? View.GONE : View.VISIBLE);
        }else if(index==7){
            recycleViewAttachment.setVisibility(selected ? View.GONE : View.VISIBLE);
        }else {
            llContents.get(index).setVisibility(selected ? View.GONE : View.VISIBLE);
        }
        ivItems.get(index).setSelected(!selected);
    }

    private boolean isComplete() {
        boolean complete = true;
        if (StringUtil.isEmpty(monthlySupervision.getCreateName())) {
            complete = false;
            showToast("请填写编写人");
        } else if (StringUtil.isEmpty(monthlySupervision.getMonthlyReportDate())) {
            complete = false;
            showToast("请填写月报日期");
        } else if (StringUtil.isEmpty(monthlySupervision.getConstructionUnit())) {
            complete = false;
            showToast("请填写施工单位");
        } else if (StringUtil.isEmpty(monthlySupervision.getMonthlyText())) {
            complete = false;
            showToast("请填写月报内容");
        }
        return complete;
    }

    @Override
    public void showCommitResult(String str) {
        String[] backs = str.split(",");
        if (backs != null && backs.length == 3) {
            ((ApplyActivity) getContext()).setBack(str);
        }
    }


    @Override// 通过Id获取  返回的月报详情信息
    public void infobackMonthlySupervision(MonthlySupervision info) {
        if (info != null) {
            tvSubmit.setVisibility(View.GONE);
            tvProjectName.setText(StringUtil.isEmpty(info.getProjectName()) ? projectInfo.getProjectName() :
                    info.getProjectName());
            tvCreater.setText(info.getCreateName());
            tvProjectManager.setText(info.getPjDirName());
            etSection.setText(info.getSupervisionSection());
            etSection.setHint("");
            etSection.setEnabled(false);
            etConsCompany.setText(info.getConstructionUnit());
            etConsCompany.setHint("");
            etConsCompany.setEnabled(false);
            ivMoreTime.setVisibility(View.GONE);
            tvTime.setText(DateUtil.getYmdByTime(info.getMonthlyReportDate()));
            tvTime.setEnabled(false);
            llTime.setEnabled(false);
            tvCreateDate.setText(info.getCreateTime());
            etContent.setText(info.getMonthlyText());
            etContent.setHint("");
            etContent.setEnabled(false);
            etSyjhjd.setText(info.getLastRatio());
            etSyjhjd.setHint("");
            etSyjhjd.setEnabled(false);
            etXyjhjd.setText(info.getNextRatio());
            etXyjhjd.setHint("");
            etXyjhjd.setEnabled(false);
            etLjjd.setText(info.getCurrRatio());
            etLjjd.setHint("");
            etLjjd.setEnabled(false);
            etJdms.setText(info.getProgRemark());
            etJdms.setHint("");
            etJdms.setEnabled(false);
            etCzwt.setText(info.getProbMeasure());
            etCzwt.setHint("");
            etCzwt.setEnabled(false);

            etSywc.setText(info.getLastCompamount());
            etSywc.setHint("");
            etSywc.setEnabled(false);
            etByzf.setText(info.getCurrPayamount());
            etByzf.setHint("");
            etByzf.setEnabled(false);
            etLjwc.setText(info.getTotalCompamount());
            etLjwc.setHint("");
            etLjwc.setEnabled(false);
            etLjzf.setText(info.getToatlPayamount());
            etLjzf.setHint("");
            etLjzf.setEnabled(false);
            etRemark.setText(info.getPayAmountremark());
            etRemark.setHint("");
            etRemark.setEnabled(false);

            etZtxw.setText(info.getSafRemark1());
            etZtxw.setHint("");
            etZtxw.setEnabled(false);
            etSgxczlqk.setText(info.getSafRemark2());
            etSgxczlqk.setHint("");
            etSgxczlqk.setEnabled(false);
            etZlqxyhqk.setText(info.getSafRemark3());
            etZlqxyhqk.setHint("");
            etZlqxyhqk.setEnabled(false);
            etJcqk.setText(info.getSafRemark4());
            etJcqk.setHint("");
            etJcqk.setEnabled(false);
            etQtqk.setText(info.getSafRemark5());
            etQtqk.setHint("");
            etQtqk.setEnabled(false);

            etByaqqk.setText(info.getQuaRemark());
            etByaqqk.setHint("");
            etByaqqk.setEnabled(false);

        }


    }

    @Override
    public void EnclosureListBack(List<EnclosureListDto> dtoList) {
        this.dtoList.clear();
        if (dtoList != null) {
            this.dtoList.addAll(dtoList);
        }
        enclosureAdapter.setNewData(this.dtoList);
        images.clear();
        for (EnclosureListDto enclosureListDto : this.dtoList) {
            images.add(enclosureListDto.getFilePath());
        }
        imageAdapter.setNewData(images);
    }

    @Override
    public void showMonthCountInfo(String info) {
        etContent.setText(info);
    }

    @Override
    public void checkMonthlySupBack(Boolean has) {
        if (has) {
            showToast("选择的月份月报已存在，请重新选择");
            tvTime.setText("");
            monthlySupervision.setMonthlyReportDate(null);
        }
    }

    @Override
    public void showUpdateResult(boolean isOk) {
        if (isOk) {
            showToast("修改成功");
            getActivity().finish();
        } else {
            showToast("修改失败");
        }
    }

    @Override
    public void monthlyInfoBack(MonthlySupervision info) {
        if (info != null) {
            monthlySupervision = info;
            tvSubmit.setText("修改");

            FunctionPermission permissionFunction = SettingManager.getInstance().getPermissionFunction();
            if (permissionFunction != null && permissionFunction.isHasUpdateFunction()) {
                tvSubmit.setVisibility(View.VISIBLE);
            }

            tvProjectName.setText(StringUtil.isEmpty(info.getProjectName()) ? projectInfo.getProjectName() :
                    info.getProjectName());
            tvCreater.setText(info.getCreateName());
            tvProjectManager.setText(info.getPjDirName());
            etSection.setText(info.getSupervisionSection());
            etSection.setHint("");
            //etSection.setEnabled(false);
            etConsCompany.setText(info.getConstructionUnit());
            etConsCompany.setHint("");
            //etConsCompany.setEnabled(false);
            ivMoreTime.setVisibility(View.GONE);
            tvTime.setText(DateUtil.getYmdByTime(info.getMonthlyReportDate()));
            tvTime.setEnabled(false);
            llTime.setEnabled(false);
            tvCreateDate.setText(info.getCreateTime());
            etContent.setText(info.getMonthlyText());
            etContent.setHint("");
            //etContent.setEnabled(false);
            etSyjhjd.setText(info.getLastRatio());
            etSyjhjd.setHint("");
            //etSyjhjd.setEnabled(false);
            etXyjhjd.setText(info.getNextRatio());
            etXyjhjd.setHint("");
            //etXyjhjd.setEnabled(false);
            etLjjd.setText(info.getCurrRatio());
            etLjjd.setHint("");
            //etLjjd.setEnabled(false);
            etJdms.setText(info.getProgRemark());
            etJdms.setHint("");
            //etJdms.setEnabled(false);
            etCzwt.setText(info.getProbMeasure());
            etCzwt.setHint("");
            //etCzwt.setEnabled(false);

            etSywc.setText(info.getLastCompamount());
            etSywc.setHint("");
            //etSywc.setEnabled(false);
            etByzf.setText(info.getCurrPayamount());
            etByzf.setHint("");
            //etByzf.setEnabled(false);
            etLjwc.setText(info.getTotalCompamount());
            etLjwc.setHint("");
            //etLjwc.setEnabled(false);
            etLjzf.setText(info.getToatlPayamount());
            etLjzf.setHint("");
            //etLjzf.setEnabled(false);
            etRemark.setText(info.getPayAmountremark());
            etRemark.setHint("");
            //etRemark.setEnabled(false);

            etZtxw.setText(info.getSafRemark1());
            etZtxw.setHint("");
            //etZtxw.setEnabled(false);
            etSgxczlqk.setText(info.getSafRemark2());
            etSgxczlqk.setHint("");
            //etSgxczlqk.setEnabled(false);
            etZlqxyhqk.setText(info.getSafRemark3());
            etZlqxyhqk.setHint("");
            //etZlqxyhqk.setEnabled(false);
            etJcqk.setText(info.getSafRemark4());
            etJcqk.setHint("");
            //etJcqk.setEnabled(false);
            etQtqk.setText(info.getSafRemark5());
            etQtqk.setHint("");
            //etQtqk.setEnabled(false);

            etByaqqk.setText(info.getQuaRemark());
            etByaqqk.setHint("");
            //etByaqqk.setEnabled(false);
        }


    }

    /**
     * 通过url，展示照片
     *
     * @param url
     */
    private void showImage(String url) {
        if (imageDialog != null) {
            imageDialog.showImageDialog(getActivity().getFragmentManager(), url);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }
}




/*package com.kisoft.yuejianli.views;

import com.kisoft.yuejianli.base.BaseFragment;

public class ApplyJLYBFragment extends BaseFragment {
    @Override
    public int getRootView() {
        return 0;
    }
}*/

