package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.EnclosureListAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.contract.MailDetailContract;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.MailInfoDetail;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.MailDetailModel;
import com.kisoft.yuejianli.presenter.MailDetailPresenter;
import com.kisoft.yuejianli.utils.ScreenUtils;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.recyclerview.HorizontalDividerItemDecoration;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;

/**
 * 邮箱详情附近
 */
public class MailAttachmentFragment extends BaseFragment<MailDetailModel, MailDetailPresenter> implements MailDetailContract.View {
    private static final String ARG_PARAM1 = "param1";
    @BindView(R.id.recycleViewFile)
    RecyclerView recycleViewFile;
    Unbinder unbinder;

    private String fmailId;
    private List<EnclosureListDto> dtoList=new ArrayList<>();
    private EnclosureListAdapter adapter;

    public MailAttachmentFragment() {
        // Required empty public constructor
    }

    /**
     * Use this factory method to create a new instance of
     * this fragment using the provided parameters.
     *
     * @param param1 Parameter 1.
     * @return A new instance of fragment MailAttachmentFragment.
     */
    public static MailAttachmentFragment newInstance(String param1) {
        MailAttachmentFragment fragment = new MailAttachmentFragment();
        Bundle args = new Bundle();
        args.putString(ARG_PARAM1, param1);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            fmailId = getArguments().getString(ARG_PARAM1);
        }
        mModel=new MailDetailModel(getContext());
        mPresenter=new MailDetailPresenter(this,mModel);
    }

    @Override
    public int getRootView() {
        return R.layout.fragment_mail_attachment;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        mPresenter.viewInBox(fmailId);
        recycleViewFile.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false));
        recycleViewFile.addItemDecoration(new HorizontalDividerItemDecoration.Builder(getContext())
                .drawable(R.color.line_space)
                .size(ScreenUtils.dip2px(getContext(), 1))
                .build());
        adapter = new EnclosureListAdapter(dtoList);
        recycleViewFile.setAdapter(adapter);
        recycleViewFile.addOnItemTouchListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (!StringUtil.isEmpty(dtoList.get(position).getFilePath())) {
                    Intent intent = new Intent();
                    intent.setAction(Intent.ACTION_VIEW);
                    intent.setData(Uri.parse(StringUtil.handlerUrl(dtoList.get(position).getFilePath() + "&filename=" + dtoList.get(position).getFileName())));
                    mContext.startActivity(intent);
                } else {
                    showToast("附件地址为空");
                }
            }
        });
    }

    @Override
    public void showMailDetail(MailInfoDetail mailInfoDetail) {
        if(null!=mailInfoDetail&&null!=mailInfoDetail.getAttachmentList()){
            dtoList.clear();
            for (MailInfoDetail.TMsgAnnexDto tMsgAnnexDto : mailInfoDetail.getAttachmentList()) {
                dtoList.add(new EnclosureListDto(tMsgAnnexDto.getFfileName(), SettingManager.getInstance().getBaseUrl()+"/servlet/FileService?method=downloadAttachment&fannexId="+tMsgAnnexDto.getFannexId()));
            }
            adapter.setNewData(dtoList);
        }
    }

    @Override
    public void transOk(MailInfoDetail mailInfoDetail) {

    }

    @Override
    public void sendMail(boolean isOk) {

    }

    @Override
    public void showError() {

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }
}
