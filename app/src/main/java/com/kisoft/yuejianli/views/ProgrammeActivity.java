package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ProgrammeAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.contract.ProgrammeContract;
import com.kisoft.yuejianli.entity.ProgrammeInfoList;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ProgrammeModel;
import com.kisoft.yuejianli.presenter.ProgrammePresenter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class ProgrammeActivity extends BaseActivity<ProgrammeModel,
        ProgrammePresenter> implements ProgrammeContract.ProgrammeViewContract {


    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;


    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private ProgrammePresenter presenter;
    private ProgrammeModel model;
    private String time;


    private View empotyView;
    private ProgrammeAdapter mAdapter;
    private List<ProgrammeInfoList.DataBean> prodataBeans = new ArrayList<>();

    @Override
    public int getLayoutId() {
        return R.layout.activity_programme;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ButterKnife.bind(this);
        model = new ProgrammeModel(this);
        presenter = new ProgrammePresenter(this, model);
        initMVP(model, presenter);
        getData();
        initData();
        initView();
    }

    private void getData() {
        time = getIntent().getStringExtra("time");
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
       /* SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        Date date = new Date();
        String format = dateFormat.format(date);*/
        if (projectInfo != null) {
            presenter.getProgrammeList(userInfo.getId(), time);
        }

    }

    private void initView() {
        tvTitle.setText(time);
        tvSubmit.setText("增加");
        tvSubmit.setVisibility(View.VISIBLE);

        empotyView = getLayoutInflater().inflate(R.layout.activity_programme, null);
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new ProgrammeAdapter(R.layout.activity_programme_item, prodataBeans);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                seeDetail(prodataBeans.get(position));
            }
        });
        mAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                switch (view.getId()) {
                    case R.id.tv_item_update:
                        Intent intent = new Intent();
                        intent.setClass(ProgrammeActivity.this, ProgrammeAddActivity.class);
                        intent.putExtra("cldGuid", prodataBeans.get(position).getCldGuid());
                        intent.putExtra("programType", 3);
                        startActivity(intent);
                        break;
                }
            }
        });
        rvContent.setAdapter(mAdapter);

    }

    private void seeDetail(ProgrammeInfoList.DataBean dataBean) {
        Intent intent = new Intent();
        intent.setClass(this, ProgrammeAddActivity.class);
        intent.putExtra("programType", 2);
        intent.putExtra("cldGuid", dataBean.getCldGuid());
        startActivity(intent);
    }

    @Override
    public void showProgrammeList(List<ProgrammeInfoList.DataBean> list) {
        if (list != null) {
            prodataBeans.clear();

            Collections.sort(list, new Comparator<ProgrammeInfoList.DataBean>() {
                @Override
                public int compare(ProgrammeInfoList.DataBean dataBean, ProgrammeInfoList.DataBean t1) {
                    return dataBean.getHh24().compareTo(t1.getHh24());
                }
            });

            for (int i=0;i<list.size();i++){
                if(i==0){
                    list.get(i).setUsername("1");
                }else if(i>0){
                    if (list.get(i).getHh24().equals(list.get(i-1).getHh24())){
                        list.get(i).setUsername("2");
                    }else{
                        list.get(i).setUsername("1");
                    }
                }
            }
            this.prodataBeans.addAll(list);
            mAdapter.notifyDataSetChanged();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        presenter.getProgrammeList(userInfo.getId(), time);
    }

    @OnClick({R.id.iv_back, R.id.tv_submit, R.id.tv_title})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.tv_submit:
                Intent intent = new Intent();
                intent.setClass(ProgrammeActivity.this, ProgrammeAddActivity.class);
                startActivity(intent);
                break;
            /*case R.id.tv_update:
                Intent intent1=new Intent();
                intent1.setClass(this,ProgrammeAddActivity.class);
                intent1.putExtra("isUpdate",true);
                startActivity(intent1);
                break;*/

           /* case R.id.tv_title:
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
                Date date = new Date();
                String format = dateFormat.format(date);
                presenter.getProgrammeList(userInfo.getId(),format);
                break;*/
        }
    }

}











