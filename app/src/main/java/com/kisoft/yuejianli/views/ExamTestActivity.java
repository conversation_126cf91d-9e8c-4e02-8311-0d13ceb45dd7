package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.widget.Toolbar;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 试题练习
 */
public class ExamTestActivity extends BaseActivity {

    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.tool_bar)
    Toolbar toolBar;
    @BindView(R.id.tvOneDayTest)
    TextView tvOneDayTest;
    @BindView(R.id.tvOrderTest)
    TextView tvOrderTest;
    @BindView(R.id.tvClassifyTest)
    TextView tvClassifyTest;
    @BindView(R.id.tvRandomTest)
    TextView tvRandomTest;

    public static void launch(Activity activity) {
        Intent intent = new Intent(activity, ExamTestActivity.class);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        init();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_exam_test;
    }

    private void init(){
        tvTitle.setText("试题练习");
    }

    @OnClick({R.id.iv_back,R.id.tvOneDayTest,R.id.tvOrderTest,R.id.tvClassifyTest,R.id.tvRandomTest})
    void buttonClick(View view){
        switch (view.getId()){
            case R.id.iv_back:
                finish();
                break;
            case R.id.tvOneDayTest:
                ExamIngActivity.launch(this,"每日一练",ExamIngActivity.EXAM_TYPE_DAILY_PRACTICE,null,null);
                break;
            case R.id.tvOrderTest:
                ExamIngActivity.launch(this,"顺序练习",ExamIngActivity.EXAM_TYPE_ORDER_PRACTICE,null,null);
                break;
            case R.id.tvClassifyTest:
                ExamClassifyTestActivity.launch(this);
                break;
            case R.id.tvRandomTest:
                ExamIngActivity.launch(this,"随机练习",ExamIngActivity.EXAM_TYPE_RANDOM_PRACTICE,null,null);
                break;
        }
    }

}
