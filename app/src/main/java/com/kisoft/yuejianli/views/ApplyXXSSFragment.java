package com.kisoft.yuejianli.views;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ImageAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ApplyXXSSContract;
import com.kisoft.yuejianli.entity.InformationInfo;
import com.kisoft.yuejianli.entity.PhotoUploadResulte;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ApplyXXSSModel;
import com.kisoft.yuejianli.presenter.ApplyXXSSPresenter;
import com.kisoft.yuejianli.ui.ImageDialog;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.PermissionsChecker;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.views.watermark.CallBackActivity;
import com.kisoft.yuejianli.views.watermark.ImageUtil;
import com.kisoft.yuejianli.views.watermark.PhotoCaptureActivity;
import com.kisoft.yuejianli.views.watermark.PhotoListener;
import com.kisoft.yuejianli.views.watermark.WaterMask;
import com.kisoft.yuejianli.views.watermark.WaterMaskHelper;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;

public class ApplyXXSSFragment extends BaseFragment<ApplyXXSSModel, ApplyXXSSPresenter> implements ApplyXXSSContract.ApplyXXSSViewContract, WaterMask.WaterMaskListener, PhotoListener {


    @BindView(R.id.tv_projectname)
    TextView tvProjectname;
    @BindView(R.id.tv_name)
    TextView tvName;
    @BindView(R.id.sp_appeal_type)
    Spinner spAppealType;
    @BindView(R.id.tv_file_type)
    TextView tvFileType;
    @BindView(R.id.sp_project_name)
    Spinner spProjectName;
    @BindView(R.id.tv_project_name)
    TextView tvProjectName;
    @BindView(R.id.sp_project_unit)
    Spinner spProjectUnit;
    @BindView(R.id.tv_project_unit)
    TextView tvProjectUnit;
    @BindView(R.id.ll_project_unit)
    LinearLayout llProjectUnit;
    @BindView(R.id.et_reasons)
    EditText etReasons;
    @BindView(R.id.rv_xxssenclosure)
    RecyclerView rvXxssenclosure;
    @BindView(R.id.tv_sub)
    TextView tvSub;
    Unbinder unbinder;
    @BindView(R.id.ll_rizhi)
    LinearLayout llRizhi;
    @BindView(R.id.sp_year)
    Spinner spYear;
    @BindView(R.id.sp_month)
    Spinner spMonth;
    @BindView(R.id.tv_workdate)
    TextView tvWorkdate;
    @BindView(R.id.tv_month_report)
    TextView tvMonth_report;

    @BindView(R.id.ll_yuebao)
    LinearLayout llYuebao;
    @BindView(R.id.line_yuebao)
    View lineYuebao;
    @BindView(R.id.ll_project)
    LinearLayout llProject;
    @BindView(R.id.ll_yebao1)
    LinearLayout llYebao1;

    private boolean isApply;
    private String flowStateName="";
    private ApplyXXSSModel model;
    private ApplyXXSSPresenter presenter;
    InformationInfo informationInfo = new InformationInfo();
    ProcessListBean bean;

    List<String> yearStr = new ArrayList<>();
    List<String> monthStr = new ArrayList<>();

    private String year = "--请选择--";
    private String month = "--请选择--";
    private String appealType = "--请选择--";//申诉类型：0.日志 1.月报
    private String userName = SettingManager.getInstance().getUserInfo().getName();
    private String userId = SettingManager.getInstance().getUserId();


    private PermissionsChecker permissionsChecker;
    private List<String> images = new ArrayList<>();
    private BGAPhotoHelper mPhotoHelper;
    private ImageAdapter imageAdapter;
    static final String[] PERMISSIONS = new String[]{
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA
    };
    private static final int REQUEST_CODE_CHOOSE_PHOTO = 1;
    private static final int REQUEST_CODE_TAKE_PHOTO = 2;
    private static final int REQUEST_CODE_CROP = 3;
    private static final int REQUEST_CONSTRUCTIONUNITLIST_ACTIVITY = 4;
    private ImageDialog imageDialog;

    private boolean update=false;
    private String wfId;

    private View lastView;
    private int maskLocation=4;
    private ArrayList<String> uris;
    WaterMaskHelper waterMaskHelper = new WaterMaskHelper(mContext);
    private boolean isTakePhoto;
    AMapLocation locationdata = null;
    String address="";

    @Override
    public int getRootView() {
        return R.layout.activity_apply_xxss_layout;
    }

    @Subscribe
    public void onEvent(Object object) {

    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        // TODO: inflate a fragment view
        View rootView = super.onCreateView(inflater, container, savedInstanceState);
        model = new ApplyXXSSModel(mContext);
        presenter = new ApplyXXSSPresenter(this, model);
        unbinder = ButterKnife.bind(this, rootView);
        initWater();
        initData();
        initView();
        return rootView;
    }
    //初始化水印
    private void initWater() {
        //binding.setPresenter(new Presenter());
        //初始化水印工具
        waterMaskHelper = new WaterMaskHelper(mContext, this, this);
        //lastView = binding.txtCenter;
        //lastView.setSelected(true);
        maskLocation = WaterMask.DefWaterMaskParam.Location.center;
    }

    @Override                       //添加水印：
    public WaterMask.WaterMaskParam onDraw() {

        //拍照后调用，设置水印的基本参数
        WaterMask.WaterMaskParam param = new WaterMask.WaterMaskParam();
        //创建当前时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        Date date = new Date();
        String format = dateFormat.format(date);
        param.txt.add(format+" "+address);
        param.location = 3;
        param.itemCount = 60;
        return param;
    }

    @Override
    public void onChoose(ArrayList<String> photos) {
        uris = photos;
        //Glide.with(CompleteCheckAddActivity.this).load(photos.get(0)).placeholder(R.mipmap.ic_launcher).centerCrop().error(R.mipmap.ic_launcher).crossFade().into(ivSfenclosure);
    }
    private void initData() {
        isApply = ((ApplyActivity) getActivity()).isApply;
        flowStateName=((ApplyActivity) getActivity()).flowStateName;

        yearStr.add(0, "--请选择--");
        yearStr.add(1, "2017");
        yearStr.add(2, "2018");
        yearStr.add(3, "2019");
        yearStr.add(4, "2020");
        yearStr.add(5, "2021");
        yearStr.add(6, "2022");
        yearStr.add(7, "2023");
        yearStr.add(8, "2024");
        yearStr.add(9, "2025");
        yearStr.add(10, "2026");
        yearStr.add(11, "2027");
        yearStr.add(12, "2028");

        monthStr.add(0, "--请选择--");
        monthStr.add(1, "01");
        monthStr.add(2, "02");
        monthStr.add(3, "03");
        monthStr.add(4, "04");
        monthStr.add(5, "05");
        monthStr.add(6, "06");
        monthStr.add(7, "07");
        monthStr.add(8, "08");
        monthStr.add(9, "09");
        monthStr.add(10, "10");
        monthStr.add(11, "11");
        monthStr.add(12, "12");

        if(isApply){
            permissionsChecker = new PermissionsChecker(mContext);
            // 初始添加现场照片
            images.add("");
            //images.add("");
            //inspection.setQiEnclosure(StringUtil.imageArryToString(images));
        }
    }

    private void initView() {
        tvProjectname.setText(SettingManager.getInstance().getProject().getProjectName());
        tvName.setText(userName);

        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);
        //判断照片是否为空
        if (rvXxssenclosure.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(mContext);
            manager.setOrientation(LinearLayoutManager.HORIZONTAL);
            rvXxssenclosure.setLayoutManager(manager);
        }
        imageAdapter = new ImageAdapter(R.layout.item_iamge_show, images);
        imageAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (StringUtil.isEmpty(images.get(position))) {
                    if (permissionsChecker.lacksPermissions(PERMISSIONS)) {
                        getPermissions(Constant.REQUEST_CODE_TAKE_POHO);
                    } else {
                        // todo 空照片 ，添加
                        String str[] = new String[]{"系统相机", "手机相册"};
                        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
                        ab.setItems(str, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                switch (which) {
                                    case 0://相机
                                        isTakePhoto = true;
                                        takePhoto();
                                        break;
                                    case 1://相册
                                        isTakePhoto = false;
                                        getPhoto();
                                        break;
                                }
                            }
                        });
                        ab.show();
                    }
                } else {
                    showImage(images.get(position));
                }
            }
        });
        rvXxssenclosure.setAdapter(imageAdapter);
        imageDialog = new ImageDialog();

        spAppealType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position == 1) {
                    lineYuebao.setVisibility(View.VISIBLE);
                    llYuebao.setVisibility(View.GONE);
                    llRizhi.setVisibility(View.VISIBLE);
                    appealType = "0";//申诉类型0=日志、1=月报
                } else if (position == 2) {
                    lineYuebao.setVisibility(View.VISIBLE);
                    llRizhi.setVisibility(View.GONE);
                    llYuebao.setVisibility(View.VISIBLE);
                    appealType = "1";//申诉类型0=日志、1=月报
                } else {
                    lineYuebao.setVisibility(View.GONE);
                    llYuebao.setVisibility(View.GONE);
                    llRizhi.setVisibility(View.GONE);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        ArrayAdapter<String> yearAdapter = new ArrayAdapter<String>(mContext, R.layout.support_simple_spinner_dropdown_item, yearStr);
        spYear.setAdapter(yearAdapter);
        spYear.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                year = yearStr.get(position);
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        ArrayAdapter<String> monthAdapter = new ArrayAdapter<String>(mContext, R.layout.support_simple_spinner_dropdown_item, monthStr);
        spMonth.setAdapter(monthAdapter);
        spMonth.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                month = monthStr.get(position);
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        if(isApply){//增加界面


        }else{//详情
            tvName.setEnabled(false);
            etReasons.setEnabled(false);
            spAppealType.setVisibility(View.GONE);
            tvSub.setVisibility(View.GONE);
            tvWorkdate.setEnabled(false);

            bean = ((ApplyActivity) getActivity()).bean;
            presenter.getApplyXXSSInfo("",
                    bean.getFlowTaskId(),
                    bean.getWfType(),
                    bean.getFlowTaskState(),
                    bean.getBusinessId(),
                    SettingManager.getInstance().getUserInfo().getId());
        }
    }
    BGAPhotoHelper1 mPhotoHelper1=new BGAPhotoHelper1();
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_CODE_CHOOSE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    double fileSize = FileUtil.getFileOrFilesSize(mPhotoHelper.getFilePathFromUri(data.getData()), 3);
                    Log.i("fileSize", fileSize + "");
                    if (fileSize > 1) {
                        startActivityForResult(mPhotoHelper1.getCropIntent(ImageUtil.compressImage(mPhotoHelper.getFilePathFromUri(data.getData())), 800, 800), REQUEST_CODE_CROP);
                    } else {
                        startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), REQUEST_CODE_CROP);
                    }
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        }
        if (requestCode == REQUEST_CODE_TAKE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    double fileSize = FileUtil.getFileOrFilesSize(mPhotoHelper.getCameraFilePath(), 3);
                    Log.i("fileSize", fileSize + "");
                    if (fileSize > 1) {
                        startActivityForResult(mPhotoHelper1.getCropIntent(ImageUtil.compressImage(mPhotoHelper.getCameraFilePath()), 800, 800), REQUEST_CODE_CROP);
                    } else {
                        startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800, 800), REQUEST_CODE_CROP);
                    }
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        }
        if (requestCode == REQUEST_CODE_CROP) {
            if (resultCode == Activity.RESULT_OK) {
                if (isTakePhoto) {
                    //重点在这里
                    // getCropIntent  获取裁剪完图片的路径
                    File file = new File(mPhotoHelper1.getCropFilePath());
                    //应该在这里把绘制完的水印图片路径传过去
                    ArrayList<String> strings = new ArrayList<>();
                    strings.add(file.toString());
                    if (CallBackActivity.getPhotoListener() != null){
                        //选择照片的uri，默认为下标1的元素
                        CallBackActivity.getPhotoListener().onChoose(strings);
                    }
                    if (CallBackActivity.getWaterMarkListener() != null) {
                        WaterMask.WaterMaskParam maskParam = CallBackActivity.getWaterMarkListener().onDraw();
                        CallBackActivity.getWaterMarkListener().onDraw();
                        Bitmap bitmap = ImageUtil.getBitmap(String.valueOf((file)));
                        WaterMask.draw(mContext, bitmap, String.valueOf((file)), maskParam);
                        mContext.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(file)));
                    }
                    presenter.uploadPhotoImage(file.getPath());
                    Log.i("tag",file.getPath());
                }else{
                    presenter.uploadPhotoImage(mPhotoHelper1.getCropFilePath());
                }
            } else {
                mPhotoHelper.deleteCameraFile();
                mPhotoHelper.deleteCropFile();
            }
        }


        if (requestCode == 630) {
            if (resultCode == Constant.REQEST_CODE_SELECT_COMPANY_ORG_INFO) {
                if (data != null) {
                    List<String> ids = new ArrayList<>();
                    List<String> names = new ArrayList<>();
                    ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                    names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                    if (names != null && names.size() > 0) {
                        userId = ids.get(0);
                        userName = names.get(0);
                        tvName.setText(userName);
                    }
                }
            }
        }


    }

    @OnClick({R.id.tv_name, R.id.rv_xxssenclosure, R.id.tv_sub})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_name:
                Intent intent = new Intent();
                intent.putExtra("isSingle", true);
                intent.setClass(mContext, CompanyOrgInfoActivity.class);
                startActivityForResult(intent, 630);
                break;
            case R.id.rv_xxssenclosure:
                break;
            case R.id.tv_sub:
                if(update){//修改
                    if(appealType.equals("0")){//修改日志
                        Intent intentToLog=new Intent(mContext,SupervisionLogAvcivity.class);
                        intentToLog.putExtra("date",tvWorkdate.getText().toString());
                        intentToLog.putExtra("wfId",wfId);
                        intentToLog.putExtra("isUpdate",true);
                        startActivity(intentToLog);
                        getActivity().finish();
                    }else if(appealType.equals("1")){//修改月报
                        Intent intentToMonth=new Intent(mContext,MonthlySupActivity.class);
                        intentToMonth.putExtra("date",tvMonth_report.getText().toString()+"-01");
                        intentToMonth.putExtra("wfId",wfId);
                        intentToMonth.putExtra("isUpdate",true);
                        startActivity(intentToMonth);
                        getActivity().finish();
                    }


                }else{//增加
                    if (appealType.equals("--请选择--")) {
                        showToast("请选择申诉类型");
                        return;
                    } else if (appealType.equals("0")) {
                        if (TextUtils.isEmpty(tvWorkdate.getText())) {
                            showToast("请选择申诉日期");
                        }
                    } else if (appealType.equals("1")) {
                        if (year.equals("--请选择--") || month.equals("--请选择--")) {
                            showToast("请选择月报月份");
                        }
                    }
                    informationInfo.setSaId("");
                    informationInfo.setProjectId(SettingManager.getInstance().getProject().getProjectId());
                    informationInfo.setProjectName(SettingManager.getInstance().getProject().getProjectName());
                    informationInfo.setAppealType(appealType);//申诉类型0=日志、1=月报
                    if(appealType.equals("0")){
                        informationInfo.setAppealDate(tvWorkdate.getText().toString());//申诉日期
                        informationInfo.setCurrYear("");
                        informationInfo.setCurrMonth("");
                    }else if(appealType.equals("1")){
                        informationInfo.setCurrYear(year);
                        informationInfo.setCurrMonth(month);
                        informationInfo.setAppealDate("");
                    }
                    informationInfo.setReason(etReasons.getText().toString());//申诉原因
                    informationInfo.setUserId(userId);//申诉人Id
                    informationInfo.setUserName(userName);//申诉人Name
                    informationInfo.setCreateId(SettingManager.getInstance().getUserId());
                    informationInfo.setCreateName(SettingManager.getInstance().getUserInfo().getName());
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
                    Date date = new Date();
                    String format = dateFormat.format(date);
                    informationInfo.setCreateTime(format);//创建时间
                    informationInfo.setWfId("");
                    informationInfo.setWfStatus("");

                    //手机端照片路径
                    String imgUrls = "";
                    for (String url : images) {
                        imgUrls += url + ",";
                    }
                    if (!StringUtil.isEmpty(imgUrls)) {
                        imgUrls = imgUrls.substring(0, imgUrls.length() - 1);
                    }
                    String xxssEnclosure = imgUrls;//手机端照片
                    informationInfo.setSaEnclosure(xxssEnclosure);

                    presenter.submitApplyXXSS(informationInfo);
                }

                break;


        }
    }

    @Override
    public void applyXXSSInfoBack(InformationInfo info) {
        LinearLayout.LayoutParams params;
        if (info != null) {
            tvProjectname.setText(info.getProjectName());
            tvName.setText(info.getUserName());
            tvFileType.setVisibility(View.VISIBLE);
            appealType=info.getAppealType();
            if(appealType.equals("0")){
                tvFileType.setText("日志");
                llRizhi.setVisibility(View.VISIBLE);
                tvWorkdate.setText(info.getAppealDate());
                lineYuebao.setVisibility(View.VISIBLE);
            }else if(appealType.equals("1")){
                tvFileType.setText("月报");
                llYebao1.setVisibility(View.VISIBLE);
                tvMonth_report.setText(info.getCurrYear()+"-"+info.getCurrMonth());
                params=(LinearLayout.LayoutParams)lineYuebao.getLayoutParams();
                params.topMargin=(10);
                lineYuebao.setLayoutParams(params);
                lineYuebao.setVisibility(View.VISIBLE);
            }
            etReasons.setText(info.getReason());

            images.clear();
            String[] imgArr = info.getSaEnclosure().split(",");
            for (String url : imgArr) {
                if (!StringUtil.isEmpty(url)||!url.equals(""))
                    images.add(url);
            }
            imageAdapter.notifyDataSetChanged();

            /*images.clear();
            String[] imgArr = info.getPtEnclosure().split(",");
            for (String url : imgArr) {
                if (!StringUtil.isEmpty(url)||!url.equals(""))
                    images.add(url);
            }
            imageAdapter.notifyDataSetChanged();*/

            if(info.getCreateId().equals(info.getUserId())&&StringUtil.isEmpty(info.getModifyStatus())&&!StringUtil.isEmpty(flowStateName)&&flowStateName.equals("完成")){
                update=true;
                wfId=info.getWfId();
                tvSub.setText("去修改");
                tvSub.setVisibility(View.VISIBLE);
            }

        }
    }

    @Override
    public void applyXXSSBack(String str) {
        String[] backs = str.split(",");
        if (backs != null && backs.length == 3) {
            ((ApplyActivity) getContext()).setBack(str);
        }
    }

    //拍照功能代码

    /**
     * 6.0之上权限
     */
    private void getPermissions(int code) {
        ActivityCompat.requestPermissions(getActivity(), PERMISSIONS, code);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), REQUEST_CODE_TAKE_PHOTO);
            PhotoCaptureActivity.setWaterListener(waterMaskHelper.getWaterMarkListener());
            PhotoCaptureActivity.setPhotoListener(waterMaskHelper.getPhotoListener());
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 通过url，展示照片
     *
     * @param url
     */
    private void showImage(String url) {
        if (imageDialog != null) {
            imageDialog.showImageDialog(getActivity().getFragmentManager(), url);
        }
    }

    private void initImage(String url) {
        Log.i("000", "______图片地址：" + url);
        images.add(0, url);
        imageAdapter.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(PhotoUploadResulte resulte) {
        dismissProgress();
        showToast(resulte.getMessge());
        if (resulte.isIsok()) {
            // todo 图片上传成功
            String imageUri = SettingManager.getInstance().getBaseUrl() + resulte.getImageUrl();
            initImage(imageUri);
        } else {
            initImage(resulte.getImageUrl());
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == Constant.REQUEST_CODE_TAKE_POHO) {
            takePhoto();
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this);
    }

    @Override
    public void onStop() {
        super.onStop();
        if (EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().unregister(this);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();

    }

    /**
     * 展示日期选择对话框
     */
    private void showDatePickerDialog(final TextView tv) {
        Calendar c = Calendar.getInstance();
        new DatePickerDialog(mContext, new DatePickerDialog.OnDateSetListener() {
            @Override
            public void onDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                // TODO Auto-generated method stub
                int m = monthOfYear + 1;
                String month = m >= 10 ? m + "" : "0" + m;
                String day = dayOfMonth >= 10 ? dayOfMonth + "" : "0" + dayOfMonth;
                String dateStr = year + "-" + month + "-" + day;
                tv.setText(dateStr);
            }
        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)).show();
    }

    @OnClick(R.id.tv_workdate)
    public void onViewClicked() {
        showDatePickerDialog(tvWorkdate);
    }

    private AMapLocationClient locationClient = null;
    private AMapLocationClientOption locationOption = null;
    private void initLocation(){
        //初始化client
        locationClient = new AMapLocationClient(mContext);
        locationOption = getDefaultOption();
        //设置定位参数
        locationClient.setLocationOption(locationOption);
        // 设置定位监听
        locationClient.setLocationListener(locationListener);

    }
    /**
     * 默认的定位参数
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private AMapLocationClientOption getDefaultOption() {
        AMapLocationClientOption mOption = new AMapLocationClientOption();
        mOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);//可选，设置定位模式，可选的模式有高精度、仅设备、仅网络。默认为高精度模式
        mOption.setGpsFirst(false);//可选，设置是否gps优先，只在高精度模式下有效。默认关闭
        mOption.setHttpTimeOut(30000);//可选，设置网络请求超时时间。默认为30秒。在仅设备模式下无效
        mOption.setInterval(10000);//可选，设置定位间隔。默认为2秒
        mOption.setNeedAddress(true);//可选，设置是否返回逆地理地址信息。默认是true
        mOption.setOnceLocation(false);//可选，设置是否单次定位。默认是false
        mOption.setOnceLocationLatest(false);//可选，设置是否等待wifi刷新，默认为false.如果设置为true,会自动变为单次定位，持续定位时不要使用
        AMapLocationClientOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTP);//可选， 设置网络请求的协议。可选HTTP或者HTTPS。默认为HTTP
        mOption.setSensorEnable(false);//可选，设置是否使用传感器。默认是false
        mOption.setWifiScan(true); //可选，设置是否开启wifi扫描。默认为true，如果设置为false会同时停止主动刷新，停止以后完全依赖于系统刷新，定位位置可能存在误差
        mOption.setLocationCacheEnable(true); //可选，设置是否使用缓存定位，默认为true
        mOption.setGeoLanguage(AMapLocationClientOption.GeoLanguage.DEFAULT);//可选，设置逆地理信息的语言，默认值为默认语言（根据所在地区选择语言）
        return mOption;
    }
    /**
     * 定位监听
     */
    AMapLocationListener locationListener = new AMapLocationListener() {
        @Override
        public void onLocationChanged(AMapLocation location) {
            if (null != location) {
                locationdata = location;
                //errCode等于0代表定位成功，其他的为定位失败，具体的可以参照官网定位错误码说明
                if (location.getErrorCode() == 0) {
                    address=location.getAddress();
                    stopLocation();
                    destroyLocation();
                }
            } else {
                showToast("定位失败");
            }
        }
    };
    /**
     * 开始定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void startLocation() {
        // 设置定位参数
        locationClient.setLocationOption(locationOption);
        // 启动定位
        locationClient.startLocation();
    }
    /**
     * 停止定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void stopLocation() {
        // 停止定位
        locationClient.stopLocation();
    }

    /**
     * 销毁定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void destroyLocation() {
        if (null != locationClient) {
            /**
             * 如果AMapLocationClient是在当前Activity实例化的，
             * 在Activity的onDestroy中一定要执行AMapLocationClient的onDestroy
             */
            locationClient.onDestroy();
            locationClient = null;
            locationOption = null;
        }
    }
    @Override
    public void onResume() {
        super.onResume();
        //初始化定位
        initLocation();
        //启动定位
        startLocation();
    }
}
