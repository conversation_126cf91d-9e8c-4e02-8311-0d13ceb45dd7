package com.kisoft.yuejianli.views.autoform;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.viewpager.widget.ViewPager;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.MainFrameAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.FunctionPermission;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.entity.httpresult.MonthReportListResult;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.ui.NoScrollViewPager;
import com.kisoft.yuejianli.ui.YSelectTextViewCell;
import com.kisoft.yuejianli.ui.badgeview.YBadgeImageView;
import com.kisoft.yuejianli.ui.dragview.FloatLayer;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.views.ApplySPFragment;
import com.kisoft.yuejianli.views.ApplySPFragment1;
import com.kisoft.yuejianli.views.CompanyOrgInfoActivity;
import com.kisoft.yuejianli.views.YApprovalLogsActivity;

import org.xutils.common.util.LogUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Description: 流程审批基本信息  审批办理
 * Author     : bhd119
 * QQ         : *********
 */
public class PublicAutoFormApplyActivity extends BaseActivity {
    private static final String TAG = "ApplyActivity";
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rb_task_detail)
    RadioButton rbTaskDetail;
    @BindView(R.id.rb_task_record)
    RadioButton rbTaskRecord;
    @BindView(R.id.rg_title)
    RadioGroup rgTitle;
    @BindView(R.id.view_task_detail)
    View viewTaskDetail;
    @BindView(R.id.view_task_record)
    View viewTaskRecord;
    @BindView(R.id.vp_mainframe_content)
    NoScrollViewPager vpMainframeContent;

    // 附件
    @BindView(R.id.rb_task_attachment)
    RadioButton rbTaskAttachment;

    @BindView(R.id.view_task_attachment)
    View viewTaskAttachment;

    @BindView(R.id.tv_submit)
    TextView tvSubmit;


    /**
     * 导航的fragment界面集合
     **/
    private List<Fragment> list_fragments = new ArrayList<>();

    /**
     * 适配器
     **/
    private MainFrameAdapter adapter = null;

    /**
     * 页面类型  true：发起申请  false：申请详情
     */
    public boolean isApply;

    /**
     * 申请类型     0：员工请假  1：借款申请  2：补签申请      3：费用报销
     * 4：离职申请  5：开票申请  6：用印申请  7：保证金申请    8：证件借用申请  9：现场信息申诉（日志、月报）
     */

    //流程类型（1=待审批、2=已审批、3=我发起的）
    public String workType;

    //业务类型
    private String businessType;
    //回调名称
    private String callBackName;

    public ProcessListBean bean;

    private String titleName;

    public String transType;

    public String flowStateName;

    //private boolean isMonthReport=false;
    public MonthReportListResult.DataBean monthReportBean;

    CardView floatCardView = null;
    FloatLayer floatLayer = null;

    CardView floatCardView1 = null;
    FloatLayer floatLayer1 = null;
    private Map<String, Object> zfData;
    YSelectTextViewCell lczhuanfa;
    private PublicAutoFormSetDataInfo mData;

    public static void launch(Activity activity, PublicAutoFormSetDataInfo data,String formDefinitionId) {
        Intent intent = new Intent(activity, PublicAutoFormApplyActivity.class);
        intent.putExtra("PublicAutoFormApplyActivityData", (Serializable) data);
        intent.putExtra("formDefinitionId", formDefinitionId);
        activity.startActivity(intent);
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_document_detail;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        mData = (PublicAutoFormSetDataInfo) getIntent().getSerializableExtra(
                "PublicAutoFormApplyActivityData");
        isApply = getIntent().getBooleanExtra("isApply", true);
        String formDefinitionId = getIntent().getStringExtra("formDefinitionId");


        businessType = getIntent().getStringExtra("businessType");

        workType = getIntent().getStringExtra("workType");
        transType = getIntent().getStringExtra("transType");
        flowStateName = getIntent().getStringExtra("flowStateName");
        bean = (ProcessListBean) getIntent().getSerializableExtra("bean");
        String pageState = getIntent().getStringExtra("pageState");

        initData(formDefinitionId);
        initView();

        // 判断流程菜单功能
        FunctionPermission permissionFunction = SettingManager.getInstance().getPermissionFunction();
        if (permissionFunction != null && permissionFunction.isHasProcessMenu()) {
            if (!this.isApply) {
                initMenuPopView();
                initZhuanfaPopView();
                tvSubmit.setVisibility(View.VISIBLE);
                tvSubmit.setText("菜单");
            }
            // 转发查阅，送阅查阅
            if (StringUtil.isEqual("1", pageState) || StringUtil.isEqual("2", pageState)) {
                tvSubmit.setVisibility(View.GONE);
            }
        } else {
            tvSubmit.setVisibility(View.GONE);
        }
    }


    // 弹窗
    @OnClick(R.id.tv_submit)
    public void popMenu() {
        if (floatLayer == null) {
            initMenuPopView();
        } else {
            floatLayer.show(false);
        }
    }


    private void initMenuPopView() {

        //View inflate = LayoutInflater.from(mContext).inflate(R.layout.pop_bottom_menu, null, false);
        View headerView = LayoutInflater.from(mContext).inflate(R.layout.ease_search_bar, null);
        View inflate = LayoutInflater.from(mContext).inflate(R.layout.pop_bottom_menu, null);

        floatCardView = new CardView(mContext);
        floatCardView.addView(inflate);
        floatCardView.setCardBackgroundColor(Color.TRANSPARENT);
        floatCardView.setAlpha(0.5f);

        floatLayer = new FloatLayer(mContext);
        floatLayer.floatView(floatCardView);
        floatLayer.snapEdge(FloatLayer.Edge.NONE);
        floatLayer.outside(false);
        floatLayer.defPercentX(1);
        floatLayer.defPercentY(1F);
        floatLayer.paddingLeft(0);
        floatLayer.paddingTop(0);
        floatLayer.paddingRight(0);
        floatLayer.paddingBottom(0);
        floatLayer.marginLeft(0);
        floatLayer.marginTop(0);
        floatLayer.marginRight(0);
        floatLayer.marginBottom(0);

        FrameLayout pbm_bg_clear = inflate.findViewById(R.id.pbm_bg_clear);
        pbm_bg_clear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer.dismiss(false);
            }
        });

        // 流程转发
        YBadgeImageView lczhuanfa = inflate.findViewById(R.id.lczhuanfa);
        lczhuanfa.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer.dismiss(false);
                zfData = new HashMap<>();
                // 转发
                if (floatLayer1 == null) {
                    initZhuanfaPopView();
                } else {
                    floatLayer1.show(false);
                }
            }
        });

        // 流程撤回
        YBadgeImageView lcchehui = inflate.findViewById(R.id.lcchehui);
        lcchehui.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer.dismiss(false);
                lcchehui();
            }
        });

        // 流程催办
        YBadgeImageView lcchuiban = inflate.findViewById(R.id.lcchuiban);
        lcchuiban.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer.dismiss(false);
                lcChuiBan();
            }
        });

        // 流程记录
        YBadgeImageView lcjilu = inflate.findViewById(R.id.lcjilu);
        lcjilu.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer.dismiss(false);
                YApprovalLogsActivity.launch(PublicAutoFormApplyActivity.this, 1, bean.getFlowId(),
                        bean.getBusinessId());
            }
        });

        // 审批日志
        YBadgeImageView lcrizhi = inflate.findViewById(R.id.lcrizhi);
        lcrizhi.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer.dismiss(false);
                YApprovalLogsActivity.launch(PublicAutoFormApplyActivity.this, 2, bean.getFlowId(),
                        bean.getBusinessId());
            }
        });

        // 转发记录
        YBadgeImageView zhuanfajilu = inflate.findViewById(R.id.zhuanfajilu);
        zhuanfajilu.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer.dismiss(false);
                YApprovalLogsActivity.launch(PublicAutoFormApplyActivity.this, 3, bean.getFlowId(),
                        bean.getBusinessId());
            }
        });


        //流程类型（1=待审批、2=已审批、3=我发起的）
        if (StringUtil.isEqual("1", workType)) {
            lczhuanfa.setEnabled(true);
            lcjilu.setEnabled(true);
            lcrizhi.setEnabled(true);
            zhuanfajilu.setEnabled(true);
            lcchuiban.setEnabled(false);
            lcchehui.setEnabled(false);
        } else if (StringUtil.isEqual("2", workType)) {
            lczhuanfa.setEnabled(true);
            lcjilu.setEnabled(true);
            lcrizhi.setEnabled(true);
            zhuanfajilu.setEnabled(true);
            lcchuiban.setEnabled(true);
            lcchehui.setEnabled(false);
        } else if (StringUtil.isEqual("3", workType)) {
            lczhuanfa.setEnabled(true);
            lcjilu.setEnabled(true);
            lcrizhi.setEnabled(true);
            zhuanfajilu.setEnabled(true);
            lcchuiban.setEnabled(true);
            lcchehui.setEnabled(true);
        }

        if (!isApply) {
            if (StringUtil.isEqual("完成", bean.getFlowStateName()) || StringUtil.isEqual("已终止",
                    bean.getFlowStateName())) {
                lcchuiban.setEnabled(false);
                lcchehui.setEnabled(false);
            }
        }
    }

    private void initZhuanfaPopView() {

        LayoutInflater layoutInflater = LayoutInflater.from(mContext);
        View inflate = layoutInflater.inflate(R.layout.pop_menu_lczhuanfa, null, false);
        floatCardView1 = new CardView(mContext);
        floatCardView1.addView(inflate);
        floatCardView1.setCardBackgroundColor(Color.TRANSPARENT);
        floatCardView1.setAlpha(0.5f);

        floatLayer1 = new FloatLayer(mContext);
        floatLayer1.floatView(floatCardView1);
        floatLayer1.snapEdge(FloatLayer.Edge.NONE);
        floatLayer1.outside(false);
        floatLayer1.defPercentX(1);
        floatLayer1.defPercentY(1F);
        floatLayer1.paddingLeft(0);
        floatLayer1.paddingTop(0);
        floatLayer1.paddingRight(0);
        floatLayer1.paddingBottom(0);
        floatLayer1.marginLeft(0);
        floatLayer1.marginTop(0);
        floatLayer1.marginRight(0);
        floatLayer1.marginBottom(0);


        FrameLayout pbm_bg_clear = inflate.findViewById(R.id.pbm_bg_clear);
        pbm_bg_clear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                floatLayer1.dismiss(false);
            }
        });


        lczhuanfa = inflate.findViewById(R.id.lcZhuanfaCell);
        // 转发人
        lczhuanfa.getSelectView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent2 = new Intent();
                intent2.setClass(mContext, CompanyOrgInfoActivity.class);
                startActivityForResult(intent2, 2369);
            }
        });

        EditText et_remark = (EditText) inflate.findViewById(R.id.et_remark);

        TextView tv_sub = (TextView) inflate.findViewById(R.id.tv_sub);
        tv_sub.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                lczhuanfa(et_remark.getText().toString());
            }
        });
    }


    // 流程转发
    private void lczhuanfa(String s) {
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        zfData.put("remark", s);
        zfData.put("createId", userInfo.getId());
        zfData.put("createName", userInfo.getName());
        zfData.put("createTime", DateUtil.dateToString(new Date(), DateUtil.YMD_HM));
        zfData.put("businessId", bean.getBusinessId());
        zfData.put("businessType", bean.getBusinessType());
        zfData.put("state", "0");
        zfData.put("fileName", bean.getName());
        zfData.put("wfId", bean.getFlowId());
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("data", StringUtil.objectToJson(zfData));
        Api.getGbkApiserver().saveSysForwardRecord("saveSysForwardRecord_APP", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    showToast("转发成功");
                    floatLayer1.dismiss(false);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }


    // 流程撤回
    private void lcchehui() {
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("wfId", bean.getFlowId());
        parameters.put("userId", userInfo.getId());
        Api.getGbkApiserver().withdrawnew("withdraw_new", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    showToast(response.body().getData().toString());
                    floatLayer.dismiss(false);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    // 流程催办
    private void lcChuiBan() {
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("wfId", bean.getFlowId());
        parameters.put("userId", userInfo.getId());

        Api.getGbkApiserver().hastenWork("hastenWork", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    showToast("催办成功");
                    floatLayer.dismiss(false);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    private void initData(String formDefinitionId) {
        rbTaskAttachment.setVisibility(View.GONE);
        viewTaskAttachment.setVisibility(View.GONE);
        tvTitle.setText(mData.getTitle());

        PublicAutoFormApplyFragment fragment = new PublicAutoFormApplyFragment();
        fragment.businessType = "T_FORM_DATA_STORE_" + formDefinitionId;
        fragment.formDefinitionId = formDefinitionId;
        callBackName = "hr_WFCallBackService";
        fragment.setData(mData.getData());
        list_fragments.add(fragment);
        if (isApply) {
            list_fragments.add(new ApplySQFragment());
        } else {
            list_fragments.add(new ApplySPFragment());
        }

        adapter = new MainFrameAdapter(getSupportFragmentManager(), list_fragments);
        vpMainframeContent.setAdapter(adapter);
    }


    private void initView() {

        /**
         *	viewPager页面改变事件
         */
        vpMainframeContent.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageSelected(int position) {
                switch (position) {
                    case 0:
                        rbTaskDetail.setChecked(true);
                        rbTaskDetail.setTextColor(getResources().getColor(R.color.colorAccent));
                        viewTaskDetail.setBackgroundColor(getResources().getColor(R.color.colorAccent));
                        rbTaskRecord.setTextColor(getResources().getColor(R.color.text_main_black));
                        viewTaskRecord.setBackgroundColor(getResources().getColor(R.color.line_space));
                        break;
                    case 1:
                        rbTaskRecord.setChecked(true);
                        rbTaskRecord.setTextColor(getResources().getColor(R.color.colorAccent));
                        rbTaskDetail.setTextColor(getResources().getColor(R.color.text_main_black));
                        viewTaskDetail.setBackgroundColor(getResources().getColor(R.color.line_space));
                        viewTaskRecord.setBackgroundColor(getResources().getColor(R.color.colorAccent));
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });

        rgTitle.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                if (!isApply) {
                    switch (checkedId) {
                        case R.id.rb_task_detail:
                            vpMainframeContent.setCurrentItem(0, false);
                            break;
                        case R.id.rb_task_record:
                            vpMainframeContent.setCurrentItem(1, false);
                            break;
                    }
                }
            }
        });

        vpMainframeContent.setOffscreenPageLimit(2);//预加载
    }

    @OnClick(R.id.iv_back)
    public void onClick() {
        finish();
    }

    /**
     * 基本信息提交返回数据
     */
    private String back;

    public void setBack(String back) {
        this.back = back;
        vpMainframeContent.setCurrentItem(1, false);
    }

    public String getBack() {
        return back;
    }

    public String getBusinessType() {
        return businessType;
    }

    public String getCallBackName() {
        return callBackName;
    }

    public FragmentManager fragmentManager;


    //在fragment的管理类中，我们要实现这部操作，而他的主要作用是，当D这个activity回传数据到这里碎片管理器下面的fragnment中时，往往会经过这个管理器中的onActivityResult的方法。
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 2369) { // 送阅入
            if (data != null) {
                StringBuilder name = new StringBuilder();
                StringBuilder id = new StringBuilder();
                List<String> ids = new ArrayList<>(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                List<String> names = new ArrayList<>(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));

                if (ids.size() > 0) {
                    String idsStr = ids.get(0);
                    String namesStr = names.get(0);
                    for (int i = 1; i < ids.size(); i++) {
                        idsStr = idsStr + "," + ids.get(i);
                        namesStr = namesStr + "," + names.get(i);
                    }

                    zfData.put("recipient", namesStr);
                    zfData.put("recipientId", idsStr);

                    lczhuanfa.getEtContent().setText(namesStr);
                }
            }
        }
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        LogUtil.e(TAG + " onDestroy");
    }
}
