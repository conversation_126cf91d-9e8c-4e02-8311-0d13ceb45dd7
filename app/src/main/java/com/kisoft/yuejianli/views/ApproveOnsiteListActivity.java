package com.kisoft.yuejianli.views;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import android.view.View;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.MFragmentAdapter;
import com.kisoft.yuejianli.base.BaseActivity;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.BindViews;
import butterknife.OnClick;

/**
 * Created by tudou on 2018/3/15.
 */
public class ApproveOnsiteListActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.rg_tab)
    RadioGroup rgTab;
    @BindViews({R.id.rb_tab1, R.id.rb_tab2,R.id.rb_tab3})
    List<RadioButton> radioButtons;
    @BindViews({R.id.view1, R.id.view2,R.id.view3})
    List<View> vLines;

    @BindView(R.id.vp_content)
    ViewPager vpContent;

    private List<Fragment> fragments = new ArrayList<>();

    // 页面类型 0我发起的     1待我审批
    private int pageType;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        pageType = getIntent().getIntExtra("pageType", 0);
        initView();
    }

    private void initView() {
        switch (pageType) {
            case 0:
                tvTitle.setText("我已审批");
                if (fragments.size() == 0) {
                    NoticeListFragment fragment = new NoticeListFragment();
                    Bundle bundle = new Bundle();
                    bundle.putInt("type", 0);
                    bundle.putInt("pageType", pageType);
                    fragment.setArguments(bundle);

                    QuestionListFragment fragment1 = new QuestionListFragment();
                    Bundle bundle1 = new Bundle();
                    bundle1.putInt("type", 0);
                    fragment1.setArguments(bundle1);

                    ArchProcessListFragment fragment2 = new ArchProcessListFragment();
                    Bundle bundle2 = new Bundle();
                    bundle2.putString("workType", "2");
                    bundle2.putString("transType","-1");
                    fragment2.setArguments(bundle2);

                    fragments.add(fragment);
                    fragments.add(fragment1);
                    fragments.add(fragment2);
                }
                break;
            case 1:
                tvTitle.setText("待我审批");
                if (fragments.size() == 0) {
                    NoticeListFragment fragment = new NoticeListFragment();
                    Bundle bundle = new Bundle();
                    bundle.putInt("type", 1);
                    bundle.putInt("pageType", pageType);
                    fragment.setArguments(bundle);

                    QuestionListFragment fragment1 = new QuestionListFragment();
                    Bundle bundle1 = new Bundle();
                    bundle1.putInt("type", 1);
                    bundle1.putInt("pageType", pageType);
                    fragment1.setArguments(bundle1);


                    ArchProcessListFragment fragment2 = new ArchProcessListFragment();
                    Bundle bundle2 = new Bundle();
                    bundle2.putString("workType", "1");
                    bundle2.putString("transType","-1");
                    fragment2.setArguments(bundle2);

                    fragments.add(fragment);
                    fragments.add(fragment1);
                    fragments.add(fragment2);
                }
                break;
        }
        vpContent.setAdapter(new MFragmentAdapter(getSupportFragmentManager(), fragments));

        vpContent.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                /*tabChange(position);*/
                switch (position) {
                    case 0:
                        radioButtons.get(0).setChecked(true);
                        radioButtons.get(0).setTextColor(getResources().getColor(R.color.colorAccent));
                        vLines.get(0).setBackgroundColor(getResources().getColor(R.color.colorAccent));
                        radioButtons.get(1).setTextColor(getResources().getColor(R.color.text_main_black));
                        vLines.get(1).setBackgroundColor(getResources().getColor(R.color.line_space));
                        radioButtons.get(2).setTextColor(getResources().getColor(R.color.text_main_black));
                        vLines.get(2).setBackgroundColor(getResources().getColor(R.color.line_space));
                        break;
                    case 1:
                        radioButtons.get(1).setChecked(true);
                        radioButtons.get(1).setTextColor(getResources().getColor(R.color.colorAccent));
                        vLines.get(1).setBackgroundColor(getResources().getColor(R.color.colorAccent));
                        radioButtons.get(0).setTextColor(getResources().getColor(R.color.text_main_black));
                        vLines.get(0).setBackgroundColor(getResources().getColor(R.color.line_space));
                        radioButtons.get(2).setTextColor(getResources().getColor(R.color.text_main_black));
                        vLines.get(2).setBackgroundColor(getResources().getColor(R.color.line_space));
                        break;
                    case 2:
                        radioButtons.get(2).setChecked(true);
                        radioButtons.get(2).setTextColor(getResources().getColor(R.color.colorAccent));
                        vLines.get(2).setBackgroundColor(getResources().getColor(R.color.colorAccent));
                        radioButtons.get(0).setTextColor(getResources().getColor(R.color.text_main_black));
                        vLines.get(0).setBackgroundColor(getResources().getColor(R.color.line_space));
                        radioButtons.get(1).setTextColor(getResources().getColor(R.color.text_main_black));
                        vLines.get(1).setBackgroundColor(getResources().getColor(R.color.line_space));
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

        rgTab.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int id) {
                switch (id) {
                    case R.id.rb_tab1:
                        vpContent.setCurrentItem(0, false);
                        /*tabChange(0);
                        vpContent.setCurrentItem(0);*/
                        break;
                    case R.id.rb_tab2:
                        vpContent.setCurrentItem(1, false);
                        /*tabChange(1);
                        vpContent.setCurrentItem(1);*/
                        break;
                    case R.id.rb_tab3:
                        vpContent.setCurrentItem(2, false);
                        /*tabChange(2);
                        vpContent.setCurrentItem(2);*/
                        break;
                }
            }
        });
        vpContent.setOffscreenPageLimit(3);//预加载  给viewpager设置adapter。代码很简单。并设置预加载页数为1，当然也可以不设置，因为默认就是预加载一页。
    }

   /* private void tabChange(int index) {
        if (index < 0 || index > radioButtons.size()) {
            return;
        }
        for (int i = 0; i < radioButtons.size(); i++) {
            if (i == index) {
                radioButtons.get(i).setChecked(true);
                radioButtons.get(i).setTextColor(getResources().getColor(R.color.colorAccent));
                vLines.get(i).setBackgroundColor(getResources().getColor(R.color.colorAccent));
            } else {
                radioButtons.get(i).setTextColor(getResources().getColor(R.color.text_main_black));
                vLines.get(i).setBackgroundColor(getResources().getColor(R.color.line_space));
            }
        }
    }*/

    @Override
    public int getLayoutId() {
        return R.layout.activity_apporve_onsite_list;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }
}
