package com.kisoft.yuejianli.views;


import android.os.Bundle;
import android.util.Log;
import android.view.SurfaceView;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseActivity;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

import com.king.zxing.CaptureHelper;
import com.king.zxing.OnCaptureCallback;
import com.king.zxing.ViewfinderView;




public class ScanCodeActivity extends BaseActivity  implements OnCaptureCallback {





    @Override
    public int getLayoutId() {
        return R.layout.activity_scancode;
    }

    @BindView(R.id.tv_title)
    TextView tvTitle;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ButterKnife.bind(this);
        initView();
    }

    private void initView() {

        tvTitle.setText("扫码登录");

    }


    @OnClick(R.id.iv_back)
    public void onClick() {
        finish();
    }

    @Override
    public boolean onResultCallback(String s) {
        Log.e("ScanCodeActivity", "onResultCallback: "+ s );
        finish();
        return false;
    }
}