package com.kisoft.yuejianli.views.watermark;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ArrivalAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ErrPeopleInfo;
import com.kisoft.yuejianli.entity.ItemArrivalInfo;
import com.kisoft.yuejianli.entity.LeavePeopleInfo;
import com.kisoft.yuejianli.entity.ProjectApproverInfo;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.PunchCardInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ArrivalFragment extends BaseFragment implements BaseRefreshListener {

    public View mRootView;
    Unbinder unbinder;


    @BindView(R.id.rv_content)
    RecyclerView rvContent;
    @BindView(R.id.ptrl_content)
    PullToRefreshLayout ptrlContent;

    private View emptyView;

    private ArrivalAdapter arrayAdapter;

    private String pageType;
    private String strDate;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private int count = 0;
    private int page = 1;
    private int pageCount = 0;
    private int pageSize = 10;

    private List<ProjectApproverInfo> projectApproverInfos=new ArrayList<>();;
    private List<ItemArrivalInfo> itemArrivalInfos;

    private List<PunchCardInfo> punchCardInfos=new ArrayList<>();

    private List<LeavePeopleInfo.ListBean> listBean=new ArrayList<>();

    private List<ErrPeopleInfo.ListBean> errPeopleBeans=new ArrayList<>();;


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initData();
        initView(inflater);
        return mRootView;
    }

    private void initView(LayoutInflater inflater) {
        itemArrivalInfos = new ArrayList<>();
        emptyView = inflater.inflate(R.layout.page_no_data, null);
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(getContext());
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        arrayAdapter = new ArrivalAdapter(R.layout.item_arrival, itemArrivalInfos);
        arrayAdapter.setEmptyView(emptyView);
        rvContent.setAdapter(arrayAdapter);

        ptrlContent.setRefreshListener(this);
        refresh();
    }
    @Override
    public void refresh() {
        if (pageType.equals("1")) {//全部
            //获取考勤人员列表
            projectApproverInfos.clear();
        } else if (pageType.equals("2")) {//到岗
            //获取今日到岗人数
            punchCardInfos.clear();
        } else if (pageType.equals("3")) {//请假
            //获取请假人员列表 getLeaveListByProjectId
            listBean.clear();
        } else if (pageType.equals("4")) {//异常
            //获取异常考勤人员列表  getAbnormalAttList
            errPeopleBeans.clear();
        }
        page = 1;
        count=0;
        pageCount = 0;
        getData();
    }

    public void finshRefresh() {
        ptrlContent.finishRefresh();
        ptrlContent.finishLoadMore();
    }
    @Override
    public void loadMore() {
        if (pageType.equals("1")) {//全部
            //获取考勤人员列表
            if (projectApproverInfos.size() >= count) {
                showToast("没有更多数据了");
                ptrlContent.finishLoadMore();
            } else {
                page++;
                pageCount = projectApproverInfos.size() + pageSize;
                getData();
            }
        } else if (pageType.equals("2")) {//到岗
            //获取今日到岗人数
            if (punchCardInfos.size() >= count) {
                showToast("没有更多数据了");
                ptrlContent.finishLoadMore();
            } else {
                page++;
                pageCount = punchCardInfos.size() + pageSize;
                getData();
            }


        } else if (pageType.equals("3")) {//请假
            //获取请假人员列表 getLeaveListByProjectId
            if (listBean.size() >= count) {
                showToast("没有更多数据了");
                ptrlContent.finishLoadMore();
            } else {
                page++;
                pageCount = listBean.size() + pageSize;
                getData();
            }
        } else if (pageType.equals("4")) {//异常
            //获取异常考勤人员列表  getAbnormalAttList
            if (errPeopleBeans.size() >= count) {
                showToast("没有更多数据了");
                ptrlContent.finishLoadMore();
            } else {
                page++;
                pageCount = errPeopleBeans.size() + pageSize;
                getData();
            }
        }
    }

    private void getData() {
        /*SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        Date date = new Date();
        String currDate = dateFormat.format(date);*/

        if(projectInfo != null){
            String c = Integer.toString(count);
            String p = Integer.toString(page);
            String z = Integer.toString(pageSize);
            if (!StringUtil.isEmpty(pageType)) {
                if (pageType.equals("1")) {//全部
                    //获取考勤人员列表
                    getUserIdsAndNamesByProjectId(projectInfo.getProjectId(), strDate);
                } else if (pageType.equals("2"
                )) {//到岗
                    //获取今日到岗人数
                    getAttendance(projectInfo.getProjectId(), strDate);
                } else if (pageType.equals("3")) {//请假
                    //获取请假人员列表 getLeaveListByProjectId
                    getLeaveListByProjectId(projectInfo.getProjectId(),strDate, c, p, z);
                } else if (pageType.equals("4")) {//异常
                    //获取异常考勤人员列表  getAbnormalAttList
                    getAbnormalAttList(projectInfo.getProjectId(), strDate, c, p, z);
                }
            }
        }
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        if (getArguments() != null) {
            pageType = getArguments().getString("pageType");
            strDate=getArguments().getString("strDate");
        }
    }

    private void getUserIdsAndNamesByProjectId(String projectId, String currDate ) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("projectId", projectId);
        pramaras.put("time", currDate);
        Api.getGbkApiserver().getUserIdsAndNamesByProjectId(Constant.HTTP_GET_USERIDS_AND_NAMES_BY_PROJECTID, pramaras).enqueue(new Callback<NetworkResponse<List<ProjectApproverInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ProjectApproverInfo>>> call, Response<NetworkResponse<List<ProjectApproverInfo>>> response) {
                finshRefresh();
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    if (response.body().getData() != null) {
                        projectApproverInfos.clear();
                        count=response.body().getData().size();
                        projectApproverInfos.addAll(response.body().getData());

                        TextView allNum = (TextView) getActivity().findViewById(R.id.rb_all_num);
                        allNum.setText("全部("+response.body().getData().size()+")");

                        itemArrivalInfos.clear();
                        for (int i=0;i<projectApproverInfos.size();i++){
                            ItemArrivalInfo itemArrivalInfo=new ItemArrivalInfo();
                            itemArrivalInfo.setTvName(projectApproverInfos.get(i).getUserName());
                            itemArrivalInfo.setTvText(projectApproverInfos.get(i).getPostName());
                            itemArrivalInfos.add(i,itemArrivalInfo);
                        }
                        arrayAdapter.notifyDataSetChanged();
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<ProjectApproverInfo>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }


    private void getAttendance(String projectId, String time) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("projectId", projectId);
        pramaras.put("time", time);
        Api.getGbkApiserver().getProjecrtPunchCardRecord(Constant.HTTP_GET_PROJECT_ALL_PUNCHCARD_RECORD, pramaras).enqueue(new Callback<NetworkResponse<List<PunchCardInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<PunchCardInfo>>> call, Response<NetworkResponse<List<PunchCardInfo>>> response) {
                finshRefresh();
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    if (response.body().getData() != null) {

                        TextView cometoNum = (TextView) getActivity().findViewById(R.id.rb_cometo_num);
                        cometoNum.setText("到岗("+response.body().getData().size()+")");

                        punchCardInfos.clear();
                        count=response.body().getData().size();
                        punchCardInfos.addAll(response.body().getData());
                        itemArrivalInfos.clear();
                        for (int i=0;i<punchCardInfos.size();i++){
                            ItemArrivalInfo itemArrivalInfo=new ItemArrivalInfo();
                            itemArrivalInfo.setTvName(punchCardInfos.get(i).getUserName());
                            itemArrivalInfo.setTvText(punchCardInfos.get(i).getPunchTime1());
                            itemArrivalInfos.add(i,itemArrivalInfo);
                        }
                        arrayAdapter.notifyDataSetChanged();

                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<PunchCardInfo>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    private void getLeaveListByProjectId(String projectId, String currDate,final String count1,final String page, String pageSize) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("projectId" ,projectId);
        pramaras.put("currDate" ,currDate);
        pramaras.put("count" ,count1);
        pramaras.put("page" ,page);
        pramaras.put("pageSize" ,pageSize);
        Api.getGbkApiserver().getLeaveListByProjectId(Constant.HTTP_GET_LEAVE_COUNTS_LIST, pramaras).enqueue(new Callback<NetworkResponse<LeavePeopleInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<LeavePeopleInfo>> call, Response<NetworkResponse<LeavePeopleInfo>> response) {
                finshRefresh();
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    if (response.body().getData() != null) {

                        TextView leaveNum = (TextView) getActivity().findViewById(R.id.rb_leave_num);
                        leaveNum.setText("请假("+response.body().getData().getCount()+")");

                        listBean.clear();
                        count=response.body().getData().getCount();
                        listBean.addAll(response.body().getData().getList());
                        itemArrivalInfos.clear();
                        for (int i=0;i<listBean.size();i++){
                            ItemArrivalInfo itemArrivalInfo=new ItemArrivalInfo();
                            itemArrivalInfo.setTvName(listBean.get(i).getEmpname());
                            itemArrivalInfo.setTvText(listBean.get(i).getStartdate()+"至"+listBean.get(i).getEnddate());
                            itemArrivalInfos.add(i,itemArrivalInfo);
                        }
                        arrayAdapter.notifyDataSetChanged();
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<LeavePeopleInfo>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    private void getAbnormalAttList(String projectId, String currDate,final String count1,final String page, String pageSize) {
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("projectId" ,projectId);
        pramaras.put("currDate" ,currDate);
        pramaras.put("count" ,count1);
        pramaras.put("page" ,page);
        pramaras.put("pageSize" ,pageSize);
        Api.getGbkApiserver().getAbnormalAttList(Constant.HTTP_GET_ABNORMAL_ATT_LIST, pramaras).enqueue(new Callback<NetworkResponse<ErrPeopleInfo>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ErrPeopleInfo>> call, Response<NetworkResponse<ErrPeopleInfo>> response) {
                finshRefresh();
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    if (response.body().getData() != null) {

                        TextView errNum = (TextView) getActivity().findViewById(R.id.rb_err_num);
                        errNum.setText("异常("+response.body().getData().getCount()+")");

                        errPeopleBeans.clear();
                        count=response.body().getData().getCount();
                        errPeopleBeans.addAll(response.body().getData().getList());
                        itemArrivalInfos.clear();
                        for (int i=0;i<errPeopleBeans.size();i++){
                            ItemArrivalInfo itemArrivalInfo=new ItemArrivalInfo();
                            itemArrivalInfo.setTvName(errPeopleBeans.get(i).getUserName());
                            itemArrivalInfo.setTvText("无打卡、请假记录");
                            itemArrivalInfos.add(i,itemArrivalInfo);
                        }
                        arrayAdapter.notifyDataSetChanged();
                    }
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ErrPeopleInfo>> call, Throwable t) {
                t.printStackTrace();
            }
        });

    }


    @Override
    public int getRootView() {
        return R.layout.activity_arrival_fragment;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }
}
