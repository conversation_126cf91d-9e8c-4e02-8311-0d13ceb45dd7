package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import androidx.annotation.Nullable;
import androidx.databinding.adapters.SeekBarBindingAdapter;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.MFragmentAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.ExamMaterialsInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.ui.MyJzvd;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.BindViews;
import cn.jzvd.Jzvd;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class CourseDetailsViewController extends BaseActivity implements MyJzvd.OnPlayStateListener {
    @BindView(R.id.rg_sheet_type)
    RadioGroup rgSheetType;
    @BindViews({R.id.bt_type_1, R.id.bt_type_2, R.id.bt_type_3, R.id.bt_type_4})
    List<RadioButton> radioButtons;
    @BindViews({R.id.view1, R.id.view2, R.id.view3, R.id.view4})
    List<View> vLines;

    @BindView(R.id.vp_content)
    ViewPager vpContent;

    @BindView(R.id.jz_video)
    com.kisoft.yuejianli.ui.MyJzvd jzvideo;
    ExamMaterialsInfo data = null;
    CrouseCommentFragment fragment3;
    CrouseCommentFragment fragment4;

    Map mSelMap = new HashMap<>();
    int mCurrentTime;
    int mDuration;

    private List<Fragment> fragments = new ArrayList<>();
    public static void launch(Activity activity, String url, String title, String fileGuid, boolean isStudy) {
        Intent intent = new Intent(activity, CourseDetailsViewController.class);
        intent.putExtra("url", url);
        intent.putExtra("title", title);
        intent.putExtra("fileGuid", fileGuid);
        intent.putExtra("isStudy", isStudy);
        activity.startActivity(intent);
    }

    public static void launch(Activity activity, ExamMaterialsInfo data) {
        Intent intent = new Intent(activity, CourseDetailsViewController.class);
        intent.putExtra("data",data);
        activity.startActivity(intent);
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_course_details_view_controller;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        data = (ExamMaterialsInfo)getIntent().getSerializableExtra("data");
        initVideoView();
        initView();

    }
    private void initVideoView(){

//        CrouseChapterFragment fragment = new CrouseChapterFragment();
//        fragment.setChapterOnClickListener(new CrouseChapterFragment.ChapterOnClickListener() {
//            @Override
//            public void selectChapter(Map map) {
//
//            }
//        });

//        String url = getIntent().getStringExtra("url");
//        String title = getIntent().getStringExtra("title");
//        jzvideo.setUp(url, title);
//        jzvideo.thumbImageView.setImageDrawable(getResources().getDrawable(R.drawable.bd_ocr_gallery));
    }

    private void initView() {
        rgSheetType.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int id) {
                switch (id) {
                    case R.id.bt_type_1:
                        tabChange(0);
                        vpContent.setCurrentItem(0);
                        break;
                    case R.id.bt_type_2:
                        tabChange(1);
                        vpContent.setCurrentItem(1);
                        break;
                    case R.id.bt_type_3:
                        tabChange(2);
                        vpContent.setCurrentItem(2);
                        break;
                    case R.id.bt_type_4:
                        tabChange(3);
                        vpContent.setCurrentItem(3);
                        break;
                }
            }
        });

        if (fragments.size() == 0) {

            CourseIntroductionFragment fragment1 = new CourseIntroductionFragment();
            CrouseChapterFragment fragment2 = new CrouseChapterFragment();
            fragment2.setChapterOnClickListener(new CrouseChapterFragment.ChapterOnClickListener() {
                @Override
                public void selectChapter(Map map) {
                    mSelMap = map;
                    selectWare(map);
                }
            });

            fragment3 = new CrouseCommentFragment();
            fragment4 = new CrouseCommentFragment();

            Bundle bundle = new Bundle();
            bundle.putSerializable("data",data);

            Bundle bundle1 = new Bundle();
            bundle1.putSerializable("data",data);
            bundle1.putInt("type",0);

            Bundle bundle2 = new Bundle();
            bundle2.putSerializable("data",data);
            bundle2.putInt("type",1);

            fragment1.setArguments(bundle);
            fragment2.setArguments(bundle);
            fragment3.setArguments(bundle1);
            fragment4.setArguments(bundle2);

            fragments.add(fragment1);
            fragments.add(fragment2);
            fragments.add(fragment3);
            fragments.add(fragment4);
        }
//        FragmentManager fragmentManager =getSupportFragmentManager();
//        FragmentTransaction fragmentTransaction=fragmentManager.beginTransaction();
//        fragmentTransaction.add(R.id.fl_content,fragments.get(0));// 或者fragmentTransaction.replace(ViewId,fragment);
//        fragmentTransaction.commit();

        vpContent.setAdapter(new MFragmentAdapter(getSupportFragmentManager(), fragments));
        vpContent.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                tabChange(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    void selectWare(Map map){

        Map<String,String> mMap = new HashMap<>();
        mMap.put("courseInfoId",data.getId());
        mMap.put("courseName",data.getCourseName());
        mMap.put("catalogId",map.get("catalogId").toString());
        mMap.put("catalogName",map.get("catalogName").toString());
        mMap.put("coursewareId",map.get("wareId").toString());
        mMap.put("coursewareName",map.get("coursewareName").toString());
        fragment3.setInfo(mMap);
        fragment4.setInfo(mMap);

        String url = SettingManager.getInstance().getBaseUrl() + "servlet/ExamCourseCoursewareServlet?id=" + map.get("wareId");
       // jzvideo.setUp(url, map.get("coursewareName").toString());
        jzvideo.progressBar.setEnabled(false);
        jzvideo.SAVE_PROGRESS = false;//不保存播放进度
        jzvideo.setOnPlayStateListener(this);


        jzvideo.setUp(url
//        jzVideo.setUp("http://192.168.0.103:8001/wlp_web_war_exploded/download/Pubload.do?id=402880e778e9a7b60178eccb4eed0008"
                , "");

        jzvideo.thumbImageView.setImageDrawable(getResources().getDrawable(R.drawable.bd_ocr_gallery));
    }


    private void tabChange(int index) {
        if (index < 0 || index > radioButtons.size()) {
            return;
        }
        for (int i = 0; i < radioButtons.size(); i++) {
            if (i == index) {
                radioButtons.get(i).setChecked(true);
                radioButtons.get(i).setTextColor(getResources().getColor(R.color.colorAccent));
                vLines.get(i).setBackgroundColor(getResources().getColor(R.color.colorAccent));
            } else {
                radioButtons.get(i).setTextColor(getResources().getColor(R.color.text_main_black));
                vLines.get(i).setBackgroundColor(getResources().getColor(R.color.line_space));
            }
        }
    }

    @Override
    public void onBackPressed() {
        if (Jzvd.backPress()) {
            return;
        }
        saveExamCourseLearnRecord();
        super.onBackPressed();
    }


    @Override
    protected void onPause() {
        super.onPause();
        Jzvd.releaseAllVideos();
    }


    void saveExamCourseLearnRecord(){

        if (mSelMap == null || mSelMap.isEmpty()){

        }else {


            Map<String, Object> parameters = new HashMap<>();
            Map<String, String> mMap = new HashMap();
            mMap.put("courseInfoId", data.getId());
            mMap.put("courseName", data.getCourseName());
            mMap.put("catalogId", mSelMap.get("catalogId").toString());
            mMap.put("catalogName", mSelMap.get("catalogName").toString());
            mMap.put("coursewareId", mSelMap.get("wareId").toString());
            mMap.put("coursewareName", mSelMap.get("coursewareName").toString());

            mMap.put("integral", mSelMap.get("integral").toString());
            mMap.put("done", mCurrentTime < mDuration ? "0" : "1");
            parameters.put("watchTime", mCurrentTime);//视频播放时间（单位：秒）
            jzvideo.getDuration();

            parameters.put("data", StringUtil.objectToJson(mMap));

//        try {
//            parameters.put("notesObject" , URLEncoder.encode(StringUtil.objectToJson(mMap).toString(),"UTF-8"));
//            parameters.put("notesObject" , URLEncoder.encode(URLEncoder.encode(StringUtil.objectToJson(mMap).toString(),"UTF-8"),"UTF-8"));
//        } catch (UnsupportedEncodingException e) {
//            e.printStackTrace();
//        }
//        try {
//            Api.getGbkApiserver().saveExamCourseLearnRecord ("saveExamCourseLearnRecord_APP", parameters).execute();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

            Api.getGbkApiserver().saveExamCourseLearnRecord("saveExamCourseLearnRecord_APP", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
                @Override
                public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                    if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
//                        showToast("提交成功");

                    }
                }

                @Override
                public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                    t.printStackTrace();

                }
            });
        }
    }


    @Override
    public void onPlaySucceed() {

    }

    @Override
    public void onPlayError() {

    }

    @Override
    public void onPlayComplete() {

    }

    @Override
    public void onProgress(int progress, long position, long duration) {
        Log.i("TAG", "onProgress: progress = " + progress + "position = " + position + "duration = " + duration);
        mCurrentTime = (int) (duration / 1000);
        mDuration = (int) (position / 1000);
    }
}