package com.kisoft.yuejianli.views;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import android.view.View;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.kisoft.yuejianli.adpter.MFragmentAdapter;
import com.kisoft.yuejianli.base.BaseActivity;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.BindViews;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/14.
 */
public class EndServiceActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rg_tab)
    RadioGroup rgTab;
    @BindViews({R.id.rb_tab1, R.id.rb_tab2})
    List<RadioButton> radioButtons;
    @BindViews({R.id.view1, R.id.view2})
    List<View> vLines;
    @BindView(R.id.vp_content)
    ViewPager vpContent;

    private List<Fragment> content = new ArrayList<>();
    private List<String> types = new ArrayList<>();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initData();
        initView();
    }

    private void initData(){
        if (types.size() == 0) {
            types.add("维保回访计划");
            types.add("突然事件处理");
        }

        if (content.size() == 0) {
            content.add(new EndServiceVisitPlanFragment());
            content.add(new UnexceptedEventFragment());
        }
    }

    private void initView(){
        tvTitle.setText("后期服务");
        vpContent.setAdapter(new MFragmentAdapter(getSupportFragmentManager(), content));
        vpContent.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                tabChange(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        rgTab.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int id) {
                switch (id) {
                    case R.id.rb_tab1:
                        tabChange(0);
                        vpContent.setCurrentItem(0);
                        break;
                    case R.id.rb_tab2:
                        tabChange(1);
                        vpContent.setCurrentItem(1);
                        break;
                }
            }
        });
    }

    private void tabChange(int index) {
        if (index <0 || index>radioButtons.size()) {
            return;
        }
        for (int i=0; i<radioButtons.size(); i++) {
            if (i == index) {
                radioButtons.get(i).setChecked(true);
                radioButtons.get(i).setTextColor(getResources().getColor(R.color.colorAccent));
                vLines.get(i).setBackgroundColor(getResources().getColor(R.color.colorAccent));
            }else {
                radioButtons.get(i).setTextColor(getResources().getColor(R.color.text_main_black));
                vLines.get(i).setBackgroundColor(getResources().getColor(R.color.line_space));
            }
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_end_service;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }
}
