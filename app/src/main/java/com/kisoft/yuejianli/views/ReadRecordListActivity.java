package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ProcessListAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ReadRecordListActivity extends BaseActivity{
    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    @BindView(R.id.ptrl_content)
    PullToRefreshLayout ptrlContent;
    
    private ProcessListAdapter processListAdapter;

    private List<ProcessListBean> dtoList = new ArrayList<>();

    @Override
    public int getLayoutId() {
        return R.layout.activity_read_record_list;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ButterKnife.bind(this);
        initView();
        getCarbonCopyList();
    }

    private void initView() {

        tvTitle.setText("已阅记录");

        ptrlContent.setRefreshListener(new BaseRefreshListener() {
            @Override
            public void refresh() {
                getCarbonCopyList();
                ptrlContent.setCanLoadMore(false);
            }

            @Override
            public void loadMore() {

            }
        });
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(layoutManager);
        }
        rvContent.addOnItemTouchListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int position) {
                ProcessListBean processListBean = processListAdapter.getData().get(position);
                processListBean.setFlowId(processListBean.getWfId());
                Intent intent = new Intent(mContext, ApplyActivity.class);
                intent.putExtra("isApply", false);
                intent.putExtra("bean", processListBean);
                intent.putExtra("workType", "3");
                intent.putExtra("businessType", processListBean.getBusinessType());
                intent.putExtra("flowStateName", processListBean.getFlowStateName());
                intent.putExtra("pageState", "2");
                startActivity(intent);
            }
        });

        processListAdapter = new ProcessListAdapter();
        rvContent.setAdapter(processListAdapter);
    }

    private void getCarbonCopyList() {
        // 待阅
        //workType类型（1=待办、2=已办、3=我发起的）
        Map<String, Object> pramaras = new HashMap<>();
        pramaras.put("workType" ,"1");
        pramaras.put("userId" , SettingManager.getInstance().getUserInfo().getId());
        pramaras.put("businessType", ApplyActivity.businessTypes());
        pramaras.put("month", DateUtil.getTodayDate(DateUtil.YM));
        Api.getGbkApiserver().getCarbonCopyList(Constant.HTTP_GET_DAIYUE_APP_LIST, pramaras).enqueue(new Callback<NetworkResponse<List<ProcessListBean>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ProcessListBean>>> call, Response<NetworkResponse<List<ProcessListBean>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    List<ProcessListBean> ListBeans = response.body().getData();
                    if (ListBeans != null) {
                        if (ListBeans != null) {
                            dtoList.clear();
                            for (int i = 0; i < ListBeans.size(); i++) {
                                ProcessListBean bean = ListBeans.get(i);
                                bean.setWfType("3");
                                dtoList.add(bean);
                            }
                        }
                        processListAdapter.replaceData(dtoList);
                    }
                }
                ptrlContent.finishLoadMore();
                ptrlContent.finishRefresh();
            }
            @Override
            public void onFailure(Call<NetworkResponse<List<ProcessListBean>>> call, Throwable t) {

            }
        });
    }

    @OnClick(R.id.iv_back)
    public void onClick() {
        finish();
    }

}