package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.WbsListAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.WbsListContract;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.entity.WbsListDataInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.WbsListModel;
import com.kisoft.yuejianli.presenter.WbsListPresenter;
import com.kisoft.yuejianli.utils.AppUtils;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class WbsListActivity extends BaseActivity <WbsListModel, WbsListPresenter>implements WbsListContract.WbsListViewContract {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_wbsList)
    RecyclerView rvWbsList;

    public static final int SECTION_VIEW = 0;
    public static final int ITEM_VIEW = 1;

    private WbsListModel mModel;
    private WbsListPresenter mPresenter;
    private View empotyView;

    private List<MultiItemEntity> mList = new ArrayList<>();
    private WbsListAdapter mAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        mModel = new WbsListModel(this);
        mPresenter = new WbsListPresenter(this,mModel);
        initMVP(mModel,mPresenter);
        initData();
        initView();
    }

    private void initData() {
        UserInfo info = SettingManager.getInstance().getUserInfo();
        mPresenter.getDataList(info.getId());
    }

    private void initView() {
        tvTitle.setText("当日待销项");
        if (rvWbsList.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(this);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvWbsList.setLayoutManager(layoutManager);
        }
        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        mAdapter = new WbsListAdapter(mList);
        rvWbsList.setAdapter(mAdapter);
        mAdapter.setOnClickListItemListener(new WbsListAdapter.OnClickListItemListener() {
            @Override
            public void onClickItemListener(WbsListDataInfo.TaskListDataBean info) {
                UserInfo infos = SettingManager.getInstance().getUserInfo();
                if ("1".equals(info.getComplete())){
                    showToast("该任务已完成");
                    return;
                }
                if (infos.getProjectId().equals(info.getProjectId())) {
                    if (StringUtil.isEmpty(info.getAndroidUrl())){
                        showToast("请在电脑端完成操作");
                    }else {
                        if ("com.kisoft.yuejianli.views.SupervisionLogAvcivity".equals(info.getAndroidUrl())){
                            Intent intent = new Intent();
                            intent.setClass(mContext, SupervisionLogAvcivity.class);
                            intent.putExtra(Constant.INTENT_KEY_CAN_EDIT, true);
                            startActivity(intent);
                        }else {
                            AppUtils.launchActivity(WbsListActivity.this,info.getAndroidUrl(),info.getAndroidUrl());
                        }
                    }
                }else {
                 showToast("请先切换至任务所在项目");
                }
            }
        });
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_wbs_list;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    @Override
    public void showDataList(List<WbsListDataInfo> list) {
        mList.clear();
        for (WbsListDataInfo info : list) {
            mList.add(info);
            for (WbsListDataInfo.TaskListDataBean bean : info.getTaskList()) {
                mList.add(bean);
            }
        }
        mAdapter.notifyDataSetChanged();
    }
}
