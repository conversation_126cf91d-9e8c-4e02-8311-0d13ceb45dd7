package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.app.DatePickerDialog;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;
import android.widget.AdapterView;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;

import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ProjectInfo;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.contract.ProjectProcessAddContract;
import com.kisoft.yuejianli.entity.ProjectProcess;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.entity.ZxDto;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ProjectProcessAddModel;
import com.kisoft.yuejianli.presenter.ProjectProcessAddPresenter;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.Calendar;
import java.util.Date;

/**
 * Created by tudou on 2018/5/21.
 */
public class ProjectProcessAddActivity extends BaseActivity<ProjectProcessAddModel,ProjectProcessAddPresenter> implements
        ProjectProcessAddContract.ProjectProcessAddViewContract{

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_project)
    TextView tvProject;
    @BindView(R.id.tv_process_name)
    TextView tvProcessName;
    @BindView(R.id.et_process_num)
    EditText etProcessNum;
    @BindView(R.id.et_complete_num)
    EditText etCompleteNum;
    @BindView(R.id.tv_reason)
    TextView tvReason;
    @BindView(R.id.et_reason)
    EditText etReason;
    @BindView(R.id.sp_type)
    Spinner spType;
    @BindView(R.id.et_remark)
    EditText etRemark;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    private ProjectProcess process;

    private ProjectProcessAddModel model;
    private ProjectProcessAddPresenter presenter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new ProjectProcessAddModel(this);
        presenter = new ProjectProcessAddPresenter(this, model);
        initMVP(model, presenter);
        initView();
    }

    private void initView(){
        process = new ProjectProcess();
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        if(projectInfo != null){
            tvProject.setText(projectInfo.getProjectName());
            process.setProjectId(projectInfo.getProjectId());
            process.setProjectName(projectInfo.getProjectName());
        }
        if (userInfo != null) {
            process.setCreateId(userInfo.getId());
            process.setCreateName(userInfo.getName());
        }

        tvTitle.setText("添加项目专项/工序");
        process.setProgState("0");
        spType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int position, long id) {
                process.setProgState(position + "");
                if(position == 2) {
                    tvReason.setVisibility(View.VISIBLE);
                    etReason.setVisibility(View.VISIBLE);
                }else {
                    tvReason.setVisibility(View.GONE);
                    etReason.setVisibility(View.GONE);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_project_process_add;
    }

    @OnClick({R.id.iv_back, R.id.ll_zx, R.id.tv_begin_date, R.id.tv_end_date})
    public void onViewClicked(View view){
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.ll_zx:
                Intent intent = new Intent(mContext, ZxListActivity.class);
                startActivityForResult(intent, Constant.REQUEST_CODE_ZXLIST);
                break;
            case R.id.tv_begin_date:
            case R.id.tv_end_date:
                showDatePickerDialog((TextView) view);
                break;
        }
    }

    @OnClick(R.id.tv_sub)
    public void addProcess(){
        process.setBuildNum(etProcessNum.getText().toString());
        process.setCompNum(etCompleteNum.getText().toString());
        process.setReason(etReason.getText().toString());
        process.setRemark(etRemark.getText().toString());
        process.setCreateTime(DateUtil.dateToString(new Date(), DateUtil.YMD_HM));

        // todo 判断数据的完整性
        mPresenter.submitProcess(userInfo.getId(), projectInfo.getProjectId(), process);
    }

    /**
     * 展示日期选择对话框
     */
    private void showDatePickerDialog(final TextView tv) {
        Calendar c = Calendar.getInstance();
        new DatePickerDialog(mContext, new DatePickerDialog.OnDateSetListener() {

            @Override
            public void onDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                // TODO Auto-generated method stub
                int m = monthOfYear + 1;
                String month = m >= 10 ? m + "" : "0" + m;
                String day = dayOfMonth >= 10 ? dayOfMonth + "" : "0" + dayOfMonth;
                String dateStr = year + "-" + month + "-" + day;
                tv.setText(dateStr);
                switch (tv.getId()) {
                    case R.id.tv_begin_date:
                        process.setBeginDate(dateStr);
                        break;
                    case R.id.tv_end_date:
                        process.setEndDate(dateStr);
                        break;
                }
            }
        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)).show();
    }

    @Override
    public void showSubmitResulte(boolean isOk) {
        if(isOk){
            this.setResult(Constant.REQUEST_CODE_ADD_PROCESS);
            finish();
        }else {
            showToast("添加失败！");
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case Constant.REQUEST_CODE_ZXLIST:
                if (resultCode == Activity.RESULT_OK && data != null) {
                    ZxDto zxDto = (ZxDto) data.getSerializableExtra("data");
                    if (zxDto !=null) {
                        tvProcessName.setText(zxDto.getScName());
                        etProcessNum.setText(zxDto.getBuildNum());
                        process.setScale(zxDto.getScale());
                        process.setScId(zxDto.getScId());
                        process.setScName(zxDto.getScName());
                        process.setBuildNum(zxDto.getBuildNum());
                    }
                }
                break;
        }
    }
}
