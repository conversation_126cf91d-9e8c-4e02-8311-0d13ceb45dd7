package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ApplySPContract;
import com.kisoft.yuejianli.entity.ApproveInfo;
import com.kisoft.yuejianli.entity.JDData;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ShouWenTJModel;
import com.kisoft.yuejianli.presenter.ShouWenTJPresenter;
import com.kisoft.yuejianli.ui.YSelectTextViewCell;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;

public class ShouWenTJFragment  extends BaseFragment<ShouWenTJModel, ShouWenTJPresenter>
        implements ApplySPContract.ApplySPViewContract {

    public View mRootView;

    Unbinder unbinder;

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.sp_process)
    Spinner spProcess;
    @BindView(R.id.sp_next)
    Spinner spNext;
    @BindView(R.id.et_remark)
    EditText etRemark;
    @BindView(R.id.ll_next)
    LinearLayout llNext;
    @BindView(R.id.songyueView)
    YSelectTextViewCell syView;

    String businessId;
    String businessType;
    String callBackName;
    String nodeId = "";

    /**
     * 审批流程
     */
    private List<JDData> process = new ArrayList<>();
    private ArrayAdapter<JDData> processAdapter;
    /**
     * 审批节点
     */
    private List<JDData.JDBean> next = new ArrayList<>();
    private ArrayAdapter<JDData.JDBean> nextAdapter;

    ApproveInfo info = new ApproveInfo();

    private ShouWenTJModel model;
    private ShouWenTJPresenter presenter;
    private static final int SELECT_TRACK_DJORG = 300;
    @Override
    public int getRootView() {
        return R.layout.activity_apply_sp;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new ShouWenTJModel(mContext);
        presenter = new ShouWenTJPresenter(this, model);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initView();
        return mRootView;
    }

    private void initView() {
        // 流程模板
        processAdapter = new ArrayAdapter<>(mContext, android.R.layout.simple_list_item_1, process);
        spProcess.setAdapter(processAdapter);
        if (info.getWfId() == null) {
            info.setWfId("");
        }
        spProcess.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int position, long id) {
                presenter.initWf(businessId, process.get(position).getId(),info.getWfId(), process.get(position).getName(), businessType, callBackName);
            }
            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });

        // 下一节点审批人
        nextAdapter = new ArrayAdapter<>(mContext, android.R.layout.simple_list_item_1, next);
        spNext.setAdapter(nextAdapter);
        spNext.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int position, long id) {
                String nid = nodeId + "," + next.get(position).getId();
                info.setAssignActors(nid);
                // info.setAssignActors(next.get(position).getId());
            }
            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });

        // 送阅人
        syView.getSelectView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent2 = new Intent();
                intent2.setClass(mContext, CompanyOrgInfoActivity.class);
                startActivityForResult(intent2,SELECT_TRACK_DJORG);
            }
        });
    }

    // 提交流程审批
    @OnClick({R.id.tv_sub})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tv_sub:
                String reason = etRemark.getText().toString().trim();
                if (StringUtil.isEmpty(reason)) {
                    reason = "无";
                }
                info.setAppRemark(reason);
                presenter.submitApplySP(info);
                Log.d("info.getUserId",info.getUserId().toString());
                break;
        }
    }

    // 获取流程模板
    @Override
    public void processListBack(List<JDData> leaveTypes) {
        this.process.clear();
        if (leaveTypes != null && !leaveTypes.isEmpty()) {
            //重点是这里
            this.process.addAll(leaveTypes);
            processAdapter.notifyDataSetChanged();
        }
    }

    // 审批节点人员列表
    @Override
    public void nextListBack(List<JDData> next) {
        this.next.clear();
        if (next != null && !next.isEmpty() &&
                next.get(0).getActors() != null && !next.get(0).getActors().isEmpty()){
            this.next.addAll(next.get(0).getActors());
            this.nodeId = next.get(0).getId();
            nextAdapter.notifyDataSetChanged();
            info.setAssignActors(next.get(0).getActors().get(0).getId());
        }
    }

    // 初始化流程
    @Override
    public void initWfBack(String back) {
        if (!StringUtil.isEmpty(back)) {
            String[] strings = back.split(",");
            if (strings != null && strings.length == 3) {
                if (strings[0] != null) {
                    info.setWfId(strings[0]);
                }else {
                    info.setWfId("");
                }
                info.setWfTaskId(strings[1]);
                if ("1".equals(strings[2])){
                    // 是否指定下一节点
                    llNext.setVisibility(View.VISIBLE);
                    presenter.getNextList(businessType, callBackName, strings[1]);////获取审批节点列表
                }else{
                    // 没有下一节点
                    llNext.setVisibility(View.GONE);
                    info.setAssignActors("");
                }
            }
        }
    }

    @Override
    public void submitApplySPBack(String str) {
        showToast("提交成功");
        getActivity().finish();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == SELECT_TRACK_DJORG) { // 送阅入
            if (data != null) {
                StringBuilder name = new StringBuilder();
                StringBuilder id = new StringBuilder();
                List<String> ids = new ArrayList<>(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                List<String> names = new ArrayList<>(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));

                if (ids.size() > 0){
                    String idsStr = ids.get(0);
                    String namesStr = names.get(0);
                    for(int i = 1; i < ids.size(); i++) {
                        idsStr = idsStr + "," + ids.get(i);
                        namesStr = namesStr + "," + names.get(i);
                    }
                    info.setCopyIds(idsStr);
                    info.setCopyNames(namesStr);
                    syView.getEtContent().setText(namesStr);
                }
            }
        }
    }
    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && presenter != null) {
            String back = ((ShouWenDBListActivity) getActivity()).getBack();
            if (back != null) {
                String[] strings = back.split(",");
                if (strings!=null && strings.length == 3) {
                    businessId = strings[1];
                    info.setArcId(businessId);
                    info.setUserId(SettingManager.getInstance().getUserInfo().getId());
                    tvTitle.setText(strings[2]);
                }
            }
            businessType = ((ShouWenDBListActivity) getActivity()).getBusinessType();
            callBackName = ((ShouWenDBListActivity) getActivity()).getCallBackName();
            info.setCallBackName(callBackName);
            info.setBusinessType(businessType);
            presenter.getProcessList(businessType, callBackName);
        }
    }
}
