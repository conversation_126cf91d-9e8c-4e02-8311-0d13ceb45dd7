package com.kisoft.yuejianli.views;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.bigkoo.pickerview.TimePickerView;
import com.bigkoo.pickerview.lib.WheelView;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.adpter.ImageAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.SheiteredCheckContract;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.FunctionPermission;
import com.kisoft.yuejianli.entity.PhotoUploadResulte;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.QualityInvisibility;
import com.kisoft.yuejianli.entity.UnSubmitInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.entity.event.ImageCompressEvent;
import com.kisoft.yuejianli.greendao.DaoUtil;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.SheiteredCheckModel;
import com.kisoft.yuejianli.presenter.SheiteredCheckPresenter;
import com.kisoft.yuejianli.ui.ImageDialog;
import com.kisoft.yuejianli.ui.TipsQuestionView;
import com.kisoft.yuejianli.ui.UnitListActivity;
import com.kisoft.yuejianli.ui.YFileListView;
import com.kisoft.yuejianli.ui.YTextFieldCell;
import com.kisoft.yuejianli.ui.YTextViewCell;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.PermissionsChecker;
import com.kisoft.yuejianli.utils.PhoneUtil;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.views.watermark.ImageUtil;
import com.kisoft.yuejianli.views.watermark.PhotoCaptureActivity;
import com.kisoft.yuejianli.views.watermark.PhotoListener;
import com.kisoft.yuejianli.views.watermark.WaterMask;
import com.kisoft.yuejianli.views.watermark.WaterMaskHelper;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.common.util.LogUtil;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by tudou on 2018/3/16.
 */
public class ShelteredCheckActivity extends BaseActivity<SheiteredCheckModel, SheiteredCheckPresenter>
        implements SheiteredCheckContract.SheiteredCheckViewContract, RadioGroup.OnCheckedChangeListener,
        WaterMask.WaterMaskListener, PhotoListener {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;

    @BindView(R.id.tv_project)
    TextView tvProject;

    //    @BindView(R.id.et_accept_point)
//    EditText etAcceptPoint;
    @BindView(R.id.tips_view)
    TipsQuestionView mQuestionView;

    @BindView(R.id.et_accept_content)
    YTextViewCell etAcceptContent;
    @BindView(R.id.tv_company)
    TextView tvCompany;
    @BindView(R.id.iv_select_contruction_unit)
    ImageView iv_select_contruction_unit;
    @BindView(R.id.et_point)
    EditText etPoint;
    @BindView(R.id.et_charge_person)
    EditText etChargePerson;
    @BindView(R.id.tv_invisible_time)
    TextView tvInvisibleTime;
    @BindView(R.id.tv_accept_time)
    TextView tvAcceptTime;

    @BindView(R.id.rg_acceptance_result)
    RadioGroup rgAcceptResult;
    @BindView(R.id.rb_ok)
    RadioButton rbYes;
    @BindView(R.id.rb_no)
    RadioButton rbNo;
    @BindView(R.id.rv_check_picture)
    RecyclerView rvCheckPicture;
    @BindView(R.id.et_dis)
    EditText etDis;

    @BindView(R.id.ll_answer)
    View answerView;
    @BindView(R.id.iv_time_more)
    ImageView ivTimeMore;
    @BindView(R.id.iv_invisible_time_more)
    ImageView ivInvisibleTimeMore;

    @BindView(R.id.acceptPerson)
    YTextFieldCell acceptPerson;

    @BindView(R.id.y_file_list)
    YFileListView mFileView;


    private boolean canEdit = false;
    private QualityInvisibility invisibility;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    private List<String> images = new ArrayList<>();
    private ImageAdapter imageAdapter;

    private ImageDialog imageDialog;
    private TimePickerView tpv;

    private SheiteredCheckModel model;
    private SheiteredCheckPresenter presenter;
    boolean modify; // 是否可以修改
    boolean isUpdate; // 是否可以修改

    private int timeType = 1;
    private static final int TIME_TYPE_INVISIBLE = 1;
    private static final int TIME_TYPE_CHECK = 2;

    static final String[] PERMISSIONS = new String[]{
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA

    };
    private PermissionsChecker permissionsChecker;

    private View lastView;
    private int maskLocation = 4;
    private ArrayList<String> uris;
    WaterMaskHelper waterMaskHelper = new WaterMaskHelper(this);
    private boolean isTakePhoto;
    AMapLocation locationdata = null;
    String address = "";

    private ArrayList<EnclosureListDto> mFileList = new ArrayList<>();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new SheiteredCheckModel(this);
        presenter = new SheiteredCheckPresenter(this, model);
        initMVP(model, presenter);
        initWater();
        initData();
        initView();
//        String code = SettingManager.getInstance().getCompanyCode();
        if (SettingManager.getInstance().getCompanyCode().equals("scgljl") || SettingManager.getInstance().getCompanyCode().equals("local")) {
            rvCheckPicture.setVisibility(View.GONE);
            filMethod();
        }else{
            mFileView.setVisibility(View.GONE);
        }

    }

    //初始化水印
    private void initWater() {
        //binding.setPresenter(new Presenter());
        //初始化水印工具
        waterMaskHelper = new WaterMaskHelper(this, this, this);
        //lastView = binding.txtCenter;
        //lastView.setSelected(true);
        maskLocation = WaterMask.DefWaterMaskParam.Location.center;
    }

    @Override                       //添加水印：
    public WaterMask.WaterMaskParam onDraw() {

        //拍照后调用，设置水印的基本参数
        WaterMask.WaterMaskParam param = new WaterMask.WaterMaskParam();
        //创建当前时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        Date date = new Date();
        String format = dateFormat.format(date);
        UserInfo userInfo = SettingManager.getInstance().getUserInfo();
        if (userInfo != null) {
            param.txt.add("记录人：" + userInfo.getName());
        }
        ProjectInfo project = SettingManager.getInstance().getProject();
        if (project != null) {
            param.txt.add("项目：" + project.getProjectName());
        }
        if (isTakePhoto) {
            param.txt.add(format + "  " + address);
        } else {
            param.txt.add("日期：" + format);
        }
        param.location = WaterMask.DefWaterMaskParam.Location.left_bottom;
        param.itemCount = 60;
        return param;
    }

    @Override
    public void onChoose(ArrayList<String> photos) {
        uris = photos;
        //Glide.with(CompleteCheckAddActivity.this).load(photos.get(0)).placeholder(R.mipmap.ic_launcher).centerCrop
        // ().error(R.mipmap.ic_launcher).crossFade().into(ivSfenclosure);
    }

    private void initData() {
        permissionsChecker = new PermissionsChecker(this);
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        Intent intent = getIntent();
        if (intent != null) {
            modify = intent.getBooleanExtra("modify", false);
            isUpdate = intent.getBooleanExtra("isUpdate", false);
            canEdit = intent.getBooleanExtra(Constant.INTENT_KEY_CAN_EDIT, false);
            if (canEdit) {
                invisibility = new QualityInvisibility();
                invisibility.setCreateId(userInfo.getId());
                invisibility.setCreateName(userInfo.getName());
                invisibility.setProjectId(projectInfo.getProjectId());
                invisibility.setProjectName(projectInfo.getProjectName());
                getUUID();
                images.add("");
                invisibility.setEncloSure(StringUtil.imageArryToString(images));
            } else {

                invisibility = (QualityInvisibility) intent.getSerializableExtra(Constant.INTENT_KEY_INVISIBILITY);
                images.addAll(StringUtil.stringToImageArry(invisibility.getEncloSure()));
                Iterator<String> iterator = images.iterator();
                while (iterator.hasNext()) {
                    String url = iterator.next();
                    if (StringUtil.isEmpty(url))
                        iterator.remove();
                }
                getFileList(invisibility.getIsId());
                if (modify) {
                    images.add("");
                } else {
                    if (!StringUtil.isEmpty(invisibility.getIsId())) {
                        tvSubmit.setText("修改");
                        FunctionPermission permissionFunction = SettingManager.getInstance().getPermissionFunction();
                        if (permissionFunction != null && permissionFunction.isHasUpdateFunction()) {
                            tvSubmit.setVisibility(View.VISIBLE);
                        }
                        tvSubmit.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                // 修改
                                finish();
                                getIntent().putExtra("modify", true);
                                getIntent().putExtra("isUpdate", true);
                                startActivity(getIntent());
                            }
                        });
                    } else {
                        tvSubmit.setVisibility(View.GONE);
                    }
                }
            }
        }
    }

    private BGAPhotoHelper mPhotoHelper;

    private static final int REQUEST_CODE_CHOOSE_PHOTO = 1;
    private static final int REQUEST_CODE_TAKE_PHOTO = 2;
    private static final int REQUEST_CODE_CROP = 3;
    private static final int REQUEST_CONSTRUCTIONUNITLIST_ACTIVITY = 4;

    private void initView() {
        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);

        tvTitle.setText("隐蔽性工程记录");
        tvProject.setText(projectInfo.getProjectName());
        rgAcceptResult.setOnCheckedChangeListener(this);
        if (rvCheckPicture.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.HORIZONTAL);
            rvCheckPicture.setLayoutManager(manager);
        }
        imageAdapter = new ImageAdapter(R.layout.item_iamge_show, images);
        imageAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (StringUtil.isEmpty(images.get(position))) {
                    if (permissionsChecker.lacksPermissions(PERMISSIONS)) {
                        getPermissions(Constant.REQUEST_CODE_TAKE_POHO);
                    } else {
                        // todo 空照片 ，添加
                        String[] str = new String[]{"系统相机", "手机相册"};
                        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
                        ab.setItems(str, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                switch (which) {
                                    case 0://相机
                                        isTakePhoto = true;
                                        takePhoto();
                                        break;
                                    case 1://相册
                                        isTakePhoto = false;
                                        getPhoto();
                                        break;
                                }
                            }
                        });
                        ab.show();
                    }
                } else {
                    showImage(images.get(position));
                }
            }
        });
        imageAdapter.setApply(canEdit);
        imageAdapter.setDeleteBtnClickListener(new ImageAdapter.DeleteBtnClickListener() {
            @Override
            public void deletePicture(String item) {
                if (!StringUtil.isEmpty(item)) {
                    images.remove(item);
                    imageAdapter.notifyDataSetChanged();
                }
            }
        });
        rvCheckPicture.setAdapter(imageAdapter);
        imageDialog = new ImageDialog();
        initDatePick();

        // 选择单位工程
        mQuestionView.getTitle().setText("检查部位");
        mQuestionView.getSelectView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(mContext, UnitListActivity.class);
                intent.putExtra("mType", 1);//分项工程3
                intent.putExtra("mEndType", 5);//隐蔽工程5
                startActivityForResult(intent, Constant.REQUEST_CODE_UNIT_DANWEI);
            }
        });
        mQuestionView.getDocBtn().setVisibility(View.VISIBLE);

        mQuestionView.setStatus(true);
        if (!canEdit) {
            answerView.setVisibility(View.GONE);
            ivTimeMore.setVisibility(View.GONE);
            ivInvisibleTimeMore.setVisibility(View.GONE);
//            etAcceptPoint.setText(invisibility.getPwiName());
//            etAcceptPoint.setEnabled(modify);
            mQuestionView.getTitle().setText("检查部位");
            mQuestionView.getQuestEdit().setText(invisibility.getPwiName());


            etPoint.setText(invisibility.getConSite());
            etPoint.setEnabled(modify);
            etAcceptContent.getEtContent().setText(invisibility.getHiddenContent());
            etAcceptContent.getEtContent().setEnabled(modify);
            if (modify) {
                tvCompany.setText(invisibility.getConsUnit());
                mQuestionView.setStatus(true);
            } else {
                mQuestionView.setStatus(false);
                if (!StringUtil.isEmpty(invisibility.getConsUnit())) {
                    String fztdw = invisibility.getConsUnit().replaceAll(",", "\n").replaceAll("，", "\n");
                    String fztdw1 = invisibility.getConsUnit().replaceAll(",", "").replaceAll("，", "");
                    int lines = invisibility.getConsUnit().length() - fztdw1.length() + 1;
                    tvCompany.setText(fztdw);
                    tvCompany.setMaxLines(lines);
                    tvCompany.setLines(lines);
                }
                tvCompany.setEnabled(false);
                iv_select_contruction_unit.setVisibility(View.GONE);
            }
            etChargePerson.setText(invisibility.getFunctionary());
            etChargePerson.setEnabled(modify);
            tvInvisibleTime.setText(invisibility.getHiddenTimeStr());
            tvAcceptTime.setText(invisibility.getInspecTimeStr());
            if (StringUtil.isEqual("1", invisibility.getInspeResult())) {
                rgAcceptResult.check(R.id.rb_ok);
            } else if (StringUtil.isEqual("0", invisibility.getInspeResult())) {
                rgAcceptResult.check(R.id.rb_no);
            }
            rgAcceptResult.setEnabled(modify);
            rbYes.setEnabled(modify);
            rbNo.setEnabled(modify);

            etDis.setText(invisibility.getReMark());
            etDis.setEnabled(modify);

            acceptPerson.getEtContent().setText(invisibility.getAcceptPerson());
            acceptPerson.getEtContent().setEnabled(modify);
        }
        if (modify) {
            answerView.setVisibility(View.VISIBLE);
            ivTimeMore.setVisibility(View.VISIBLE);
            ivInvisibleTimeMore.setVisibility(View.VISIBLE);
        }
    }

    private void initDatePick() {
        Calendar selectedDate = Calendar.getInstance();
        Calendar startDate = Calendar.getInstance();
        startDate.set(1950, 0, 1, 0, 0);
        Calendar endDate = Calendar.getInstance();
        endDate.set(2033, 11, 30, 23, 30);
        //时间选择器
        tpv = new TimePickerView.Builder(this, new TimePickerView.OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {//选中事件回调
                // 这里回调过来的v,就是show()方法里面所添加的 View 参数，如果show的时候没有添加参数，v则为null
                TextView tvDate = (TextView) v;
                tvDate.setText(DateUtil.dateToString(date, DateUtil.YMD_HM));
                switch (timeType) {
                    case TIME_TYPE_INVISIBLE:
                        invisibility.setHiddenTimeStr(DateUtil.dateToString(date, DateUtil.YMD_HM));
                        break;
                    case TIME_TYPE_CHECK:
                        invisibility.setInspecTimeStr(DateUtil.dateToString(date, DateUtil.YMD_HM));
                        break;
                }
            }
        })
                //年月日时分秒 的显示与否，不设置则默认全部显示
                .setType(new boolean[]{true, true, true, true, true, false})
                .setLabel("年", "月", "日", "时", "分", "")
                .isCenterLabel(false)
                .setDividerType(WheelView.DividerType.WRAP)
                .setDividerColor(ContextCompat.getColor(YueApplacation.mContext, R.color.colorAccent))
                .setContentSize(PhoneUtil.dpTopx(YueApplacation.mContext, 8))
                .setDate(selectedDate)
                .setRangDate(startDate, endDate)
                .setDecorView(null)
                .build();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_sheltered_check;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @OnClick(R.id.iv_invisible_time_more)
    public void getInvisibleTime() {
        timeType = TIME_TYPE_INVISIBLE;
        tpv.show(tvInvisibleTime);
    }

    @OnClick(R.id.iv_time_more)
    public void getCheck() {
        timeType = TIME_TYPE_CHECK;
        tpv.show(tvAcceptTime);
    }

    private boolean linkData() {
        if (invisibility.getPwiId() == null) {
            invisibility.setPwiId("");
        }
        //invisibility.setPwiName(etAcceptPoint.getText().toString());
        invisibility.setPwiName(mQuestionView.getQuestEdit().getText().toString());

        invisibility.setConSite(etPoint.getText().toString());
        invisibility.setConsUnit(tvCompany.getText().toString());
        invisibility.setFunctionary(etChargePerson.getText().toString());
        invisibility.setHiddenContent(etAcceptContent.getEtContent().getText().toString());
        invisibility.setReMark(etDis.getText().toString());
        invisibility.setAcceptPerson(acceptPerson.getEtContent().getText().toString());
        if (StringUtil.isEmpty(invisibility.getPwiName())) {
            showToast("请输入检查部位");
            return false;
        }
        if (StringUtil.isEmpty(invisibility.getHiddenContent())) {
            showToast("请输入工程内容");
            return false;
        }
        if (StringUtil.isEmpty(invisibility.getConsUnit())) {
            showToast("请选择施工单位");
            return false;
        }
//        if (StringUtil.isEmpty(invisibility.getConSite())) {
//            showToast("请输入施工部位");
//            return false;
//        }
        if (StringUtil.isEmpty(invisibility.getHiddenTimeStr())) {
            showToast("请选择隐蔽时间");
            return false;
        }
        if (StringUtil.isEmpty(invisibility.getInspecTimeStr())) {
            showToast("请选择检查时间");
            return false;
        }
        if (StringUtil.isEmpty(invisibility.getInspeResult())) {
            showToast("请选择检验结果");
            return false;
        }
        List<String> imageContent = new ArrayList<>();
        if (images.size() > 1) {
            for (int i = 0; i < images.size() - 1; i++) {
                imageContent.add(images.get(i));
            }
            invisibility.setEncloSure(StringUtil.imageArryToString(imageContent));
        } else {
            invisibility.setEncloSure("");
        }
        return true;
    }

    @OnClick(R.id.tv_sub)
    public void submitCheck() {
        if (linkData()) {
            if (SettingManager.getInstance().getCompanyCode().equals("scgljl") || SettingManager.getInstance().getCompanyCode().equals("local")) {
                if (isUpdate) {
                    invisibility.setReCeiver(userInfo.getId());
                    uploadMulFile(false);
                } else {
                    invisibility.setReCeiver(userInfo.getId());
                    uploadMulFile(true);
                }
            }else{
                //  todo  提交检测结果
                if (isUpdate) {
                    invisibility.setReCeiver(userInfo.getId());
                    mPresenter.commitSheiteredCheck(userInfo.getId(), invisibility, false);
                } else {
                    invisibility.setReCeiver(userInfo.getId());
                    mPresenter.commitSheiteredCheck(userInfo.getId(), invisibility, true);
                }
            }
        }
    }

    @OnClick(R.id.tv_save)
    public void save() {
        if (linkData()) {
            //  todo  提交检测结果
            UnSubmitInfo unSubmitInfo = new UnSubmitInfo(Constant.HTTP_COMMIT_QUALITY_INVISIBILITY,
                    WorkSubmitActivity.WORK_TYPE_NAME_QUALITY_INVISIBILITY
                    , userInfo.getId(), projectInfo.getProjectId(), projectInfo.getProjectName(),
                    DateUtil.getTime(DateUtil.YMD_HM),
                    StringUtil.objectToJson(invisibility));
            DaoUtil.addUnSubmitInfo(unSubmitInfo);
            showToast("保存成功");
            setResult(1);
            finish();
        }
    }

    @Override
    public void onCheckedChanged(RadioGroup radioGroup, int i) {
        switch (i) {
            case R.id.rb_ok:
                invisibility.setInspeResult("1");
                break;
            case R.id.rb_no:
                invisibility.setInspeResult("0");
                break;
        }
    }


    /**
     * 通过url，展示照片
     *
     * @param url
     */
    private void showImage(String url) {

        if (imageDialog != null) {
            imageDialog.showImageDialog(getFragmentManager(), url);
        }
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), REQUEST_CODE_TAKE_PHOTO);
            PhotoCaptureActivity.setWaterListener(waterMaskHelper.getWaterMarkListener());
            PhotoCaptureActivity.setPhotoListener(waterMaskHelper.getPhotoListener());
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }


    private void filMethod() {
        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);
        mFileView.setApply(canEdit);
        // 点击添加附件
        mFileView.getAddFileBtn().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showFileSelect();
            }
        });
        mFileView.setFileOperateContract(new YFileListView.FileOperateContract() {
            @Override
            public void openFile(int index) {
                mFileList.remove(index);
                mFileView.setList(mFileList);
            }
        });
        // 打开附件
        mFileView.setOpenFileContract(new YFileListView.OpenFileContract() {
            @Override
            public void openFile(EnclosureListDto dto) {
                WebActivity.launch(ShelteredCheckActivity.this, dto);
            }
        });
    }

    private void showFileSelect() {
        // 空照片 ，添加
        String[] str = new String[]{"系统相机", "手机相册", "手机文件"};
        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
        ab.setItems(str, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                switch (which) {
                    case 0://相机
                        isTakePhoto = true;
                        takePhotoNew();
                        break;
                    case 1://相册
                        isTakePhoto = false;
                        getPhotoNew();
                        break;
                    case 2://手机文件
                        getFile();
                        break;
                }
            }
        });
        ab.show();
    }

    /**
     * 相册获得照片
     */
    private void getPhotoNew() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), Constant.REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhotoNew() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), Constant.REQUEST_CODE_TAKE_PHOTO);
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    /**
     * 打开文件选择器
     */
    private void getFile() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("*/*");// 任意类型文件
        this.startActivityForResult(intent, Constant.REQUEST_CODE_FILE_SELECT);
    }


    BGAPhotoHelper1 mPhotoHelper1 = new BGAPhotoHelper1();

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_CODE_CHOOSE_PHOTO:
                if (resultCode == Activity.RESULT_OK) {
                    try {
                        startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), REQUEST_CODE_CROP);
                    } catch (Exception e) {
                        mPhotoHelper.deleteCropFile();
                        BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                        e.printStackTrace();
                    }
                }
                break;
            case REQUEST_CODE_TAKE_PHOTO:
                if (resultCode == Activity.RESULT_OK) {
                    try {
                        startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800,
                                800), REQUEST_CODE_CROP);
                    } catch (Exception e) {
                        mPhotoHelper.deleteCameraFile();
                        mPhotoHelper.deleteCropFile();
                        BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                        e.printStackTrace();
                    }
                }
                break;
            case REQUEST_CODE_CROP:
                if (SettingManager.getInstance().getCompanyCode().equals("scgljl") || SettingManager.getInstance().getCompanyCode().equals("local")) {

                    if (resultCode == Activity.RESULT_OK) {
                        // getCropIntent  获取裁剪完图片的路径
                        String photoPath = mPhotoHelper1.getCropFilePath();
//                showToast(photoPath);
                        File file = new File(mPhotoHelper1.getCropFilePath());
                        double fileSize = FileUtil.getFileOrFilesSize(photoPath, 3);
                        Log.i("fileSize", fileSize + "");

                        //showToast(photoPath);
                        if (fileSize > 1) {
                            ImageUtil.compressImageByLuban(photoPath);
                            String s = ImageUtil.compressImage(photoPath);
                            mFileList.add(new EnclosureListDto(file.getName(), s));
                        } else {
                            mFileList.add(new EnclosureListDto(file.getName(), photoPath));
                        }
                        mFileView.setList(mFileList);
                    } else {
                        mPhotoHelper.deleteCameraFile();
                        mPhotoHelper.deleteCropFile();
                    }

                }else{
                    if (resultCode == Activity.RESULT_OK) {
                        // getCropIntent  获取裁剪完图片的路径
                        String photoPath = mPhotoHelper1.getCropFilePath();
                        File file = new File(mPhotoHelper1.getCropFilePath());
                        double fileSize = FileUtil.getFileOrFilesSize(photoPath, 3);
                        Log.i("fileSize", fileSize + "");
                        if (fileSize > 1) {
                            ImageUtil.compressImageByLuban(photoPath);
                        } else {
                            uploadCorpPhoto(file);
                        }
                    } else {
                        mPhotoHelper.deleteCameraFile();
                        mPhotoHelper.deleteCropFile();
                    }
                }

                break;
            case REQUEST_CONSTRUCTIONUNITLIST_ACTIVITY:
                if (resultCode == Activity.RESULT_OK) {
                    tvCompany.setText(data.getStringExtra("data"));
                }
                break;
            case Constant.REQUEST_CODE_UNIT_DANWEI:
                if (resultCode == Activity.RESULT_OK) {
                    mQuestionView.getQuestEdit().setText(data.getStringExtra("data"));
                    mQuestionView.setDocContent(data.getStringExtra("data1"));
                    invisibility.setUnitProjectId(data.getStringExtra("ids"));
                }
                break;
            case Constant.REQUEST_CODE_FILE_SELECT:
                String path = "";
                if (data == null) {
                    // 用户未选择任何文件，直接返回
                    return;
                }
                path = FileUtil.getRealPath(this, data.getData());
                File file = new File(path);
                mFileList.add(new EnclosureListDto(file.getName(), path));
                mFileView.setList(mFileList);
                break;
    }
}

//加水印上传图片
private void uploadCorpPhoto(File file) {
    //应该在这里把绘制完的水印图片路径传过去
    ArrayList<String> strings = new ArrayList<>();
    strings.add(file.toString());
    if (waterMaskHelper.getPhotoListener() != null) {
        //选择照片的uri，默认为下标1的元素
        waterMaskHelper.getPhotoListener().onChoose(strings);
    }
    if (waterMaskHelper.getWaterMarkListener() != null) {
        LogUtil.e("getWaterMarkListener");
        WaterMask.WaterMaskParam maskParam = waterMaskHelper.getWaterMarkListener().onDraw();
        waterMaskHelper.getWaterMarkListener().onDraw();
        Bitmap bitmap = ImageUtil.getBitmap(String.valueOf((file)));
        WaterMask.draw(mContext, bitmap, String.valueOf((file)), maskParam);
        mContext.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(file)));
    }
    presenter.uploadPhotoImage(file.getPath());
    LogUtil.e("uploadPhotoImage " + file.getPath());
}

//新图片压缩
@Subscribe(threadMode = ThreadMode.MAIN)
public void messageEvent(ImageCompressEvent event) {
    if (event == null) return;
    if (event.getType() == ImageCompressEvent.TYPE_START) {
        showProgress();
    } else if (event.getType() == ImageCompressEvent.TYPE_END) {
        dismissProgress();
        uploadCorpPhoto(event.getFile());
    } else if (event.getType() == ImageCompressEvent.TYPE_ERROR) {
        showToast("图片压缩失败");
        dismissProgress();
    }
}

@OnClick({R.id.iv_select_contruction_unit, R.id.tv_company})
public void selectContructionUnit() {
    Intent intent = new Intent(mContext, ConstructionUnitListActivity.class);
    startActivityForResult(intent, REQUEST_CONSTRUCTIONUNITLIST_ACTIVITY);
}

@Override
public void onSaveInstanceState(Bundle outState) {
    super.onSaveInstanceState(outState);
    BGAPhotoHelper.onSaveInstanceState(mPhotoHelper, outState);
}

@Override
protected void onRestoreInstanceState(Bundle savedInstanceState) {
    super.onRestoreInstanceState(savedInstanceState);
    BGAPhotoHelper.onRestoreInstanceState(mPhotoHelper, savedInstanceState);
}

@Override
protected void onStart() {
    super.onStart();
    if (!EventBus.getDefault().isRegistered(this))
        EventBus.getDefault().register(this);
}

@Override
protected void onStop() {
    super.onStop();
    if (EventBus.getDefault().isRegistered(this))
        EventBus.getDefault().unregister(this);
}

@Subscribe(threadMode = ThreadMode.MAIN)
public void onMessageEvent(PhotoUploadResulte resulte) {
    dismissProgress();
    showToast(resulte.getMessge());
    if (resulte.isIsok()) {
        // todo 图片上传成功

        String imageUri = SettingManager.getInstance().getBaseUrl() + resulte.getImageUrl();
        initImage(imageUri);
    } else {
        initImage(resulte.getImageUrl());
    }
}


private void initImage(String url) {
    Log.i("000", "______图片地址：" + url);
    images.add(0, url);
    imageAdapter.notifyDataSetChanged();
}


@Override
public void showCommitResult(boolean isOk) {
    if (isOk) {
        showToast("质量验收报告已送审！");
        setResult(2);
        finish();
    } else {
        showToast("提交失败！");
    }
}

/**
 * 6.0之上权限生情
 */
private void getPermissions(int code) {
    ActivityCompat.requestPermissions(this, PERMISSIONS, code);
}

@Override
public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                       @NonNull int[] grantResults) {
    super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    if (requestCode == Constant.REQUEST_CODE_TAKE_POHO) {
        takePhoto();
    }
}

private AMapLocationClient locationClient = null;
private AMapLocationClientOption locationOption = null;

private void initLocation() {
    //初始化client
    locationClient = new AMapLocationClient(this.getApplicationContext());
    locationOption = getDefaultOption();
    //设置定位参数
    locationClient.setLocationOption(locationOption);
    // 设置定位监听
    locationClient.setLocationListener(locationListener);

}

/**
 * 默认的定位参数
 *
 * <AUTHOR>
 * @since 2.8.0
 */
private AMapLocationClientOption getDefaultOption() {
    AMapLocationClientOption mOption = new AMapLocationClientOption();
    mOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
    //可选，设置定位模式，可选的模式有高精度、仅设备、仅网络。默认为高精度模式
    mOption.setGpsFirst(false);//可选，设置是否gps优先，只在高精度模式下有效。默认关闭
    mOption.setHttpTimeOut(30000);//可选，设置网络请求超时时间。默认为30秒。在仅设备模式下无效
    mOption.setInterval(10000);//可选，设置定位间隔。默认为2秒
    mOption.setNeedAddress(true);//可选，设置是否返回逆地理地址信息。默认是true
    mOption.setOnceLocation(false);//可选，设置是否单次定位。默认是false
    mOption.setOnceLocationLatest(false);//可选，设置是否等待wifi刷新，默认为false.如果设置为true,会自动变为单次定位，持续定位时不要使用
    AMapLocationClientOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTP);//可选，
    // 设置网络请求的协议。可选HTTP或者HTTPS。默认为HTTP
    mOption.setSensorEnable(false);//可选，设置是否使用传感器。默认是false
    mOption.setWifiScan(true); //可选，设置是否开启wifi扫描。默认为true，如果设置为false会同时停止主动刷新，停止以后完全依赖于系统刷新，定位位置可能存在误差
    mOption.setLocationCacheEnable(true); //可选，设置是否使用缓存定位，默认为true
    mOption.setGeoLanguage(AMapLocationClientOption.GeoLanguage.DEFAULT);//可选，设置逆地理信息的语言，默认值为默认语言（根据所在地区选择语言）
    return mOption;
}

/**
 * 定位监听
 */
AMapLocationListener locationListener = new AMapLocationListener() {
    @Override
    public void onLocationChanged(AMapLocation location) {
        if (null != location) {
            locationdata = location;
            //errCode等于0代表定位成功，其他的为定位失败，具体的可以参照官网定位错误码说明
            if (location.getErrorCode() == 0) {
                address = location.getAddress();
                stopLocation();
                destroyLocation();
            }
        } else {
            showToast("定位失败");
        }
    }
};

/**
 * 开始定位
 *
 * <AUTHOR>
 * @since 2.8.0
 */
private void startLocation() {
    // 设置定位参数
    locationClient.setLocationOption(locationOption);
    // 启动定位
    locationClient.startLocation();
}

/**
 * 停止定位
 *
 * <AUTHOR>
 * @since 2.8.0
 */
private void stopLocation() {
    // 停止定位
    locationClient.stopLocation();
}

/**
 * 销毁定位
 *
 * <AUTHOR>
 * @since 2.8.0
 */
private void destroyLocation() {
    if (null != locationClient) {
        /**
         * 如果AMapLocationClient是在当前Activity实例化的，
         * 在Activity的onDestroy中一定要执行AMapLocationClient的onDestroy
         */
        locationClient.onDestroy();
        locationClient = null;
        locationOption = null;
    }
}

@Override
protected void onResume() {
    super.onResume();
    //初始化定位
    initLocation();
    //启动定位
    startLocation();
}


    private void uploadMulFile(boolean isUpdate) {
        showProgress();
        if (mFileList.isEmpty()) {
            mPresenter.commitSheiteredCheck(userInfo.getId(), invisibility, isUpdate);
        } else {
            if (StringUtil.isEmpty(invisibility.getIsId())) {
//            if ( invisibility.getIsId().isEmpty()) {
                getUUID();
                showToast("未获取到主键");
                return;
            }
            String s = DateUtil.dateToString(new Date(), DateUtil.YM1);
            HashMap<String, String> paras = new HashMap<>();
            paras.put("filePath", "pdm/quality/" + s);
            paras.put("businessId", invisibility.getIsId());
            paras.put("businessType", "T_QuInvisibility");
            OkHttpRequestManager.getInstance().uploadMulFile(mFileList, paras, new IRequestCallback() {
                @Override
                public void onSuccess(String response) {
                    Log.i("upload", "onSuccess: ");
                    mPresenter.commitSheiteredCheck(userInfo.getId(), invisibility, isUpdate);
                }
                @Override
                public void onFailure(Throwable throwable) {
                    Log.i("upload", "onFailure: ");
                }
            });
        }
    }

    // 获取附件列表
    private void getFileList(String bt) {
        HashMap<String, Object> paras = new HashMap<>();
        paras.put("businessId", bt);
        paras.put("businessType", "T_QuInvisibility");
        Api.getGbkApiserver().getEnclosureList(Constant.HTTP_GET_ENCLOSURE_LIST, paras).enqueue(new Callback<NetworkResponse<List<EnclosureListDto>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<EnclosureListDto>>> call,
                                   Response<NetworkResponse<List<EnclosureListDto>>> response) {
                if (response.body().getData() != null) {
                    mFileList.clear();
                    mFileList.addAll(response.body().getData());
                    mFileView.setList(mFileList);
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<List<EnclosureListDto>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

    // 获取UUID
    private void getUUID() {
        Api.getGbkApiserver().testApi("getUUID", new HashMap<>()).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
//                mView.dismissProgress();
                Log.i("TAG", "onResponse: " + response.body().getData());
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    String isId = response.body().getData().toString();
                    invisibility.setIsId(isId);
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

}
