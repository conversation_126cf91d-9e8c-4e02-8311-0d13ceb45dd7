package com.kisoft.yuejianli.views;

import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.contract.PendingContract;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.ProjectUsInfoDto;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.PendingModel;
import com.kisoft.yuejianli.presenter.PendingPresenter;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

/**
 * Description: 待审批
 * Author     : yanlu
 * Date       : 2018/12/24 14:30
 */

public class PendingActivity extends BaseActivity<PendingModel, PendingPresenter> implements PendingContract.PendingViewContract {
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_sponsor)
    TextView tvSponsor;
    @BindView(R.id.tv_status)
    TextView tvStatus;
    @BindView(R.id.tv_pend_num)
    TextView tvPendNum;
    @BindView(R.id.tv_notice_type)
    TextView tvNoticeType;
    @BindView(R.id.tv_content)
    TextView tvContent;
    @BindView(R.id.tv_author)
    TextView tvAuthor;
    @BindView(R.id.tv_create_author)
    TextView tvCreateAuthor;
    @BindView(R.id.tv_create)
    TextView tvCreate;
    @BindView(R.id.tv_create_status)
    TextView tvCreateStatus;
    @BindView(R.id.tv_create_time)
    TextView tvCreateTime;
    @BindView(R.id.tv_approver_author)
    TextView tvApproverAuthor;
    @BindView(R.id.tv_approver)
    TextView tvApprover;
    @BindView(R.id.tv_approver_status)
    TextView tvApproverStatus;
    @BindView(R.id.tv_approver_time)
    TextView tvApproverTime;
    @BindView(R.id.ll_btn)
    LinearLayout llBtn;

    private PopupWindow popupWindow = null;

    /**
     * 通知单状态；1=待审批；3=已通过；
     */
    private String snStatus;

    private ProjectUsInfoDto dto;

    private PendingModel mModel;

    private PendingPresenter presenter;

    private String approveStatus;//审批状态（0=同意；1=拒绝）
    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    @Override
    public int getLayoutId() {
        return R.layout.activity_pending;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initPopupWindow();
        snStatus = getIntent().getStringExtra("snStatus");
        dto = (ProjectUsInfoDto) getIntent().getSerializableExtra("ProjectUsInfoDto");

        mModel = new PendingModel(mContext);
        presenter = new PendingPresenter(this, mModel);
        initMVP(mModel, presenter);

        initData();
        initView();
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
    }

    private void initView() {
        tvTitle.setText("通知单审批");
        tvSponsor.setText(dto.getCreateName());
        if (dto.getCreateName().length() > 0) {
            tvCreateAuthor.setText(dto.getCreateName().substring(0, 1));
            tvAuthor.setText(dto.getCreateName().substring(0, 1));
        }

        switch (snStatus) {
            case "1":
                tvStatus.setText("待审批");
                tvApproverStatus.setText("待审批");
                break;
            case "3":
                tvStatus.setText("已通过");
                tvApproverStatus.setText("已通过");
                llBtn.setVisibility(View.GONE);
                break;
        }

        tvPendNum.setText(dto.getSnNumber());

        if (dto.getIsHidDange().equals("0")) {      //安全隐患通知单
            tvNoticeType.setText("安全隐患通知单");
        } else {
            tvNoticeType.setText("监理通知单");
        }

        tvContent.setText(dto.getContent());

        tvCreate.setText(dto.getCreateName());
        tvCreateTime.setText(dto.getCreateTime());
        tvApprover.setText(dto.getUserName());
        if (dto.getUserName().length() > 0) {
            tvApproverAuthor.setText(dto.getUserName().substring(0, 1));
        }
        tvApproverTime.setText(dto.getApproveTime());

        if (userInfo.getId().equals(dto.getUserId())) {
            llBtn.setVisibility(View.VISIBLE);
        } else {
            llBtn.setVisibility(View.GONE);
        }
    }

    @OnClick({R.id.iv_back, R.id.btn_agree, R.id.btn_refuse})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.btn_agree:
                approveStatus = "0";
                //设置显示的位置
                popupWindow.showAtLocation(view, Gravity.CENTER, 0, 0);
                //显示popupWindow时屏幕为半透明
                backgroundAlpha(0.5f);

                break;
            case R.id.btn_refuse:
                approveStatus = "1";
                //设置显示的位置
                popupWindow.showAtLocation(view, Gravity.CENTER, 0, 0);
                //显示popupWindow时屏幕为半透明
                backgroundAlpha(0.5f);

                break;
        }
    }

    private void initPopupWindow() {
        // 一个自定义的布局，作为显示的内容
        View view = LayoutInflater.from(mContext).inflate(R.layout.layout_pending_popwindow_style, null);

        // 下面是两种方法得到宽度和高度 getWindow().getDecorView().getWidth()
        popupWindow = new PopupWindow(view,
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT);
        // 设置popWindow弹出窗体可点击，这句话必须添加，并且是true
        popupWindow.setFocusable(true);

        //物理返回键有响应
        popupWindow.setBackgroundDrawable(new BitmapDrawable());

        //窗口关闭事件
        popupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                backgroundAlpha(1f);
            }
        });

        // 设置popWindow的显示和消失动画
        popupWindow.setAnimationStyle(R.style.MyPopupWindow_anim_style);

        final EditText et_reason = view.findViewById(R.id.et_reason);
        Button btn_cancel = view.findViewById(R.id.btn_cancel);
        Button btn_commit = view.findViewById(R.id.btn_commit);

        btn_cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                popupWindow.dismiss();
            }
        });
        btn_commit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //审批意见
                String approveRemark = et_reason.getText().toString();
                popupWindow.dismiss();
                showProgress();
                presenter.updateSupNotice(dto.getSnId(), approveStatus, approveRemark);
            }
        });

    }


    /**
     * 设置添加屏幕的背景透明度
     */
    public void backgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.alpha = bgAlpha; //0.0-1.0
        getWindow().setAttributes(lp);
    }

    @Override
    public void updateSupNotice(Boolean b) {
        dismissProgress();
        if (b) {
            showToast("审批成功");
            finish();
        }
    }
}
