package com.kisoft.yuejianli.views;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.adpter.QualityAccidentAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectQualityCountMonthContract;
import com.kisoft.yuejianli.entity.AccidentInfos;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.QualityAccident;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ProjectQualityCountMonthModel;
import com.kisoft.yuejianli.presenter.ProjectQualityCountMonthPresenter;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/4/18.
 */

public class ProjectQualityCountMonthFragment extends BaseFragment<ProjectQualityCountMonthModel,
        ProjectQualityCountMonthPresenter> implements ProjectQualityCountMonthContract.ProjectQualityCountMonthViewContract, View.OnClickListener {


    private TextView tvTime;
    private ImageView ivLeft;
    private ImageView ivRight;

    private TextView tvComplete;
    private TextView tvCompleteNum;
    private TextView tvDoing;
    private TextView tvDoingNum;
    private View llComplete;
    private View llDoing;
    private RecyclerView rvContent;

    private List<QualityAccident> accidents = new ArrayList<>();
    private List<QualityAccident> selectAccidents = new ArrayList<>();
    private QualityAccidentAdapter mAdapter;

    private int count = 0;
    private int page = 1;
    private int pageSize = 20;
    private String month;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private View empotyView;

    private int doingCount = 0;
    private int handelCount = 0;
    private int contentType = 0;
    private int enableColor = ContextCompat.getColor(YueApplacation.mContext, R.color.ic_text_normal);
    private int disableColor = ContextCompat.getColor(YueApplacation.mContext, R.color.text_bg);

    private ProjectQualityCountMonthModel model;
    private ProjectQualityCountMonthPresenter mPresenter;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        model = new ProjectQualityCountMonthModel(getContext());
        mPresenter = new ProjectQualityCountMonthPresenter(this, model);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        initView(inflater);
        initData();
        return mRootView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    private void initView(LayoutInflater inflater) {
        mRootView = inflater.inflate(getRootView(), null);
        empotyView = inflater.inflate(R.layout.page_no_data, null);
        tvTime = mRootView.findViewById(R.id.tv_month);
        ivLeft = mRootView.findViewById(R.id.iv_left);
        ivRight = mRootView.findViewById(R.id.iv_right);
        ivLeft.setOnClickListener(this);
        ivRight.setOnClickListener(this);
        month = DateUtil.getMonthDate();
        tvTime.setText(month);

        llComplete = mRootView.findViewById(R.id.ll_complete);
        llComplete.setOnClickListener(this);
        tvComplete = mRootView.findViewById(R.id.tv_complete);
        tvCompleteNum = mRootView.findViewById(R.id.tv_complete_num);
        llDoing = mRootView.findViewById(R.id.ll_doing);
        llDoing.setOnClickListener(this);
        tvDoingNum = mRootView.findViewById(R.id.tv_doing_num);
        tvDoing = mRootView.findViewById(R.id.tv_doing);
        rvContent = mRootView.findViewById(R.id.rv_quality_count_all);
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(layoutManager);
        }
        mAdapter = new QualityAccidentAdapter(R.layout.item_quality_accident, selectAccidents);
        mAdapter.setEmptyView(empotyView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {

            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                //todo
            }
        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                // todo
                rvContent.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (accidents.size() >= count) {
                            mAdapter.loadMoreEnd();
                        } else {
                            getData();
                        }
                    }
                }, Constant.LOAD_MORE_DELAY);
            }

        }, rvContent);
        rvContent.setAdapter(mAdapter);
        initCheckStatus();

    }

    private void initData() {
        count = 0;
        page = 1;
        accidents.clear();
        selectAccidents.clear();
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        if(projectInfo != null){
            getHandelCount();
        }
    }

    private void getHandelCount() {
        mPresenter.getHandelCount(userInfo.getId(), projectInfo.getProjectId(), month);
    }

    private void getData() {
        String c = Integer.toString(count);
        String s = Integer.toString(pageSize);
        String p = Integer.toString(page);
        mPresenter.getAllAccidents(userInfo.getId(), projectInfo.getProjectId(), month, c, s, p);
    }

    @Override
    public int getRootView() {
        return R.layout.fragment_project_quality_count_month;
    }

    private void initCheckStatus() {
        // 处理页面
        switch (contentType) {
            case 1:
                llComplete.setEnabled(false);
                tvCompleteNum.setTextColor(disableColor);
                tvComplete.setTextColor(disableColor);
                llDoing.setEnabled(true);
                tvDoingNum.setTextColor(enableColor);
                tvDoing.setTextColor(enableColor);
                break;

            case 0:
                llComplete.setEnabled(true);
                tvCompleteNum.setTextColor(enableColor);
                tvComplete.setTextColor(enableColor);
                llDoing.setEnabled(false);
                tvDoingNum.setTextColor(disableColor);
                tvDoing.setTextColor(disableColor);
                break;

            default:

                break;
        }
        // 处理数据
        initCheckData();
    }


    private void initCheckData() {
        selectAccidents.clear();
        if (contentType == 1) {
            for (QualityAccident accident : accidents) {
                if (StringUtil.isEqual(QualityAccident.ACCIDENT_LEVEL1, accident.getAccidentLevel())) {
                    selectAccidents.add(accident);
                }
            }

        } else {
            for (QualityAccident accident : accidents) {
                if (!StringUtil.isEqual(QualityAccident.ACCIDENT_LEVEL1, accident.getAccidentLevel())) {
                    selectAccidents.add(accident);
                }
            }
        }
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ll_doing:
                contentType = 0;
                initCheckStatus();
                break;
            case R.id.ll_complete:
                contentType = 1;
                initCheckStatus();
                break;

            case R.id.iv_left:
                getLastMonth();
                break;

            case R.id.iv_right:
                getAfterMonth();
                break;

            default:
                break;
        }
    }

    private void getLastMonth() {
        String time = this.month;
        month = DateUtil.getLsatMonth(time);
        tvTime.setText(month);
        initData();
        mAdapter.notifyDataSetChanged();
    }

    private void getAfterMonth() {
        String time = this.month;
        month = DateUtil.getAfterMonth(time);
        tvTime.setText(month);
        initData();
        mAdapter.notifyDataSetChanged();
    }


    @Override
    public void showAccidents(AccidentInfos infos, int type) {
        if (infos != null) {
            count = infos.getCount();
            doingCount = count - handelCount;
            tvDoingNum.setText(doingCount + "");
            if (infos.getList() != null) {
                if (type == 0) {
                    accidents.clear();
                    accidents.addAll(infos.getList());
                    page++;
                    selectAccidents.addAll(getSelectAccidents(infos.getList()));
                } else {
                    accidents.addAll(infos.getList());
                    page++;
                    mAdapter.loadMoreComplete();
                    selectAccidents.addAll(getSelectAccidents(infos.getList()));
                }
            }
            mAdapter.notifyDataSetChanged();

        }
    }

    @Override
    public void showHandelCount(String count) {
        tvDoingNum.setText(count);
        handelCount = Integer.valueOf(count);
        getData();
    }

    private List<QualityAccident> getSelectAccidents(List<QualityAccident> info) {
        List<QualityAccident> selectInfo = new ArrayList<>();
        if (contentType == 1) {
            for (QualityAccident accident : info) {
                if (StringUtil.isEqual(QualityAccident.ACCIDENT_LEVEL1, accident.getAccidentLevel())) {
                    selectInfo.add(accident);
                }
            }
        } else {
            for (QualityAccident accident : info) {
                if (!StringUtil.isEqual(QualityAccident.ACCIDENT_LEVEL1, accident.getAccidentLevel())) {
                    selectInfo.add(accident);
                }
            }
        }
        return selectInfo;
    }
}
