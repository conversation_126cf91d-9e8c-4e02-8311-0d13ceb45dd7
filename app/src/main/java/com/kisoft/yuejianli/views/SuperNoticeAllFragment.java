package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.adpter.SuperNoticeAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.SuperNoticeAllContract;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.SuperNoticeInfos;
import com.kisoft.yuejianli.entity.SurpervisionNotice;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.SuperNoticeAllModel;
import com.kisoft.yuejianli.presenter.SuperNoticeAllPresenter;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.R;

import com.kisoft.yuejianli.entity.SuperNoticeAnswer;

/**
 * Created by tudou on 2018/5/15.
 */

public class SuperNoticeAllFragment extends BaseFragment<SuperNoticeAllModel, SuperNoticeAllPresenter>
        implements SuperNoticeAllContract.SuperNoticeAllViewContract,View.OnClickListener{


    private TextView tvComplete;
    private TextView tvCompleteNum;
    private TextView tvDoing;
    private TextView tvDoingNum;
    private View llComplete;
    private View llDoing;
    private RecyclerView rvContent;

    private List<SurpervisionNotice> notices = new ArrayList<>();
    private List<SurpervisionNotice> selectNotices = new ArrayList<>();
    private SuperNoticeAdapter mAdapter;

    private int count = 0;
    private int page = 1;
    private int pageSize = 20;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private View empotyView;

    private int doingCount = 0;
    private int handelCount = 0;
    private String contentType = SurpervisionNotice.IS_ANSWER_YES;
    private int enableColor = ContextCompat.getColor(YueApplacation.mContext, R.color.ic_text_normal);
    private int disableColor = ContextCompat.getColor(YueApplacation.mContext ,R.color.text_bg);

    private SuperNoticeAllModel mModel;
    private SuperNoticeAllPresenter mPresenter;


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mModel = new SuperNoticeAllModel(getContext());
        mPresenter = new SuperNoticeAllPresenter(this, mModel);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        initView(inflater);
        initData();
        return mRootView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    private void initView(LayoutInflater inflater) {
        mRootView = inflater.inflate(getRootView(), null);
        empotyView = inflater.inflate(R.layout.page_no_data, null);
        llComplete = mRootView.findViewById(R.id.ll_complete);
        llComplete.setOnClickListener(this);
        tvComplete = mRootView.findViewById(R.id.tv_complete);
        tvCompleteNum = mRootView.findViewById(R.id.tv_complete_num);
        llDoing = mRootView.findViewById(R.id.ll_doing);
        llDoing.setOnClickListener(this);
        tvDoingNum = mRootView.findViewById(R.id.tv_doing_num);
        tvDoing = mRootView.findViewById(R.id.tv_doing);
        rvContent = mRootView.findViewById(R.id.rv_quality_count_all);
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(layoutManager);
        }
        mAdapter = new SuperNoticeAdapter(R.layout.item_super_notice, selectNotices);
        mAdapter.setEmptyView(empotyView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {

            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                //todo
                goAnswerInfo(selectNotices.get(position));
            }
        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                // todo
                rvContent.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (notices.size() >= count) {
                            mAdapter.loadMoreEnd();
                        } else {
                            getData();
                        }
                    }
                }, Constant.LOAD_MORE_DELAY);
            }

        }, rvContent);
        rvContent.setAdapter(mAdapter);
        initCheckStatus();


    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        getHandelCount();

    }

    private void getHandelCount(){
        mPresenter.getHandelCount(userInfo.getId(), projectInfo.getProjectId());
    }

    private void getData() {
        String c = Integer.toString(count);
        String s = Integer.toString(pageSize);
        String p = Integer.toString(page);
        mPresenter.getAllSuperNotices(userInfo.getId(), projectInfo.getProjectId(), null,c,s,p);
    }

    @Override
    public int getRootView() {
        return R.layout.fragment_super_notice_all;
    }


    private void initCheckStatus() {
        // 处理页面
        switch (Integer.valueOf(contentType)) {
            case 1:
                llComplete.setEnabled(false);
                tvCompleteNum.setTextColor(disableColor);
                tvComplete.setTextColor(disableColor);
                llDoing.setEnabled(true);
                tvDoingNum.setTextColor(enableColor);
                tvDoing.setTextColor(enableColor);
                break;

            case 0:
                llComplete.setEnabled(true);
                tvCompleteNum.setTextColor(enableColor);
                tvComplete.setTextColor(enableColor);
                llDoing.setEnabled(false);
                tvDoingNum.setTextColor(disableColor);
                tvDoing.setTextColor(disableColor);
                break;

            default:
                break;

        }
        // 处理数据
        initCheckData();

    }


    private void initCheckData(){
        selectNotices.clear();
        if(StringUtil.isEqual(SurpervisionNotice.IS_ANSWER_YES,contentType)){
            for(SurpervisionNotice notice: notices){
                if(StringUtil.isEqual(SurpervisionNotice.IS_ANSWER_YES,notice.getSnStatus())){
                    selectNotices.add(notice);
                }
            }
        }else {
            for(SurpervisionNotice notice: notices){
                if(!StringUtil.isEqual(SurpervisionNotice.IS_ANSWER_YES,notice.getSnStatus())){
                    selectNotices.add(notice);
                }
            }
        }
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ll_doing:
                contentType = SurpervisionNotice.IS_ANSWER_NO;
                initCheckStatus();
                break;
            case R.id.ll_complete:
                contentType = SurpervisionNotice.IS_ANSWER_YES;
                initCheckStatus();
                break;
            default:
                break;
        }

    }


    @Override
    public void showSuperNotices(SuperNoticeInfos infos, int type) {
        if(infos != null){
            count = infos.getCount();
            doingCount = count-handelCount;
            tvDoingNum.setText(doingCount+"");
            if(infos.getList() != null ){
                if(type == 0){
                    notices.clear();
                    notices.addAll(infos.getList());
                    selectNotices.addAll(getSelectNotices(infos.getList()));
                }else {
                    notices.addAll(infos.getList());
                    page++;
                    mAdapter.loadMoreComplete();
                    selectNotices.addAll(getSelectNotices(infos.getList()));
                }
                mAdapter.notifyDataSetChanged();
            }

        }
    }

    @Override
    public void showHandelCount(String count) {
        tvCompleteNum.setText(count);
        handelCount = Integer.valueOf(count);
        getData();
    }


    private List<SurpervisionNotice> getSelectNotices(List<SurpervisionNotice> info){
        List<SurpervisionNotice> selectInfo = new ArrayList<>();
        if(StringUtil.isEqual(SurpervisionNotice.IS_ANSWER_YES, contentType)){
            for(SurpervisionNotice notice: info){
                if(StringUtil.isEqual(SurpervisionNotice.IS_ANSWER_YES,notice.getSnStatus())){
                    selectInfo.add(notice);
                }
            }
        }else {
            for(SurpervisionNotice notice: info){
                if(!StringUtil.isEqual(SurpervisionNotice.IS_ANSWER_YES,notice.getSnStatus())){
                    selectInfo.add(notice);
                }
            }
        }
        return selectInfo;
    }

    private void goAnswerInfo(SurpervisionNotice notice){
        Intent intent = new Intent();
        intent.setClass(getActivity(),SuperNoticeAnswerActivity.class);
        Bundle bundle = new Bundle();
        bundle.putString(Constant.INTENT_KEY_NOTICE_TYPE , SuperNoticeAnswer.ANSWER_TYPE_SUPER_NOTICE);
        bundle.putSerializable(Constant.INTENT_KEY_NOTICE_INFO, notice);
        intent.putExtras(bundle);

        startActivity(intent);
    }
}
