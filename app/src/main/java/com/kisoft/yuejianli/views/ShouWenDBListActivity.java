package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.viewpager.widget.ViewPager;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.MainFrameAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.ui.NoScrollViewPager;

import org.xutils.common.util.LogUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class ShouWenDBListActivity extends BaseActivity {
    private static final String TAG = "ApplyActivity";
    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.rb_task_detail)
    RadioButton rbTaskDetail;
    @BindView(R.id.rb_task_record)
    RadioButton rbTaskRecord;
    @BindView(R.id.rg_title)
    RadioGroup rgTitle;
    @BindView(R.id.view_task_detail)
    View viewTaskDetail;
    @BindView(R.id.view_task_record)
    View viewTaskRecord;
    @BindView(R.id.vp_mainframe_content)
    NoScrollViewPager vpMainframeContent;

    /**
     * 导航的fragment界面集合
     **/
    private List<Fragment> list_fragments = new ArrayList<>();

    /**
     * 适配器
     **/
    private MainFrameAdapter adapter = null;

    /**
     * 页面类型  true：发起申请  false：申请详情
     */
    public boolean isApply;

    //流程类型（1=待审批、2=已审批、3=我发起的）
    public String workType;

    //业务类型
    private String businessType;

    //回调名称
    private String callBackName;

    public ProcessListBean bean;

    private String titleName;

    public String transType;

    public String flowStateName;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        isApply = getIntent().getBooleanExtra("isApply", true);
        businessType = getIntent().getStringExtra("businessType");

        workType = getIntent().getStringExtra("workType");
        transType = getIntent().getStringExtra("transType");
        flowStateName = getIntent().getStringExtra("flowStateName");
        //isMonthReport=getIntent().getBooleanExtra("isMonthReport",false);
        bean = (ProcessListBean) getIntent().getSerializableExtra("bean");

        initData();
        initView();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_shou_wen_dblist;
    }

    private void initData() {
//        ApplyOtherFragment fragment = new ApplyOtherFragment();
//        List data = new ArrayList();

        list_fragments.add(new ShouWenDJFragment());
        callBackName = "roa_wfCallBackService";
        if (isApply) {
            // 流程提交
            list_fragments.add(new ShouWenTJFragment());
            //list_fragments.add(new HdmhApplySQFragment());
        } else {
            // 流程审批
            list_fragments.add(new ShouWenBLFragment());
            //list_fragments.add(new HdmhApplySPFragment());
        }
        adapter = new MainFrameAdapter(getSupportFragmentManager(), list_fragments);
        vpMainframeContent.setAdapter(adapter);
    }


    private void initView() {
        tvTitle.setText("收文登记");

        /**
         *	viewPager页面改变事件
         */
        vpMainframeContent.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageSelected(int position) {
                switch (position) {
                    case 0:
                        rbTaskDetail.setChecked(true);
                        rbTaskDetail.setTextColor(getResources().getColor(R.color.colorAccent));
                        viewTaskDetail.setBackgroundColor(getResources().getColor(R.color.colorAccent));
                        rbTaskRecord.setTextColor(getResources().getColor(R.color.text_main_black));
                        viewTaskRecord.setBackgroundColor(getResources().getColor(R.color.line_space));
                        break;
                    case 1:
                        rbTaskRecord.setChecked(true);
                        rbTaskRecord.setTextColor(getResources().getColor(R.color.colorAccent));
                        rbTaskDetail.setTextColor(getResources().getColor(R.color.text_main_black));
                        viewTaskDetail.setBackgroundColor(getResources().getColor(R.color.line_space));
                        viewTaskRecord.setBackgroundColor(getResources().getColor(R.color.colorAccent));
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });

        rgTitle.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                if (!isApply) {
                    switch (checkedId) {
                        case R.id.rb_task_detail:
                            vpMainframeContent.setCurrentItem(0, false);
                            break;
                        case R.id.rb_task_record:
                            vpMainframeContent.setCurrentItem(1, false);
                            break;
                    }
                }
            }
        });

        vpMainframeContent.setOffscreenPageLimit(2);//预加载
    }

    @OnClick(R.id.iv_back)
    public void onClick() {
        finish();
    }
    /**
     * 基本信息提交返回数据
     */
    private String back;

    public void setBack(String back) {
        this.back = back;
        vpMainframeContent.setCurrentItem(1, false);
    }

    public String getBack() {
        return back;
    }

    public String getBusinessType() {
        return businessType;
    }

    public String getCallBackName() {
        return callBackName;
    }

    public FragmentManager fragmentManager;

    /*在fragment的管理类中，我们要实现这部操作，而他的主要作用是，当D这个activity回传数据到
这里碎片管理器下面的fragnment中时，往往会经过这个管理器中的onActivityResult的方法。*/
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        /*在这里，我们通过碎片管理器中的Tag，就是每个碎片的名称，来获取对应的fragment*/
//        Fragment f = fragmentManager.findFragmentByTag("DocumentBasicDraftFragment");
        /*然后在碎片中调用重写的onActivityResult方法*/
        // f.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        LogUtil.e(TAG + " onDestroy");
    }
}