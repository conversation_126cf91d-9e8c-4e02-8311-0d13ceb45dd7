package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kisoft.yuejianli.adpter.MaterialAddSelectAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.MaterialAddSelectContract;
import com.kisoft.yuejianli.entity.MaterialInspect;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.MaterialAddSelectModel;
import com.kisoft.yuejianli.presenter.MaterialAddSelectPresenter;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/30.
 */

public class MaterialAddSelectActivity extends BaseActivity<MaterialAddSelectModel, MaterialAddSelectPresenter> implements
        MaterialAddSelectContract.MaterialAddSelectViewContract{

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    private View empotyView;

    private List<MaterialInspect> inspects = new ArrayList<>();
    private MaterialAddSelectAdapter mAdapter;
    private List<MaterialInspect> selectItems = new ArrayList<>();

    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    private MaterialAddSelectModel model;
    private MaterialAddSelectPresenter presenter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new MaterialAddSelectModel(this);
        presenter = new MaterialAddSelectPresenter(this, model);
        initMVP(model, presenter);

        initView();
        initData();
    }

    private void initView(){
        tvTitle.setText("材料/设备进场");
        ivAction.setImageResource(R.drawable.ic_ti_jiao);
        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        if(rvContent.getLayoutManager() == null){
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new MaterialAddSelectAdapter(R.layout.item_material_add_select, inspects);
        mAdapter.setEmptyView(empotyView);
        rvContent.setAdapter(mAdapter);
    }


    private void initData(){
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        if(projectInfo!= null){
            mPresenter.getMaterialInspections(userInfo.getId(), projectInfo.getProjectId(), DateUtil.getTodayDate(DateUtil.YMD_1_HMS));
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_material_add_setect;
    }

    @Override
    public void showMaterialInspections(List<MaterialInspect> inspects) {
        if(inspects!= null){
            this.inspects.clear();
            this.inspects.addAll(inspects);
            mAdapter.notifyDataSetChanged();
        }
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    @OnClick(R.id.iv_action)
    public void commitSelect(){
        ArrayList<String> list = new ArrayList<>();
        for(Integer integer: mAdapter.getSelectItems()){
            selectItems.add(inspects.get(integer));
        }
        for(int i = 0; i<selectItems.size(); i++){
            StringBuffer content  = new StringBuffer();
            content.append(selectItems.get(i).getSerialName());
            content.append(selectItems.get(i).getCount()+",");
            if(StringUtil.isEqual("1",selectItems.get(i).getIsEnter())){
                content.append("经检查质量符合要求，同意进场。");
            }else if (StringUtil.isEqual("0",selectItems.get(i).getIsEnter())){
                content.append("经检查质量不达标，未同意进场。");
            }

            list.add(StringUtil.getLogContent(i+1, content.toString()));
        }
        Intent intent = new Intent();
        intent.putStringArrayListExtra(Constant.INTENT_KEY_LOG_TODAY_MATERIAL, list);
        this.setResult(Constant.REQEST_CODE_GET_TODAY_MATERIAL_ADD, intent);
        finish();
    }
}
