package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.GxListAdapter;
import com.kisoft.yuejianli.adpter.interf.GlobalListener;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.contract.GxListContract;
import com.kisoft.yuejianli.entity.GxDto;
import com.kisoft.yuejianli.entity.GxListDto;
import com.kisoft.yuejianli.entity.TProgDetailDTO;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.GxListModel;
import com.kisoft.yuejianli.presenter.GxListPresenter;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class GxListActivity extends BaseActivity<GxListModel, GxListPresenter>
        implements GxListContract.GxListViewContract, BaseRefreshListener {
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;
    @BindView(R.id.ptrl_content)
    PullToRefreshLayout ptrlContent;

    private GxListModel model;
    private GxListPresenter presenter;

    private int count = 0;
    private int page = 1;
    private int pageCount = 0;
    private int pageSize = 10;
    private List<GxDto> dtoList = new ArrayList<>();
    private GxListAdapter adapter;
    boolean isSelectModel;

    @Override
    public int getLayoutId() {
        return R.layout.activity_refresh_list;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new GxListModel(mContext);
        presenter = new GxListPresenter(this, model);
        initMVP(model, presenter);
        initData();
        initView();
    }

    private void initData() {
        isSelectModel = getIntent().getBooleanExtra("isSelectModel", true);
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            layoutManager.setSmoothScrollbarEnabled(false);
            rvContent.setLayoutManager(layoutManager);
            rvContent.setNestedScrollingEnabled(false);
        }
        adapter = new GxListAdapter(dtoList);
        adapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (isSelectModel) {
                    Intent intent = new Intent();
                    intent.putExtra("data", dtoList.get(position));
                    setResult(Activity.RESULT_OK, intent);
                    finish();
                }
            }
        });
        rvContent.setAdapter(adapter);
    }

    private void initView() {
        tvTitle.setText("项目工序选择");
        ptrlContent.setRefreshListener(this);
    }

    @Override
    public void toReferList(GxListDto dtoList) {
        if (dtoList != null) {
            if (pageCount == 0)
                count = dtoList.getCount();
            this.dtoList.addAll(dtoList.getList());
            adapter.notifyDataSetChanged();
        }
    }

    @Override
    public void finshRefresh() {
        ptrlContent.finishRefresh();
        ptrlContent.finishLoadMore();
    }

    @Override
    public void refresh() {
        dtoList.clear();
        page = 1;
        pageCount = 0;
        getData();
    }

    @Override
    public void loadMore() {
        if (dtoList.size() >= count) {
            showToast("没有更多数据了");
            ptrlContent.finishLoadMore();
        } else {
            page++;
            pageCount = dtoList.size() + pageSize;
            getData();
        }
    }

    private void getData() {
        presenter.toReferList(SettingManager.getInstance().getUserInfo().getId(),
                SettingManager.getInstance().getProject().getProjectId(),
                pageSize + "", page + "", pageCount + "");
    }

    @Override
    public void onResume() {
        super.onResume();
        refresh();
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }

    @OnClick(R.id.tv_submit)
    public void goAddProgress(){
        Intent intent = new Intent(this, AddProgressActivity.class);
        startActivity(intent);
    }
}
