package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.YueApplacation;
import com.kisoft.yuejianli.adpter.ProjectSafeProblemAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectSafeProblemCountAllContract;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.ProjectSafeInspection;
import com.kisoft.yuejianli.entity.ProjectSafeProblemInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ProjectSafeProblemCountAllModel;
import com.kisoft.yuejianli.presenter.ProjectSafeProblemCountAllPresenter;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.R;

import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/4/18.
 */

public class ProjectSafeCountAllFragment extends BaseFragment<ProjectSafeProblemCountAllModel,ProjectSafeProblemCountAllPresenter> implements
        ProjectSafeProblemCountAllContract.ProjectSafeProblemCountAllViewContract, View.OnClickListener{

    private TextView tvComplete;
    private TextView tvCompleteNum;
    private TextView tvDoing;
    private TextView tvDoingNum;
    private View llComplete;
    private View llDoing;
    private RecyclerView rvContent;

    private List<ProjectSafeInspection> safeInspections = new ArrayList<>();
    private List<ProjectSafeInspection> selectInspection = new ArrayList<>();
    private ProjectSafeProblemAdapter mAdapter;

    private int count = 0;
    private int page = 1;
    private int pageSize = 20;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private View empotyView;


    private int doingCount = 0;
    private int handelCount = 0;
    private int contentType = 0;
    private int enableColor = ContextCompat.getColor(YueApplacation.mContext, R.color.ic_text_normal);
    private int disableColor = ContextCompat.getColor(YueApplacation.mContext, R.color.text_bg);

    private ProjectSafeProblemCountAllModel model;
    private ProjectSafeProblemCountAllPresenter presenter;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new ProjectSafeProblemCountAllModel(getContext());
        presenter = new ProjectSafeProblemCountAllPresenter(this, model);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        initView(inflater);
        initData();
        return mRootView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    private void initView(final LayoutInflater inflater) {
        mRootView = inflater.inflate(getRootView(), null);
        empotyView = inflater.inflate(R.layout.page_no_data, null);
        llComplete = mRootView.findViewById(R.id.ll_complete);
        llComplete.setOnClickListener(this);
        tvComplete = mRootView.findViewById(R.id.tv_complete);
        tvCompleteNum = mRootView.findViewById(R.id.tv_complete_num);
        llDoing = mRootView.findViewById(R.id.ll_doing);
        llDoing.setOnClickListener(this);
        tvDoingNum = mRootView.findViewById(R.id.tv_doing_num);
        tvDoing = mRootView.findViewById(R.id.tv_doing);
        rvContent = mRootView.findViewById(R.id.rv_quality_count_all);
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(layoutManager);
        }
        mAdapter = new ProjectSafeProblemAdapter(R.layout.item_project_safe_problem, selectInspection);
        mAdapter.setEmptyView(empotyView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {

            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                //todo
                Intent intent = new Intent();
                intent.setClass(getContext(), SceneSafeInspectActivity.class);
                intent.putExtra("data", (ProjectSafeInspection) adapter.getItem(position));
                startActivity(intent);
            }
        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                // todo
                rvContent.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (safeInspections.size() >= count) {
                            mAdapter.loadMoreEnd();
                        } else {
                            getData();
                        }
                    }
                }, Constant.LOAD_MORE_DELAY);
            }

        }, rvContent);
        rvContent.setAdapter(mAdapter);
        initCheckStatus();
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();

        if(projectInfo != null){
            getHandelCount();
        }
    }

    private void getHandelCount(){
        presenter.getHandelCount(userInfo.getId(), projectInfo.getProjectId(),null,ProjectSafeInspection.PDS_QUALITY_INSPECT_YYZ);
    }

    private void getData(){
        String c = Integer.toString(count);
        String s = Integer.toString(pageSize);
        String p = Integer.toString(page);
        presenter.getProblems(userInfo.getId(), projectInfo.getProjectId(), null,null,c,s,p);
    }

    @Override
    public int getRootView() {
        return R.layout.fragment_project_quality_count_all;
    }

    @Override
    public void showHandelCount(String count) {
        tvDoingNum.setText(count);
        handelCount = Integer.valueOf(count);
        getData();
    }

    @Override
    public void showProblems(ProjectSafeProblemInfo info, int type) {
        if(info != null){
            count = info.getCount();
            doingCount = count-handelCount;
            tvDoingNum.setText(doingCount+"");
            if(info.getList() != null){
                if(type == 0){
                    safeInspections.clear();
                    safeInspections.addAll(info.getList());
                    page++;
                    safeInspections.addAll(getSelectAccidents(info.getList()));
                }else {
                    safeInspections.addAll(info.getList());
                    page++;
                    mAdapter.loadMoreComplete();
                    selectInspection.addAll(getSelectAccidents(info.getList()));
                }
            }
            mAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ll_doing:
                contentType = 0;
                initCheckStatus();
                break;
            case R.id.ll_complete:
                contentType = 1;
                initCheckStatus();
                break;
            default:
                break;
        }
    }



    private void initCheckStatus() {
        // 处理页面
        switch (contentType) {
            case 1:
                llComplete.setEnabled(false);
                tvCompleteNum.setTextColor(disableColor);
                tvComplete.setTextColor(disableColor);
                llDoing.setEnabled(true);
                tvDoingNum.setTextColor(enableColor);
                tvDoing.setTextColor(enableColor);
                break;

            case 0:
                llComplete.setEnabled(true);
                tvCompleteNum.setTextColor(enableColor);
                tvComplete.setTextColor(enableColor);
                llDoing.setEnabled(false);
                tvDoingNum.setTextColor(disableColor);
                tvDoing.setTextColor(disableColor);
                break;

            default:

                break;
        }
        // 处理数据
        initCheckData();
    }

    private void initCheckData(){
        selectInspection.clear();
        if(contentType == 1){
            for(ProjectSafeInspection safeInspection: safeInspections){
                if(StringUtil.isEqual(ProjectSafeInspection.PDS_QUALITY_INSPECT_YYZ,safeInspection.getInspectStatus())){
                    selectInspection.add(safeInspection);
                }
            }

        }else {
            for(ProjectSafeInspection safeInspection: safeInspections){
                if(!StringUtil.isEqual(ProjectSafeInspection.PDS_QUALITY_INSPECT_YYZ,safeInspection.getInspectStatus())){
                    selectInspection.add(safeInspection);
                }
            }
        }
        mAdapter.notifyDataSetChanged();
    }

    private List<ProjectSafeInspection> getSelectAccidents(List<ProjectSafeInspection> info) {
        List<ProjectSafeInspection> selectInfo = new ArrayList<>();
        if (contentType == 1) {
            for (ProjectSafeInspection safeInspection : info) {
                if(StringUtil.isEqual(ProjectSafeInspection.PDS_QUALITY_INSPECT_YYZ,safeInspection.getInspectStatus())){
                    selectInspection.add(safeInspection);
                }
            }
        } else {
            for (ProjectSafeInspection safeInspection: info) {
                if(!StringUtil.isEqual(ProjectSafeInspection.PDS_QUALITY_INSPECT_YYZ,safeInspection.getInspectStatus())){
                    selectInspection.add(safeInspection);
                }
            }
        }
        return selectInfo;
    }
}
