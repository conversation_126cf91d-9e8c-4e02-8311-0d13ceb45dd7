package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ExamLearnProgressAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ExamLearnProgressContract;
import com.kisoft.yuejianli.entity.ExamLearnProgressList;
import com.kisoft.yuejianli.model.ExamLearnProgressModel;
import com.kisoft.yuejianli.presenter.ExamLearnProgressPresenter;
import com.kisoft.yuejianli.utils.ScreenUtils;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.recyclerview.HorizontalDividerItemDecoration;

import java.text.MessageFormat;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 学习进度总汇
 */
public class ExamLearnProgressActivity extends BaseActivity<ExamLearnProgressModel, ExamLearnProgressPresenter> implements ExamLearnProgressContract.View, BaseRefreshListener {

    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.tool_bar)
    Toolbar toolBar;
    @BindView(R.id.mRecyclerView)
    RecyclerView mRecyclerView;
    @BindView(R.id.pullToRefreshLayout)
    PullToRefreshLayout pullToRefreshLayout;
    @BindView(R.id.tvSum)
    TextView tvSum;
    private ExamLearnProgressAdapter mAdapter;
    private int mCount=0;

    public static void launch(Activity activity) {
        Intent intent = new Intent(activity, ExamLearnProgressActivity.class);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        init();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_exam_learn_progress;
    }

    private void init() {
        mModel=new ExamLearnProgressModel(this);
        mPresenter=new ExamLearnProgressPresenter(this,mModel);
        tvTitle.setText("学习进度");
        View emptyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
        mRecyclerView.addItemDecoration(new HorizontalDividerItemDecoration.Builder(this)
                .drawable(R.color.line_space)
                .size(ScreenUtils.dip2px(this, 1))
                .build());
        mAdapter = new ExamLearnProgressAdapter();
        mRecyclerView.setAdapter(mAdapter);
        mAdapter.setEmptyView(emptyView);
        pullToRefreshLayout.setRefreshListener(this);
        refresh();
//        HashMap<String, Object> parameters = new HashMap<>();
//        parameters.put("userId", SettingManager.getInstance().getUserId());
//        parameters.put("userName", SettingManager.getInstance().getUserInfo().getName());
//        parameters.put("count", String.valueOf(0));
//        parameters.put("page", String.valueOf(1));
//        parameters.put("pageSize", String.valueOf(10));
//        Api.getGbkApiserver().testApi(Constant.HTTP_LEARN_PROGRESS_SUM, parameters).enqueue(new Callback<NetworkResponse<Object>>() {
//            @Override
//            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
//
//            }
//
//            @Override
//            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
//
//            }
//        });
    }

    @OnClick(R.id.iv_back)
    void back() {
        finish();
    }

    @Override
    public void refresh() {
        mCount=0;
        mPresenter.getLearnProgressList(mCount,Constant.LOAD_DATA_NORMAL);
    }

    @Override
    public void loadMore() {
        if(mAdapter.getData().size()>=mCount){
            showToast("没有更多数据了");
            pullToRefreshLayout.finishLoadMore();
        }else {
            mPresenter.getLearnProgressList(mCount,Constant.LOAD_DATA_MORE);
        }
    }

    @Override
    public void showLearnProgressList(ExamLearnProgressList examLearnProgressList, int type) {
        if(examLearnProgressList!=null){
            tvSum.setText(MessageFormat.format("总学时：{0}", StringUtil.getPlayVideoTime(examLearnProgressList.getSumTime())));
            ExamLearnProgressList.Result result = examLearnProgressList.getResult();
            mCount=result.getCount();
            if(type==Constant.LOAD_DATA_NORMAL){
                mAdapter.setNewData(result.getList());
            }else {
                mAdapter.addData(result.getList());
            }
        }
    }

    @Override
    public void finishRefresh() {
        pullToRefreshLayout.finishRefresh();
        pullToRefreshLayout.finishLoadMore();
    }

    @Override
    public void showError() {
        pullToRefreshLayout.finishRefresh();
        pullToRefreshLayout.finishLoadMore();
    }
}
