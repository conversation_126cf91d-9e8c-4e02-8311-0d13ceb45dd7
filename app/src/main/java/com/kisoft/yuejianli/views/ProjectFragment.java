package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.adpter.ProjectContentAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectContract;
import com.kisoft.yuejianli.entity.ProjectContent;
import com.kisoft.yuejianli.entity.ProjectEmployer;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ProjectModel;
import com.kisoft.yuejianli.presenter.ProjectPresenter;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/3/12.
 */

public class ProjectFragment extends BaseFragment<ProjectModel, ProjectPresenter>
        implements ProjectContract.ProjectViewContract{


    private List<ProjectContent> contents = new ArrayList<>();
    private RecyclerView rvContent;
    private ProjectContentAdapter mAdapter;
    public static final int PROJECT_SAFETY = 2;        //安全
    public static final int PROJECT_QUALITY = 1;      // 质量
    public static final int PROJECT_PROGRESS = 3;      //进度
    public static final int PROJECT_CONTRACT = 4;      // 合同
    public static final int PROJECT_INVESTMENT = 5;     // 投资
    public static final int PROJECT_LIBRARY = 6;           // 文库
    public static final int PROJECT_TREE = 7;            // 组织结构
    public static final int PROJECT_RECORD = 8;            // 考勤

    private String projectOrg = "";

    private ProjectModel mModel;
    private ProjectPresenter mPresenter;


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mModel = new ProjectModel(getActivity());
        mPresenter = new ProjectPresenter(this,mModel);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        initData();
        initView(inflater);
        return mRootView;
    }

    @Override
    public int getRootView() {
        return R.layout.fragment_project;
    }

    private void initView(final LayoutInflater inflater) {
        rvContent = mRootView.findViewById(R.id.rv_project_content);
        if (rvContent.getLayoutManager() == null) {
            GridLayoutManager layoutManager = new GridLayoutManager(getContext(), 3);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(layoutManager);
        }
        mAdapter = new ProjectContentAdapter(R.layout.item_project_content, contents);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {

            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                ProjectContent con = contents.get(position);
                // todo 处理点击事件
                onClickContent(con.getId());

            }
        });
        rvContent.setAdapter(mAdapter);
    }


    private void initData() {
        // todo 初始化项目内容(获得项目的id)
        contents.clear();
        contents.add(new ProjectContent(PROJECT_QUALITY, "质量"));
        contents.add(new ProjectContent(PROJECT_SAFETY, "安全"));
        contents.add(new ProjectContent(PROJECT_PROGRESS, "进度"));
        contents.add(new ProjectContent(PROJECT_CONTRACT, "合同"));
        contents.add(new ProjectContent(PROJECT_INVESTMENT, "投资"));
        contents.add(new ProjectContent(PROJECT_LIBRARY, "文库"));
        contents.add(new ProjectContent(PROJECT_TREE, "组织结构"));
        contents.add(new ProjectContent(PROJECT_RECORD, "考勤"));
    }



    private void onClickContent(int id) {
        switch (id) {
            case PROJECT_QUALITY:
                goProjectQuality();
                break;

            case PROJECT_SAFETY:
                goProjectSafe();
                break;

            case PROJECT_PROGRESS:
                goProjectProgress();
                break;

            case PROJECT_CONTRACT:
                goContract();
                break;

            case PROJECT_INVESTMENT:
                // todo 投资
                break;

            case PROJECT_LIBRARY:
                // todo 文库
                goKnownLedge();
                break;

            case PROJECT_TREE:
                goTree();
                break;

            case PROJECT_RECORD:
                if (StringUtil.isEmpty(projectOrg)) {
                    getProjectOrg();
                }else {
                    goRecord();
                }

                break;
            default:
                break;
        }
    }

    private void goProjectQuality(){
        Intent intent = new Intent();
        intent.setClass(getContext(), ProjectQualityActivity.class);
        startActivity(intent);
    }

    private void goProjectSafe(){
        Intent intent = new Intent();
        intent.setClass(getContext(), ProjectSafeActivity.class);
        startActivity(intent);
    }

    private void goProjectProgress() {
        Intent intent = new Intent();
        intent.setClass(getContext(), ProjectProgressActivity.class);
        startActivity(intent);
    }

    private void goContract() {
        Intent intent = new Intent();
        intent.setClass(getContext(), ContractTypeActivity.class);
        startActivity(intent);
    }

    private void goKnownLedge(){
        Intent intent = new Intent();
        intent.setClass(getContext(), KnowledgeActivity.class);
        startActivity(intent);
    }

    private void goTree(){
        Intent intent = new Intent();
        intent.setClass(getContext(), PhoneDirectoryActivity.class);
        startActivity(intent);
    }

    private void goRecord(){
            Intent intent = new Intent();
            intent.setClass(getContext(), AttendanceRecordsActivity.class);
            intent.putExtra(Constant.INTENT_KEY_IDS, projectOrg);
            startActivity(intent);
    }

    @Override
    public void showProjectOrgInfo(List<String> ids) {
        dismissProgress();
        String data = StringUtil.arryToString(ids);
        if(!StringUtil.isEmpty(data)){
            projectOrg = data;
            goRecord();
        }
    }

    private void getProjectOrg(){
        List<ProjectEmployer> employers = SettingManager.getInstance().getProjectEmployer();
        if(employers == null){
            showProgress();
            String projectId = SettingManager.getInstance().getProject().getProjectId();
            if(!StringUtil.isEmpty(projectId)){
                mPresenter.getProjectOrgInfo(projectId);
            }
        }else {
            List<String> ids = new ArrayList<>();
            for(ProjectEmployer employer : employers){
                ids.add(employer.getId());
            }
            showProjectOrgInfo(ids);
        }
    }
}
