package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.EnclosureListAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.contract.ApplyHTSPContract;
import com.kisoft.yuejianli.entity.ConBeanInfo;
import com.kisoft.yuejianli.entity.ContractListInfo;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.ProcessListBean;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ApplyHTSPModel;
import com.kisoft.yuejianli.model.MonthlySupModel;
import com.kisoft.yuejianli.presenter.ApplyHTSPPresenter;
import com.kisoft.yuejianli.presenter.MonthlySupPresenter;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;

public class ApplyHTSPFragment extends BaseFragment<ApplyHTSPModel, ApplyHTSPPresenter> implements ApplyHTSPContract.ApplyHTSPViewContract {

    @BindView(R.id.tv_ssfs)
    TextView tvSsfs;
    @BindView(R.id.tv_htlb)
    TextView tvHtlb;
    @BindView(R.id.tv_ywxz)
    TextView tvYwxz;
    @BindView(R.id.tv_hqfs)
    TextView tvHqfs;
    @BindView(R.id.tv_zjly)
    TextView tvZjly;
    @BindView(R.id.tv_htbh)
    TextView tvHtbh;
    @BindView(R.id.tv_htmc)
    TextView tvHtmc;
    @BindView(R.id.tv_jsdw)
    TextView tvJsdw;
    @BindView(R.id.tv_ssxm)
    TextView tvSsxm;
    @BindView(R.id.tv_ztze)
    TextView tvZtze;
    @BindView(R.id.tv_htje)
    TextView tvHtje;
    @BindView(R.id.tv_gcmj)
    TextView tvGcmj;
    @BindView(R.id.tv_qdrq)
    TextView tvQdrq;
    @BindView(R.id.tv_qddd)
    TextView tvQddd;
    @BindView(R.id.tv_lxdh)
    TextView tvLxdh;
    @BindView(R.id.tv_htzbj)
    TextView tvHtzbj;
    @BindView(R.id.tv_zbjdqrq)
    TextView tvZbjdqrq;
    @BindView(R.id.tv_htlyj)
    TextView tvHtlyj;
    @BindView(R.id.tv_lyjdqrq)
    TextView tvLyjdqrq;
    @BindView(R.id.tv_htjhksrq)
    TextView tvHtjhksrq;
    @BindView(R.id.tv_htjhjsrq)
    TextView tvHtjhjsrq;
    @BindView(R.id.tv_htfcrq)
    TextView tvHtfcrq;
    @BindView(R.id.tv_hthlrq)
    TextView tvHthlrq;
    @BindView(R.id.tv_bazj)
    TextView tvBazj;
    @BindView(R.id.tv_xmzj)
    TextView tvXmzj;
    @BindView(R.id.tv_xmfzr)
    TextView tvXmfzr;
    @BindView(R.id.tv_createName)
    TextView tvCreateName;
    @BindView(R.id.tv_createtime)
    TextView tvCreatetime;
    @BindView(R.id.tv_htfkfs)
    TextView tvHtfkfs;
    @BindView(R.id.tv_remarks)
    TextView tvRemarks;
    @BindView(R.id.tv_zfcs)
    TextView tvZfcs;
    @BindView(R.id.tv_zfsj)
    TextView tvZfsj;
    @BindView(R.id.tv_zfbl)
    TextView tvZfbl;
    @BindView(R.id.tv_zfje)
    TextView tvZfje;
    @BindView(R.id.tv_explain)
    TextView tvExplain;
    @BindView(R.id.tv_sub)
    TextView tvSub;
    @BindView(R.id.tv_htgq)
    TextView tvHtgq;
    Unbinder unbinder;
@BindView(R.id.rv_list)
RecyclerView rvContent;

    private ApplyHTSPModel model;
    private ApplyHTSPPresenter presenter;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    ProcessListBean bean;
    private EnclosureListAdapter adapter;
    private List<EnclosureListDto> dtoList = new ArrayList<>();

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View rootView = super.onCreateView(inflater, container, savedInstanceState);
        model = new ApplyHTSPModel(mContext);
        presenter = new ApplyHTSPPresenter(this, model);
        unbinder = ButterKnife.bind(this, rootView);
        initData();
        initView();
        return rootView;
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        bean = ((ApplyActivity) getActivity()).bean;
        presenter.getConInfo(bean.getBusinessId(), bean.getFlowTaskId(), bean.getWfType(), bean.getFlowTaskState(), bean.getBusinessId(), userInfo.getId());
    }

    private void initView() {
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            layoutManager.setSmoothScrollbarEnabled(false);
            rvContent.setLayoutManager(layoutManager);
            rvContent.setNestedScrollingEnabled(false);
        }
        adapter = new EnclosureListAdapter(dtoList);
        rvContent.setAdapter(adapter);
        rvContent.addOnItemTouchListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (!StringUtil.isEmpty(dtoList.get(position).getFilePath())) {
                    Intent intent = new Intent();
                    intent.setAction(Intent.ACTION_VIEW);
                    intent.setData(Uri.parse(StringUtil.handlerUrl(dtoList.get(position).getFilePath() + "&filename=" + dtoList.get(position).getFileName())));
                    mContext.startActivity(intent);
                } else {
                    showToast("附件地址为空");
                }
            }
        });


        if(bean!=null&&!StringUtil.isEmpty(bean.getBusinessId())){
            presenter.getEnclosureList(bean.getBusinessId());
        }else{
            showToast("BusinessId为空");
        }


    }

    @Override
    public int getRootView() {
        return R.layout.activity_apply_htspfragment;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    @OnClick(R.id.tv_sub)
    public void onViewClicked() {

    }

    @Override
    public void infobackConInfo(ConBeanInfo info) {
        if (info != null) {
            tvSsfs.setText(info.getConClassName());
            tvHtlb.setText(info.getConTypeName());
            tvYwxz.setText(info.getBusiNatureName());
            tvHqfs.setText(info.getAcquiWayName());
            tvZjly.setText(info.getSourceFondsName());

            tvHtbh.setText(info.getConNo());
            tvHtmc.setText(info.getConName());
            //建设单位
            tvJsdw.setText(info.getOwnName());
            tvSsxm.setText(info.getProjectName());
            tvZtze.setText(info.getPjTotalInv());
            tvHtje.setText(info.getConAmount());
            tvHtgq.setText(info.getConPeriod());
            tvGcmj.setText(info.getProjectArea());
            tvQdrq.setText(info.getConDate());
            tvQddd.setText(info.getConAddress());
            tvLxdh.setText(info.getLinkTel());
            tvHtzbj.setText(info.getConBond());
            tvZbjdqrq.setText(info.getBondWarrDate());
            tvHtlyj.setText(info.getConPerfFee());
            tvLyjdqrq.setText(info.getPerFeeWarrDate());
            tvHtjhksrq.setText(info.getPlanStartDate());
            tvHtjhjsrq.setText(info.getPlanEndDate());
            tvHtfcrq.setText(info.getIssueDate());
            tvHthlrq.setText(info.getRefulxDate());
            tvBazj.setText(info.getBidDirName());
            tvXmzj.setText(info.getChargeDirName());
            tvXmfzr.setText(info.getChargeEngName());
            tvCreateName.setText(info.getCreateName());
            tvCreatetime.setText(info.getCreateTime());
            tvHtfkfs.setText(info.getPayRemark());
            tvRemarks.setText(info.getRemark());

            ((ApplyActivity) getActivity()).bean.setFlowId(info.getWfId());
        }

    }

    @Override
    public void EnclosureListBack(List<EnclosureListDto> dtoList) {
        this.dtoList.clear();
        if (dtoList != null) {
            this.dtoList.addAll(dtoList);
        }
        adapter.notifyDataSetChanged();
    }
}
