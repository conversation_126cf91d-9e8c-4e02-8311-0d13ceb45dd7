package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.SubContractInfoListAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.SubContractListDataInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class SubcontractListActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.p_list)
    PullToRefreshLayout pList;
    @BindView(R.id.r_list)
    RecyclerView rList;
    SubContractInfoListAdapter adapter;

    private int page = 1;           // 第几页
    private int count = 0;          // 总条数

//    List<ContractListInfo.ContractBean> contractInfos = new ArrayList<>();
    List<SubContractListDataInfo> contractInfos = new ArrayList<>();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_contract_info_list;
    }

    private void initView() {
        tvTitle.setText("合同选择列表");
        pList.setRefreshListener(new BaseRefreshListener() {
            @Override
            public void refresh() {
                pList.setCanLoadMore(false);
                page = 1;
                contractInfos.clear();
                requestData();
            }

            @Override
            public void loadMore() {
                if (contractInfos.size() >= count){
                    pList.finishLoadMore();
                    pList.setCanLoadMore(false);
                    showToast("已经到底了");
                    return;
                }
                page++;
                requestData();
            }
        });

        if(rList.getLayoutManager() == null){
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rList.setLayoutManager(manager);
        }
        adapter = new SubContractInfoListAdapter(R.layout.item_contract_info_list, contractInfos);
        rList.setAdapter(adapter);
        rList.addOnItemTouchListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int position) {
                Intent intent = new Intent();
                intent.putExtra("data", contractInfos.get(position));
                setResult(Activity.RESULT_OK, intent);
                finish();
            }
        });

        pList.setCanRefresh(true);
        pList.autoRefresh();
    }

    @OnClick(R.id.iv_back)
    public void onClick() {
        finish();
    }

    private void requestData() {
        Api.getGbkApiserver().getSubContractListAPP(Constant.HTTP_GET_SUBCONTRACT_INFO_LIST, new HashMap<String, Object>()).enqueue(new Callback<NetworkResponse<List<SubContractListDataInfo>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<SubContractListDataInfo>>> call,
                                   Response<NetworkResponse<List<SubContractListDataInfo>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                   List<SubContractListDataInfo> info = response.body().getData();
                    if (info != null) {
                        contractInfos.addAll(info);
                        adapter.notifyDataSetChanged();
                    }
                }
                pList.finishRefresh();
            }

            @Override
            public void onFailure(Call<NetworkResponse<List<SubContractListDataInfo>>> call, Throwable t) {
                pList.finishRefresh();
                t.printStackTrace();
            }
        });


//        Api.getGbkApiserver().getSubContractListAPP(Constant.HTTP_GET_SUBCONTRACT_INFO_LIST, new HashMap<String, Object>()).enqueue(new Callback<NetworkResponse<Object>>() {
//            @Override
//            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
//                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
//                    String s = GsonUtil.GsonString(response.body());
//                    Log.i("TAG", "onResponse: s = " + s);
//
//                }
//                pList.finishRefresh();
//            }
//
//            @Override
//            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
//                pList.finishRefresh();
//                t.printStackTrace();
//            }
//        });


//        Api.getGbkApiserver().getContractInfoList(Constant.HTTP_GET_SUBCONTRACT_INFO_LIST, map).enqueue(new Callback<NetworkResponse<ContractListInfo>>() {
//            @Override
//            public void onResponse(Call<NetworkResponse<ContractListInfo>> call, Response<NetworkResponse<ContractListInfo>> response) {
//                pList.finishLoadMore();
//                pList.finishRefresh();
//                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
//                    ContractListInfo info = response.body().getData();
//                    if (info != null) {
//                        if (count == 0)
//                            count = info.getCount();
//                        contractInfos.addAll(info.getList());
//                        adapter.notifyDataSetChanged();
//                    }
//                }
//            }
//
//            @Override
//            public void onFailure(Call<NetworkResponse<ContractListInfo>> call, Throwable t) {
//                pList.finishLoadMore();
//                pList.finishRefresh();
//                t.printStackTrace();
//            }
//        });
    }

}


//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_subcontract_list);
//    }
//}