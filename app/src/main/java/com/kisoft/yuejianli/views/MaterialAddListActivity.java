package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.adpter.MaterialAddAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.MaterialAddListContract;
import com.kisoft.yuejianli.entity.MaterialCheckInfo;
import com.kisoft.yuejianli.entity.MaterialInspect;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.MaterialAddListModel;
import com.kisoft.yuejianli.presenter.MaterialAddListPresenter;
import com.kisoft.yuejianli.utils.DateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/5/25.
 */

public class MaterialAddListActivity extends BaseActivity<MaterialAddListModel, MaterialAddListPresenter> implements MaterialAddListContract.MaterialAddListViewContract {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.iv_action)
    ImageView ivAction;

    private MaterialAddAdapter mAdapter;
    private List<MaterialInspect> inspects = new ArrayList<>();

    private View empotyView;

    private int count = 0;
    private int page = 1;
    private int pageSize = 20;
    private String month;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private MaterialAddListModel model;
    private MaterialAddListPresenter presenter;
    private boolean mXmkn = false;
    private String selectType;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new MaterialAddListModel(this);
        presenter = new MaterialAddListPresenter(this,model);
        initMVP(model,presenter);
        Intent it = getIntent();
        if (it != null){
            mXmkn = it.getBooleanExtra("xmkb", false);
            selectType = it.getStringExtra("selectType");
        }
        initView();
        initData();
    }

    private void initView(){
        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        tvTitle.setText("材料、设备进场");
        ivAction.setVisibility(View.GONE);
        if (mXmkn){
            tvSubmit.setVisibility(View.GONE);
        }else {
            tvSubmit.setVisibility(View.VISIBLE);
        }
        tvSubmit.setText("新增");
        if(rvContent.getLayoutManager() == null){
            LinearLayoutManager layoutManager = new LinearLayoutManager(this);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(layoutManager);
        }
        mAdapter = new MaterialAddAdapter(R.layout.item_material_add , inspects);
        mAdapter.setEmptyView(empotyView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                seeDetail(inspects.get(position));
            }
        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                rvContent.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (inspects.size() >= count) {
                            mAdapter.loadMoreEnd();
                        } else {
                            getData();
                        }
                    }
                }, Constant.LOAD_MORE_DELAY);
            }
        }, rvContent);
        rvContent.setAdapter(mAdapter);
    }

    String c;
    String s;
    String p;

    private void initData(){
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo =SettingManager.getInstance().getProject();
        if(projectInfo != null){
            getData();
        }
    }

    private void getData(){
        c = Integer.toString(count);
        s = Integer.toString(pageSize);
        p = Integer.toString(page);
        month = DateUtil.dateToString(new Date(), DateUtil.YM);
        if (mXmkn){
            mPresenter.getMaterialCheckList("", projectInfo.getProjectId(),month,c,s,p,selectType);
        }else {
            mPresenter.getMaterialCheckList(userInfo.getId(), projectInfo.getProjectId(),month,c,s,p,"");
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_material_check_list;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    @OnClick(R.id.tv_submit)
    public void addMore(){
        Intent intent = new Intent();
        intent.setClass(MaterialAddListActivity.this, MaterialQualityCheckActivity.class);
        intent.putExtra(Constant.INTENT_KEY_CAN_EDIT , true);
        startActivityForResult(intent, 1);
    }

    @Override
    public void showMaterialCheckList(MaterialCheckInfo info, int type) {
        if(info!= null){
            count = info.getCount();
            if(info.getList() != null){
                if(type == 0){
                    inspects.clear();
                    page++;
                    inspects.addAll(info.getList());
                }else {
                    page++;
                    inspects.addAll(info.getList());
                    mAdapter.loadMoreComplete();
                }
            }
            mAdapter.notifyDataSetChanged();
        }
    }

    private void seeDetail(MaterialInspect inspect){
        Intent intent = new Intent();
        intent.setClass(MaterialAddListActivity.this, MaterialQualityCheckActivity.class);
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constant.INTENT_KEY_MATERAIL_CHECK, inspect);
        intent.putExtras(bundle);
        startActivity(intent);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case 1:
                count = 0;
                page = 1;
                pageSize = 20;
                getData();
                break;
        }
    }
}
