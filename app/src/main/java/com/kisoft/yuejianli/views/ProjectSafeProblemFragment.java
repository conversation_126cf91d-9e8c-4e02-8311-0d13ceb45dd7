package com.kisoft.yuejianli.views;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.adpter.ProjectSafeProblemAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.contract.ProjectSafeProblemContract;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ProjectSafeProblemModel;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.R;

import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ProjectSafeInspection;
import com.kisoft.yuejianli.entity.ProjectSafeProblemInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.presenter.ProjectSafeProblemPresenter;

/**
 * Created by tudou on 2018/4/18.
 */

public class ProjectSafeProblemFragment extends BaseFragment<ProjectSafeProblemModel,ProjectSafeProblemPresenter> implements
        ProjectSafeProblemContract.ProjectSafeProblemViewContract {

    private RecyclerView rvQualityProblem;
    private List<ProjectSafeInspection> inspections = new ArrayList<>();
    private ProjectSafeProblemAdapter mAdapter;

    private int count = 0;
    private int page = 1;
    private int pageSize = 20;
    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private View empotyView;

    private ProjectSafeProblemModel model;
    private ProjectSafeProblemPresenter presenter;


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new ProjectSafeProblemModel(getContext());
        presenter = new ProjectSafeProblemPresenter(this, model);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        initView(inflater);
        initData();
        return mRootView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }


    private void initView(LayoutInflater inflater){
        empotyView = inflater.inflate(R.layout.page_no_data, null);
        mRootView = inflater.inflate(getRootView(), null);
        rvQualityProblem = mRootView.findViewById(R.id.rv_quality_problem);
        if(rvQualityProblem.getLayoutManager() == null){
            LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvQualityProblem.setLayoutManager(layoutManager);
        }
        mAdapter = new ProjectSafeProblemAdapter(R.layout.item_project_safe_problem,inspections);
        mAdapter.setEmptyView(empotyView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {

            }
        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                // todo
                rvQualityProblem.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (inspections.size() >= count) {
                            mAdapter.loadMoreEnd();
                        } else {
                            getData();
                        }
                    }
                }, Constant.LOAD_MORE_DELAY);
            }

        }, rvQualityProblem);
        rvQualityProblem.setAdapter(mAdapter);

    }


    private void initData(){
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        if(projectInfo != null){
            getData();
        }
    }

    private void getData(){
        String c = Integer.toString(count);
        String s = Integer.toString(pageSize);
        String p = Integer.toString(page);
        presenter.getProblems(userInfo.getId(), projectInfo.getProjectId(),null,ProjectSafeInspection.PDS_QUALITY_INSPECT_DZG
                ,c,s,p);
    }
    @Override
    public int getRootView() {
        return R.layout.fragment_project_quality_problem;
    }

    @Override
    public void showProblems(ProjectSafeProblemInfo info, int type) {
        if(info != null){
            count = info.getCount();
            if(info.getList() != null){
                if(type == 0){
                    page++;
                    this.inspections.clear();
                    this.inspections.addAll(info.getList());
                }else {
                    this.inspections.addAll(info.getList());
                    page++;
                    mAdapter.loadMoreComplete();
                }
            }
        }
    }



}
