package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ProjectListAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ProjectChoseContract;
import com.kisoft.yuejianli.entity.Communication;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.ProjectChoseModel;
import com.kisoft.yuejianli.presenter.ProjectChosePresenter;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * Created by tudou on 2018/6/6.
 */
public class ProjectChoseActivity extends BaseActivity<ProjectChoseModel, ProjectChosePresenter> implements ProjectChoseContract.ProjectChoseViewContract{

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.et_query_content)
    EditText etQuery;
    @BindView(R.id.rv_project_list)
    RecyclerView rvContent;

    private String queryStr = "";

    private boolean selectModel;

    private List<ProjectInfo> selectProject = new ArrayList<>();
    private List<ProjectInfo> projectInfos = new ArrayList<>();
    private ProjectInfo projectInfo;
    private ProjectListAdapter mAdapter;
    private View empotyView;

    private ProjectChoseModel model;
    private ProjectChosePresenter presenter;
    private String mAutoForm;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new ProjectChoseModel(this);
        presenter = new ProjectChosePresenter(this, model);
        selectModel = getIntent().getBooleanExtra("selectModel", false);
        mAutoForm = getIntent().getStringExtra("autoForm");
        initMVP(model, presenter);
        initView();
        initData();
    }

    private void initView() {
        tvTitle.setText("项目监管");
        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        etQuery.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (editable != null) {
                    queryStr = editable.toString().trim();
                    queryProject();
                }
            }
        });
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new ProjectListAdapter(R.layout.item_project_list, selectProject);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (selectModel) {
                    Intent intent = new Intent();
                    intent.putExtra("data", selectProject.get(position));
                    intent.putExtra("autoForm",mAutoForm);
                    setResult(Activity.RESULT_OK, intent);
                    finish();
                }else {
                    storeProject(selectProject.get(position));
                }
            }
        });
        mAdapter.setEmptyView(empotyView);
        rvContent.setAdapter(mAdapter);
    }

    private void initData() {
        projectInfos = SettingManager.getInstance().getAllProjects();
        queryProject();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_project_chose;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        setResult(Constant.REQUEST_CODE_CHANGE_PEOJECT);
        finish();
    }

    @OnClick(R.id.tv_query)
    public void queryProject() {
        selectProject.clear();
        for (ProjectInfo projectInfo : projectInfos) {
            if (isSelect(projectInfo.getProjectName())) {
                selectProject.add(projectInfo);
            }
        }
        mAdapter.notifyDataSetChanged();
    }

    private void storeProject(ProjectInfo projectInfo) {

        // 空照片 ，添加
        String[] str = new String[]{"确定", "取消"};
        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
        ab.setTitle("确认切换当前项目吗？");
        ab.setNegativeButton("取消", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // Toast.makeText(mContext, "你点击了取消按钮~", Toast.LENGTH_SHORT).show();
            }
        });
        ab.setPositiveButton("确定", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {

                ProjectChoseActivity.this.showProgress();
                new Thread(){
                    @Override
                    public void run() {
                        super.run();
                        ProjectChoseActivity.this.projectInfo = projectInfo;
                        getProjectOrgInfo(projectInfo.getProjectId());
                        SettingManager.getInstance().deleteproject();
                        SettingManager.getInstance().saveProject(projectInfo);
                    }
                }.start();

            }
        });
        ab.show();

//        this.projectInfo = projectInfo;
//        getProjectOrgInfo(projectInfo.getProjectId());
//        SettingManager.getInstance().deleteproject();
//        SettingManager.getInstance().saveProject(projectInfo);
    }


    private void getProjectOrgInfo(String projectId) {
        mPresenter.getProjectOrgInfo(projectId);
    }

    private boolean isSelect(String name) {
        if (queryStr.contains(name) || name.contains(queryStr)) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void projectsBack(List<Communication> communications) {
        SettingManager.getInstance().saveProjectOrgInfo(communications);

        presenter.modifyFavoriteProject(projectInfo.getProjectId(), SettingManager.getInstance().getUserInfo().getId());
    }

    @Override
    public void modifyBack(Boolean success) {
        if (success) {
            mPassword = SettingManager.getInstance().getUserInfo().getPassword();
            presenter.login();
        }
    }
    String mPassword;
    @Override
    public void loginBack(boolean ok, UserInfo userInfo) {
        if (ok) {
            if(StringUtil.isEmpty(userInfo.getScheduleWay())) {
                userInfo.setScheduleWay("1");
            }
            userInfo.setPassword(mPassword);
            SettingManager.getInstance().saveUserInfo(userInfo);
            presenter.getProjectInfo(userInfo.getProjectId());
        }
    }

    @Override
    public void projectBack(ProjectInfo projectInfo) {
        if (projectInfo != null) {
            SettingManager.getInstance().deleteproject();
            SettingManager.getInstance().saveProject(projectInfo);
        }
        goBack();
    }
}
