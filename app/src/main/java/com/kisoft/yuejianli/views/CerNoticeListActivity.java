package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.jwenfeng.library.pulltorefresh.BaseRefreshListener;
import com.jwenfeng.library.pulltorefresh.PullToRefreshLayout;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.interf.GlobalListener;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.utils.GsonUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class CerNoticeListActivity extends BaseActivity implements BaseRefreshListener{

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;
    @BindView(R.id.ptrl_content)
    PullToRefreshLayout ptrlContent;


    private int page = 1;
    private int pageSize = 10;
    private List<Map> mList = new ArrayList<>();
    private CerNoticeListAdapter mAdapter;

    @Override
    public int getLayoutId() {
        return R.layout.activity_cernotice_list;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ButterKnife.bind(this);

        initData();
        initView();
    }

    private void initData() {
        if (rvContent.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(this);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            layoutManager.setSmoothScrollbarEnabled(false);
            rvContent.setLayoutManager(layoutManager);
            rvContent.setNestedScrollingEnabled(false);
        }
        mAdapter = new CerNoticeListAdapter(mList, new GlobalListener<Map>() {
            @Override
            public void onViewClick(int id, int position, Map model) {
            }

            @Override
            public void onRootViewClick(View view, int position, Map model) {
//                pushViewController(model);
            }
        });

        rvContent.setAdapter(mAdapter);
        refresh();
    }

    private void pushViewController(Map model){
        Intent intent=new Intent(this,CerNoticeListDetailActivity.class);
        intent.putExtra("data", GsonUtil.GsonString(model));
        startActivity(intent);
    }

    private void initView() {
        tvTitle.setText("证件公告");
        ptrlContent.setRefreshListener(this);
    }

    @OnClick(R.id.iv_back)
    public void onClick() {
        finish();
    }

//    @Override
//    public void finshRefresh() {
//        ptrlContent.finishRefresh();
//        ptrlContent.finishLoadMore();
//    }
//
//    @Override
//    public void toRefersh() {
//        refresh();
//    }

    @Override
    public void refresh() {
        mList.clear();
        page = 1;
        getData();
    }

    @Override
    public void loadMore() {
        page++;
        getData();
    }

    private void getData() {

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("page", ""+page);
        parameters.put("limit", "10");
        Api.getGbkApiserver().getCertificateList("getCertificateNoticeList_APP", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                   Map resMap  = (Map) response.body().getData();
                    List<Map> list = (List<Map>) resMap.get("data");
                    if (list.size() < pageSize){
                        showToast("没有更多数据了");
                        ptrlContent.finishLoadMore();
                    }
                    mList.addAll(list);
                    mAdapter.notifyDataSetChanged();
                }
                ptrlContent.finishRefresh();
                ptrlContent.finishLoadMore();
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }


    @Override
    protected void onResume() {
        super.onResume();
        refresh();
    }


    public class CerNoticeListAdapter extends BaseQuickAdapter<Map, CerNoticeListAdapter.ViewHolder> {

        GlobalListener<Map> globalListener;
        public CerNoticeListAdapter(@Nullable List<Map> data, GlobalListener<Map> globalListener) {
            super(R.layout.item_message, data);
            this.globalListener = globalListener;
        }

        @Override
        protected void convert(@NonNull ViewHolder helper, Map item) {
            helper.tv_title.setText(item.get("title").toString());
            helper.tv_title.setMaxLines(1);
            helper.tv_time.setText(item.get("content").toString());
            helper.tv_time.setMaxLines(2);

            helper.content.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (globalListener != null) {
                        globalListener.onRootViewClick(v, helper.getAdapterPosition(), item);
                    }
                }
            });
        }


        public class ViewHolder extends BaseViewHolder {
            @BindView(R.id.tv_title)
            TextView tv_title;
            @BindView(R.id.tv_time)
            TextView tv_time;
            @BindView(R.id.content)
            View content;

            ViewHolder(View view) {
                super(view);
                ButterKnife.bind(this, view);
            }
        }
    }

}



