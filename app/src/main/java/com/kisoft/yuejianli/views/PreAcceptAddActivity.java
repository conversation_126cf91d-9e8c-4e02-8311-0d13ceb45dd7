package com.kisoft.yuejianli.views;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ImageAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.PreAcceptanceContract;
import com.kisoft.yuejianli.data.ProjectTimeBean;
import com.kisoft.yuejianli.entity.PhotoUploadResulte;
import com.kisoft.yuejianli.entity.PreAcceptanceInfo;
import com.kisoft.yuejianli.entity.QualityInspection;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.PreAcceptanceModel;
import com.kisoft.yuejianli.presenter.PreAcceptancePresenter;
import com.kisoft.yuejianli.ui.ImageDialog;
import com.kisoft.yuejianli.ui.TipsQuestionView;
import com.kisoft.yuejianli.ui.UnitListActivity;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.PermissionsChecker;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.views.watermark.CallBackActivity;
import com.kisoft.yuejianli.views.watermark.ImageUtil;
import com.kisoft.yuejianli.views.watermark.PhotoCaptureActivity;
import com.kisoft.yuejianli.views.watermark.PhotoListener;
import com.kisoft.yuejianli.views.watermark.WaterMask;
import com.kisoft.yuejianli.views.watermark.WaterMaskHelper;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import butterknife.BindView;
import butterknife.OnClick;

import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;

public class PreAcceptAddActivity extends BaseActivity<PreAcceptanceModel,
        PreAcceptancePresenter> implements PreAcceptanceContract.PreAcceptanceViewContract, WaterMask.WaterMaskListener, PhotoListener {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_consunit)
    TextView tvConsunit;
    @BindView(R.id.tv_directortime)
    TextView tvDirectortime;
    @BindView(R.id.tv_supervisortime)
    TextView tvSupervisortime;
    @BindView(R.id.tv_checkuptime)
    TextView tvCheckuptime;
    @BindView(R.id.tv_supervisor)
    TextView tvSupervisor;
    @BindView(R.id.rv_picturepath)
    RecyclerView rvPicturepath;

    @BindView(R.id.tv_projectname)
    TextView tvProjectname;

//    @BindView(R.id.et_engineeringname)
//    TextView etEngineeringname;
    @BindView(R.id.tips_view)
    TipsQuestionView mQuestionView;// 检查部位

    @BindView(R.id.et_acceptanceid)
    EditText etAcceptanceid;
    @BindView(R.id.et_projectmanage)
    EditText etProjectmanage;
   @BindView(R.id.et_prijectdirector)
   EditText etPrijectdirector;
   @BindView(R.id.et_acceptidea)
   EditText etAcceptidea;
   @BindView(R.id.et_remarks)
   EditText etRemarks;
    @BindView(R.id.tv_auditstate)
    TextView tvAuditstate;

    @BindView(R.id.sp_auditstate)
    Spinner spAuditstate;

    @BindView(R.id.tv_sub)
    TextView tvSub;

    boolean modify; // 是否可以修改

    private QualityInspection inspection;
    private ImageAdapter imageAdapter;
    private ImageDialog imageDialog;
    private List<String> images = new ArrayList<>();

    private PermissionsChecker permissionsChecker;
    static final String[] PERMISSIONS = new String[]{
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA
    };
    private PreAcceptanceModel model;
    private PreAcceptancePresenter presenter;
    PreAcceptanceInfo preAcceptanceInfo=new PreAcceptanceInfo();
    private boolean isAdd = false;
    private UserInfo userInfo;

    private static final int REQUEST_CODE_CHOOSE_PHOTO = 1;
    private static final int REQUEST_CODE_TAKE_PHOTO = 2;
    private static final int REQUEST_CODE_CROP = 3;
    private static final int REQUEST_CONSTRUCTIONUNITLIST_ACTIVITY = 4;

    private View lastView;
    private int maskLocation;
    private ArrayList<String> uris;
    WaterMaskHelper waterMaskHelper = new WaterMaskHelper(this);
    private boolean isTakePhoto;
    AMapLocation locationdata = null;
    String address="";
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new PreAcceptanceModel(this);
        presenter = new PreAcceptancePresenter(this, model);
        initMVP(model, presenter);
        getOpenInfo();
        initWater();
        initData();
        initView();
    }
    private void getOpenInfo() {
        userInfo = SettingManager.getInstance().getUserInfo();
        preAcceptanceInfo = (PreAcceptanceInfo) getIntent().getSerializableExtra(Constant.INTENT_KEY_PREACCEPTANCE_INFO);
        if (preAcceptanceInfo == null) {
            preAcceptanceInfo = new PreAcceptanceInfo();
            preAcceptanceInfo.setCreateId(userInfo.getId());
            preAcceptanceInfo.setCreateName(userInfo.getName());
            //preAcceptanceInfo.setCreateTime(DateUtil.getTime(DateUtil.YMD_HMS));
            isAdd = true;
        }
    }

    BGAPhotoHelper1 mPhotoHelper1=new BGAPhotoHelper1();
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode==REQUEST_CODE_CHOOSE_PHOTO){
            if (resultCode == Activity.RESULT_OK) {
                try {
                    double fileSize = FileUtil.getFileOrFilesSize(mPhotoHelper.getFilePathFromUri(data.getData()), 3);
                    Log.i("fileSize", fileSize + "");
                    if (fileSize > 1) {
                        startActivityForResult(mPhotoHelper1.getCropIntent(ImageUtil.compressImage(mPhotoHelper.getFilePathFromUri(data.getData())), 800, 800), REQUEST_CODE_CROP);
                    } else {
                        startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), REQUEST_CODE_CROP);
                    }
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        }

        if(requestCode==REQUEST_CODE_TAKE_PHOTO){
            if (resultCode == Activity.RESULT_OK) {
                try {

                    double fileSize = FileUtil.getFileOrFilesSize(mPhotoHelper.getCameraFilePath(), 3);
                    Log.i("fileSize", fileSize + "");
                    if (fileSize > 1) {
                        startActivityForResult(mPhotoHelper1.getCropIntent(ImageUtil.compressImage(mPhotoHelper.getCameraFilePath()), 800, 800), REQUEST_CODE_CROP);
                    } else {
                        startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800, 800), REQUEST_CODE_CROP);
                    }
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        }

        if(requestCode==REQUEST_CODE_CROP){
            if (resultCode == Activity.RESULT_OK) {
                if (isTakePhoto) {
                    //重点在这里
                    // getCropIntent  获取裁剪完图片的路径
                    File file = new File(mPhotoHelper1.getCropFilePath());
                    //File file = new File(ImageUtil.compressImage(mPhotoHelper1.getCropFilePath()));
                    //应该在这里把裁剪完的图片路径传过去
                    ArrayList<String> strings = new ArrayList<>();
                    strings.add(file.toString());
                    if (CallBackActivity.getPhotoListener() != null){
                        //选择照片的uri，默认为下标1的元素
                        CallBackActivity.getPhotoListener().onChoose(strings);
                    }
                    if (CallBackActivity.getWaterMarkListener() != null) {
                        WaterMask.WaterMaskParam maskParam = CallBackActivity.getWaterMarkListener().onDraw();
                        CallBackActivity.getWaterMarkListener().onDraw();
                        //图片按比例大小压缩
                        Bitmap bitmap = ImageUtil.getBitmap(String.valueOf((file)));

                        WaterMask.draw(this, bitmap, String.valueOf((file)), maskParam);
                        sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(file)));
                    }
                    presenter.uploadPhotoImage(file.getPath());
                    Log.i("tag",file.getPath());
                }else{
                    //presenter.uploadPhotoImage(ImageUtil.compressImage(mPhotoHelper1.getCropFilePath()));
                    presenter.uploadPhotoImage(mPhotoHelper1.getCropFilePath());
                }
            } else {
                mPhotoHelper.deleteCameraFile();
                mPhotoHelper.deleteCropFile();
            }
        }

        if (requestCode == REQUEST_CONSTRUCTIONUNITLIST_ACTIVITY) {
            if (resultCode == Activity.RESULT_OK) {
                tvConsunit.setText(data.getStringExtra("data"));
            }
        }

        if (requestCode == 210) {
            if(resultCode==Constant.REQEST_CODE_SELECT_COMPANY_ORG_INFO){
                if (data!= null){
                    List<String> ids = new ArrayList<>();
                    List<String> names = new ArrayList<>();
                    ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                    names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                    if (names != null  && names.size() >0   ){
                        preAcceptanceInfo.setSupervisionEngineerId(ids.get(0));
                        preAcceptanceInfo.setSupervisionEngineer(names.get(0));
                        tvSupervisor.setText(names.get(0));
                    }
                }
            }
        }

        if (requestCode == Constant.REQUEST_CODE_UNIT_DANWEI) {
            if (resultCode == Activity.RESULT_OK) {
                mQuestionView.getQuestEdit().setText(data.getStringExtra("data"));
                mQuestionView.setDocContent(data.getStringExtra("data1"));
                preAcceptanceInfo.setUnitProjectId(data.getStringExtra("ids"));
            }
        }
    }

    private void initData() {
        /*Bundle bundle = getIntent().getExtras();    //得到传过来的bundle
        bundle.getSerializable(Constant.INTENT_KEY_PREACCEPTANCE_INFO);*/

        if(isAdd){
            permissionsChecker = new PermissionsChecker(this);
            // 初始添加现场照片
            images.add("");
            //images.add("");
            //inspection.setQiEnclosure(StringUtil.imageArryToString(images));
        }
    }

    private BGAPhotoHelper mPhotoHelper;
    private void initView() {
        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);
        //判断照片是否为空
        if (rvPicturepath.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.HORIZONTAL);
            rvPicturepath.setLayoutManager(manager);
        }
        imageAdapter = new ImageAdapter(R.layout.item_iamge_show, images);
        imageAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (StringUtil.isEmpty(images.get(position))) {
                    if (permissionsChecker.lacksPermissions(PERMISSIONS)) {
                        getPermissions(Constant.REQUEST_CODE_TAKE_POHO);
                    } else {
                        // todo 空照片 ，添加
                        String str[] = new String[]{"系统相机", "手机相册"};
                        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
                        ab.setItems(str, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                switch (which) {
                                    case 0://相机
                                        isTakePhoto = true;
                                        takePhoto();
                                        break;
                                    case 1://相册
                                        isTakePhoto = false;
                                        getPhoto();
                                        break;
                                }
                            }
                        });
                        ab.show();
                    }
                } else {
                    showImage(images.get(position));
                }
            }
        });

        imageAdapter.setApply(isAdd);
        imageAdapter.setDeleteBtnClickListener(new ImageAdapter.DeleteBtnClickListener() {
            @Override
            public void deletePicture(String item) {
                if (!StringUtil.isEmpty(item)){
                    images.remove(item);
                    imageAdapter.notifyDataSetChanged();
                }
            }
        });

        rvPicturepath.setAdapter(imageAdapter);
        imageDialog = new ImageDialog();

        if(isAdd){//增加
            tvTitle.setText("新增预验收");
            tvProjectname.setText(SettingManager.getInstance().getProject().getProjectName());
            // 审核状态 spinner点击事件
            //data.setProbLevel("0");
            spAuditstate.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> adapterView, View view, int position, long id) {
                    if(position==0){
                        tvAuditstate.setText("同意");
                    }else if(position==1){
                        tvAuditstate.setText("不同意");
                    }else{
                        tvAuditstate.setText("同意");
                    }
                }
                @Override
                public void onNothingSelected(AdapterView<?> adapterView) {

                }
            });

            // 选择单位工程
            mQuestionView.getTitle().setText("单位工程");
            mQuestionView.getSelectView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    Intent intent = new Intent(mContext, UnitListActivity.class);
                    intent.putExtra("mType",1);//分项工程3
                    intent.putExtra("mEndType",3);//分项工程3
                    startActivityForResult(intent,Constant.REQUEST_CODE_UNIT_DANWEI);
                }
            });
            mQuestionView.getDocBtn().setVisibility(View.VISIBLE);

        }else{//详情
            tvTitle.setText("预验收详情");

            tvProjectname.setText(preAcceptanceInfo.getProjectName());
//            etEngineeringname.setText(preAcceptanceInfo.getEngineeringName());
//            etEngineeringname.setEnabled(false);
//            etEngineeringname.setHint("");
            // 选择单位工程
            mQuestionView.getTitle().setText("单位工程");
            mQuestionView.getQuestEdit().setText(preAcceptanceInfo.getEngineeringName());
            mQuestionView.getDocBtn().setVisibility(View.GONE);
            mQuestionView.setStatus(false);

            etAcceptanceid.setText(preAcceptanceInfo.getCompleteNumber());
            etAcceptanceid.setEnabled(false);
            etAcceptanceid.setHint("");
            etProjectmanage.setText(preAcceptanceInfo.getSupervisoryOrganiza());
            etProjectmanage.setEnabled(false);
            etProjectmanage.setHint("");
            tvConsunit.setText(preAcceptanceInfo.getConstructUnit());
            tvConsunit.setEnabled(false);

            etPrijectdirector.setText(preAcceptanceInfo.getProjectManager());
            etPrijectdirector.setEnabled(false);
            etPrijectdirector.setHint("");

            tvDirectortime.setText(preAcceptanceInfo.getManageData());
            tvDirectortime.setEnabled(false);
            tvSupervisor.setText(preAcceptanceInfo.getSupervisionEngineer());
            tvSupervisor.setEnabled(false);
            tvSupervisortime.setText(preAcceptanceInfo.getSupervisionData());
            tvSupervisortime.setEnabled(false);
            etAcceptidea.setText(preAcceptanceInfo.getAcceptanceOpinion());
            etAcceptidea.setEnabled(false);
            etAcceptidea.setHint("");
            tvCheckuptime.setText(preAcceptanceInfo.getCompleteData());
            tvCheckuptime.setEnabled(false);
            tvAuditstate.setText(preAcceptanceInfo.getAuditStatus());
            tvAuditstate.setVisibility(View.VISIBLE);
            spAuditstate.setVisibility(View.INVISIBLE);
            etRemarks.setText(preAcceptanceInfo.getRemarks());
            etRemarks.setEnabled(false);
            etRemarks.setHint("");
            tvSub.setVisibility(View.INVISIBLE);

            images.clear();
            String[] imgArr = preAcceptanceInfo.getCaEnclosure().split(",");

            for (String url : imgArr) {
                if (!StringUtil.isEmpty(url)||!url.equals(""))
                    images.add(url);
            }
            imageAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 通过url，展示照片
     *
     * @param url
     */
    private void showImage(String url) {
        if (imageDialog != null) {
            imageDialog.showImageDialog(getFragmentManager(), url);
        }
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), REQUEST_CODE_TAKE_PHOTO);
            PhotoCaptureActivity.setWaterListener(waterMaskHelper.getWaterMarkListener());
            PhotoCaptureActivity.setPhotoListener(waterMaskHelper.getPhotoListener());
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    /**
     * 6.0之上权限
     */
    private void getPermissions(int code) {
        ActivityCompat.requestPermissions(this, PERMISSIONS, code);
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_pre_accept_add;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    //选择相应公司负责人(总监理工程师)
    @OnClick(R.id.tv_supervisor)
    public void getPerson() {
        Intent intent = new Intent();
        intent.putExtra("isSingle", true);
        intent.setClass(this, CompanyOrgInfoActivity.class);
        startActivityForResult(intent, 210);
    }

    @OnClick(R.id.tv_sub)
    public void sub(){
        Log.d("新增预验收","haha");
        String completeId="";       //主键
        String projectName=SettingManager.getInstance().getProject().getProjectName();      //项目名称
        String projectId=SettingManager.getInstance().getProject().getProjectId();         //项目Id
        String createId=SettingManager.getInstance().getUserInfo().getId();          //创建人Id
        String createName=SettingManager.getInstance().getUserInfo().getName();       //创建人姓名
        String completeNumber=etAcceptanceid.getText().toString();   //竣工验收编号
        String auditStatus=tvAuditstate.getText().toString();        //审核状态
        String completeData=tvCheckuptime.getText().toString();      //竣工验收日期
        String supervisoryOrganiza=etProjectmanage.getText().toString(); //项目监理机构
        String constructUnit=tvConsunit.getText().toString();     //施工单位
        String projectManager=etPrijectdirector.getText().toString();   //项目经理
        String manageData=tvDirectortime.getText().toString();      //项目经理签字日期
        String acceptanceOpinion=etAcceptidea.getText().toString(); //验收意见
        String supervisionData=tvSupervisortime.getText().toString();//总监理工程师签字日期
        String remarks=etRemarks.getText().toString();           //备注
        //String engineeringName=etEngineeringname.getText().toString();   //单位工程名称
        String engineeringName=mQuestionView.getQuestEdit().getText().toString();   //单位工程名称

        String imgUrls = "";
        for (String url : images) {
            imgUrls += url + ",";
        }
        if (!StringUtil.isEmpty(imgUrls)) {
            imgUrls = imgUrls.substring(0, imgUrls.length() - 1);
        }

        preAcceptanceInfo.setCompleteId("");
        preAcceptanceInfo.setProjectName(projectName);
        preAcceptanceInfo.setProjectId(projectId);
        ProjectTimeBean projectTime=new ProjectTimeBean();
        preAcceptanceInfo.setProjectTime(projectTime);
        /*SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
        Date date = new Date();
        String format = dateFormat.format(date);
        preAcceptanceInfo.setProjectTime(format);//创建时间*/
        preAcceptanceInfo.setCreateId(createId);
        preAcceptanceInfo.setCreateName(createName);
        preAcceptanceInfo.setCompleteNumber(completeNumber);
        preAcceptanceInfo.setAuditStatus(auditStatus);
        preAcceptanceInfo.setCompleteData(completeData);
        preAcceptanceInfo.setSupervisoryOrganiza(supervisoryOrganiza);
        preAcceptanceInfo.setConstructUnit(constructUnit);
        preAcceptanceInfo.setProjectManager(projectManager);
        preAcceptanceInfo.setManageData(manageData);
        preAcceptanceInfo.setAcceptanceOpinion(acceptanceOpinion);

        preAcceptanceInfo.setSupervisionData(supervisionData);
        preAcceptanceInfo.setRemarks(remarks);
        preAcceptanceInfo.setEngineeringName(engineeringName);
        preAcceptanceInfo.setCaEnclosure(imgUrls);

        mPresenter.addPreAcceptance(preAcceptanceInfo,createId,projectId);

    }

    //获取施工单位
    @OnClick(R.id.tv_consunit)
    public void consunit() {
        Intent intent = new Intent();
        intent.setClass(this, ConstructionUnitListActivity.class);
        /*Bundle bundle = new Bundle();
        bundle.putSerializable(Constant.INTENT_KEY_TENDER_INFO , preAcceptInfo);
        intent.putExtras(bundle);*/
        startActivityForResult(intent,REQUEST_CONSTRUCTIONUNITLIST_ACTIVITY);
    }

    //选择签字日期
    @OnClick({ R.id.tv_directortime, R.id.tv_supervisortime,R.id.tv_checkuptime,R.id.tv_sub})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tv_directortime:
                showDatePickerDialog(tvDirectortime);
                break;
            case R.id.tv_supervisortime:
                showDatePickerDialog(tvSupervisortime);
                break;
            case R.id.tv_checkuptime:
                showDatePickerDialog(tvCheckuptime);
                break;
            /*case R.id.tv_sub:
                String beginTime=tvLeaveTimeBegin.getText().toString().trim();
                String endTime=tvLeaveTimeEnd.getText().toString().trim();
                String title = etLeaveTitle.getText().toString().trim();
                String reason = etLeaveReason.getText().toString().trim();
                if (StringUtil.isEmpty(title)) {
                    showToast("标题不能为空");
                    return;
                }
                if (beginTime.equals("请选择开始时间")||beginTime.equals("")){
                    showToast("请选择请假起始时间");
                    return;
                }
                if (endTime.equals("请选择截止时间")||endTime.equals("")){
                    showToast("请选择请假结束时间");
                    return;
                }
                if (StringUtil.isEmpty(reason)) {
                    showToast("请假原因不能为空");
                    return;
                }
                applyLeaveInfo.setLeavetitle(title);
                applyLeaveInfo.setCause(reason);
                applyLeaveInfo.setStart(beginTime);
                applyLeaveInfo.setEnd(endTime);
                presenter.submitApplyLeave(applyLeaveInfo);
                break;*/

        }
    }
    /**
     * 展示日期选择对话框
     */
    private void showDatePickerDialog(final TextView tv) {
        Calendar c = Calendar.getInstance();
        new DatePickerDialog(mContext, new DatePickerDialog.OnDateSetListener() {

            @Override
            public void onDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                // TODO Auto-generated method stub
                int m = monthOfYear + 1;
                String month = m >= 10 ? m + "" : "0" + m;
                String day = dayOfMonth >= 10 ? dayOfMonth + "" : "0" + dayOfMonth;
                String dateStr = year + "-" + month + "-" + day;
                tv.setText(dateStr);
                /*switch (view.getId()) {
                    case R.id.tv_directortime:
                        preAcceptInfo.setDirectorTime(dateStr);
                        break;
                    case R.id.tv_supervisortime:
                        tvSupervisortime.setText(dateStr);
                        preAcceptInfo.setSupervisorTime(dateStr);
                        break;
                    case R.id.tv_checkuptime:
                        tvCheckuptime.setText(dateStr);
                        preAcceptInfo.setCheckupTime(dateStr);
                        break;
                }*/
            }
        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)).show();
    }

    @Override
    public void showAddResult(boolean isok) {
        if(isok){
            showToast("提交成功！");
            backLastPage();
        }else {
            showToast("系统繁忙！");
        }
    }
    private void backLastPage(){
        // todo 成功，返回上个页面
        finish();
    }

    private void initImage(String url) {
        Log.i("000", "______图片地址：" + url);
        images.add(0, url);
        imageAdapter.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(PhotoUploadResulte resulte) {
        dismissProgress();
        showToast(resulte.getMessge());
        if (resulte.isIsok()) {
            // todo 图片上传成功
            String imageUri = SettingManager.getInstance().getBaseUrl() + resulte.getImageUrl();
            initImage(imageUri);
        } else {
            initImage(resulte.getImageUrl());
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == Constant.REQUEST_CODE_TAKE_POHO) {
            takePhoto();
        }
    }

    //初始化水印
    private void initWater() {
        //binding.setPresenter(new Presenter());
        //初始化水印工具
        waterMaskHelper = new WaterMaskHelper(this, this, this);
        //lastView = binding.txtCenter;
        //lastView.setSelected(true);
        maskLocation = WaterMask.DefWaterMaskParam.Location.left_top;
    }

    @Override                       //添加水印：
    public WaterMask.WaterMaskParam onDraw() {
        //拍照后调用，设置水印的基本参数
        WaterMask.WaterMaskParam param = new WaterMask.WaterMaskParam();
        //创建当前时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        Date date = new Date();
        String format = dateFormat.format(date);
        param.txt.add(format+" "+address);
        param.location = 3;
        param.itemCount = 60;
        return param;
    }

    @Override
    public void onChoose(ArrayList<String> photos) {
        uris = photos;
        //Glide.with(CompleteCheckAddActivity.this).load(photos.get(0)).placeholder(R.mipmap.ic_launcher).centerCrop().error(R.mipmap.ic_launcher).crossFade().into(ivSfenclosure);
    }
    private AMapLocationClient locationClient = null;
    private AMapLocationClientOption locationOption = null;
    private void initLocation(){
        //初始化client
        locationClient = new AMapLocationClient(this.getApplicationContext());
        locationOption = getDefaultOption();
        //设置定位参数
        locationClient.setLocationOption(locationOption);
        // 设置定位监听
        locationClient.setLocationListener(locationListener);

    }
    /**
     * 默认的定位参数
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private AMapLocationClientOption getDefaultOption() {
        AMapLocationClientOption mOption = new AMapLocationClientOption();
        mOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);//可选，设置定位模式，可选的模式有高精度、仅设备、仅网络。默认为高精度模式
        mOption.setGpsFirst(false);//可选，设置是否gps优先，只在高精度模式下有效。默认关闭
        mOption.setHttpTimeOut(30000);//可选，设置网络请求超时时间。默认为30秒。在仅设备模式下无效
        mOption.setInterval(10000);//可选，设置定位间隔。默认为2秒
        mOption.setNeedAddress(true);//可选，设置是否返回逆地理地址信息。默认是true
        mOption.setOnceLocation(false);//可选，设置是否单次定位。默认是false
        mOption.setOnceLocationLatest(false);//可选，设置是否等待wifi刷新，默认为false.如果设置为true,会自动变为单次定位，持续定位时不要使用
        AMapLocationClientOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTP);//可选， 设置网络请求的协议。可选HTTP或者HTTPS。默认为HTTP
        mOption.setSensorEnable(false);//可选，设置是否使用传感器。默认是false
        mOption.setWifiScan(true); //可选，设置是否开启wifi扫描。默认为true，如果设置为false会同时停止主动刷新，停止以后完全依赖于系统刷新，定位位置可能存在误差
        mOption.setLocationCacheEnable(true); //可选，设置是否使用缓存定位，默认为true
        mOption.setGeoLanguage(AMapLocationClientOption.GeoLanguage.DEFAULT);//可选，设置逆地理信息的语言，默认值为默认语言（根据所在地区选择语言）
        return mOption;
    }
    /**
     * 定位监听
     */
    AMapLocationListener locationListener = new AMapLocationListener() {
        @Override
        public void onLocationChanged(AMapLocation location) {
            if (null != location) {
                locationdata = location;
                //errCode等于0代表定位成功，其他的为定位失败，具体的可以参照官网定位错误码说明
                if (location.getErrorCode() == 0) {
                    address=location.getAddress();
                    stopLocation();
                    destroyLocation();
                }
            } else {
                showToast("定位失败");
            }
        }
    };
    /**
     * 开始定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void startLocation() {
        // 设置定位参数
        locationClient.setLocationOption(locationOption);
        // 启动定位
        locationClient.startLocation();
    }
    /**
     * 停止定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void stopLocation() {
        // 停止定位
        locationClient.stopLocation();
    }

    /**
     * 销毁定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void destroyLocation() {
        if (null != locationClient) {
            /**
             * 如果AMapLocationClient是在当前Activity实例化的，
             * 在Activity的onDestroy中一定要执行AMapLocationClient的onDestroy
             */
            locationClient.onDestroy();
            locationClient = null;
            locationOption = null;
        }
    }
    @Override
    protected void onResume() {
        super.onResume();
        //初始化定位
        initLocation();
        //启动定位
        startLocation();
    }
}
