package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.appcompat.widget.Toolbar;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ExamButtonAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.ExamMainContract;
import com.kisoft.yuejianli.entity.BaseList;
import com.kisoft.yuejianli.entity.ButtonBean;
import com.kisoft.yuejianli.entity.ExamQuestionInfo;
import com.kisoft.yuejianli.model.ExamMainModel;
import com.kisoft.yuejianli.presenter.ExamMainPresenter;
import com.kisoft.yuejianli.utils.ScreenUtils;
import com.kisoft.yuejianli.utils.recyclerview.GridDividerItemDecoration;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 监理考试主页
 */
public class ExamMainActivity extends BaseActivity<ExamMainModel, ExamMainPresenter> implements ExamMainContract.View,BaseQuickAdapter.OnItemClickListener {

    private static final int BUTTON_TYPE_TEST=0;
    private static final int BUTTON_TYPE_SIMULATION=1;
    private static final int BUTTON_TYPE_ONLINE=2;
    private static final int BUTTON_TYPE_WRONG=3;
    private static final int BUTTON_TYPE_COLLECT=4;
    private static final int BUTTON_TYPE_LEARN=5;
    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.tool_bar)
    Toolbar toolBar;
    @BindView(R.id.mRecyclerView)
    RecyclerView mRecyclerView;
    private boolean isLoading;

    public static void launch(Activity activity){
        Intent intent=new Intent(activity,ExamMainActivity.class);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();

    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_exam_main;
    }

    private void initView() {
        mModel=new ExamMainModel(this);
        mPresenter=new ExamMainPresenter(this,mModel);
        tvTitle.setText("监理考试");
        mRecyclerView.setLayoutManager(new GridLayoutManager(this,3));
        mRecyclerView.addItemDecoration(new GridDividerItemDecoration.Builder(this)
                .setHorizontalSpan((float) ScreenUtils.dip2px(this,1))
                .setVerticalSpan((float)ScreenUtils.dip2px(this,1))
                .setColor(ContextCompat.getColor(this,R.color.line_space)).setShowLastLine(true).build());
        ExamButtonAdapter examButtonAdapter=new ExamButtonAdapter();
        mRecyclerView.setAdapter(examButtonAdapter);
        List<ButtonBean> buttonBeans=new ArrayList<>();
        buttonBeans.add(new ButtonBean("试题练习",R.drawable.icon_exam01,BUTTON_TYPE_TEST));
        buttonBeans.add(new ButtonBean("模拟考试",R.drawable.icon_exam02,BUTTON_TYPE_SIMULATION));
        buttonBeans.add(new ButtonBean("在线考试",R.drawable.icon_exam03,BUTTON_TYPE_ONLINE));
        buttonBeans.add(new ButtonBean("错题本",R.drawable.icon_exam04,BUTTON_TYPE_WRONG));
        buttonBeans.add(new ButtonBean("我的收藏",R.drawable.icon_exam05,BUTTON_TYPE_COLLECT));
        buttonBeans.add(new ButtonBean("学习资料",R.drawable.icon_exam06,BUTTON_TYPE_LEARN));
        examButtonAdapter.setNewData(buttonBeans);
        examButtonAdapter.setOnItemClickListener(this);
    }

    @OnClick(R.id.iv_back)
    void back(){
        finish();
    }

    @OnClick(R.id.tv_title)
    void test(){
    }

    @Override
    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
        Object object=adapter.getData().get(position);
        if(object instanceof ButtonBean){
            ButtonBean buttonBean= (ButtonBean) object;
            switch (buttonBean.getType()){
                case BUTTON_TYPE_TEST:
                    ExamTestActivity.launch(this);
                    break;
                case BUTTON_TYPE_SIMULATION:
                    ExamPracticeActivity.launch(this);
                    break;
                case BUTTON_TYPE_ONLINE:
                    ExamOnlineActivity.launch(this);
                    break;
                case BUTTON_TYPE_WRONG:
                    if(!isLoading){
                        isLoading=true;
                        mPresenter.getWrongbookList(0, Constant.LOAD_DATA_NORMAL);
                    }
//                    ExamErrorRecordActivity.launch(this);
                    break;
                case BUTTON_TYPE_COLLECT:
                    if(!isLoading){
                        isLoading=true;
                        mPresenter.getFavoriteList();
                    }
//                    ExamCollectActivity.launch(this);
                    break;
                case BUTTON_TYPE_LEARN:
                    //ExamMaterialsListActivity.launch(this);
                    ExamMaterialsNewActivity.launch(this);


                    break;
            }
        }
    }

    @Override
    public void showWrongbookList(BaseList<ExamQuestionInfo> examUserInfoList, int type) {
        isLoading=false;
        if(examUserInfoList==null||examUserInfoList.getList()==null||examUserInfoList.getList().size()==0){
            showToast("当前无错题");
        }else {
            ExamIngActivity.launch(this,"错题本",ExamIngActivity.EXAM_TYPE_ERROR,null,null);
        }
    }

    @Override
    public void showFavoriteList(BaseList<ExamQuestionInfo> examUserInfoList, int type) {
        isLoading=false;
        if(examUserInfoList==null||examUserInfoList.getList()==null||examUserInfoList.getList().size()==0){
            showToast("当前无收藏试题");
        }else {
            ExamIngActivity.launch(this,"我的收藏",ExamIngActivity.EXAM_TYPE_COLLECT,null,null);
        }
    }

    @Override
    public void showQuestionListError() {
        isLoading=false;
        showToast(getString(R.string.request_error));
    }

    @Override
    public void showError() {

    }
}
