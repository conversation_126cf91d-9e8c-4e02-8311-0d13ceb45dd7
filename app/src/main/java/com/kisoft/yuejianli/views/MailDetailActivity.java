package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import androidx.appcompat.widget.Toolbar;
import android.view.View;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.MainFrameAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.event.MessageEvent;
import com.kisoft.yuejianli.ui.NoScrollViewPager;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.BindViews;
import butterknife.OnClick;

/**
 * 邮箱详情
 */
public class MailDetailActivity extends BaseActivity {

    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.tool_bar)
    Toolbar toolBar;
    @BindView(R.id.rg_tab)
    RadioGroup rgTab;
    @BindViews({R.id.rb_tab1, R.id.rb_tab2, R.id.rb_tab3,R.id.rb_tab4})
    List<RadioButton> radioButtons;
    @BindViews({R.id.view1, R.id.view2, R.id.view3,R.id.view4})
    List<View> vLines;
    @BindView(R.id.vp_mainframe_content)
    NoScrollViewPager vpMainframeContent;
    @BindView(R.id.rb_tab4)
    RadioButton rbTab4;
    @BindView(R.id.view4)
    View view4;
    /**
     * 导航的fragment界面集合
     **/
    private List<Fragment> list_fragments = new ArrayList<>();
    /**
     * 适配器
     **/
    private MainFrameAdapter adapter = null;
    private String fmailId;
    private boolean isOut = false;

    public static void launch(Activity activity, String fmailId, boolean isOut) {
        Intent intent = new Intent(activity, MailDetailActivity.class);
        intent.putExtra("fmailId", fmailId);
        intent.putExtra("isOut", isOut);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        init();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_mail_detail;
    }

    private void init() {
        tvTitle.setText("邮件详情");
        fmailId = getIntent().getStringExtra("fmailId");
        isOut = getIntent().getBooleanExtra("isOut", false);
        list_fragments.add(MailDetailFragment.newInstance(fmailId));
        list_fragments.add(MailAttachmentFragment.newInstance(fmailId));
        list_fragments.add(MailTransmitFragment.newInstance(fmailId));
        if (isOut) {
            rbTab4.setVisibility(View.GONE);
            view4.setVisibility(View.GONE);
        } else {
            list_fragments.add(MailReplyFragment.newInstance(fmailId));
        }
        adapter = new MainFrameAdapter(getSupportFragmentManager(), list_fragments);
        vpMainframeContent.setAdapter(adapter);
        vpMainframeContent.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageSelected(int position) {
                tabChange(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });

        rgTab.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int id) {
                switch (id) {
                    case R.id.rb_tab1:
                        tabChange(0);
                        vpMainframeContent.setCurrentItem(0, false);
                        break;
                    case R.id.rb_tab2:
                        tabChange(1);
                        vpMainframeContent.setCurrentItem(1, false);
                        break;
                    case R.id.rb_tab3:
                        tabChange(2);
                        vpMainframeContent.setCurrentItem(2, false);
                        break;
                    case R.id.rb_tab4:
                        tabChange(3);
                        vpMainframeContent.setCurrentItem(3, false);
                        break;
                }
            }
        });

        if (isOut) {
            vpMainframeContent.setOffscreenPageLimit(3);//预加载
        } else {
            vpMainframeContent.setOffscreenPageLimit(4);//预加载
        }
    }

    private void tabChange(int index) {
        if (index < 0 || index > radioButtons.size()) {
            return;
        }
        for (int i = 0; i < radioButtons.size(); i++) {
            if (i == index) {
                radioButtons.get(i).setChecked(true);
                radioButtons.get(i).setTextColor(getResources().getColor(R.color.colorAccent));
                vLines.get(i).setBackgroundColor(getResources().getColor(R.color.colorAccent));
            } else {
                radioButtons.get(i).setTextColor(getResources().getColor(R.color.text_main_black));
                vLines.get(i).setBackgroundColor(getResources().getColor(R.color.line_space));
            }
        }
    }

    @OnClick(R.id.iv_back)
    public void onClick() {
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().post(new MessageEvent(MessageEvent.TYPE_UPDATE_MAIL_LIST));
    }
}
