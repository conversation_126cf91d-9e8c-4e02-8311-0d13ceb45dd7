package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.KeyEvent;
import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebStorage;
import android.widget.TextView;

import com.itheima.view.BridgeWebView;

import butterknife.BindView;
import butterknife.OnClick;

import com.kisoft.yuejianli.R;

import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/5/30.
 */
public class PdfViewActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.my_webview)
    BridgeWebView webView;

    private String fileurl = "";

    private boolean canZoom;
    private String mTitle;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        initData();
        initView();
    }


    private void initView() {

        tvTitle.setText(mTitle);
        webView.getSettings().setJavaScriptEnabled(true);
        webView.getSettings().setUseWideViewPort(true);
        webView.getSettings().setBlockNetworkImage(false);
        webView.getSettings().setAllowFileAccess(true);
        webView.getSettings().setLoadWithOverviewMode(true);

//        if (canZoom) {
        // 设置可以支持缩放
        webView.getSettings().setSupportZoom(true);
        //扩大比例的缩放
        webView.getSettings().setUseWideViewPort(true);
        //自适应屏幕
        webView.getSettings().setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN);
        //设置WebView是否使用其内置的变焦机制
        webView.getSettings().setBuiltInZoomControls(true);
//        }

        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.HONEYCOMB) {
            webView.getSettings().setDisplayZoomControls(false);
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webView.getSettings().setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onReachedMaxAppCacheSize(long requiredStorage, long quota, WebStorage.QuotaUpdater quotaUpdater) {
                quotaUpdater.updateQuota(requiredStorage * 2);
            }

            @Override
            public void onShowCustomView(View view, CustomViewCallback callback) {
                super.onShowCustomView(view, callback);
            }
        });
        if (!StringUtil.isEmpty(fileurl)) {
            webView.loadUrl(fileurl);
        }
    }

    private void initData() {
        Intent intent = getIntent();
        if (intent != null) {
            fileurl = intent.getStringExtra(Constant.INTENT_KEY_FILE_URL);
            canZoom = intent.getBooleanExtra(Constant.INTENT_CAN_ZOOM, false);
            mTitle = intent.getStringExtra(Constant.INTENT_KEY_FILE_NAME);
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            finish();
            return true;
        } else {
            onBackPressed();
        }
        return super.onKeyDown(keyCode, event);
    }


    @Override
    public int getLayoutId() {
        return R.layout.activity_pdf_view;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }
}
