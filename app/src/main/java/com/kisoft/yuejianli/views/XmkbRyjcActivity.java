package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ArrivalAdapter;
import com.kisoft.yuejianli.adpter.MainFrameAdapter;
import com.kisoft.yuejianli.adpter.XmkbRyjcAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.ui.NoScrollViewPager;
import com.kisoft.yuejianli.views.watermark.ArrivalFragment;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class XmkbRyjcActivity extends BaseActivity {
    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;

    @BindView(R.id.tool_bar)
    Toolbar toolBar;
    @BindView(R.id.rb_all_num)
    RadioButton rbAllNum;
    @BindView(R.id.rb_jc_num)
    RadioButton rbCometoNum;
    @BindView(R.id.rb_tc_num)
    RadioButton rbLeaveNum;

    @BindView(R.id.rg_title)
    RadioGroup rgTitle;

    @BindView(R.id.view_1)
    View view1;
    @BindView(R.id.view_2)
    View view2;
    @BindView(R.id.view_3)
    View view3;

    @BindView(R.id.vp_mainframe_content)
    NoScrollViewPager vpMainframeContent;

    private XmkbRyjcFragment ryjcFragment1;
    private XmkbRyjcFragment ryjcFragment2;
    private XmkbRyjcFragment ryjcFragment3;

    /**
     * 导航的fragment界面集合
     **/
    private List<Fragment> list_fragments = null;
    /**
     * 适配器
     **/
    private MainFrameAdapter adapter = null;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        initData();
        initView();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_xmkb_ryjc;
    }

    private void initData() {
        Intent intent = getIntent();
        if (intent != null) {
//            bundle.putSerializable("alldata",mRYJCList.get(0));
//            bundle.putSerializable("jcdata", mRYJCList.get(1));
//            bundle.putSerializable("tcdata", mRYJCList.get(2));

//            headCount=getIntent().getStringExtra("headCount");
//            leaveCount=getIntent().getStringExtra("leaveCount");
//            errCount=getIntent().getStringExtra("errCount");
//            workCount=getIntent().getStringExtra("workCount");
//            if(StringUtil.isEmpty(headCount)){
//                headCount="0";
//            }
//            if(StringUtil.isEmpty(leaveCount)){
//                leaveCount="0";
//            }
//            if(StringUtil.isEmpty(errCount)){
//                errCount="0";
//            }
//            if(StringUtil.isEmpty(workCount)){
//                workCount="0";
//            }
        }
    }

    private void initView() {
        tvTitle.setText("到岗情况");

//        rbAllNum.setText("全部("+headCount+")");
//        rbCometoNum.setText("到岗("+workCount+")");
//        rbLeaveNum.setText("请假("+leaveCount+")");
//        rbErrNum.setText("异常("+errCount+")");


//        if(StringUtil.isEmpty(strDate)){
//            date = DateUtil.getTodayDate();
//            tvMonth.setText(DateUtil.getFormatDate(date,DateUtil.YMD));
//            strDate=DateUtil.getFormatDate(date,DateUtil.YMD);
//        }
        Intent intent = getIntent();
        if (intent == null) {
          return;
//            bundle.putSerializable("alldata",mRYJCList.get(0));
//            bundle.putSerializable("jcdata", mRYJCList.get(1));
//            bundle.putSerializable("tcdata", mRYJCList.get(2));
//            intent.getSerializableExtra("alldata");
        }


        list_fragments = new ArrayList<>();
        Bundle bundle1 = new Bundle();
        bundle1.putSerializable("data",intent.getSerializableExtra("alldata"));


        Bundle bundle2 = new Bundle();
        bundle2.putSerializable("data",intent.getSerializableExtra("jcdata"));

        Bundle bundle3 = new Bundle();
        bundle3.putSerializable("data",intent.getSerializableExtra("tcdata"));

        ryjcFragment1 = new XmkbRyjcFragment();
        ryjcFragment1.setArguments(bundle1);

        ryjcFragment2 = new XmkbRyjcFragment();
        ryjcFragment2.setArguments(bundle2);

        ryjcFragment3 = new XmkbRyjcFragment();
        ryjcFragment3.setArguments(bundle3);


        list_fragments.add(ryjcFragment1);//全部
        list_fragments.add(ryjcFragment2);//进场
        list_fragments.add(ryjcFragment3);//退场

        adapter = new MainFrameAdapter(getSupportFragmentManager(), list_fragments);
        vpMainframeContent.setAdapter(adapter);


        /**
         *	viewPager页面改变事件
         */
        vpMainframeContent.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageSelected(int position) {
                switch (position) {
                    case 0:
                        rbAllNum.setChecked(true);
                        rbAllNum.setTextColor(getResources().getColor(R.color.colorAccent));
                        view1.setBackgroundColor(getResources().getColor(R.color.colorAccent));
                        rbCometoNum.setTextColor(getResources().getColor(R.color.text_main_black));
                        view2.setBackgroundColor(getResources().getColor(R.color.line_space));
                        rbLeaveNum.setTextColor(getResources().getColor(R.color.text_main_black));
                        view3.setBackgroundColor(getResources().getColor(R.color.line_space));
                        break;
                    case 1:
                        rbCometoNum.setChecked(true);
                        rbAllNum.setTextColor(getResources().getColor(R.color.text_main_black));
                        view1.setBackgroundColor(getResources().getColor(R.color.line_space));
                        rbCometoNum.setTextColor(getResources().getColor(R.color.colorAccent));
                        view2.setBackgroundColor(getResources().getColor(R.color.colorAccent));
                        rbLeaveNum.setTextColor(getResources().getColor(R.color.text_main_black));
                        view3.setBackgroundColor(getResources().getColor(R.color.line_space));
                        break;
                    case 2:
                        rbLeaveNum.setChecked(true);
                        rbAllNum.setTextColor(getResources().getColor(R.color.text_main_black));
                        view1.setBackgroundColor(getResources().getColor(R.color.line_space));
                        rbCometoNum.setTextColor(getResources().getColor(R.color.text_main_black));
                        view2.setBackgroundColor(getResources().getColor(R.color.line_space));
                        rbLeaveNum.setTextColor(getResources().getColor(R.color.colorAccent));
                        view3.setBackgroundColor(getResources().getColor(R.color.colorAccent));
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });

        rgTitle.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                switch (checkedId) {
                    case R.id.rb_all_num:
                        vpMainframeContent.setCurrentItem(0, false);
                        break;
                    case R.id.rb_jc_num:
                        vpMainframeContent.setCurrentItem(1, false);
                        break;
                    case R.id.rb_tc_num:
                        vpMainframeContent.setCurrentItem(2, false);
                        break;
                }
            }
        });
        vpMainframeContent.setOffscreenPageLimit(4);//预加载


        int currentItem = vpMainframeContent.getCurrentItem();


    }


    @OnClick({R.id.iv_back, R.id.rb_all_num, R.id.rb_jc_num, R.id.rb_tc_num})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.rb_all_num:
                break;
            case R.id.rb_jc_num:
                break;
            case R.id.rb_tc_num:
                break;
        }
    }

}