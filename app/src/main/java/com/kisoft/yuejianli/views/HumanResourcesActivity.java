package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ProjectContentAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.ProjectContent;

/**
 * Created by tudou on 2018/6/7.
 */

public class HumanResourcesActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    private List<ProjectContent> contents = new ArrayList<>();
    private ProjectContentAdapter mAdapter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        initData();
        initView();
    }

    private void initView() {
        tvTitle.setText("人力资源");
        if (rvContent.getLayoutManager() == null) {
            GridLayoutManager layoutManager = new GridLayoutManager(this, 3);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(layoutManager);
        }
        mAdapter = new ProjectContentAdapter(R.layout.item_project_content, contents);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {

            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                ProjectContent con = contents.get(position);
                // todo 处理点击事件
                onClickContent(con.getId());

            }
        });
        rvContent.setAdapter(mAdapter);
    }

    private void initData() {
        contents.add(new ProjectContent(ProjectContent.PROJECT_ATTENDANCE_SGIN_IN, ProjectContent.PROJECT_ATTENDANCE_SGIN_IN_NAME));
        contents.add(new ProjectContent(ProjectContent.PROJECT_ATTENDANCE_RECORD, ProjectContent.PROJECT_ATTENDANCE_RECORD_NAME));
        contents.add(new ProjectContent(ProjectContent.PROJECT_HUMAN_ASSESSMENT, ProjectContent.PROJECT_HUMAN_ASSESSMENT_NAME));
//        contents.add(new ProjectContent(ProjectContent.PROJECT_COMPANY_TRAINING, ProjectContent.PROJECT_COMPANY_TRAINING_NAME));
        contents.add(new ProjectContent(ProjectContent.PROJECT_LIBRARY, ProjectContent.PROJECT_LIBRARY_NAME));

    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_human_resources;
    }

    @OnClick(R.id.iv_back)
    public void goBack() {
        finish();
    }


    private void onClickContent(int id) {
        switch (id) {

            case ProjectContent.PROJECT_ATTENDANCE_SGIN_IN:              // 考勤打卡
                goSign();
                break;

            case ProjectContent.PROJECT_ATTENDANCE_RECORD: // 考勤记录
                goAttendanceRecord();
                break;

            case ProjectContent.PROJECT_HUMAN_ASSESSMENT:  // 绩效考核
                goAssessment();
                break;

/*            case ProjectContent.PROJECT_COMPANY_TRAINING: // 企业培训

                goTraining();
                break;*/

            case ProjectContent.PROJECT_LIBRARY:          // 知识库
                goComKnownledge();
                break;

            default:
                break;
        }
    }

    private void goComKnownledge() {
        Intent intent = new Intent();
        intent.setClass(this, KnowledgeActivity.class);
        startActivity(intent);
    }

    // todo 绩效考核
    private void goAssessment() {
//        Intent intent = new Intent();
//        intent.setClass(this , );
//        startActivity(intent);

    }

    // todo 企业培训
    private void goTraining() {
//        Intent intent = new Intent();
//        intent.setClass(this , );
//        startActivity(intent);
    }

    private void goSign(){
        Intent intent = new Intent();
        intent.setClass(this, PunchCardActivity.class);
        startActivity(intent);
    }

    private void goAttendanceRecord(){
        Intent intent = new Intent();
        intent.setClass(this, AttendanceRecordsActivity.class);
        startActivity(intent);
    }
}
