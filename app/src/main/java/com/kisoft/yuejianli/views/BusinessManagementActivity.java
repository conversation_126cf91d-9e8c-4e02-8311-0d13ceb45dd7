package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.adpter.ProjectContentAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.entity.ProjectContent;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.PhoneUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by tudou on 2018/6/7.
 */

public class BusinessManagementActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    private List<ProjectContent> contents = new ArrayList<>();
    private ProjectContentAdapter mAdapter;



    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        initData();
        initView();
    }

    private void initView(){
        tvTitle.setText("经营管理");
        if (rvContent.getLayoutManager() == null) {
            GridLayoutManager layoutManager = new GridLayoutManager(this, 3);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(layoutManager);
        }
        mAdapter = new ProjectContentAdapter(R.layout.item_project_content, contents);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {

            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                ProjectContent con = contents.get(position);
                // todo 处理点击事件
                onClickContent(con.getId());

            }
        });
        rvContent.setAdapter(mAdapter);
    }

    private void initData(){
        contents.clear();
        contents.add(new ProjectContent(ProjectContent.PROJECT_TENDER_PLAN, ProjectContent.PROJECT_TENDER_PLAN_NAME));
//        contents.add(new ProjectContent(ProjectContent.PROJECT_TENDER_MONEY, ProjectContent.PROJECT_TENDER_MONEY_NAME));
//        contents.add(new ProjectContent(ProjectContent.PROJECT_TENDER_FILE, ProjectContent.PROJECT_TENDER_FILE_NAME));
        contents.add(new ProjectContent(ProjectContent.PROJECT_TENDER_START, ProjectContent.PROJECT_TENDER_START_NAME));
        contents.add(new ProjectContent(ProjectContent.PROJECT_CUSTOMER_MANAGEMENT, ProjectContent.PROJECT_CUSTOMER_MANAGEMENT_NAME));
        contents.add(new ProjectContent(ProjectContent.PROJECT_RESOURCE_MANAGEMENT, ProjectContent.PROJECT_RESOURCE_MANAGEMENT_NAME));
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_business_management;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    private void onClickContent(int id) {
        switch (id) {

            case ProjectContent.PROJECT_TENDER_PLAN:              // 投标计划
                goProjectTenderPlan();
                break;

            case ProjectContent.PROJECT_TENDER_MONEY:           // 保函、保证金
                goProjectTenderMoney();
                break;

            case ProjectContent.PROJECT_TENDER_FILE:            // 投标文件
                goProjectTenderFile();
                break;

            case ProjectContent.PROJECT_TENDER_START:          // 开标
                goProjectStartTender();
                break;

            case ProjectContent.PROJECT_CUSTOMER_MANAGEMENT:          // 客户管理
                goCustomerManagement();
                break;

            case ProjectContent.PROJECT_RESOURCE_MANAGEMENT:          // 资源管理
                goResourceManagement();
                break;
            default:
                break;
        }
    }

    private void goCustomerManagement() {
        Intent intent = new Intent();
        intent.setClass(this, CustomerManagementActivity.class);
        startActivity(intent);
    }

    private void goResourceManagement() {
        Intent intent = new Intent();
        intent.setClass(this, ResourceManagementActivity.class);
        startActivity(intent);
    }

    private void goProjectStartTender() {
        Intent intent = new Intent();
        intent.setClass(this, TenderOpenInfoActivity.class);
        startActivity(intent);
    }

    private void goProjectTenderFile() {

    }

    private void goProjectTenderMoney() {
        Intent intent = new Intent();
        intent.setClass(this,TenderDepositActivity.class);
        startActivity(intent);
    }

    private void goProjectTenderPlan() {
        Intent intent = new Intent();
        intent.setClass(this,TenderPlanActivity.class);
        startActivity(intent);
    }


}
