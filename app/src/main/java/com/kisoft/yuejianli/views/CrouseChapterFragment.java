package com.kisoft.yuejianli.views;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.entity.ComPanyOrgInfo;
import com.kisoft.yuejianli.entity.ExamMaterialsInfo;
import com.kisoft.yuejianli.entity.JDData;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;

public class CrouseChapterFragment extends BaseFragment {
    Unbinder unbinder;

    @BindView(R.id.progress_tv)
    TextView progressTv;
    @BindView(R.id.chapter_rv)
    RecyclerView rvChapter;
    private ChapterListAdapter mChapterListAdapter;
    ExamMaterialsInfo mData = null;

    @Override
    public int getRootView() {
        return R.layout.fragment_crouse_chapter;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle != null) {
            mData = (ExamMaterialsInfo) bundle.getSerializable("data");
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initData();
        initView();
        return mRootView;
    }

    private void initData() {

    }

    private void initView() {
        //model.studyTimeOnUser.doubleValue / model.hoursOfStudents.doubleValue
        if (mData != null) {

        Double aDouble = Double.valueOf(mData.getStudyTimeOnUser());
        Double bDouble = Double.valueOf(mData.getHoursOfStudents());
        double v = aDouble / bDouble;

        double one= Double.parseDouble(mData.getStudyTimeOnUser());
        double two= Double.parseDouble(mData.getHoursOfStudents());
        double percent = one/ two;
        //获取格式化对象
        NumberFormat nt = NumberFormat.getPercentInstance();
        //设置百分数精确度2即保留两位小数
        nt.setMinimumFractionDigits(2);
        //最后格式化并输出
        progressTv.setText("学习进度：" + nt.format(percent));
        initChapterView();
        }
    }

    private void initChapterView() {
        if (rvChapter.getLayoutManager() == null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            layoutManager.setSmoothScrollbarEnabled(false);
            rvChapter.setLayoutManager(layoutManager);
            rvChapter.setNestedScrollingEnabled(false);
        }

        List<Map> beans = new ArrayList();

        for (int i = 0; i < mData.getCatalogDTOList().size(); i++) {
            ExamMaterialsInfo.CatalogDTOListDataBean dtoListDataBean = mData.getCatalogDTOList().get(i);
            Map<String,String> map = new HashMap<>();
            map.put("catalogName",dtoListDataBean.getCatalogName());
            map.put("id",dtoListDataBean.getId());
            map.put("type","0");
            beans.add(map);
            for (int i1 = 0; i1 < dtoListDataBean.getCoursewareList().size(); i1++) {
                ExamMaterialsInfo.CatalogDTOListDataBean.CoursewareListDataBean wareListDataBean =
                        dtoListDataBean.getCoursewareList().get(i1);
                Map<String,String> map1 = new HashMap<>();
                map1.put("coursewareName",wareListDataBean.getCoursewareName());
                map1.put("wareId",wareListDataBean.getId());
                map1.put("catalogId",wareListDataBean.getCatalogId());
                map1.put("catalogName",dtoListDataBean.getCatalogName());
                map1.put("integral",wareListDataBean.getIntegral());
                map1.put("type","1");
                beans.add(map1);
            }
        }
        mChapterListAdapter=new ChapterListAdapter(beans);
        mChapterListAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                Map map = beans.get(position);
                if ("1".equals(map.get("type"))){
                    if (mChapterOnClickListener != null){
                        mChapterOnClickListener.selectChapter(map);
                    }
                }
            }
        });
        rvChapter.setAdapter(mChapterListAdapter);
    }


public class ChapterListAdapter extends BaseQuickAdapter<Map, ChapterListAdapter.ViewHolder> {
    public ChapterListAdapter(@Nullable List<Map> data) {
        super(R.layout.item_enclosure_list, data);
    }

    @Override
    protected void convert(@NonNull ChapterListAdapter.ViewHolder helper, Map item) {
        if (item != null) {
            if ("0".equals(item.get("type"))){
                helper.tvFileName.setText(item.get("catalogName").toString());
                helper.bgll.setBackgroundColor(getResources().getColor(R.color.colorAccent));
            }else {
                helper.tvFileName.setText(item.get("coursewareName").toString());
                helper.bgll.setBackgroundColor(getResources().getColor(R.color.nomal_bg));
            }
        }
    }

    public class ViewHolder extends BaseViewHolder {

        @BindView(R.id.bg_ll)
        LinearLayout bgll;

        @BindView(R.id.tv_file_name)
        TextView tvFileName;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}

    //选择组织部门
    private ChapterOnClickListener mChapterOnClickListener;

    public void setChapterOnClickListener(ChapterOnClickListener listener) {
        this.mChapterOnClickListener = listener;
    }

    public interface ChapterOnClickListener{
        void selectChapter(Map map);
    }
}