package com.kisoft.yuejianli.views;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.contract.ApproveFromUserContract;
import com.kisoft.yuejianli.entity.ApproveInfos;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.QualityApprove;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.ArrayList;
import java.util.List;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.UserApproveAdapter;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.model.ApproveFromUserModel;
import com.kisoft.yuejianli.presenter.ApproveFromUserPresenter;
import com.kisoft.yuejianli.utils.StringUtil;

/**
 * Created by tudou on 2018/4/20.
 */

public class ApproveFromUserFragment extends BaseFragment<ApproveFromUserModel, ApproveFromUserPresenter>
        implements ApproveFromUserContract.ApproveFromUserViewContract {

    private RecyclerView rvContent;
    private List<QualityApprove> approves = new ArrayList<>();
    private UserApproveAdapter mAdapter ;
    private View empotyView;


    private UserInfo userInfo;
    private ProjectInfo projectInfo;
    private int count = 0;
    private int page = 1;
    private int pageSize = 15;

    private ApproveFromUserModel mModel;
    private ApproveFromUserPresenter mPresenter;


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mModel = new ApproveFromUserModel(getActivity());
        mPresenter = new ApproveFromUserPresenter(this, mModel);

    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        initView(inflater);
        initData();
        return mRootView;
    }

    private void initView(LayoutInflater inflater){
        empotyView = inflater.inflate(R.layout.page_no_data, null);
        mRootView = inflater.inflate(getRootView(),null);
        rvContent = mRootView.findViewById(R.id.rv_approve);
        if(rvContent.getLayoutManager() == null){
            LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(layoutManager);
        }
        mAdapter = new UserApproveAdapter(R.layout.item_approve_notice, approves);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                // todo
                QualityApprove approve = approves.get(position);
                if(StringUtil.isEqual(QualityApprove.APPROVE_TYPE_SCENE_SAFE,approve.getBusinessType())){

                    goSafeHandle(approve);
                }else {
                    goApproveHandle(approve);
                }
            }
        });
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                // todo
                rvContent.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (approves.size() >= count) {
                            mAdapter.loadMoreEnd();
                        } else {
                            getData();
                        }
                    }
                }, Constant.LOAD_MORE_DELAY);
            }

        },rvContent);
        mAdapter.setEmptyView(empotyView);
        rvContent.setAdapter(mAdapter);


    }

    private void initData(){
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        if(count == 0){
            getData();
        }
    }

    private void getData(){
        String c = Integer.toString(count);
        String s = Integer.toString(pageSize);
        String p = Integer.toString(page);
        mPresenter.getUserApproves(projectInfo.getProjectId(), userInfo.getId(),s,p,c);
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    @Override
    public int getRootView() {
        return R.layout.fragment_approve_from_user;
    }

    @Override
    public void showUserApproves(ApproveInfos info, int type) {
        count = info.getCount();
        if(info!= null && info.getList().size() >0){
            if(type == 0){
                approves.clear();
                approves.addAll(info.getList());
            }else {
                approves.addAll(info.getList());
                page++;
                mAdapter.loadMoreComplete();
            }

        }
        mAdapter.notifyDataSetChanged();
    }


    private void goApproveHandle(QualityApprove approve){
        Intent intent = new Intent();
        intent.setClass(getActivity(),ApproveHandleActivity.class);
        Bundle bundle = new Bundle();
        bundle.putBoolean(Constant.INTENT_KEY_ISEDIT, true);
        bundle.putSerializable(Constant.INTENT_KEY_APPROVE, approve);
        intent.putExtras(bundle);
        startActivity(intent);
    }

    private void goSafeHandle(QualityApprove approve){
        Intent intent = new Intent();
        intent.setClass(getActivity(), ProjectSafeApproveHandleActivity.class);
        Bundle bundle = new Bundle();
        bundle.putBoolean(Constant.INTENT_KEY_ISEDIT ,true);
        bundle.putSerializable(Constant.INTENT_KEY_APPROVE ,approve);
        intent.putExtras(bundle);
        startActivity(intent);
        getActivity().finish();
    }
}
