package com.kisoft.yuejianli.views;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.CheckBox;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Spinner;
import android.widget.TextView;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.ImageAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.AddNoticeContract;
import com.kisoft.yuejianli.entity.PhotoUploadResulte;
import com.kisoft.yuejianli.entity.ProjectApproverInfo;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.SupervisionNoticeDTO;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.AddNoticeModel;
import com.kisoft.yuejianli.presenter.AddNoticePresenter;
import com.kisoft.yuejianli.ui.ImageDialog;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.PermissionsChecker;
import com.kisoft.yuejianli.utils.StringUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.views.watermark.CallBackActivity;
import com.kisoft.yuejianli.views.watermark.ImageUtil;
import com.kisoft.yuejianli.views.watermark.PhotoCaptureActivity;
import com.kisoft.yuejianli.views.watermark.PhotoListener;
import com.kisoft.yuejianli.views.watermark.WaterMask;
import com.kisoft.yuejianli.views.watermark.WaterMaskHelper;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;

/**
 * Description:新增通知单
 * Author     : yanlu
 * Date       : 2018/12/21 21:02
 */

public class AddNoticeActivity extends BaseActivity<AddNoticeModel, AddNoticePresenter>
        implements AddNoticeContract.AddNoticeViewContract, WaterMask.WaterMaskListener, PhotoListener {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_project_name)
    TextView tvProjectName;
    @BindView(R.id.et_reply_form_num)
    EditText etReplyFormNum;
    @BindView(R.id.tv_company)
    TextView tvCompany;
    @BindView(R.id.iv_select_contruction_unit)
    ImageView iv_select_contruction_unit;
    @BindView(R.id.tv_reply_time)
    TextView tvReplyTime;
    @BindView(R.id.cb_security_risks)
    CheckBox cbSecurityRisks;
    @BindView(R.id.spinner_approval_person)
    Spinner spinnerApprovalPerson;
    @BindView(R.id.et_description)
    EditText etDescription;
    @BindView(R.id.et_overview)
    EditText etOverview;
    @BindView(R.id.rv_check_picture)
    RecyclerView rvCheckPicture;

    private AddNoticeModel model;
    private AddNoticePresenter presenter;

    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private ImageDialog imageDialog;
    private List<String> images = new ArrayList<>();
    private ImageAdapter imageAdapter;

    private ArrayAdapter<String> mAdapter;
    /**
     * 审核人列表
     */
    private List<ProjectApproverInfo> list;
    private String userId, username;

    private BGAPhotoHelper mPhotoHelper;

    private static final int REQUEST_CODE_CHOOSE_PHOTO = 1;
    private static final int REQUEST_CODE_TAKE_PHOTO = 2;
    private static final int REQUEST_CODE_CROP = 3;
    private static final int REQUEST_CONSTRUCTIONUNITLIST_ACTIVITY = 4;

    private View lastView;
    private int maskLocation=4;
    private ArrayList<String> uris;
    WaterMaskHelper waterMaskHelper = new WaterMaskHelper(this);
    private boolean isTakePhoto;
    AMapLocation locationdata = null;
    String address="";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        model = new AddNoticeModel(mContext);
        presenter = new AddNoticePresenter(this, model);
        initMVP(model, presenter);
        initWater();
        initData();
        initView();

    }

    //初始化水印
    private void initWater() {
        //binding.setPresenter(new Presenter());
        //初始化水印工具
        waterMaskHelper = new WaterMaskHelper(this, this, this);
        //lastView = binding.txtCenter;
        //lastView.setSelected(true);
        maskLocation = WaterMask.DefWaterMaskParam.Location.center;
    }

    @Override                       //添加水印：
    public WaterMask.WaterMaskParam onDraw() {

        //拍照后调用，设置水印的基本参数
        WaterMask.WaterMaskParam param = new WaterMask.WaterMaskParam();
        //创建当前时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        Date date = new Date();
        String format = dateFormat.format(date);
        param.txt.add(format+" "+address);
        param.location = 3;
        param.itemCount = 60;
        return param;
    }

    @Override
    public void onChoose(ArrayList<String> photos) {
        uris = photos;
        //Glide.with(CompleteCheckAddActivity.this).load(photos.get(0)).placeholder(R.mipmap.ic_launcher).centerCrop().error(R.mipmap.ic_launcher).crossFade().into(ivSfenclosure);
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        BGAPhotoHelper.onSaveInstanceState(mPhotoHelper, outState);
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        BGAPhotoHelper.onRestoreInstanceState(mPhotoHelper, savedInstanceState);
    }

    private void initData() {
        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);

        permissionsChecker = new PermissionsChecker(this);
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();

        if (projectInfo != null) {
            presenter.getUserIdsAndNamesByProjectId(projectInfo.getProjectId());
        }

        if (rvCheckPicture.getLayoutManager() == null) {
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.HORIZONTAL);
            rvCheckPicture.setLayoutManager(manager);
        }
        /*
        imageAdapter = new ImageAdapter(R.layout.item_iamge_show, images);
        imageAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (StringUtil.isEmpty(images.get(position))) {
                    selectDialog();
                } else {
                    showImage(images.get(position));
                }
            }
        });
        rvCheckPicture.setAdapter(imageAdapter);
        imageDialog = new ImageDialog();
        */


        imageAdapter = new ImageAdapter(R.layout.item_iamge_show, images);
        imageAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (StringUtil.isEmpty(images.get(position))) {
                    if (permissionsChecker.lacksPermissions(PERMISSIONS)) {
                        getPermissions(Constant.REQUEST_CODE_TAKE_POHO);
                    } else {
                        // todo 空照片 ，添加
                        String[] str = new String[]{"系统相机", "手机相册"};
                        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
                        ab.setItems(str, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                switch (which) {
                                    case 0://相机
                                        isTakePhoto = true;
                                        takePhoto();
                                        break;
                                    case 1://相册
                                        isTakePhoto = false;
                                        getPhoto();
                                        break;
                                }
                            }
                        });
                        ab.show();
                    }
                } else {
                    showImage(images.get(position));
                }
            }
        });

        imageAdapter.setApply(true);
        imageAdapter.setDeleteBtnClickListener(new ImageAdapter.DeleteBtnClickListener() {
            @Override
            public void deletePicture(String item) {
                if (!StringUtil.isEmpty(item)){
                    images.remove(item);
                    imageAdapter.setNewData(images);
                    //imageAdapter.notifyDataSetChanged();
                }
            }
        });
        rvCheckPicture.setAdapter(imageAdapter);
        imageDialog = new ImageDialog();




        // 初始添加现场照片
        images.add("");
        spinnerApprovalPerson.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (list != null) {
                    userId = list.get(position).getUserId();
                    username = list.get(position).getUserName();
                }

            }
            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    private void initView() {
        tvTitle.setText("新增通知单");

        if (projectInfo != null) {
            tvProjectName.setText(projectInfo.getProjectName());
        }
    }

    @OnClick({R.id.iv_back, R.id.tv_sub, R.id.tv_reply_time})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.tv_sub:
                submit();
                break;
            case R.id.tv_reply_time:
                showDatePickerDialog();
                break;
        }
    }

    /**
     * 提交
     */
    private void submit() {
        SupervisionNoticeDTO dto = new SupervisionNoticeDTO();
        dto.setProjectId(projectInfo.getProjectId());
        dto.setProjectName(projectInfo.getProjectName());
        dto.setSnNumber(etReplyFormNum.getText().toString());   //编号
        dto.setCompany(tvCompany.getText().toString());
        dto.setRenson(etDescription.getText().toString());
        dto.setContent(etOverview.getText().toString());
        dto.setSnStatus("1"); //1待审批
        //附件
        List<String> imageContent = new ArrayList<>();
        if (images.size() > 1) {
            for (int i = 0; i < images.size() - 1; i++) {
                imageContent.add(images.get(i));
            }
            dto.setSnEnclosure(StringUtil.imageArryToString(imageContent));
        } else {
            dto.setSnEnclosure("");
        }

        dto.setCreateId(userInfo.getId());
        dto.setCreateTime(DateUtil.getNowTime());
        dto.setSuperUnit(projectInfo.getPjJldw());
        dto.setCreateName(userInfo.getName());
        dto.setReplyTime(tvReplyTime.getText().toString());
        if (cbSecurityRisks.isChecked()) {
            dto.setIsHidDange("0");         //安全隐患通知单
        } else {
            dto.setIsHidDange("1");
        }
        dto.setUserId(userId);
        dto.setUserName(username);
        if (StringUtil.isEmpty(dto.getSnNumber())) {
            showToast("请输入通知单编号");
            return;
        }
        if (StringUtil.isEmpty(dto.getCompany())) {
            showToast("请输入施工单位");
            return;
        }
        if (StringUtil.isEmpty(dto.getRenson())) {
            showToast("请输入事由");
            return;
        }
        if (StringUtil.isEmpty(dto.getContent())) {
            showToast("请输入内容概述");
            return;
        }
        if (StringUtil.isEmpty(dto.getReplyTime())) {
            showToast("请选择要求回复时间");
            return;
        }

        presenter.addSupNotice(dto);
        showProgress();
    }


    /**
     * 展示日期选择对话框
     */
    private void showDatePickerDialog() {
        Calendar c = Calendar.getInstance();
        new DatePickerDialog(mContext, new DatePickerDialog.OnDateSetListener() {

            @Override
            public void onDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                // TODO Auto-generated method stub
                tvReplyTime.setText(year + "-" + (monthOfYear + 1) + "-" + dayOfMonth);
            }
        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)).show();

    }


    /**
     * 获取到审核人列表 加载到spinner中
     *
     * @param list
     */
    @Override
    public void showUserIdsAndNamesByProjectId(List<ProjectApproverInfo> list) {
        this.list = list;
        List<String> person = new ArrayList<>();
        String[] m_Countries = new String[list.size()];

        for (int i = 0; i < list.size(); i++) {
            person.add(list.get(i).getUserName());
        }

        if (person.size() > 0) {
            person.toArray(m_Countries);
        }
        if (list != null && !list.isEmpty()) {
            userId = list.get(0).getUserId();
            username = list.get(0).getUserName();
        }
        mAdapter = new ArrayAdapter(this, android.R.layout.simple_spinner_item, m_Countries);
        spinnerApprovalPerson.setAdapter(mAdapter);
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void returnAddSupNotice(Boolean b) {
        dismissProgress();
        if (b) {
            showToast("提交成功");
            finish();
        }
    }

    static final String[] PERMISSIONS = new String[]{
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA

    };
    private PermissionsChecker permissionsChecker;

    /**
     * 6.0之上权限
     */
    private void getPermissions(int code) {
        ActivityCompat.requestPermissions(this, PERMISSIONS, code);
    }

    public void selectDialog() {
        if (permissionsChecker.lacksPermissions(PERMISSIONS)) {
            getPermissions(Constant.REQUEST_CODE_TAKE_POHO);
        } else {
            String str[] = new String[]{"系统相机", "手机相册"};
            AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
            ab.setItems(str, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    switch (which) {
                        case 0://相机
                            isTakePhoto = true;
                            takePhoto();
                            break;
                        case 1://相册
                            isTakePhoto = false;
                            getPhoto();
                            break;
                    }
                }
            });
            ab.show();
        }
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), REQUEST_CODE_TAKE_PHOTO);
            PhotoCaptureActivity.setWaterListener(waterMaskHelper.getWaterMarkListener());
            PhotoCaptureActivity.setPhotoListener(waterMaskHelper.getPhotoListener());
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

//    /**
//     * 相册获得照片
//     */
//    private void getPhoto() {
//        Intent intent = new Intent(Intent.ACTION_PICK, android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
//        startActivityForResult(intent, PhotoUtil.REQUESTCODE_GET_PHOTO);
//    }
//
//    /**
//     * 拍照
//     */
//    private void takePhoto() {
//        if (permissionsChecker.lacksPermissions(PERMISSIONS)) {
//            getPermissions(Constant.REQUEST_CODE_TAKE_POHO);
//        } else {
//            PhotoUtil.takePhoto(this);
//        }
//    }

    /**
     * 通过url，展示照片
     *
     * @param url
     */
    private void showImage(String url) {

        if (imageDialog != null) {
            imageDialog.showImageDialog(getFragmentManager(), url);
        }
    }

    private void uploadPhoto(Bitmap bitmap, Uri uri) {
        showProgress();
        if (bitmap != null) {
            mPresenter.uploadPhoto(projectInfo.getProjectId(), bitmap, uri);
        }
    }

    BGAPhotoHelper1 mPhotoHelper1=new BGAPhotoHelper1();
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            if (requestCode == REQUEST_CODE_CHOOSE_PHOTO) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            } else if (requestCode == REQUEST_CODE_TAKE_PHOTO) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800, 800), REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            } else if (requestCode == REQUEST_CODE_CROP) {
                if (isTakePhoto) {
                    //重点在这里
                    // getCropIntent  获取裁剪完图片的路径
                    File file = new File(mPhotoHelper1.getCropFilePath());
                    //应该在这里把绘制完的水印图片路径传过去
                    ArrayList<String> strings = new ArrayList<>();
                    strings.add(file.toString());
                    if (CallBackActivity.getPhotoListener() != null){
                        //选择照片的uri，默认为下标1的元素
                        CallBackActivity.getPhotoListener().onChoose(strings);
                    }
                    if (CallBackActivity.getWaterMarkListener() != null) {
                        WaterMask.WaterMaskParam maskParam = CallBackActivity.getWaterMarkListener().onDraw();
                        CallBackActivity.getWaterMarkListener().onDraw();
                        Bitmap bitmap = ImageUtil.getBitmap(String.valueOf((file)));
                        WaterMask.draw(this, bitmap, String.valueOf((file)), maskParam);
                        sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(file)));
                    }
                    uploadPhoto(BitmapFactory.decodeFile(file.getPath()), Uri.parse(file.getPath()));
                    Log.i("tag",file.getPath());
                }else{
                    uploadPhoto(BitmapFactory.decodeFile(mPhotoHelper1.getCropFilePath()), Uri.parse(mPhotoHelper1.getCropFilePath()));
                }

            }
        } else if (requestCode == REQUEST_CODE_CROP) {
            mPhotoHelper.deleteCameraFile();
            mPhotoHelper.deleteCropFile();
        }
        if (requestCode == REQUEST_CONSTRUCTIONUNITLIST_ACTIVITY) {
            if (resultCode == Activity.RESULT_OK) {
                tvCompany.setText(data.getStringExtra("data"));
            }
        }
    }

    @OnClick({R.id.iv_select_contruction_unit, R.id.tv_company})
    public void selectContructionUnit() {
        Intent intent = new Intent(mContext, ConstructionUnitListActivity.class);
        startActivityForResult(intent, REQUEST_CONSTRUCTIONUNITLIST_ACTIVITY);
    }

//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//        switch (requestCode) {
//            case PhotoUtil.REQUESTCODE_GET_PHOTO: {
//                String path = getExternalCacheDir().getPath();
//                String name = "output.png";
//                startActivityForResult(PhotoUtil.cutForCamera(mContext,path, name), PhotoUtil.REQUESTCODE_CUT_IMAGE);
//            }
//                break;
//            case PhotoUtil.REQUESTCODE_TAKE_PHOTO:
//                String path = getExternalCacheDir().getPath();
//                String name = "output.png";
//                startActivityForResult(PhotoUtil.cutForCamera(mContext,path, name), PhotoUtil.REQUESTCODE_CUT_IMAGE);
//                break;
//            case PhotoUtil.REQUESTCODE_CUT_IMAGE:
//                //获取裁剪后的图片，并显示出来
//                try {
//                    Bitmap bitmap = BitmapFactory.decodeStream(getContentResolver().openInputStream(PhotoUtil.mCutUri));
//                    uploadPhoto(bitmap, PhotoUtil.mCutUri);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//
//                break;
//
//            default:
//
//                break;
//        }
//    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(PhotoUploadResulte resulte) {
        dismissProgress();
        showToast(resulte.getMessge());
        if (resulte.isIsok()) {
            // todo 图片上传成功
            String imageUri = SettingManager.getInstance().getBaseUrl() + resulte.getImageUrl();
            initImage(imageUri);
        } else {
            initImage(resulte.getImageUrl());
        }
    }


    private void initImage(String url) {
        Log.i("000", "______图片地址：" + url);
        images.add(0, url);
        imageAdapter.notifyDataSetChanged();
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this);
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().unregister(this);
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_add_notice;
    }


    private AMapLocationClient locationClient = null;
    private AMapLocationClientOption locationOption = null;
    private void initLocation(){
        //初始化client
        locationClient = new AMapLocationClient(this.getApplicationContext());
        locationOption = getDefaultOption();
        //设置定位参数
        locationClient.setLocationOption(locationOption);
        // 设置定位监听
        locationClient.setLocationListener(locationListener);

    }
    /**
     * 默认的定位参数
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private AMapLocationClientOption getDefaultOption() {
        AMapLocationClientOption mOption = new AMapLocationClientOption();
        mOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);//可选，设置定位模式，可选的模式有高精度、仅设备、仅网络。默认为高精度模式
        mOption.setGpsFirst(false);//可选，设置是否gps优先，只在高精度模式下有效。默认关闭
        mOption.setHttpTimeOut(30000);//可选，设置网络请求超时时间。默认为30秒。在仅设备模式下无效
        mOption.setInterval(10000);//可选，设置定位间隔。默认为2秒
        mOption.setNeedAddress(true);//可选，设置是否返回逆地理地址信息。默认是true
        mOption.setOnceLocation(false);//可选，设置是否单次定位。默认是false
        mOption.setOnceLocationLatest(false);//可选，设置是否等待wifi刷新，默认为false.如果设置为true,会自动变为单次定位，持续定位时不要使用
        AMapLocationClientOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTP);//可选， 设置网络请求的协议。可选HTTP或者HTTPS。默认为HTTP
        mOption.setSensorEnable(false);//可选，设置是否使用传感器。默认是false
        mOption.setWifiScan(true); //可选，设置是否开启wifi扫描。默认为true，如果设置为false会同时停止主动刷新，停止以后完全依赖于系统刷新，定位位置可能存在误差
        mOption.setLocationCacheEnable(true); //可选，设置是否使用缓存定位，默认为true
        mOption.setGeoLanguage(AMapLocationClientOption.GeoLanguage.DEFAULT);//可选，设置逆地理信息的语言，默认值为默认语言（根据所在地区选择语言）
        return mOption;
    }
    /**
     * 定位监听
     */
    AMapLocationListener locationListener = new AMapLocationListener() {
        @Override
        public void onLocationChanged(AMapLocation location) {
            if (null != location) {
                locationdata = location;
                //errCode等于0代表定位成功，其他的为定位失败，具体的可以参照官网定位错误码说明
                if (location.getErrorCode() == 0) {
                    address=location.getAddress();
                    stopLocation();
                    destroyLocation();
                }
            } else {
                showToast("定位失败");
            }
        }
    };
    /**
     * 开始定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void startLocation() {
        // 设置定位参数
        locationClient.setLocationOption(locationOption);
        // 启动定位
        locationClient.startLocation();
    }
    /**
     * 停止定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void stopLocation() {
        // 停止定位
        locationClient.stopLocation();
    }

    /**
     * 销毁定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void destroyLocation() {
        if (null != locationClient) {
            /**
             * 如果AMapLocationClient是在当前Activity实例化的，
             * 在Activity的onDestroy中一定要执行AMapLocationClient的onDestroy
             */
            locationClient.onDestroy();
            locationClient = null;
            locationOption = null;
        }
    }
    @Override
    protected void onResume() {
        super.onResume();
        //初始化定位
        initLocation();
        //启动定位
        startLocation();
    }
}
