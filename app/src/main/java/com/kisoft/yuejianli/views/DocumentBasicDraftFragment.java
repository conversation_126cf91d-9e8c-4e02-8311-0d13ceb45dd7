package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.SelectDateAdapter;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.IRequestCallback;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.api.OkHttpRequestManager;
import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.contract.DocumentBasicDraftContract;
import com.kisoft.yuejianli.entity.ArchivesDocListBean;
import com.kisoft.yuejianli.entity.ArchivesDto;
import com.kisoft.yuejianli.entity.ArchivesDtos;
import com.kisoft.yuejianli.entity.ArchivesTempletDto;
import com.kisoft.yuejianli.entity.EnclosureListDto;
import com.kisoft.yuejianli.entity.ToArchivesBean;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.model.DocumentBasicDraftModel;
import com.kisoft.yuejianli.presenter.DocumentBasicDraftPresenter;
import com.kisoft.yuejianli.ui.MzMonthView;
import com.kisoft.yuejianli.ui.YFileListView;
import com.kisoft.yuejianli.ui.YTextViewCell;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.FileUtil;
import com.kisoft.yuejianli.utils.photopicker.BGAPhotoHelper1;
import com.kisoft.yuejianli.views.watermark.ImageUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import cn.bingoogolapple.photopicker.util.BGAPhotoHelper;
import cn.bingoogolapple.photopicker.util.BGAPhotoPickerUtil;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Description: 公文起草 基本信息
 * Author     : yanlu
 * Date       : 2019/1/4 16:31
 */
public class DocumentBasicDraftFragment extends BaseFragment<DocumentBasicDraftModel, DocumentBasicDraftPresenter>
        implements DocumentBasicDraftContract.DocumentBasicDraftViewContract{

    @BindView(R.id.et_reply_time)
    EditText etReplyTime;
    @BindView(R.id.tv_document_dep)
    TextView  tvDocumentDep;
    @BindView(R.id.tv_document_basic_date)
    TextView tvDocumentBasicDate;
    @BindView(R.id.et_document_person)
    EditText etDocumentPerson;
    @BindView(R.id.et_document_basic_font_num)
    EditText etDocumentBasicFontNum;
    @BindView(R.id.et_document_basic_num)
    EditText etDocumentBasicNum;
    @BindView(R.id.spinner_document_basic_level)
    Spinner  spinnerDocumentBasicLevel;
    @BindView(R.id.spinner_document_basic_term)
    Spinner  spinnerDocumentBasicTerm;
    @BindView(R.id.spinner_document_basic_urgent)
    Spinner  spinnerDocumentBasicUrgent;
    @BindView(R.id.et_document_basic_main)
    EditText etDocumentBasicMain;
    @BindView(R.id.et_document_basic_cc)
    EditText etDocumentBasicCc;
    @BindView(R.id.tv_document_basic_user)
    TextView tvDocumentBasicUser;
    @BindView(R.id.tv_document_basic_take_date)
    TextView tvDocumentBasicTakeDate;
    @BindView(R.id.tv_document_basic_draft)
    TextView tvDocumentBasicDraft;
    @BindView(R.id.tv_document_basic_review)
    TextView tvDocumentBasicReview;
    @BindView(R.id.tv_document_basic_verify)
    TextView tvDocumentBasicVerify;
    @BindView(R.id.tv_document_basic_proof)
    TextView tvDocumentBasicProof;
    @BindView(R.id.tv_document_basic_sign)
    TextView tvDocumentBasicSign;
    @BindView(R.id.tv_document_basic_lssue)
    TextView tvDocumentBasicLssue;
    @BindView(R.id.arcBody)
    YTextViewCell arcBody;

    @BindView(R.id.y_doc_list)
    com.kisoft.yuejianli.ui.YFileListView mDocView;// 文档
    @BindView(R.id.y_file_list)
    com.kisoft.yuejianli.ui.YFileListView mFileView;// 附件


    Unbinder unbinder;
    private UserInfo   userInfo;
    //private ArcWorkDTO dto;

    private DocumentBasicDraftModel     model;
    private DocumentBasicDraftPresenter presenter;

    /**日期列表**/
    private ListView lv_date;
    private List<String> list_date =new ArrayList<>();
    private SelectDateAdapter adapter_date;

    private int yearArray[]=new int[10];
    private String yearArray_str[]=new String[yearArray.length];

    private ArchivesTempletDto.ArchivesTempletDtoBean dto;

    private ArrayAdapter<String> securityAdapter,urgentAdapter;

    private ArchivesDtos dtos;

    private final int DRAFT = 100;
    private final int REVIEW = 200;
    private final int VERIFY = 300;
    private final int PROOF = 400;
    private final int SIGN = 500;
    private final int LSSUE = 600;
    private final int FACTFILE = 700;

    private String securityValue = "1";
    private String cfield8 = "";//保密期限
    private String urgent = "1";

    private boolean mIsApply = true;
    private String formkey = "";
    private ArrayList<EnclosureListDto> mDocList = new ArrayList<>();

    @Override
    public int getRootView() {
        return R.layout.fragment_document_basic_draft;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new DocumentBasicDraftModel(mContext);
        presenter = new DocumentBasicDraftPresenter(this, model);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mRootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, mRootView);
        initData();
        initView();
        filMethod();
        return mRootView;
    }

    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        dto = (ArchivesTempletDto.ArchivesTempletDtoBean) getArguments().getSerializable("DTO");
        presenter.toArchivesAdd(dto.getTemId(),dto.getFmoduleId());
        presenter.getSecAndUrg();
        initDialog();
    }

    private void initView() {

        tvDocumentBasicDate.setText(DateUtil.getTodayDate());
        tvDocumentDep.setText(userInfo.getCompanyShortName());
        tvDocumentBasicUser.setText(userInfo.getName());
        tvDocumentBasicTakeDate.setText(DateUtil.getTodayDate());
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    @OnClick({R.id.tv_sub,R.id.et_reply_time,R.id.ll_document_basic_draft, R.id.ll_document_basic_review, R.id.ll_document_basic_verify, R.id.ll_document_basic_proof, R.id.ll_document_basic_sign, R.id.ll_document_basic_lssue})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.et_reply_time:
                list_date.clear();
                for (int i = 0; i < yearArray_str.length; i++) {
                    list_date.add(yearArray_str[i]);
                }
                popupWindow.showAsDropDown(etReplyTime,-MzMonthView.dipToPx(mContext,15), MzMonthView.dipToPx(mContext,15));
                //显示popupWindow时屏幕为半透明
                backgroundAlpha(0.5f);
                break;
            case R.id.ll_document_basic_draft:
                getPersonInfo(DRAFT);
                break;
            case R.id.ll_document_basic_review:
                getPersonInfo(REVIEW);
                break;
            case R.id.ll_document_basic_verify:
                getPersonInfo(VERIFY);
                break;
            case R.id.ll_document_basic_proof:
                getPersonInfo(PROOF);
                break;
            case R.id.ll_document_basic_sign:
                getPersonInfo(SIGN);
                break;
            case R.id.ll_document_basic_lssue:
                getPersonInfo(LSSUE);
                break;
//            case R.id.btn_document_basic_factfile:
//                getPersonInfo(FACTFILE);
//                break;
            case R.id.tv_sub:
                uploadMulFile();
                break;
        }
    }

    /**提交**/
    private void submit() {
        ArchivesDto dto = new ArchivesDto();
        dto.setTemId(this.dto.getTemId());
        dto.setCfield3(userInfo.getCompanyShortName());
        dto.setCreatorid(userInfo.getId());
        /**责任者**/
        String documentPerson = etDocumentPerson.getText().toString().trim();
        if (TextUtils.isEmpty(documentPerson)) {
            showToast("请输入责任者");
            return;
        } else {
            dto.setCfield5(documentPerson);
        }
        /**文件字号**/
        String documentBasicFontNum = etDocumentBasicFontNum.getText().toString().trim();
        if (TextUtils.isEmpty(documentBasicFontNum)) {
            showToast("请输入文件字号");
            return;
        } else {
            dto.setArcNo(documentBasicFontNum);
        }
        /**标题**/
        String documentBasicNum = etDocumentBasicNum.getText().toString().trim();
        if (TextUtils.isEmpty(documentBasicNum)) {
            showToast("请输入标题");
            return;
        } else {
            dto.setTitle(documentBasicNum);
        }
        if (securityValue.equals("1")) {
            showToast("请选择密级");
            return;
        } else {
            dto.setSecurity(securityValue);
            dto.setSecurity(dtos.getSecurity());
            dto.setSesige(dtos.getSesige());
        }
        if (TextUtils.isEmpty(cfield8)) {
            showToast("请选择保密期限");
            return;
        } else {
            dto.setCfield8(cfield8);
        }
        if (urgent.equals("1")) {
            showToast("请选择紧急程度");
            return;
        } else {
            dto.setUrgent(urgent);
            dto.setUrgent(dtos.getUrgent());
            dto.setUrsige(dtos.getUrsige());
        }
        /**主送**/
        String documentBasicMain = etDocumentBasicMain.getText().toString().trim();
        if (TextUtils.isEmpty(documentBasicMain)) {
            showToast("请输入主送人");
            return;
        } else {
            dto.setCfield6(documentBasicMain);
        }
        /**抄送**/
        String documentBasicCc = etDocumentBasicCc.getText().toString().trim();
        if (TextUtils.isEmpty(documentBasicCc)) {
            showToast("请输入抄送人");
            return;
        } else {
            dto.setCfield13(documentBasicCc);
        }
        /**承办人**/
        dto.setCfield4(userInfo.getName());
        /**承办日期**/
        dto.setCreatedate(DateUtil.getTodayDate());
        /**拟稿人**/
        String draft = tvDocumentBasicDraft.getText().toString().trim();
        /**核稿人**/
        String review = tvDocumentBasicReview.getText().toString().trim();
        /**审稿人**/
        String verify = tvDocumentBasicVerify.getText().toString().trim();
        /**校对人**/
        String proof = tvDocumentBasicProof.getText().toString().trim();
        /**会签人**/
        String sign = tvDocumentBasicSign.getText().toString().trim();
        /**签发人**/
        String lssue = tvDocumentBasicLssue.getText().toString().trim();

        dto.setDfield1(DateUtil.getTodayDate());
        dto.setCfield9(draft);
        dto.setCfield10(review);
        dto.setCfield11(verify);
        dto.setCfield12(proof);
        dto.setCfield14(sign);
        dto.setCfield17(lssue);
        dto.setArcBody(arcBody.getEtContent().getText().toString().trim());

        dto.setArcId(formkey);

        //todo 附件
        presenter.addArchives(dto);
    }

    public void getPersonInfo(int requestCode){
        Intent intent = new Intent();
        intent.setClass(mContext, CompanyOrgInfoActivity.class);
        startActivityForResult(intent, requestCode);
    }


    PopupWindow popupWindow =null;
    private void initDialog() {
        // 一个自定义的布局，作为显示的内容
        View view = LayoutInflater.from(mContext).inflate(R.layout.l_dialog_select_date, null);

        // 下面是两种方法得到宽度和高度 getWindow().getDecorView().getWidth()
        popupWindow= new PopupWindow(view,
                MzMonthView.dipToPx(mContext,80),
                MzMonthView.dipToPx(mContext,145));
        // 设置popWindow弹出窗体可点击，这句话必须添加，并且是true
        popupWindow.setFocusable(true);

        //物理返回键有响应
        popupWindow.setBackgroundDrawable(new BitmapDrawable());

        //窗口关闭事件
        popupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                backgroundAlpha(1f);
            }
        });

        // 设置popWindow的显示和消失动画
        //        popupWindow.setAnimationStyle(R.style.mypoppopupWindow_anim_style);
        lv_date= (ListView) view.findViewById(R.id.lv_dialog_select_list);
        adapter_date=new SelectDateAdapter(mContext,list_date);
        lv_date.setAdapter(adapter_date);
        lv_date.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                etReplyTime.setText(yearArray[position]+"");
            }
        });
    }

    /**
     * 设置添加屏幕的背景透明度
     */
    public void backgroundAlpha(float bgAlpha)
    {
        WindowManager.LayoutParams lp = getActivity().getWindow().getAttributes();
        lp.alpha = bgAlpha; //0.0-1.0
        getActivity().getWindow().setAttributes(lp);
    }

    @Override
    public void showArchivesAdd(ToArchivesBean dto) {
        etReplyTime.setText(dto.getCurrYear());


    }

    @Override
    public void showSecAndUrg(final ArchivesDtos dtos) {
        this.dtos = dtos;
        /**密级数据**/
        String[] m_Countries = new String[dtos.getSecurityList().size()];
        List<String> securityList = new ArrayList<>();
        for (int i = 0; i < dtos.getSecurityList().size(); i++) {
            securityList.add(dtos.getSecurityList().get(i).getLabel());
        }
        if (securityList.size()>0) {
            securityList.toArray(m_Countries);
        }

        securityAdapter = new ArrayAdapter<>(mContext, android.R.layout.simple_spinner_item, m_Countries);
        spinnerDocumentBasicLevel.setAdapter(securityAdapter);
        securityAdapter.notifyDataSetChanged();
        /**紧急数据**/
        String[] m_datas= new String[dtos.getUrgentList().size()];
        List<String> m_dataList = new ArrayList<>();
        for (int i = 0; i < dtos.getUrgentList().size(); i++) {
            m_dataList.add(dtos.getUrgentList().get(i).getLabel());
        }
        if (m_dataList.size()>0) {
            m_dataList.toArray(m_datas);
        }

        urgentAdapter = new ArrayAdapter<>(mContext, android.R.layout.simple_spinner_item, m_datas);
        spinnerDocumentBasicUrgent.setAdapter(urgentAdapter);
        urgentAdapter.notifyDataSetChanged();

        spinnerDocumentBasicLevel.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                Log.i("yanlu",position+",Level");
                securityValue = dtos.getSecurityList().get(position).getValue();
                dtos.setSecurity(securityValue);
                dtos.setSesige(dtos.getSecurityList().get(position).getLabel());

            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        });
        spinnerDocumentBasicTerm.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                Log.i("yanlu",position+",term");
                String[] cfi = getResources().getStringArray(R.array.array_document_basic_term);
                cfield8 = cfi[position];
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
        spinnerDocumentBasicUrgent.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                Log.i("yanlu",position+",urgent");
                urgent = dtos.getUrgentList().get(position).getValue();
                dtos.setUrgent(urgent);
                dtos.setUrsige(dtos.getUrgentList().get(position).getLabel());
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    @Override
    public void addArchives(String str) {
        String[] backs = str.split(",");
        if (backs != null && backs.length == 2) {
            Log.e("back", backs[1]);
            ((DocumentDetailActivity)getContext()).setBackId(backs[1]);
            showToast("提交成功！");
        }
    }

    // 获取文档列表
    private void getAddInformation(String formkey){
        HashMap<String, Object> paras = new HashMap<>();
        paras.put("temId", dto.getTemId());
        paras.put("arcId", formkey);
        Api.getGbkApiserver().getArchivesDocList(Constant.HTTP_GET_ARCHIVES_DOCUMENT_LIST, paras).enqueue(new Callback<NetworkResponse<List<ArchivesDocListBean>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<ArchivesDocListBean>>> call,
                                   Response<NetworkResponse<List<ArchivesDocListBean>>> response) {
                if (response.body().getData() != null) {
                    mDocList.clear();
                    for (int i = 0; i < response.body().getData().size(); i++) {
                        ArchivesDocListBean bean = response.body().getData().get(i);
                        mDocList.add(new EnclosureListDto(bean.getFileName(),bean.getFilePath(),bean.getEditPath(),bean.getId()));
                    }
                    mDocView.setList(mDocList);
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<List<ArchivesDocListBean>>> call, Throwable t) {

            }
        });
    }

    private ArrayList<EnclosureListDto> mFileList = new ArrayList<>();
    private boolean isTakePhoto;
    private BGAPhotoHelper mPhotoHelper;
    private void filMethod(){

        // 拍照后照片的存放目录，改成你自己拍照后要存放照片的目录。如果不传递该参数的话就没有拍照功能
        File takePhotoDir = new File(Environment.getExternalStorageDirectory(), "BGAPhotoPickerTakePhoto");
        mPhotoHelper = new BGAPhotoHelper(takePhotoDir);
        mFileView.setApply(mIsApply);
        // 点击添加附件
        mFileView.getAddFileBtn().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showFileSelect();
            }
        });
        mFileView.setFileOperateContract(new YFileListView.FileOperateContract() {
            @Override
            public void openFile(int index) {
                mFileList.remove(index);
                mFileView.setList(mFileList);
            }
        });
        // 打开附件
        mFileView.setOpenFileContract(new YFileListView.OpenFileContract() {
            @Override
            public void openFile(EnclosureListDto dto) {
                WebActivity.launch(getActivity(),dto);
            }
        });

        // 打开文档
        mDocView.setApply(false);
        mDocView.setOpenFileContract(new YFileListView.OpenFileContract() {
            @Override
            public void openFile(EnclosureListDto dto) {
                getDocumentInformation(dto.getIds());
            }
        });

        // 获取主键
        getPrimarykey(new GetPrimarykeyContract() {
            @Override
            public void getPrimarykey(String str) {
                formkey = str;
                getAddInformation(formkey);
            }
        });
    }

    private void showFileSelect() {
        // 空照片 ，添加
        String[] str = new String[]{"系统相机", "手机相册", "手机文件"};
        AlertDialog.Builder ab = new AlertDialog.Builder(mContext);
        ab.setItems(str, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                switch (which) {
                    case 0://相机
                        isTakePhoto = true;
                        takePhoto();
                        break;
                    case 1://相册
                        isTakePhoto = false;
                        getPhoto();
                        break;
                    case 2://手机文件
                        getFile();
                        break;
                }
            }
        });
        ab.show();
    }

    /**
     * 相册获得照片
     */
    private void getPhoto() {
        startActivityForResult(mPhotoHelper.getChooseSystemGalleryIntent(), Constant.REQUEST_CODE_CHOOSE_PHOTO);
    }

    /**
     * 拍照
     */
    private void takePhoto() {
        try {
            startActivityForResult(mPhotoHelper.getTakePhotoIntent(), Constant.REQUEST_CODE_TAKE_PHOTO);
        } catch (Exception e) {
            BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_take_photo);
        }
    }

    /**
     * 打开文件选择器
     */
    private void getFile() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("*/*");// 任意类型文件
        this.startActivityForResult(intent, Constant.REQUEST_CODE_FILE_SELECT);
    }

    BGAPhotoHelper1 mPhotoHelper1 = new BGAPhotoHelper1();
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQUEST_CODE_CHOOSE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getFilePathFromUri(data.getData()), 800, 800), Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_TAKE_PHOTO) {
            if (resultCode == Activity.RESULT_OK) {
                try {
                    startActivityForResult(mPhotoHelper1.getCropIntent(mPhotoHelper.getCameraFilePath(), 800, 800),
                            Constant.REQUEST_CODE_CROP);
                } catch (Exception e) {
                    mPhotoHelper.deleteCameraFile();
                    mPhotoHelper.deleteCropFile();
                    BGAPhotoPickerUtil.show(R.string.bga_pp_not_support_crop);
                    e.printStackTrace();
                }
            }
        } else if (requestCode == Constant.REQUEST_CODE_CROP) {
            if (resultCode == Activity.RESULT_OK) {
                // getCropIntent  获取裁剪完图片的路径
                String photoPath = mPhotoHelper1.getCropFilePath();
                //showToast(photoPath);
                File file = new File(mPhotoHelper1.getCropFilePath());
                double fileSize = FileUtil.getFileOrFilesSize(photoPath, 3);
                Log.i("fileSize", fileSize + "");

                //showToast(photoPath);
                if (fileSize > 1) {
                    ImageUtil.compressImageByLuban(photoPath);
                    String s = ImageUtil.compressImage(photoPath);
                    mFileList.add(new EnclosureListDto(file.getName(), s));
                } else {
                    mFileList.add(new EnclosureListDto(file.getName(), photoPath));
                }
                mFileView.setList(mFileList);
            } else {
                mPhotoHelper.deleteCameraFile();
                mPhotoHelper.deleteCropFile();
            }
        } else if (requestCode == Constant.REQUEST_CODE_FILE_SELECT) {
            String path = "";
            if (data == null) {
                // 用户未选择任何文件，直接返回
                return;
            }
            path = FileUtil.getRealPath(this.getContext(), data.getData());
            File file = new File(path);
            mFileList.add(new EnclosureListDto(file.getName(), path));
            mFileView.setList(mFileList);
            //showToast(path);
        }else if (requestCode == SIGN) {
            if (data != null) {
                StringBuffer name = new StringBuffer();
                StringBuffer id = new StringBuffer();
                List<String> ids = new ArrayList<>();
                List<String> names = new ArrayList<>();
                ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                if (names != null && names.size() > 0) {
                    for (int i = 0; i < names.size(); i++){
                        if (i != 0) {
                            name.append(",");
                        }
                        name.append(names.get(i));
                    }
                    tvDocumentBasicSign.setText(name.toString());

                    for (int i = 0; i < ids.size(); i++) {
                        if (i != 0) {
                            id.append(",");
                        }
                        id.append(ids.get(i));
                    }
                }
            }
        }else {
            TextView tv = null;
            switch (requestCode) {
                case DRAFT:  //拟稿人
                    tv = tvDocumentBasicDraft;
                    break;
                case REVIEW:
                    tv = tvDocumentBasicReview;
                    break;
                case VERIFY:
                    tv = tvDocumentBasicVerify;
                    break;
                case PROOF:
                    tv = tvDocumentBasicProof;
                    break;
                case LSSUE:
                    tv = tvDocumentBasicLssue;
                    break;
                case FACTFILE:
//                tv = tvDocumentBasicFactfile;
                    break;
            }
            if (data!= null){
                List<String> ids = new ArrayList<>();
                List<String> names = new ArrayList<>();
                ids.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_ID));
                names.addAll(data.getStringArrayListExtra(Constant.INTENT_KEY_ORG_NAME));
                if (names != null  && names.size() >0   ){
                    //plan.setConName(names.get(0));
                    if (tv != null) {
                        tv.setText(names.get(0));
                    }
                    //masterGuidName = names.get(0);
                    //masterGuid = ids.get(0);
                }
            }
        }

    }

    private void uploadMulFile(){

        if (mFileList.isEmpty()) {
            submit();
//            presenter.submitApply(jkInfo);
        } else {
            if (formkey.isEmpty()) {
                getPrimarykey(new GetPrimarykeyContract() {
                    @Override
                    public void getPrimarykey(String str) {
                        formkey = str;
                    }
                });
                showToast("未获取到主键");
                return;
            }

            String s = DateUtil.dateToString(new Date(), DateUtil.YM1);
            HashMap<String, String> paras = new HashMap<>();
            paras.put("filePath", "roa/archives/" + s + "/annex");
            paras.put("businessId", formkey);
            paras.put("businessType", "t_doc");
            OkHttpRequestManager.getInstance().uploadMulFile(mFileList, paras, new IRequestCallback() {
                @Override
                public void onSuccess(String response) {
                    Log.i("upload", "onSuccess: ");
                    submit();
                }

                @Override
                public void onFailure(Throwable throwable) {
                    Log.i("upload", "onFailure: ");
                }
            });
        }
    }


    // 获取文档详情
    private void getDocumentInformation(String ids){
        HashMap<String, Object> paras = new HashMap<>();
        paras.put("is", ids);
        Api.getGbkApiserver().getArchivesDocDetail(Constant.HTTP_GET_ARCHIVES_DOC_DETAIL, paras).enqueue(new Callback<NetworkResponse<ArchivesDocListBean>>() {
            @Override
            public void onResponse(Call<NetworkResponse<ArchivesDocListBean>> call,
                                   Response<NetworkResponse<ArchivesDocListBean>> response) {
                if (response.body().getData() != null) {

                    ArchivesDocListBean bean = response.body().getData();
                    EnclosureListDto enclosureListDto = new EnclosureListDto(bean.getFileName(), bean.getFilePath(),
                            bean.getEditPath(), bean.getId());
                    WebActivity.launch(getActivity(),enclosureListDto,true);
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<ArchivesDocListBean>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }

}

