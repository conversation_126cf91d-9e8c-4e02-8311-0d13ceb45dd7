package com.kisoft.yuejianli.views;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import com.kisoft.yuejianli.base.BaseFragment;
import com.kisoft.yuejianli.entity.ProjectInfo;
import com.kisoft.yuejianli.entity.QualityRefromSuperNotice;
import com.kisoft.yuejianli.entity.SuperNoticeAnswer;
import com.kisoft.yuejianli.entity.SurpervisionNotice;
import com.kisoft.yuejianli.entity.UserInfo;
import com.kisoft.yuejianli.manager.SettingManager;
import com.kisoft.yuejianli.utils.DateUtil;
import com.kisoft.yuejianli.utils.StringUtil;

import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/5/16.
 */

public class SuperNoticeInfoFragment extends BaseFragment {

    private TextView tvProjectName;
    private TextView tvTableNum;
    private TextView tvConsCompany;
    private TextView tvSupOrg;
    private TextView tvSup;
    private TextView tvYear;
    private TextView tvMonth;
    private TextView tvDay;

    /**
     * 通知部分
     */
    private View noticeView;
    private EditText etNoticeReason;
    private EditText etNoticeContent;

    /**
     * 整改通知部分
     */
    private View reformView;
    private TextView tvReportCompleteTime;
    private TextView tvReportTableName;
    private TextView tvCompleteWork;
    private TextView tvReformStand;
    private TextView tvStandItem;
    private TextView tvReformEndTime;
    private TextView tvReformDemand;


    private UserInfo userInfo;
    private ProjectInfo projectInfo;

    private String type;
    private SurpervisionNotice notice;
    private QualityRefromSuperNotice qualityRefrom;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        initView(inflater);
        initData();
        return mRootView;
    }

    private void initView(LayoutInflater inflater) {
        mRootView = inflater.inflate(getRootView(), null);
        tvProjectName = mRootView.findViewById(R.id.tv_project);
        tvTableNum = mRootView.findViewById(R.id.tv_table_num);
        tvConsCompany = mRootView.findViewById(R.id.tv_construction);
        tvSupOrg = mRootView.findViewById(R.id.tv_sup_org);
        tvSup = mRootView.findViewById(R.id.tv_sup);
        tvYear = mRootView.findViewById(R.id.tv_year);
        tvMonth = mRootView.findViewById(R.id.tv_month);
        tvDay = mRootView.findViewById(R.id.tv_day);
        // 通知
        noticeView = mRootView.findViewById(R.id.ll_notice_view);
        etNoticeReason = mRootView.findViewById(R.id.et_reason);
        etNoticeContent = mRootView.findViewById(R.id.et_content);
        // 整改
        reformView = mRootView.findViewById(R.id.ll_quality_reform_view);
        tvReportCompleteTime = mRootView.findViewById(R.id.tv_report_complete_time);
        tvReportTableName = mRootView.findViewById(R.id.tv_report_table_name);
        tvCompleteWork = mRootView.findViewById(R.id.tv_report_work_name);
        tvReformStand = mRootView.findViewById(R.id.tv_reform_stand_num);
        tvStandItem = mRootView.findViewById(R.id.tv_reform_stand_item);
        tvReformEndTime = mRootView.findViewById(R.id.tv_reform_end_time);
        tvReformDemand = mRootView.findViewById(R.id.tv_reform_demand);

    }


    private void initData() {
        userInfo = SettingManager.getInstance().getUserInfo();
        projectInfo = SettingManager.getInstance().getProject();
        if (isAdded()) {
            SuperNoticeAnswerActivity parentActivity = (SuperNoticeAnswerActivity) getActivity();
            type = parentActivity.getAnswerType();
            if (StringUtil.isEqual(SuperNoticeAnswer.ANSWER_TYPE_SUPER_NOTICE, type)) {
                notice = parentActivity.getNotice();
            } else if (StringUtil.isEqual(SuperNoticeAnswer.ANSWER_TYPE_QUALITY_REFORM, type)) {
                qualityRefrom = parentActivity.getQualityReform();
            }
        }
        initBusinessInfo();
    }

    private void initBusinessInfo(){
        tvProjectName.setText(projectInfo.getProjectName());
        if(StringUtil.isEqual(SuperNoticeAnswer.ANSWER_TYPE_SUPER_NOTICE, type)){   // 监理通知
            noticeView.setVisibility(View.VISIBLE);
            reformView.setVisibility(View.GONE);
            tvTableNum.setText(notice.getSnNumber());
            tvConsCompany.setText(notice.getCompany());
            etNoticeReason.setEnabled(false);
            etNoticeReason.setText(notice.getRenson());
            etNoticeContent.setEnabled(false);
            etNoticeContent.setText(notice.getContent());
            tvSupOrg.setText(notice.getSuperUnit());
            tvSup.setText(notice.getCreateTime());
            tvYear.setText(DateUtil.getTimeStrByTime(notice.getCreateTime(), DateUtil.YMD, 1));
            tvMonth.setText(DateUtil.getTimeStrByTime(notice.getCreateTime(), DateUtil.YMD, 2));
            tvDay.setText(DateUtil.getTimeStrByTime(notice.getCreateTime(), DateUtil.YMD,3));
        }else if(StringUtil.isEqual(SuperNoticeAnswer.ANSWER_TYPE_QUALITY_REFORM, type)){  //整改通知

            reformView.setVisibility(View.VISIBLE);
            noticeView.setVisibility(View.GONE);
            tvTableNum.setText(qualityRefrom.getNumber());
            tvConsCompany.setText(qualityRefrom.getBuildCompany());
            tvReportCompleteTime.setText(qualityRefrom.getReportTime());
            tvReportTableName.setText(qualityRefrom.getReportForm());
            tvCompleteWork.setText(qualityRefrom.getSubProject());
            tvReformStand.setText(qualityRefrom.getProgramme());
            tvStandItem.setText(qualityRefrom.getProgrammeMumber());
            tvReformEndTime.setText(qualityRefrom.getRectificationTime());
            tvReformDemand.setText(qualityRefrom.getRoContent());

            tvSupOrg.setText(qualityRefrom.getSupCompany());
            tvSup.setText(qualityRefrom.getSupDirectorName());
            tvYear.setText(DateUtil.getTimeStrByTime(qualityRefrom.getVerifierTime(), DateUtil.YMD,1));
            tvMonth.setText(DateUtil.getTimeStrByTime(qualityRefrom.getVerifierTime(), DateUtil.YMD,2));
            tvDay.setText(DateUtil.getTimeStrByTime(qualityRefrom.getVerifierTime(), DateUtil.YMD,3));
        }
    }

    @Override
    public int getRootView() {
        return R.layout.fragment_super_notice;
    }
}
