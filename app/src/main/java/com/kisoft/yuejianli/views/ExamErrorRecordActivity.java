package com.kisoft.yuejianli.views;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.widget.Toolbar;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 错题本
 */
public class ExamErrorRecordActivity extends BaseActivity {

    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_action)
    ImageView ivAction;
    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.tool_bar)
    Toolbar toolBar;
    @BindView(R.id.tvAll)
    TextView tvAll;
    @BindView(R.id.tvTest)
    TextView tvTest;
    @BindView(R.id.tvPractice)
    TextView tvPractice;
    @BindView(R.id.tvOnline)
    TextView tvOnline;

    public static void launch(Activity activity){
        Intent intent=new Intent(activity,ExamErrorRecordActivity.class);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        init();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_exam_error_record;
    }

    private void init(){
        tvTitle.setText("错题本");
    }

    @OnClick({R.id.iv_back,R.id.tvAll,R.id.tvTest,R.id.tvPractice,R.id.tvOnline})
    void buttonClick(View view){
        switch (view.getId()){
            case R.id.iv_back:
                finish();
                break;
            case R.id.tvAll:
                showToast("全部错题");
                break;
            case R.id.tvTest:
                showToast("练习错题");
                break;
            case R.id.tvPractice:
                showToast("模拟考试错题");
                break;
            case R.id.tvOnline:
                showToast("在线考试错题");
                break;
        }
    }
}
