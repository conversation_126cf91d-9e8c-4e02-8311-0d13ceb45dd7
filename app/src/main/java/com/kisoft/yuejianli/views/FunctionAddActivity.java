package com.kisoft.yuejianli.views;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.widget.TextView;

import com.kisoft.yuejianli.adpter.FunctionAdapter;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.base.Constant;
import com.kisoft.yuejianli.entity.ProjectContent;
import com.kisoft.yuejianli.manager.SettingManager;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import com.kisoft.yuejianli.R;

/**
 * Created by tudou on 2018/6/7.
 */

public class FunctionAddActivity extends BaseActivity implements FunctionAdapter.OnFunctionChangedListener {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;


    private List<ProjectContent> funcations = new ArrayList<>();
    private FunctionAdapter mAdapter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initData();
        initView();
    }

    private void initView(){
        tvTitle.setText("常用功能");
        setResult(Constant.REQUEST_CODE_ADD_FUNCTION);
        if(rvContent.getLayoutManager() == null){
            LinearLayoutManager manager = new LinearLayoutManager(this);
            manager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(manager);
        }
        mAdapter = new FunctionAdapter(R.layout.item_function_add, funcations);
        mAdapter.SetOnFunctionChangedListener(this);
        rvContent.setAdapter(mAdapter);
    }

    private void initData(){
        List<ProjectContent> list = SettingManager.getInstance().getUserFuncation();
        funcations.addAll(list);
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_funcation_add;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }

    @Override
    public void OnFunctionChanged(int position) {
        if(funcations.get(position).isOpen()){
            funcations.get(position).setOpen(false);
        }else {
            funcations.get(position).setOpen(true);
        }
        SettingManager.getInstance().saveUserFuncation(funcations);
        mAdapter.notifyItemChanged(position);
    }
}
