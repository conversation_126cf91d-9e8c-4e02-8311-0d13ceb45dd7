package com.kisoft.yuejianli.views;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.kisoft.yuejianli.R;
import com.kisoft.yuejianli.adpter.LcJiLuAdapter;
import com.kisoft.yuejianli.adpter.LcZhuanFaChaYueActivity;
import com.kisoft.yuejianli.api.Api;
import com.kisoft.yuejianli.api.NetworkResponse;
import com.kisoft.yuejianli.base.BaseActivity;
import com.kisoft.yuejianli.utils.StringUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class YApprovalLogsActivity extends BaseActivity {

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.rv_content)
    RecyclerView rvContent;

    @BindView(R.id.tv_submit)
    TextView tvSubmit;
    @BindView(R.id.iv_action)
    ImageView ivAction;

    private LcJiLuAdapter mJiJuAdapter;
    private List<Map<String,Object>> mJiJuData = new ArrayList<>();

    private LcRiZhiAdapter mRiZhiAdapter;
    private List<Map> mRiZhiData = new ArrayList<Map>();


    private LcZhuanFaAdapter mZhuanFaAdapter;
    private List<Map> mZhuanFaData = new ArrayList<Map>();

    private View empotyView;

    private int mType;
    private String mWfId;
    private String mBusinessId;


    @Override
    public int getLayoutId() {
        return R.layout.activity_yapproval_logs;
    }

    @OnClick(R.id.iv_back)
    public void goBack(){
        finish();
    }
    public static void launch(Activity activity,int type,String wfId,String businessId){

        Intent intent = new Intent(activity, YApprovalLogsActivity.class);
        intent.putExtra("type",type);
        intent.putExtra("wfId",wfId);
        intent.putExtra("businessId",businessId);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mType = getIntent().getIntExtra("type", 0);
        mWfId = getIntent().getStringExtra("wfId");
        mBusinessId = getIntent().getStringExtra("businessId");

        initView();
    }

    private void initView(){
        tvSubmit.setVisibility(View.GONE);
        ivAction.setVisibility(View.GONE);

        empotyView = getLayoutInflater().inflate(R.layout.page_no_data, null);
        if(rvContent.getLayoutManager() == null){
            LinearLayoutManager layoutManager = new LinearLayoutManager(this);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            rvContent.setLayoutManager(layoutManager);
        }

        if (mType == 1){
            tvTitle.setText("流程记录");
            lcJiLu(mWfId);
            mJiJuAdapter = new LcJiLuAdapter(mJiJuData);
            mJiJuAdapter.setEmptyView(empotyView);
            rvContent.setAdapter(mJiJuAdapter);
        }else if (mType == 2){
            tvTitle.setText("审批日志");
            lcRizhi(mBusinessId);
            mRiZhiAdapter = new LcRiZhiAdapter(mRiZhiData);
            mRiZhiAdapter.setEmptyView(empotyView);
            rvContent.setAdapter(mRiZhiAdapter);
        }else if (mType == 3){
            tvTitle.setText("转发记录");
            zhuanfajilu(mBusinessId);
            mZhuanFaAdapter = new LcZhuanFaAdapter(mZhuanFaData);
            mZhuanFaAdapter.setEmptyView(empotyView);
            rvContent.setAdapter(mZhuanFaAdapter);


            mZhuanFaAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseQuickAdapter adapter, View view, int position) {

                    Object object = adapter.getData().get(position);
                    if(object instanceof Map){
                        Map map = (Map) object;
                        String id = map.get("id").toString();
                        LcZhuanFaChaYueActivity.launch(YApprovalLogsActivity.this,id);
                    }
                }
            });
        }

    }


    // 流程记录
    private void lcJiLu(String wfId) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("wfId", wfId);
        Api.getGbkApiserver().getWfTaskList("getWfTaskList", parameters).enqueue(new Callback<NetworkResponse<List<Map<String, Object>>>>() {
            @Override
            public void onResponse(Call<NetworkResponse<List<Map<String, Object>>>> call,
                                   Response<NetworkResponse<List<Map<String, Object>>>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    mJiJuData.clear();
                    mJiJuData.addAll(response.body().getData());
                    mJiJuAdapter.notifyDataSetChanged();
                }
            }
            @Override
            public void onFailure(Call<NetworkResponse<List<Map<String, Object>>>> call, Throwable t) {
                t.printStackTrace();
            }
        });
    }



    // 审批日志
    private void lcRizhi(String businessId) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("businessId", businessId);
        parameters.put("page", "1");
        parameters.put("limit", "100");
        Api.getGbkApiserver().getSysWfApproveLogList("getSysWfApproveLogList", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    mRiZhiData.clear();
                    Map data = (Map) response.body().getData();
                    List<Map> list = (List<Map>) data.get("data");
                    mRiZhiData.addAll(list);
                    mRiZhiAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable throwable) {

            }
        });
    }



    // 转发记录
    private void zhuanfajilu(String businessId) {
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("businessId", businessId);
        parameters.put("page", "1");
        parameters.put("limit", "100");
        Api.getGbkApiserver().getSysForwardRecordList("getSysForwardRecordList", parameters).enqueue(new Callback<NetworkResponse<Object>>() {
            @Override
            public void onResponse(Call<NetworkResponse<Object>> call, Response<NetworkResponse<Object>> response) {
                if (response.body() != null && response.body().getCode() == NetworkResponse.OK) {
                    mZhuanFaData.clear();
                    Map data = (Map) response.body().getData();
                    List<Map> list = (List<Map>) data.get("data");
                    mZhuanFaData.addAll(list);
                    mZhuanFaAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(Call<NetworkResponse<Object>> call, Throwable throwable) {

            }
        });
    }


    // 审批日志
    public class LcRiZhiAdapter extends BaseQuickAdapter<Map, BaseViewHolder> {

        public LcRiZhiAdapter(List<Map> data) {
            super(R.layout.item_contract_list, data);
        }
        @Override
        protected void convert(BaseViewHolder helper, Map item) {
            TextView tvContractNameT = helper.itemView.findViewById(R.id.tv_contract_name_t);
            TextView tvContractName = helper.itemView.findViewById(R.id.tv_contract_name);

            TextView tvContractNumberT = helper.itemView.findViewById(R.id.tv_contract_number_t);
            TextView tvContractNumber = helper.itemView.findViewById(R.id.tv_contract_number);

            TextView tvContractTimeT = helper.itemView.findViewById(R.id.tv_contract_time_t);
            TextView tvContractTime = helper.itemView.findViewById(R.id.tv_contract_time);


            tvContractNameT.setText("操作内容：");
            tvContractName.setText(item.get("context").toString());
            tvContractNumberT.setText("操作人：");
            tvContractNumber.setText(item.get("createName").toString());
            tvContractTimeT.setText("操作时间：");
            tvContractTime.setText(item.get("operationTime").toString());
        }
    }


    // 转发记录
    public class LcZhuanFaAdapter extends BaseQuickAdapter<Map, BaseViewHolder> {

        public LcZhuanFaAdapter(List<Map> data) {
            super(R.layout.item_blgc, data);
        }
        @Override
        protected void convert(BaseViewHolder helper, Map item) {
            TextView tvNo = helper.itemView.findViewById(R.id.tv_no);
            tvNo.setVisibility(View.GONE);
            TextView tvName = helper.itemView.findViewById(R.id.tv_name);
            tvName.setText("转发人：" + item.get("createName").toString());



            TextView tvResult = helper.itemView.findViewById(R.id.tv_result);
            if (StringUtil.isEqual(item.get("state").toString(),"1")) {
                tvResult.setText("状态：已收回");
            }else if (StringUtil.isEqual(item.get("state").toString(),"0")) {
                tvResult.setText("状态：未收回");
            }


            TextView tvYj = helper.itemView.findViewById(R.id.tv_yj);
            tvYj.setText("转发时间：" + item.get("createTime").toString());

            TextView tvStartTime = helper.itemView.findViewById(R.id.tv_start_time);
            tvStartTime.setText("接收人:" + item.get("recipient").toString());

            TextView tvFinishTime = helper.itemView.findViewById(R.id.tv_finish_time);
            tvFinishTime.setText("备注:" + item.get("remark").toString());

        }

    }
}

