<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:paddingLeft="@dimen/dp_72"
        android:paddingRight="@dimen/dp_72">

        <ImageView
            android:id="@+id/iv_left"
            android:layout_width="@dimen/title_bar_hei"
            android:layout_height="@dimen/title_bar_hei"
            android:padding="@dimen/padding"
            android:src="@drawable/ic_left"/>

        <TextView
            android:id="@+id/tv_month"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="2018-03"
            android:textColor="@color/text_main_black"
            android:gravity="center"
            android:textSize="@dimen/sp_14"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/iv_right"
            android:layout_width="@dimen/title_bar_hei"
            android:layout_height="@dimen/title_bar_hei"
            android:padding="@dimen/padding"
            android:src="@drawable/ic_right"/>
    </LinearLayout>

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="60dp">

        <LinearLayout
            android:id="@+id/ll_doing"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:background="@drawable/item_enable_bg"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_doing_num"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="0"
                android:textSize="22sp"
                android:textStyle="bold"
                android:textColor="@color/ic_text_normal"
                android:gravity="center"/>
            <TextView
                android:id="@+id/tv_doing"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="未回复"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/ic_text_normal"
                android:gravity="center"/>

        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_complete"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:background="@drawable/item_enable_bg"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_complete_num"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="0"
                android:textSize="22sp"
                android:textStyle="bold"
                android:textColor="@color/ic_text_normal"
                android:gravity="center"/>
            <TextView
                android:id="@+id/tv_complete"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="已回复"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/ic_text_normal"
                android:gravity="center"/>

        </LinearLayout>



    </LinearLayout>
    <include layout="@layout/line_space_blod"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_quality_count_all"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    </androidx.recyclerview.widget.RecyclerView>

</LinearLayout>