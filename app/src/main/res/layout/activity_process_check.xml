<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <include layout="@layout/total_bar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/padding">

            <LinearLayout
                style="@style/style_form_ll1"
                android:orientation="vertical"
                android:padding="@dimen/dp_0">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="项目名称："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_project"
                    style="@style/tv_form_content"
                    android:ellipsize="end"
                    android:minLines="2"
                    android:hint="项目名称"
                    android:layout_marginBottom="@dimen/dp_10"/>
            </LinearLayout>

            <include layout="@layout/line_space" />

            <!--
                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="@dimen/title_bar_hei"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="match_parent"
                                            android:gravity="center"
                                            android:text="检查部位:"
                                            android:textColor="@color/text_main_black"
                                            android:textSize="@dimen/sp_16" />

                                        <EditText
                                            android:id="@+id/et_check_point"
                                            style="@style/et_notice_bg1"
                                            android:layout_width="@dimen/dp_0"
                                            android:layout_weight="1"
                                            android:textColorHint="@color/edit_hit"
                                            android:hint="请输入检查部位"
                                            android:textColor="@color/ic_text_normal"
                                            android:textSize="@dimen/sp_14" />

                                    </LinearLayout>
            -->


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:minHeight="@dimen/title_bar_hei"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="施工单位："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_company"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="请选择施工单位"
                    android:textColor="@color/ic_text_normal"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />

                <ImageView
                    android:id="@+id/iv_select_contruction_unit"
                    android:layout_width="@dimen/dp_30"
                    android:layout_height="@dimen/dp_30"
                    android:layout_gravity="center_vertical"
                    android:padding="@dimen/dp_10"
                    android:src="@drawable/arrow_right" />
            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="进度情况"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <RadioGroup
                    android:id="@+id/rg_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <RadioButton
                        android:id="@+id/rb_progress_complete"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="完成"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                    <RadioButton
                        android:id="@+id/rb_progress_delay"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:gravity="center_vertical"
                        android:text="延误"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                    <RadioButton
                        android:id="@+id/rb_progress_ahead"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:gravity="center_vertical"
                        android:text="提前"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                    <RadioButton
                        android:id="@+id/rb_progress_normal"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:gravity="center_vertical"
                        android:text="正常"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                </RadioGroup>

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="质量情况"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />


                <RadioGroup
                    android:id="@+id/rg_quality"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <RadioButton
                        android:id="@+id/rb_quality_ok"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="正常"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                    <RadioButton
                        android:id="@+id/rb_quality_war"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:gravity="center_vertical"
                        android:text="预警"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                    <RadioButton
                        android:id="@+id/rb_quality_no"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:gravity="center_vertical"
                        android:text="异常"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                </RadioGroup>

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="安全情况"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <RadioGroup
                    android:id="@+id/rg_safe"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/title_bar_hei"
                    android:orientation="horizontal">
                    <RadioButton
                        android:id="@+id/rb_safe_ok"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="正常"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                    <RadioButton
                        android:id="@+id/rb_safe_war"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:gravity="center_vertical"
                        android:text="预警"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                    <RadioButton
                        android:id="@+id/rb_safe_no"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:gravity="center_vertical"
                        android:text="异常"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                </RadioGroup>

            </LinearLayout>
            <!--
                        <include layout="@layout/line_space" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/title_bar_hei"
                            android:gravity="center_vertical"
                            android:text="检查部位:"
                            android:textColor="@color/text_main_black"
                            android:textSize="@dimen/sp_16" />

                        <EditText
                            android:id="@+id/et_check_point"
                            style="@style/et_remark"
                            android:layout_marginTop="@dimen/dp_5"
                            android:hint="请输入检查部位" />
                        -->
            <include layout="@layout/line_space" />
            <com.kisoft.yuejianli.ui.YTextFieldCell
                android:id="@+id/projectScale"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="工程规模："/>

            <com.kisoft.yuejianli.ui.YTextFieldCell
                android:id="@+id/qiNumber"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="编号："/>

            <com.kisoft.yuejianli.ui.YTextFieldCell
                android:id="@+id/conSzj"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="合同段："/>
            <com.kisoft.yuejianli.ui.YTextFieldCell
                android:id="@+id/weather"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="天气："/>
            <com.kisoft.yuejianli.ui.YTextFieldCell
                android:id="@+id/hot"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="温度："/>
            <com.kisoft.yuejianli.ui.YTextFieldCell
                android:id="@+id/dirName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="总监："/>
            <include layout="@layout/line_space" />
            <com.kisoft.yuejianli.ui.TipsQuestionView
                android:id="@+id/tips_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>



            <!--
               <LinearLayout
                   android:layout_width="match_parent"
                   android:layout_height="@dimen/title_bar_hei"
                   android:orientation="horizontal">

                   <TextView
                       android:layout_width="wrap_content"
                       android:layout_height="match_parent"
                       android:gravity="center_vertical"
                       android:text="施工内容："
                       android:textColor="@color/text_main_black"
                       android:textSize="@dimen/sp_16" />

                   <EditText
                       android:id="@+id/et_check_ontent"
                       android:layout_width="wrap_content"
                       android:layout_height="match_parent"
                       android:layout_marginLeft="@dimen/line_space_wid"
                       android:layout_weight="1"
                       android:background="@null"
                       android:gravity="center_vertical"
                       android:hint="请输入施工工作内容"
                       android:textColor="@color/ic_text_normal"
                       android:textColorHint="@color/edit_hit"
                       android:textSize="@dimen/sp_14" />
               </LinearLayout>
   -->
            <include layout="@layout/line_space" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:gravity="center_vertical"
                android:text="巡查内容和问题及处理结果"
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_16" />

            <EditText
                android:id="@+id/et_dis"
                style="@style/et_remark"
                android:layout_marginTop="@dimen/dp_5"
                android:hint="请输入检查意见" />

            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/opinion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="发现的问题及处理情况/巡视意见"/>

            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/constContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="施工内容（主要施工情况）:" />
            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/remark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="质量安全环保等情况(备注):" />

            <include layout="@layout/line_space" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:gravity="center_vertical"
                android:text="现场图片"
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_16" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_check_picture"
                android:layout_width="match_parent"
                android:layout_height="@dimen/check_image_hei"
                android:layout_marginBottom="@dimen/padding"
                android:orientation="horizontal">

            </androidx.recyclerview.widget.RecyclerView>

            <include layout="@layout/line_space" />


            <LinearLayout
                android:id="@+id/ll_answer"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:layout_margin="@dimen/dp_27"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_save"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginRight="@dimen/padding"
                    android:layout_weight="1"
                    android:background="@drawable/button_fill_bg"
                    android:gravity="center"
                    android:text="保 存"
                    android:textColor="@color/text_bg"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_sub"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/padding"
                    android:layout_weight="1"
                    android:background="@drawable/button_fill_bg"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:text="提 交"
                    android:textColor="@color/text_bg"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>


        </LinearLayout>


    </ScrollView>


</LinearLayout>