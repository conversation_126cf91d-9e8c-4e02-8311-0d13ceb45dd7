<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:gravity="center">

    <RelativeLayout
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:background="@drawable/shape_dialog_loading_bg"
        android:gravity="center">

        <ProgressBar
            android:id="@+id/progressBar1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"/>

        <TextView
            android:id="@+id/tv_dialog_loading_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/progressBar1"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_centerHorizontal="true"
            android:text="loading"
            android:textColor="#ffffff"
            android:textSize="18sp"/>
    </RelativeLayout>


</LinearLayout>
