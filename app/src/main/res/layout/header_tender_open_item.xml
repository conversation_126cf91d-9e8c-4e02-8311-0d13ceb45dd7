<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/llGetPlan"
            style="@style/style_form_ll1"
            android:orientation="vertical">

            <TextView
                style="@style/style_form_title1"
                android:text="项目名称：" />

            <TextView
                android:id="@+id/tv_number"
                style="@style/tv_form_content"
                android:ellipsize="end"
                android:minLines="2"
                android:hint="项目名称"
                android:layout_marginBottom="@dimen/dp_10"/>

            <ImageView
                android:id="@+id/iv_get_plan"
                android:layout_width="@dimen/dp_40"
                android:layout_height="@dimen/dp_40"
                android:scaleType="center"
                android:src="@drawable/ic_more"
                android:visibility="gone" />

        </LinearLayout>

        <include layout="@layout/line_space" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_bar_hei"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/padding">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="开标时间："
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:id="@+id/tv_open_date"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:gravity="center_vertical"
                android:textColor="@color/ic_text_normal"
                android:textSize="@dimen/sp_14" />

            <ImageView
                android:id="@+id/iv_time_open_more"
                android:layout_width="@dimen/title_bar_hei"
                android:layout_height="@dimen/title_bar_hei"
                android:scaleType="center"
                android:src="@drawable/ic_down" />

        </LinearLayout>

        <include layout="@layout/line_space" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_bar_hei"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/padding">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="结束时间："
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:id="@+id/tv_end_date"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:gravity="center_vertical"
                android:textColor="@color/ic_text_normal"
                android:textSize="@dimen/sp_14" />

            <ImageView
                android:id="@+id/iv_time_end_more"
                android:layout_width="@dimen/title_bar_hei"
                android:layout_height="@dimen/title_bar_hei"
                android:scaleType="center"
                android:src="@drawable/ic_down" />

        </LinearLayout>

        <include layout="@layout/line_space" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_bar_hei"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/padding">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="授&#8194;权&#8194;人："
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_14" />

            <EditText
                android:id="@+id/et_person"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="请输入授权人"
                android:textColor="@color/ic_text_normal"
                android:textColorHint="@color/edit_hit"
                android:textSize="@dimen/sp_14" />

        </LinearLayout>
        <include layout="@layout/line_space" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_bar_hei"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/padding">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="记&#8194;录&#8194;人："
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_14" />

            <EditText
                android:id="@+id/et_edit_person"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="请输入记录人"
                android:textColor="@color/ic_text_normal"
                android:textColorHint="@color/edit_hit"
                android:textSize="@dimen/sp_14" />

        </LinearLayout>

        <include layout="@layout/line_space" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_bar_hei"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/padding">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="证　　件："
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_14" />

            <EditText
                android:id="@+id/et_zj"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="请输入证件"
                android:textColor="@color/ic_text_normal"
                android:textColorHint="@color/edit_hit"
                android:textSize="@dimen/sp_14" />

        </LinearLayout>

        <include layout="@layout/line_space" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_bar_hei"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/padding">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="证　　书："
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_14" />

            <EditText
                android:id="@+id/et_zs"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="请输入证书"
                android:textColor="@color/ic_text_normal"
                android:textColorHint="@color/edit_hit"
                android:textSize="@dimen/sp_14" />

        </LinearLayout>

        <include layout="@layout/line_space" />

        <TextView
            style="@style/style_form_title1"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginLeft="@dimen/dp_10"
            android:text="现场情况：" />

        <EditText
            android:id="@+id/et_remark"
            style="@style/et_remark"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginLeft="@dimen/dp_10"
            android:layout_marginRight="@dimen/dp_10"
            android:hint="请输入现场情况" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_bar_hei"
            android:background="@color/colorAccent"
            android:orientation="horizontal"
            android:layout_marginTop="10dp"
            android:paddingLeft="@dimen/padding"
            android:paddingRight="@dimen/padding">

            <TextView
                android:layout_width="0dp"
                android:layout_height="@dimen/title_bar_hei"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="投标单位信息"
                android:textColor="@color/title_text"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:id="@+id/tv_add_item"
                android:layout_width="50dp"
                android:layout_height="@dimen/title_bar_hei"
                android:gravity="center"
                android:text="添加"
                android:textColor="@color/title_text"
                android:textSize="@dimen/sp_14" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_bar_hei"
            android:orientation="horizontal">


            <TextView
                android:id="@+id/tv_tender_item_name"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="10dp"
                android:layout_weight="2"
                android:gravity="center"
                android:text="投标单位名称"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:id="@+id/tv_tender_item_money"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="投标报价"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:id="@+id/tv_tender_item_rank"
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:layout_marginRight="10dp"
                android:gravity="center"
                android:text="排 名"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/sp_14" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>