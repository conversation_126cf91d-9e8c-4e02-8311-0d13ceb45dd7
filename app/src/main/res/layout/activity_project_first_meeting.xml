<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_gradient_bg"
    android:orientation="vertical">

    <include layout="@layout/total_bar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/dp_10"
            android:background="@drawable/shape_bg"
            android:orientation="vertical"
            android:padding="10dp">

            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_27"
                android:orientation="horizontal">

                <TextView
                    android:text="工程名称："
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_12" />

                <TextView
                    android:id="@+id/tv_project_name"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="2"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_12" />

            </LinearLayout>


            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="编         号："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_number"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="项目地址："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_project_address"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="会议时间："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_meeting_time"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="施工单位："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_build_company"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>


            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="监理单位："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_sup_company"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>


            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="建设单位："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_owner_company"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>


            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="施工单位电话："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_build_company_tell"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>


            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="监理单位电话："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_sup_company_tell"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="建设单位电话："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_owner_company_tell"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="施工方负责人："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_build_charger"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>


            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="监理方负责人："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_sup_charger"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>


            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="建设方负责人："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_owner_charger"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>


            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="施工负责人电话："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_build_charger_tell"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>


            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="监理负责人电话："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_sup_charger_tell"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>


            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="建设负责人电话："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_owner_charger_tell"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_marginTop="@dimen/dp_10"
                    android:text="备注："/>

                <TextView
                    android:id="@+id/tv_remark"
                    style="@style/et_remark"
                    android:layout_marginTop="@dimen/dp_5" />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <com.kisoft.yuejianli.ui.YFileListView
                android:id="@+id/y_file_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_10"
                    android:text="附　　件：" />

                <View style="@style/v_drvier_line_h" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_list_fj"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp" />

        </LinearLayout>


    </ScrollView>

</LinearLayout>