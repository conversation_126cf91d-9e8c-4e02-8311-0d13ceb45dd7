<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".views.ExamMaterialsAddNoteActivity">
    <include layout="@layout/total_bar"/>
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="@dimen/dp_10"
            android:orientation="vertical">
            <EditText
                android:id="@+id/etNote"
                style="@style/et_remark"
                android:layout_height="@dimen/dp_280"
                android:layout_marginTop="@dimen/dp_5"
                android:hint="请输入"/>

            <com.kisoft.yuejianli.ui.YButtonSelectCell
                android:id="@+id/y_btnsel"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_66"/>

            <com.kisoft.yuejianli.ui.YSubmitCell
                android:id="@+id/submit_cell"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>