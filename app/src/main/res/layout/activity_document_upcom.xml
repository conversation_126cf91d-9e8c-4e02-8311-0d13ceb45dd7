<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/tool_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/bottom_tab_hei"
        android:background="@color/colorAccent"
        android:minHeight="?attr/actionBarSize"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Dark"
        app:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">


        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/title_bar_hei"
            android:layout_height="wrap_content"
            android:layout_gravity="left"
            android:paddingBottom="@dimen/padding"
            android:paddingTop="@dimen/padding"
            android:scaleType="centerInside"
            android:src="@drawable/ic_back" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:lines="1"
            android:textColor="@color/title_text"
            android:textSize="@dimen/title_bar_text" />

        <LinearLayout
            android:id="@+id/ll_right"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="right"
            android:layout_marginRight="@dimen/dp_10"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_3"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:padding="@dimen/padding"
                android:text="已发"
                android:visibility="gone"
                android:textColor="@android:color/white" />

            <TextView
                android:id="@+id/tv_2"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:padding="@dimen/padding"
                android:text="已办"
                android:textColor="@android:color/white" />
        </LinearLayout>

    </androidx.appcompat.widget.Toolbar>

    <com.jwenfeng.library.pulltorefresh.PullToRefreshLayout
        android:id="@+id/ptrl_document_upcom_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:descendantFocusability="beforeDescendants"
                android:orientation="vertical"
                android:paddingLeft="@dimen/dp_10"
                android:paddingRight="@dimen/dp_10">

                <LinearLayout
                    android:id="@+id/ll_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_notice_detail_bg1"
                    android:gravity="center"
                    android:paddingBottom="@dimen/dp_10"
                    android:visibility="gone"
                    android:paddingTop="@dimen/dp_10"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_no"
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:padding="@dimen/dp_5"
                        android:text="序号"
                        android:textSize="@dimen/sp_14"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_document_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="文件名称"
                        android:textSize="@dimen/sp_14"
                        android:textStyle="bold" />
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_document_upcom_list"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </com.jwenfeng.library.pulltorefresh.PullToRefreshLayout>
</LinearLayout>