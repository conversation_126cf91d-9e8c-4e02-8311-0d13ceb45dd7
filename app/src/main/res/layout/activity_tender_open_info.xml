<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/total_bar"/>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

        </androidx.recyclerview.widget.RecyclerView>



        <ImageView
            android:id="@+id/add_open_info"
            android:layout_width="@dimen/title_bar_hei"
            android:layout_height="@dimen/title_bar_hei"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="@dimen/dp_40"
            android:layout_marginRight="@dimen/padding"
            android:visibility="gone"
            android:src="@drawable/ic_table_add"/>

    </RelativeLayout>


</LinearLayout>