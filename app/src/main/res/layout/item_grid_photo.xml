<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:fresco="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:gravity="center">


    <RelativeLayout
        android:id="@+id/rl_item_grid_photo_pic"
        android:layout_width="105dp"
        android:layout_height="85dp"
        android:background="@drawable/shape_notice_detail_bg"
        android:padding="@dimen/dp_1">

        <ImageView
            android:id="@+id/iv_item_grid_photo_pic"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />

        <ImageView
            android:id="@+id/iv_item_grid_photo_del"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentTop="true"
            android:padding="5dp"
            android:src="@drawable/ib_public_delete" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_item_grid_photo_add"
        android:layout_width="105dp"
        android:layout_height="85dp">


        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="+ 添加图片"
            android:textColor="@color/colorAccent"
            android:textSize="16sp" />


    </RelativeLayout>


</LinearLayout>