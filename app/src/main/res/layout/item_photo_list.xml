<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_item_photo_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="116dp"
        android:layout_height="116dp"
        android:background="@android:color/background_light">

        <ImageView
            android:id="@+id/iv_item_photo_pic"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:scaleType="centerCrop" />

        <LinearLayout
            android:id="@+id/ll_item_photo_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentTop="true"
            android:padding="8dp">

            <CheckBox
                android:clickable="false"
                android:id="@+id/cb_item_photo_status"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:button="@drawable/cb_photo_select"
                android:focusable="false"
                android:focusableInTouchMode="false" />
        </LinearLayout>
    </RelativeLayout>

</LinearLayout>