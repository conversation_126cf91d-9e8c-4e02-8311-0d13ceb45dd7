<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:paddingLeft="@dimen/dp_5"
        android:paddingRight="@dimen/dp_5">

        <ImageView
            android:id="@+id/iv_left"
            android:layout_width="@dimen/title_bar_hei"
            android:layout_height="@dimen/title_bar_hei"
            android:padding="@dimen/padding"
            android:src="@drawable/ic_left"/>

        <TextView
            android:id="@+id/tv_month"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="2018-03"
            android:textColor="@color/text_main_black"
            android:gravity="center"
            android:textSize="@dimen/sp_14"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/iv_right"
            android:layout_width="@dimen/title_bar_hei"
            android:layout_height="@dimen/title_bar_hei"
            android:padding="@dimen/padding"
            android:src="@drawable/ic_right"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="周日"
            android:textColor="@color/text_main_black"
            android:textSize="13sp"/>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="周一"
            android:textColor="@color/text_main_black"
            android:textSize="13sp"/>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="周二"
            android:textColor="@color/text_main_black"
            android:textSize="13sp"/>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="周三"
            android:textColor="@color/text_main_black"
            android:textSize="13sp"/>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="周四"
            android:textColor="@color/text_main_black"
            android:textSize="13sp"/>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="周五"
            android:textColor="@color/text_main_black"
            android:textSize="13sp"/>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="周六"
            android:textColor="@color/text_main_black"
            android:textSize="13sp"/>

    </LinearLayout>

    <GridView
        android:id="@+id/gv_calendar_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:numColumns="7"
        android:paddingBottom="10dp"
        android:paddingTop="10dp"
        android:scrollbars="none"
        android:verticalSpacing="5dp"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="#AAAAAA"
        android:layout_margin="@dimen/dp_12"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">
        <TextView
            android:id="@+id/tv_selscted_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@drawable/ic_timer"
            android:text="2018-12-12"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:paddingLeft="@dimen/dp_15"
            android:drawablePadding="@dimen/dp_12"
            android:gravity="center_vertical"/>

        <View
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_1"
            android:layout_weight="1"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="工作时长："
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"/>

        <TextView
            android:id="@+id/tv_time_diff"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            android:textSize="@dimen/sp_14"
            android:textColor="@color/colorAccent"
            android:layout_marginRight="@dimen/dp_15"/>

    </LinearLayout>


    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="#AAAAAA"
        android:layout_margin="@dimen/dp_12"/>
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/ll_punch_card1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:src="@drawable/ic_punch_card"
            android:layout_marginLeft="@dimen/dp_15"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginLeft="@dimen/dp_12">
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/tv_punch_time1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2018-12-12 07:54:48"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="智能签到"
                    android:textSize="@dimen/sp_12"
                    android:textColor="@color/colorAccent"
                    android:background="@drawable/shap_intelligent_bg"
                    android:paddingLeft="@dimen/dp_5"
                    android:paddingRight="@dimen/dp_5"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:layout_marginLeft="@dimen/dp_10"/>
            </LinearLayout>

            <TextView
                android:id="@+id/tv_status1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/sp_14"
                android:text="正常"
                android:layout_marginTop="@dimen/dp_8"/>

        </LinearLayout>
    </LinearLayout>


    <LinearLayout
        android:id="@+id/ll_punch_card2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginTop="@dimen/dp_12"
        android:visibility="gone">

        <ImageView
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:src="@drawable/ic_punch_card"
            android:layout_marginLeft="@dimen/dp_15"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginLeft="@dimen/dp_12">
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/tv_punch_time2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2018-12-12 07:54:48"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="智能签到"
                    android:textSize="@dimen/sp_12"
                    android:textColor="@color/colorAccent"
                    android:background="@drawable/shap_intelligent_bg"
                    android:paddingLeft="@dimen/dp_5"
                    android:paddingRight="@dimen/dp_5"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:layout_marginLeft="@dimen/dp_10"/>
            </LinearLayout>

            <TextView
                android:id="@+id/tv_status2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/sp_14"
                android:text="正常"
                android:layout_marginTop="@dimen/dp_8"/>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_punch_card3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginTop="@dimen/dp_12"
        android:visibility="gone">

        <ImageView
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:src="@drawable/ic_punch_card"
            android:layout_marginLeft="@dimen/dp_15"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginLeft="@dimen/dp_12">
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/tv_punch_time3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2018-12-12 07:54:48"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="智能签到"
                    android:textSize="@dimen/sp_12"
                    android:textColor="@color/colorAccent"
                    android:background="@drawable/shap_intelligent_bg"
                    android:paddingLeft="@dimen/dp_5"
                    android:paddingRight="@dimen/dp_5"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:layout_marginLeft="@dimen/dp_10"/>
            </LinearLayout>

            <TextView
                android:id="@+id/tv_status3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/sp_14"
                android:text="正常"
                android:layout_marginTop="@dimen/dp_8"/>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_punch_card4"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginTop="@dimen/dp_12"
        android:visibility="gone">

        <ImageView
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:src="@drawable/ic_punch_card"
            android:layout_marginLeft="@dimen/dp_15"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginLeft="@dimen/dp_12">
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/tv_punch_time4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2018-12-12 07:54:48"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="智能签到"
                    android:textSize="@dimen/sp_12"
                    android:textColor="@color/colorAccent"
                    android:background="@drawable/shap_intelligent_bg"
                    android:paddingLeft="@dimen/dp_5"
                    android:paddingRight="@dimen/dp_5"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:layout_marginLeft="@dimen/dp_10"/>
            </LinearLayout>

            <TextView
                android:id="@+id/tv_status4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/sp_14"
                android:text="正常"
                android:layout_marginTop="@dimen/dp_8"/>

        </LinearLayout>
    </LinearLayout>
    </LinearLayout>
    </ScrollView>



</LinearLayout>