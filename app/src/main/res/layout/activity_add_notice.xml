<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/total_bar" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="工程名称：" />

                <TextView
                    android:id="@+id/tv_project_name"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:lines="2"
                    android:layout_weight="1" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="通知单编号：" />

                <EditText
                    android:id="@+id/et_reply_form_num"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1"
                    android:hint="请输入通知单编号" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="施工单位：" />

                <TextView
                    android:id="@+id/tv_company"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="请选择施工单位"
                    android:lineSpacingMultiplier="1.5"
                    android:textColor="@color/ic_text_normal"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />

                <ImageView
                    android:id="@+id/iv_select_contruction_unit"
                    android:layout_width="@dimen/dp_30"
                    android:layout_height="@dimen/dp_30"
                    android:layout_gravity="center_vertical"
                    android:padding="@dimen/dp_10"
                    android:src="@drawable/arrow_right" />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="要求回复时间：" />

                <TextView
                    android:id="@+id/tv_reply_time"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="存在安全隐患：" />

                <RelativeLayout
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="@dimen/dp_40"
                    android:layout_weight="1">

                    <CheckBox
                        android:id="@+id/cb_security_risks"
                        android:layout_width="@dimen/dp_40"
                        android:layout_height="@dimen/dp_40"
                        android:background="@drawable/cb_bg"
                        android:button="@null"
                        android:checked="false" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />
                </RelativeLayout>

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="审批人：" />

                <Spinner
                    android:id="@+id/spinner_approval_person"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <TextView
                style="@style/style_form_title1"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_10"
                android:text="事由描述：" />

            <EditText
                android:id="@+id/et_description"
                style="@style/et_remark"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginRight="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_5"/>

            <TextView
                style="@style/style_form_title1"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_10"
                android:text="内容概述：" />

            <EditText
                android:id="@+id/et_overview"
                style="@style/et_remark"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginRight="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_5"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_check_picture"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_90"
                android:layout_marginTop="@dimen/dp_10"
                android:orientation="horizontal" />

            <TextView
                android:id="@+id/tv_sub"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:layout_marginBottom="@dimen/dp_30"
                android:layout_marginLeft="@dimen/dp_30"
                android:layout_marginRight="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_50"
                android:background="@drawable/button_fill_bg"
                android:gravity="center"
                android:text="提 交"
                android:textColor="@color/text_bg"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>