<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:fresco="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tv_bg"
    android:layout_width="match_parent"
    android:background="@color/text_main_black"
    android:layout_height="match_parent">
    <com.kisoft.yuejianli.ui.zoomable.ZoomableDraweeView
        android:id="@+id/ivPhoto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginRight="@dimen/dp_5"
        android:layout_marginBottom="@dimen/dp_5"
        fresco:failureImage="@drawable/ic_add_picture"
        fresco:failureImageScaleType="centerInside"
        fresco:placeholderImage="@drawable/ic_add_picture"
        fresco:actualImageScaleType="centerInside"
        android:visibility="gone"/>
    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/iv_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginRight="@dimen/dp_5"
        android:layout_marginBottom="@dimen/dp_5"
        fresco:failureImage="@drawable/ic_add_picture"
        fresco:failureImageScaleType="centerInside"
        fresco:placeholderImage="@drawable/ic_add_picture"
        fresco:actualImageScaleType="fitCenter"
        android:visibility="visible"
        />
    <uk.co.senab.photoview.PhotoView
        android:id="@+id/photoView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"/>
    <!--
    fresco:actualImageScaleType="center"
     fresco:scaletype="focusCrop"
     -->
    <!--
    placeholderImageScaleType 占位图的缩放类型
    failureImageScaleType 失败图的缩放类型
    actualImageScaleType 实际图像的缩放类型
    centerInside	缩放图片使两边都在显示边界内，居中显示。和 fitCenter 不同，不会对图片进行放大。
如果图尺寸大于显示边界，则保持长宽比缩小图片。
centerCrop	保持宽高比缩小或放大，使得两边都大于或等于显示边界，且宽或高契合显示边界。居中显示。
    fresco:placeholderImageScaleType="fitCenter"
    actualImageScaleType="centerInside"
    -->

</FrameLayout>