<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginTop="5dp"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/padding"
        android:paddingRight="@dimen/padding"
        android:layout_marginBottom="5dp"
        >

        <TextView
            android:id="@+id/tv_post"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/ic_yuejianli_circle"
            android:gravity="center"
            android:textColor="@color/title_text" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="center"
                android:text="李主任"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="3dp"
                android:textSize="20sp"
                android:textColor="@color/text_main_black"
                />
            <TextView
                android:id="@+id/tv_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="center"
                android:text="监理工程师"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="3dp"
                android:textColor="@color/ic_text_normal"
                />
        </LinearLayout>

    </LinearLayout>

    <include layout="@layout/line_space"
        />

</LinearLayout>