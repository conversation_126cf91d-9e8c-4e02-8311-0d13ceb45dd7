<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:background="@color/colorAccent"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/padding"
        android:paddingRight="@dimen/padding">

        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="@dimen/title_bar_hei"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="附件列表"
            android:textColor="@color/title_text"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:visibility="gone"
            android:id="@+id/tv_add_item"
            android:layout_width="50dp"
            android:layout_height="@dimen/title_bar_hei"
            android:gravity="center"
            android:text="添加"
            android:textColor="@color/title_text"
            android:textSize="@dimen/sp_14" />

    </LinearLayout>
    <View style="@style/v_drvier_line_h" />
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_file_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>