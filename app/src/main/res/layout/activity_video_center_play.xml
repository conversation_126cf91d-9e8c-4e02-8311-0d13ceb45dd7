<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".views.VideoCenterPlayActivity">

    <com.kisoft.yuejianli.ui.MyJzvd
        android:id="@+id/jzVideo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true" />

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/title_bar_hei"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/padding"
        android:paddingBottom="@dimen/padding"
        android:scaleType="centerInside"
        android:layout_gravity="left"
        android:visibility="gone"
        android:src="@drawable/ic_back"/>
</RelativeLayout>