<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".views.ApplySSRenYuanBianGengFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusableInTouchMode="true"
            android:orientation="vertical">
            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="标题:" />

            <com.kisoft.yuejianli.ui.YLabelCell
                android:id="@+id/createName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="申请人:" />

            <com.kisoft.yuejianli.ui.YLabelCell
                android:id="@+id/createTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="申请日期:" />

            <com.kisoft.yuejianli.ui.YLabelCell
                android:id="@+id/deptName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="申请部门:" />

            <com.kisoft.yuejianli.ui.YLabelCell
                android:id="@+id/applyCompany"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="申请公司:" />

            <com.kisoft.yuejianli.ui.YSpinnerCell
                android:id="@+id/helpState"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="紧急状态:" />

            <com.kisoft.yuejianli.ui.YSelectTextViewCell
                android:id="@+id/changePerson"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="变更人姓名:"/>
            <com.kisoft.yuejianli.ui.YSpinnerCell
                android:id="@+id/changeType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="变更类型:" />

            <com.kisoft.yuejianli.ui.YSpinnerCell
                android:id="@+id/isConstruction"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="是否有在建:" />

            <com.kisoft.yuejianli.ui.YSpinnerCell
                android:id="@+id/isPay"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="是否要付费:" />

            <com.kisoft.yuejianli.ui.YSpinnerCell
                android:id="@+id/isLock"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="是否锁证:" />

            <com.kisoft.yuejianli.ui.YLabelCell
                android:id="@+id/projLockDetail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="被锁项目明细:" />

            <com.kisoft.yuejianli.ui.YSpinnerCell
                android:id="@+id/shebaoSalary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="社保工资:" />

            <com.kisoft.yuejianli.ui.YSpinnerCell
                android:id="@+id/changePersonType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="变更类型:" />
            <com.kisoft.yuejianli.ui.YSelectTextViewCell
                android:id="@+id/partnerCompany"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="合作方公司(姓名):"/>


            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/remark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="变更具体情况说明:" />

            <com.kisoft.yuejianli.ui.YFileListView
                android:id="@+id/y_file_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <com.kisoft.yuejianli.ui.YSubmitCell
                android:id="@+id/submit_cell"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</FrameLayout>