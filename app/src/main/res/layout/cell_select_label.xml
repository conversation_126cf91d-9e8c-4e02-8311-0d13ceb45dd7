<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <LinearLayout
        style="@style/style_form_ll1"
        android:layout_height="@dimen/title_bar_hei"
        android:layout_marginTop="@dimen/dp_0">
        <TextView
            android:id="@+id/tv_label_title"
            style="@style/style_form_title1"
            android:layout_marginRight="@dimen/dp_10"
            android:gravity="center_vertical"
            android:text="标题：" />
        <TextView
            android:id="@+id/tv_label_content"
            style="@style/et_notice_bg1"
            android:layout_width="@dimen/dp_0"
            android:gravity="center_vertical"
            android:lines="2"
            android:layout_weight="1"
            android:text="" />
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/arrow_right"/>
    </LinearLayout>
    <include layout="@layout/line_space" />
</LinearLayout>