<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/common_bg">

    <com.hyphenate.easeui.widget.EaseTitleBar
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/em_common_title_bar_height"
        app:titleBarDisplayHomeAsUpEnabled="true"
        app:titleBarTitle="@string/em_chat_group_detail_member_manage"
        android:background="@color/white"/>

    <com.kisoft.yuejianli.im.common.widget.ArrowItemView
        android:id="@+id/item_black_manager"
        android:layout_width="match_parent"
        android:layout_height="@dimen/em_common_item_height"
        app:arrowItemTitle="@string/em_chat_group_black_manage"
        android:background="@color/white"/>

    <com.kisoft.yuejianli.im.common.widget.ArrowItemView
        android:id="@+id/item_mute_manage"
        android:layout_width="match_parent"
        android:layout_height="@dimen/em_common_item_height"
        app:arrowItemTitle="@string/em_chat_group_mute_manage"
        android:background="@color/white"/>

    <Button
        android:id="@+id/btn_transfer"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="@dimen/em_margin_20"
        android:background="@color/white"
        android:text="@string/em_chat_group_authority_transfer"
        android:textColor="@color/em_color_warning"
        android:textSize="@dimen/em_size_big_notify"
        style="?android:attr/borderlessButtonStyle"
        android:foreground="@drawable/em_ripple_click_gray"/>

</LinearLayout>