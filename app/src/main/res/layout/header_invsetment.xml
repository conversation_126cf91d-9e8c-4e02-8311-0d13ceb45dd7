<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="@color/colorAccent"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="170dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:text="已支付"
            android:textColor="@color/chat_color_7"
            android:textSize="13sp"
            android:background="#00000000"
            android:layout_marginTop="65dp"/>

        <com.kisoft.yuejianli.ui.RingView
            android:id="@+id/rv_progress"
            android:layout_width="200dp"
            android:layout_height="140dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:background="#00000000" />
    </RelativeLayout>

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:paddingLeft="@dimen/padding"
        android:paddingRight="@dimen/padding"
        android:layout_height="@dimen/dp_40">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:text="已支付金额总计："
            android:textColor="@color/title_text"
            android:gravity="center_vertical"
            android:textSize="@dimen/sp_16"/>

        <TextView
            android:id="@+id/tv_all_money"
            android:layout_width="wrap_content"
            android:layout_weight="1"
            android:layout_height="match_parent"
            android:textColor="@color/title_text"
            android:gravity="center_vertical"
            android:textSize="@dimen/sp_16"/>

    </LinearLayout>

</LinearLayout>