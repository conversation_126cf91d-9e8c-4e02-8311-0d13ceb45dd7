<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white"
    tools:context=".views.ResourceContractDetailActivity">
    <include layout="@layout/total_bar" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:focusableInTouchMode="true"
            android:padding="@dimen/padding">

            <LinearLayout
                style="@style/style_form_ll1"
                android:orientation="vertical"
                android:padding="@dimen/dp_0">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="项目名称："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvProjectName"
                    style="@style/tv_form_content"
                    android:ellipsize="end"
                    android:minLines="2"
                    android:hint="项目名称"
                    android:layout_marginBottom="@dimen/dp_10"/>
            </LinearLayout>

            <include layout="@layout/line_space" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="合同编号："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvContractNo"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
            <include layout="@layout/line_space" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="项目总监："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvSupDrName"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
            <include layout="@layout/line_space" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="项目开始时间："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvStartDate"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
            <include layout="@layout/line_space" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="项目结束时间："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvEndDate"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
            <include layout="@layout/line_space" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="项目投资总金额："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvProjectAmount"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
            <include layout="@layout/line_space" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="监理费用："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvSupAmount"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
            <include layout="@layout/line_space" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="获奖类型："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvAwardType"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
            <include layout="@layout/line_space" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="获奖对象："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvAwardWinner"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
            <include layout="@layout/line_space" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="公司奖励类型："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvCompanyAwardType"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
            <include layout="@layout/line_space" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="公司奖励："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvCompanyAward"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
            <com.kisoft.yuejianli.ui.YFileListView
                android:id="@+id/y_file_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>