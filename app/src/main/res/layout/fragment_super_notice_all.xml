<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:background="@color/text_bg"
    android:layout_height="match_parent">


    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="60dp">

        <LinearLayout
            android:id="@+id/ll_doing"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@drawable/item_enable_bg"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_doing_num"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="0"
                android:textSize="22sp"
                android:textStyle="bold"
                android:textColor="@color/ic_text_normal"
                android:gravity="center"/>
            <TextView
                android:id="@+id/tv_doing"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="未回复"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/ic_text_normal"
                android:gravity="center"/>

        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_complete"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@drawable/item_enable_bg"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_complete_num"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="0"
                android:textSize="22sp"
                android:textStyle="bold"
                android:textColor="@color/ic_text_normal"
                android:gravity="center"/>
            <TextView
                android:id="@+id/tv_complete"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="已回复"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/ic_text_normal"
                android:gravity="center"/>

        </LinearLayout>



    </LinearLayout>
    <include layout="@layout/line_space_blod"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_quality_count_all"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    </androidx.recyclerview.widget.RecyclerView>

</LinearLayout>