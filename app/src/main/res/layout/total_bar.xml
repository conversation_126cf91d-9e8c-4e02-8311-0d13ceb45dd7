<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tool_bar"
    android:layout_width="match_parent"
    android:layout_height="@dimen/bottom_tab_hei"
    android:background="@color/colorAccent"
    android:minHeight="?attr/actionBarSize"
    app:popupTheme="@style/ThemeOverlay.AppCompat.Dark"
    app:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/title_bar_hei"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/padding"
        android:paddingBottom="@dimen/padding"
        android:scaleType="centerInside"
        android:layout_gravity="left"
        android:src="@drawable/ic_back_1"/>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:lines="2"
        android:textSize="@dimen/title_bar_text"
        android:textColor="@color/title_text"
        android:gravity="center"/>

    <ImageView
        android:id="@+id/iv_action"
        android:layout_width="@dimen/title_bar_hei"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginEnd="@dimen/dp_10"
        android:scaleType="centerInside" />
    <TextView
        android:id="@+id/tv_submit"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center"
        android:paddingLeft="@dimen/dp_10"
        android:layout_gravity="center_vertical|end"
        android:textColor="@android:color/white"
        android:textSize="@dimen/sp_14"
        android:text="提交"/>

</androidx.appcompat.widget.Toolbar>