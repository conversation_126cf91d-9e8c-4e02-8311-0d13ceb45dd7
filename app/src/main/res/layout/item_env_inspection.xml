<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:focusableInTouchMode="true"
        android:layout_marginStart="@dimen/table_div"
        android:layout_marginBottom="@dimen/table_div">
        <EditText
            android:id="@+id/checkCategoryName"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/table_text"
            android:text=""
            android:background="@color/main_bg"
            android:paddingTop="@dimen/dp_3"
            android:paddingBottom="@dimen/dp_3"
            android:layout_marginEnd="@dimen/table_div"
            />
        <EditText
            android:id="@+id/checkContentName"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/table_text"
            android:text=""
            android:background="@color/main_bg"
            android:paddingTop="@dimen/dp_3"
            android:paddingBottom="@dimen/dp_3"
            android:layout_marginEnd="@dimen/table_div"
            />
        <EditText
            android:id="@+id/checkSituation"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/table_text"
            android:text=""
            android:background="@color/main_bg"
            android:paddingTop="@dimen/dp_3"
            android:paddingBottom="@dimen/dp_3"
            android:layout_marginEnd="@dimen/table_div"
            />
        <EditText
            android:id="@+id/rectificationRequest"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/table_text"
            android:text=""
            android:background="@color/main_bg"
            android:paddingTop="@dimen/dp_3"
            android:paddingBottom="@dimen/dp_3"
            android:layout_marginEnd="@dimen/table_div"
            />
    </LinearLayout>
</LinearLayout>