<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:zhy="http://schemas.android.com/apk/res-auto"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <include layout="@layout/total_bar" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="@dimen/padding">

            <LinearLayout
                android:id="@+id/ll_gx"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="项目工序："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_gx"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="请选择"
                    android:textColor="@color/text_main_black"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />

                <ImageView
                    android:id="@+id/iv_gx"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="@dimen/dp_14"
                    android:padding="@dimen/dp_5"
                    android:src="@drawable/arrow_right" />

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="所属项目："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_project"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="计划开工日期："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_time1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:textColor="@color/text_main_black"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="计划完工日期："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_time2"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:textColor="@color/text_main_black"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="实际开工日期："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_time3"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:hint="请选择"
                    android:textColor="@color/text_main_black"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="实际完工日期："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_time4"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:hint="请选择"
                    android:textColor="@color/text_main_black"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="进度比例："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <EditText
                    android:id="@+id/et_percentage"
                    android:layout_weight="1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:digits="0123456789."
                    android:inputType="numberDecimal"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:background="@null"
                    android:hint="请输入进度比例"
                    android:textColor="@color/text_main_black"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:layout_weight="1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="%"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="进度状态："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <Spinner
                    android:id="@+id/sp_type"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_5"
                    android:layout_weight="1"
                    android:entries="@array/array_progress_type" />

                <TextView
                    android:id="@+id/tv_type"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:visibility="gone" />

            </LinearLayout>

            <include layout="@layout/line_space" />

            <TextView
                android:id="@+id/tv_reason"
                style="@style/style_form_title1"
                android:layout_marginTop="@dimen/dp_10"
                android:visibility="gone"
                android:textSize="@dimen/sp_16"
                android:text="滞后原因：" />

            <EditText
                android:id="@+id/et_reason"
                style="@style/et_remark"
                android:visibility="gone"
                android:layout_marginLeft="@dimen/dp_0"
                android:layout_marginTop="@dimen/dp_10"
                android:hint="请输入滞后原因"
                android:textSize="@dimen/sp_16"/>

            <TextView
                android:id="@+id/tv_pcfx"
                style="@style/style_form_title1"
                android:layout_marginTop="@dimen/dp_10"
                android:visibility="gone"
                android:textSize="@dimen/sp_16"
                android:text="偏差分析：" />

            <EditText
                android:id="@+id/et_pcfx"
                style="@style/et_remark"
                android:visibility="gone"
                android:layout_marginLeft="@dimen/dp_0"
                android:layout_marginTop="@dimen/dp_10"
                android:hint="请输入偏差分析"
                android:textSize="@dimen/sp_16"/>



            <TextView
                android:id="@+id/tv_jpcs"
                style="@style/style_form_title1"
                android:layout_marginTop="@dimen/dp_10"
                android:textSize="@dimen/sp_16"
                android:visibility="gone"
                android:text="纠偏措施：" />

            <EditText
                android:id="@+id/et_jpcs"
                style="@style/et_remark"
                android:visibility="gone"
                android:layout_marginLeft="@dimen/dp_0"
                android:layout_marginTop="@dimen/dp_10"
                android:textSize="@dimen/sp_16"
                android:hint="请输入纠偏措施" />

            <TextView
                android:id="@+id/tv_pcys"
                style="@style/style_form_title1"
                android:layout_marginTop="@dimen/dp_10"
                android:visibility="gone"
                android:textSize="@dimen/sp_16"
                android:text="偏差因素：" />

            <com.zhy.view.flowlayout.TagFlowLayout
                android:id="@+id/tfl_type_container"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_marginTop="@dimen/dp_5"
                android:paddingLeft="@dimen/dp_10"
                android:paddingRight="@dimen/dp_10"
                zhy:max_select="-1" />


            <TextView
                style="@style/style_form_title1"
                android:layout_marginTop="@dimen/dp_10"
                android:textSize="@dimen/sp_16"
                android:text="备　　注：" />

            <EditText
                android:id="@+id/et_remark"
                style="@style/et_remark"
                android:layout_marginLeft="@dimen/dp_0"
                android:layout_marginTop="@dimen/dp_10"
                android:textSize="@dimen/sp_16"
                android:hint="请输入备注"/>

            <TextView
                android:id="@+id/tv_sub"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:background="@drawable/button_fill_bg"
                android:text="提 交"
                android:textSize="@dimen/sp_16"
                android:textColor="@color/text_bg"
                android:layout_marginTop="@dimen/dp_50"
                android:layout_marginBottom="@dimen/dp_30"
                android:layout_marginLeft="@dimen/dp_30"
                android:layout_marginRight="@dimen/dp_30"
                android:gravity="center"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</LinearLayout>