<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/itemView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginStart="@dimen/padding"
        android:layout_marginEnd="@dimen/padding"
        android:layout_marginTop="@dimen/dp_5"
        android:padding="@dimen/padding"
        android:background="@drawable/selector_item_round_bg">

        <TextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14"
            android:textStyle="bold"
            android:text="name"/>

        <TextView
            android:id="@+id/tvType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp_12"
            android:layout_marginTop="@dimen/dp_5"
            android:textColor="@color/ic_text_normal"
            android:text="证书类型："/>
        <TextView
            android:id="@+id/tvNo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp_13"
            android:layout_marginTop="@dimen/dp_5"
            android:textColor="@color/text_main_black"
            android:text="证书编号："/>
        <TextView
            android:id="@+id/tvAwardName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp_13"
            android:layout_marginTop="@dimen/dp_5"
            android:textColor="@color/text_main_black"
            android:text="颁发机构："/>
        <TextView
            android:id="@+id/tvTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp_13"
            android:layout_marginTop="@dimen/dp_5"
            android:textColor="@color/text_main_black"
            android:text="有效期："/>

    </LinearLayout>

    <ImageView
        android:id="@+id/ivInvalid"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_shixiao"
        android:layout_gravity="end|center_vertical"
        android:layout_marginEnd="@dimen/dp_20"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/ivSelected"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_item_select"
        android:layout_gravity="end"
        android:layout_marginEnd="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_15"
        android:visibility="gone"/>

</FrameLayout>
