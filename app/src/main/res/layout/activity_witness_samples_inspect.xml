<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/main_bg"
    tools:context=".views.WitnessSamplesInspectActivity">
    <include layout="@layout/total_bar" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:focusableInTouchMode="true"
            android:padding="@dimen/padding">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="工程名称："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvProjectName"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:lines="2"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="编&#8194;&#8194;&#8194;&#8194;号："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <EditText
                    android:id="@+id/etWsNumber"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:background="@null"
                    android:hint="请输入编号"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="施工单位："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvBuilder"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:hint="请选择施工单位"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
                <ImageView
                    android:id="@+id/ivBuilder"
                    android:layout_width="@dimen/dp_30"
                    android:layout_height="@dimen/dp_30"
                    android:layout_gravity="center_vertical"
                    android:padding="@dimen/dp_10"
                    android:src="@drawable/arrow_right" />
            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="监理单位："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <EditText
                    android:id="@+id/etSupCompany"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:background="@null"
                    android:hint="请输入监理单位"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="检测单位："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <EditText
                    android:id="@+id/etReviewPosition"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:background="@null"
                    android:hint="请输入检测单位"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
            <include layout="@layout/line_space" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:gravity="center_vertical"
                android:text="备&#8194;&#8194;&#8194;注"
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_14" />

            <EditText
                android:id="@+id/etReviewContent"
                style="@style/et_remark"
                android:hint="请输入"/>

            <Space
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_16"/>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/padding">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="取样信息"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />
                <LinearLayout
                    android:id="@+id/llOpDetail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_alignParentEnd="true"
                    android:layout_gravity="end">

                    <TextView
                        android:id="@+id/tvAddDetail"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_round_main_empty_10"
                        android:text="添加"
                        android:textSize="@dimen/sp_13"
                        android:paddingStart="@dimen/padding"
                        android:paddingEnd="@dimen/padding"
                        android:paddingTop="@dimen/dp_3"
                        android:paddingBottom="@dimen/dp_3"
                        android:layout_marginEnd="@dimen/padding"
                        android:textColor="@color/colorPrimary"/>
                    <TextView
                        android:id="@+id/tvDeleteDetail"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_round_grey_empty_10"
                        android:text="删除"
                        android:textSize="@dimen/sp_13"
                        android:paddingStart="@dimen/padding"
                        android:paddingEnd="@dimen/padding"
                        android:paddingTop="@dimen/dp_3"
                        android:paddingBottom="@dimen/dp_3"
                        android:layout_marginEnd="@dimen/padding"
                        android:textColor="@color/ic_text_normal"/>

                </LinearLayout>
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@color/line_space"
                >
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/table_text"
                    android:text="取样\n日期"
                    android:background="@color/main_bg"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:layout_marginStart="@dimen/table_div"
                    android:layout_marginEnd="@dimen/table_div"
                    android:layout_marginTop="@dimen/table_div"
                    android:layout_marginBottom="@dimen/table_div"
                    />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/table_text"
                    android:text="取样\n部位"
                    android:background="@color/main_bg"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:layout_marginEnd="@dimen/table_div"
                    android:layout_marginTop="@dimen/table_div"
                    android:layout_marginBottom="@dimen/table_div"
                    />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/table_text"
                    android:text="取样\n数量"
                    android:background="@color/main_bg"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:layout_marginEnd="@dimen/table_div"
                    android:layout_marginTop="@dimen/table_div"
                    android:layout_marginBottom="@dimen/table_div"
                    />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/table_text"
                    android:text="取样人"
                    android:background="@color/main_bg"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:layout_marginEnd="@dimen/table_div"
                    android:layout_marginTop="@dimen/table_div"
                    android:layout_marginBottom="@dimen/table_div"
                    />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/table_text"
                    android:text="见证人"
                    android:background="@color/main_bg"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:layout_marginEnd="@dimen/table_div"
                    android:layout_marginTop="@dimen/table_div"
                    android:layout_marginBottom="@dimen/table_div"
                    />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/table_text"
                    android:text="送检\n日期"
                    android:background="@color/main_bg"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:layout_marginEnd="@dimen/table_div"
                    android:layout_marginTop="@dimen/table_div"
                    android:layout_marginBottom="@dimen/table_div"
                    />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/table_text"
                    android:text="送检\n数量"
                    android:background="@color/main_bg"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:layout_marginEnd="@dimen/table_div"
                    android:layout_marginTop="@dimen/table_div"
                    android:layout_marginBottom="@dimen/table_div"
                    />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/table_text"
                    android:text="报告\n日期"
                    android:background="@color/main_bg"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:layout_marginEnd="@dimen/table_div"
                    android:layout_marginTop="@dimen/table_div"
                    android:layout_marginBottom="@dimen/table_div"
                    />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/table_text"
                    android:text="报告\n编号"
                    android:background="@color/main_bg"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:layout_marginEnd="@dimen/table_div"
                    android:layout_marginTop="@dimen/table_div"
                    android:layout_marginBottom="@dimen/table_div"
                    />
            </LinearLayout>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycleViewDetail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:gravity="center_vertical"
                android:text="现场图片"
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_14" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_check_picture"
                android:layout_width="match_parent"
                android:layout_height="@dimen/check_image_hei"
                android:layout_marginBottom="@dimen/padding"
                android:orientation="horizontal">

            </androidx.recyclerview.widget.RecyclerView>

            <LinearLayout
                android:id="@+id/ll_answer"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:layout_margin="@dimen/dp_27"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_save"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginRight="@dimen/padding"
                    android:layout_weight="1"
                    android:background="@drawable/button_fill_bg"
                    android:gravity="center"
                    android:text="保 存"
                    android:textColor="@color/text_bg"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_sub"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/padding"
                    android:layout_weight="1"
                    android:background="@drawable/button_fill_bg"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:text="提 交"
                    android:textColor="@color/text_bg"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>


        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</LinearLayout>