<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/total_bar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="@dimen/padding">


            <LinearLayout
                style="@style/style_form_ll1"
                android:orientation="vertical"
                android:padding="@dimen/dp_0">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="项目名称："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_project"
                    style="@style/tv_form_content"
                    android:ellipsize="end"
                    android:minLines="2"
                    android:hint="项目名称"
                    android:layout_marginBottom="@dimen/dp_10"/>
            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="问题名称:"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_problem_name"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />


            </LinearLayout>

            <include layout="@layout/line_space" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="检查部位:"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_check_point"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="问题部位："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_check_content"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="施工单位："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_company"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="负责人："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_construction_charge"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <include layout="@layout/line_space" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:gravity="center_vertical"
                android:text="现场隐患图片"
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_16" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_check_picture"
                android:layout_width="match_parent"
                android:layout_height="@dimen/check_image_hei"
                android:layout_marginBottom="@dimen/padding"
                android:orientation="horizontal">

            </androidx.recyclerview.widget.RecyclerView>

            <include layout="@layout/line_space" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:gravity="center_vertical"
                android:text="安全隐患描述描述"
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_16" />

            <TextView
                android:id="@+id/tv_des"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="fill_vertical"
                android:minHeight="@dimen/dp_72"
                android:textColor="@color/ic_text_normal"
                android:textSize="@dimen/sp_14" />


            <include layout="@layout/line_space" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_send_todo_notice"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/title_bar_hei"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:text="下发待办事件"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />


                <CheckBox
                    android:id="@+id/cb_send_todo_notice"
                    android:layout_width="@dimen/dp_27"
                    android:layout_height="@dimen/dp_27"
                    android:background="@drawable/check_box_bg"
                    android:button="@null"
                    android:gravity="center_vertical">

                </CheckBox>

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:id="@+id/ll_todo_people"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/title_bar_hei"
                        android:gravity="center_vertical"
                        android:text="选择问题跟踪人："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />


                    <TextView
                        android:id="@+id/tv_todo_people"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                    <ImageView
                        android:id="@+id/iv_more_people"
                        android:layout_width="@dimen/title_bar_hei"
                        android:layout_height="match_parent"
                        android:scaleType="center"
                        android:src="@drawable/ic_down" />

                </LinearLayout>

                <include layout="@layout/line_space" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="待办事项内容"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <EditText
                    android:id="@+id/et_todo_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:drawableLeft="@drawable/ic_edit_hit"
                    android:drawablePadding="@dimen/dp_4"
                    android:hint="请输入待办事项内容"
                    android:minHeight="@dimen/dp_72"
                    android:textColor="@color/ic_text_normal"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/title_bar_hei"
                        android:gravity="center_vertical"
                        android:text="期限完成时间："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_todo_end_time"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                    <ImageView
                        android:id="@+id/iv_end_time_more"
                        android:layout_width="@dimen/title_bar_hei"
                        android:layout_height="match_parent"
                        android:scaleType="center"
                        android:src="@drawable/ic_down" />

                </LinearLayout>

                <include layout="@layout/line_space" />


            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp_72"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ll_go_sup_notice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/title_bar_hei"
                        android:layout_weight="1"
                        android:drawableLeft="@drawable/ic_go_qulity_accident"
                        android:drawablePadding="@dimen/padding"
                        android:gravity="center_vertical"
                        android:text="生成监理通知单"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_go_orther" />


                </LinearLayout>

                <include

                    layout="@layout/line_space" />

            </LinearLayout>

        </LinearLayout>


    </ScrollView>

</LinearLayout>