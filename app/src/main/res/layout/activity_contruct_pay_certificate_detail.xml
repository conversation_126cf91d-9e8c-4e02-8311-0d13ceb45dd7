<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent">
    <include layout="@layout/total_bar"/>
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:padding="@dimen/padding"
            android:layout_height="match_parent">


            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_27">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="工程名称："
                    android:textSize="@dimen/sp_12"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_project_name"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="2"
                    android:textSize="@dimen/sp_12"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>



            </LinearLayout>
            <include layout="@layout/line_space"/>

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="编         号："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_certificate_number"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>


            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="施工单位："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_accept_company"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="支付金额（大写）："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_pay_money_big"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="支付金额（小写）："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_pay_money_small"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="施工单位报审款："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_contruct_apply_money"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="施工单位应得款："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_contruct_check_money"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="本期应扣款："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_deduct_money"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="本期应付款："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_real_pay_money"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="支付时间："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_send_time"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:text="备         注："
                android:gravity="center_vertical"
                android:textSize="@dimen/sp_14"
                android:background="@color/line_space"
                android:textColor="@color/text_main_black"/>

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="120dp"
                android:gravity="center_vertical"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/text_main_black"/>




        </LinearLayout>

    </ScrollView>

</LinearLayout>