<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.hyphenate.easeui.widget.EaseTitleBar
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/em_common_title_bar_height"
        app:titleBarTitleTextColor="@color/white"
        app:titleBarDisplayHomeAsUpEnabled="true"
        android:background="@color/colorPrimary"
        app:titleBarTitle="@string/Select_the_contact"/>

    <TextView
        android:id="@+id/btn_start"
        android:layout_width="match_parent"
        android:layout_height="@dimen/adaptive_52dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/demo_btn_bg_conference_start"
        android:clickable="true"
        android:gravity="center"
        android:onClick="onClick"
        android:text="@string/button_start_video_conference"
        android:textColor="#FFF"
        android:textSize="@dimen/adaptive_18sp" />

    <ListView
        android:id="@+id/listView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/btn_start"
        android:layout_below="@+id/title_bar"
        android:background="@android:color/white"
        android:cacheColorHint="#00000000"
        android:descendantFocusability="afterDescendants"
        android:fastScrollEnabled="true" />

</RelativeLayout>