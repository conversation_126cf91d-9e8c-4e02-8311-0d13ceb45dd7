<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="match_parent">
    <include layout="@layout/total_bar"/>

    <RadioGroup
        android:id="@+id/rg_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="@dimen/dp_5">
        <RadioButton
            android:id="@+id/rb_task_detail"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="详细任务"
            android:textSize="@dimen/sp_15"
            android:textColor="@color/colorAccent"
            android:button="@null"
            android:padding="@dimen/dp_5"
            android:gravity="center"
            android:checked="true"/>
        <View
            android:layout_width="1dp"
            android:layout_height="@dimen/dp_25"
            android:background="@color/line_space"/>
        <RadioButton
            android:id="@+id/rb_task_record"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="工作记录"
            android:textSize="@dimen/sp_15"
            android:textColor="@color/text_main_black"
            android:button="@null"
            android:padding="@dimen/dp_5"
            android:gravity="center"/>
    </RadioGroup>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <View
            android:id="@+id/view_task_detail"
            android:layout_width="@dimen/dp_0"
            android:layout_weight="1"
            android:layout_height="@dimen/dp_1"
            android:background="@color/colorAccent"
            android:layout_margin="@dimen/dp_5"/>
        <View
            android:id="@+id/view_task_record"
            android:layout_width="@dimen/dp_0"
            android:layout_weight="1"
            android:layout_height="@dimen/dp_1"
            android:background="@color/line_space"
            android:layout_margin="@dimen/dp_5"/>
    </LinearLayout>
    <com.kisoft.yuejianli.ui.NoScrollViewPager
        android:id="@+id/vp_mainframe_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/ll_public_titleBar_fg_main"/>

</LinearLayout>