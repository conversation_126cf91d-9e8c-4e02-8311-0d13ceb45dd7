<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:paddingLeft="@dimen/padding"
    android:paddingRight="@dimen/padding"
    android:layout_height="wrap_content">

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40">

        <TextView
            android:id="@+id/tv_certificate_type"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:text="工程款支付证书"
            android:lines="1"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:gravity="center_vertical"
            android:drawablePadding="@dimen/padding"
            android:drawableLeft="@drawable/ic_pay_certificate"
            android:layout_weight="2"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:text="编号："
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:gravity="center_vertical"/>

        <TextView
            android:id="@+id/tv_number"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="1002"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/ic_text_normal"
            android:gravity="center_vertical"/>
    </LinearLayout>

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_27">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:text="支付金额："
            android:textSize="@dimen/sp_12"
            android:paddingLeft="34dp"
            android:textColor="@color/ic_text_normal"/>


        <TextView
            android:id="@+id/tv_pay_money"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="******"

            android:paddingLeft="@dimen/padding"
            android:textSize="@dimen/sp_12"
            android:textColor="@color/ic_text_normal"/>
    </LinearLayout>

    <include layout="@layout/line_space"/>

</LinearLayout>