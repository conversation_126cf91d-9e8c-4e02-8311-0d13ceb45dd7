<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:paddingTop="@dimen/padding">

    <TextView
        android:id="@+id/tvSelect"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:gravity="center"
        android:layout_marginEnd="@dimen/padding"
        android:background="@drawable/bg_answer_selector"
        android:textColor="@color/button_text_color"
        />
    <ImageView
        android:id="@+id/ivErrorSelect"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:visibility="gone"
        android:src="@drawable/icon_wrong_count"
        android:scaleType="fitXY"
        android:layout_marginEnd="@dimen/padding"/>

    <TextView
        android:id="@+id/tvContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_14"
        android:textColor="@color/text_main_black"
        android:lineSpacingExtra="@dimen/dp_3"/>

</LinearLayout>
