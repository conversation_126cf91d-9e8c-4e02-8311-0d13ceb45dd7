<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <include layout="@layout/total_bar" />
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/ll_item1"
                style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="基本信息"
                    android:textSize="@dimen/sp_16"
                    android:textStyle="bold" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <ImageView
                    android:id="@+id/iv_item1"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:padding="@dimen/dp_3"
                    android:src="@drawable/arrow_open_close" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout
                android:id="@+id/ll_content1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="@dimen/dp_5"
                android:paddingRight="@dimen/dp_5"
                android:visibility="gone">

                <LinearLayout
                    android:id="@+id/ll_time"
                    style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="年　　月：" />

                    <TextView
                        android:id="@+id/tv_time"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:lines="1"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                    <ImageView
                        android:id="@+id/iv_time_more"
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:padding="@dimen/dp_3"
                        android:src="@drawable/ic_down" />

                </LinearLayout>

                <View style="@style/v_drvier_line_h" />

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="监理标段：" />

                    <EditText
                        android:id="@+id/tv_section"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1"
                        android:hint="请输入标段信息" />

                </LinearLayout>

                <View style="@style/v_drvier_line_h" />

                <LinearLayout style="@style/style_form_ll1"
                    android:orientation="vertical">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="项目名称：" />

                    <TextView
                        android:id="@+id/tv_project_name"
                        style="@style/tv_form_content"
                        android:ellipsize="end"
                        android:minLines="2"
                        android:hint="项目名称"/>
                </LinearLayout>

                <View style="@style/v_drvier_line_h" />

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="建设单位：" />

                    <EditText
                        android:id="@+id/tv_cons_company"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1"
                        android:hint="请输入建设单位" />

                </LinearLayout>

                <View style="@style/v_drvier_line_h" />

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="项目总监：" />

                    <TextView
                        android:id="@+id/tv_project_manager"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1" />

                </LinearLayout>

                <View style="@style/v_drvier_line_h" />

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="创&#8194;建&#8194;人：" />

                    <TextView
                        android:id="@+id/tv_creater"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1" />

                </LinearLayout>

                <View style="@style/v_drvier_line_h" />

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="创建日期：" />

                    <TextView
                        android:id="@+id/tv_create_date"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1" />

                </LinearLayout>

                <View style="@style/v_drvier_line_h" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_item2"
                style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="进度情况"
                    android:textSize="@dimen/sp_16"
                    android:textStyle="bold" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <ImageView
                    android:id="@+id/iv_item2"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:padding="@dimen/dp_3"
                    android:src="@drawable/arrow_open_close" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout
                android:id="@+id/ll_content2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="@dimen/dp_5"
                android:paddingRight="@dimen/dp_5"
                android:visibility="gone">

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="上月计划进度(%)：" />

                    <EditText
                        android:id="@+id/et_syjhjd"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1"
                        android:digits=".0123456789"
                        android:inputType="numberDecimal"
                        android:hint="请输入上月计划进度"/>

                </LinearLayout>

                <View style="@style/v_drvier_line_h" />


                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="下月计划进度(%)：" />

                    <EditText
                        android:id="@+id/et_xyjhjd"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1"
                        android:digits=".0123456789"
                        android:inputType="numberDecimal"
                        android:hint="请输入下月计划进度"/>

                </LinearLayout>

                <View style="@style/v_drvier_line_h" />


                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="本月累计进度(%)：" />

                    <EditText
                        android:id="@+id/et_ljjd"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1"
                        android:digits=".0123456789"
                        android:inputType="numberDecimal"
                        android:hint="请输入本月累计进度"/>

                </LinearLayout>

                <View style="@style/v_drvier_line_h" />

                <TextView
                    style="@style/style_form_title1"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_10"
                    android:text="进度描述：" />

                <EditText
                    android:id="@+id/et_jdms"
                    style="@style/et_remark"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_5"
                    android:hint="请输入进度描述" />

                <TextView
                    style="@style/style_form_title1"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_10"
                    android:text="存在问题及解决措施：" />

                <EditText
                    android:id="@+id/et_czwt"
                    style="@style/et_remark"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_5"
                    android:hint="请输入存在问题及解决措施"/>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_item3"
                style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="计量支付情况"
                    android:textSize="@dimen/sp_16"
                    android:textStyle="bold" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <ImageView
                    android:id="@+id/iv_item3"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:padding="@dimen/dp_3"
                    android:src="@drawable/arrow_open_close" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout
                android:id="@+id/ll_content3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="@dimen/dp_5"
                android:paddingRight="@dimen/dp_5"
                android:visibility="gone">

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="上月完成(万元)：" />

                    <EditText
                        android:id="@+id/et_sywc"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1"
                        android:digits=".0123456789"
                        android:inputType="numberDecimal"
                        android:hint="请输入上月完成"/>

                </LinearLayout>

                <View style="@style/v_drvier_line_h" />

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="本月支付(万元)：" />

                    <EditText
                        android:id="@+id/et_byzf"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1"
                        android:digits=".0123456789"
                        android:inputType="numberDecimal"
                        android:hint="请输入本月支付"/>

                </LinearLayout>

                <View style="@style/v_drvier_line_h" />

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="累计完成(万元)：" />

                    <EditText
                        android:id="@+id/et_ljwc"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1"
                        android:digits=".0123456789"
                        android:inputType="numberDecimal"
                        android:hint="请输入累计完成"/>

                </LinearLayout>

                <View style="@style/v_drvier_line_h" />

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="累计支付(万元)：" />

                    <EditText
                        android:id="@+id/et_ljzf"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1"
                        android:digits=".0123456789"
                        android:inputType="numberDecimal"
                        android:hint="请输入累计支付"/>

                </LinearLayout>

                <View style="@style/v_drvier_line_h" />

                <TextView
                    style="@style/style_form_title1"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_10"
                    android:text="备　　注：" />

                <EditText
                    android:id="@+id/et_remark"
                    style="@style/et_remark"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_5"
                    android:hint="请输入备注"/>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_item4"
                style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="质量情况"
                    android:textSize="@dimen/sp_16"
                    android:textStyle="bold" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <ImageView
                    android:id="@+id/iv_item4"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:padding="@dimen/dp_3"
                    android:src="@drawable/arrow_open_close" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout
                android:id="@+id/ll_content4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="@dimen/dp_5"
                android:paddingRight="@dimen/dp_5"
                android:visibility="gone">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_10"
                    android:text="各方主体行为：" />

                <EditText
                    android:id="@+id/et_ztxw"
                    style="@style/et_remark"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_5"
                    android:hint="请输入建设各方责任主体质量行为" />

                <TextView
                    style="@style/style_form_title1"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_10"
                    android:text="施工现场质量情况：" />

                <EditText
                    android:id="@+id/et_sgxczlqk"
                    style="@style/et_remark"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_5"
                    android:hint="请输入施工现场质量情况" />

                <TextView
                    style="@style/style_form_title1"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_10"
                    android:text="质量缺陷隐患情况：" />

                <EditText
                    android:id="@+id/et_zlqxyhqk"
                    style="@style/et_remark"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_5"
                    android:hint="请输入质量缺陷隐患情况" />

                <TextView
                    style="@style/style_form_title1"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_10"
                    android:text="检查及抽查情况：" />

                <EditText
                    android:id="@+id/et_jcqk"
                    style="@style/et_remark"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_5"
                    android:hint="请输入检查及抽查情况" />

                <TextView
                    style="@style/style_form_title1"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_10"
                    android:text="其他质量情况：" />

                <EditText
                    android:id="@+id/et_qtqk"
                    style="@style/et_remark"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_5"
                    android:hint="请输入其他质量情况" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_item5"
                style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="安全情况"
                    android:textSize="@dimen/sp_16"
                    android:textStyle="bold" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <ImageView
                    android:id="@+id/iv_item5"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:padding="@dimen/dp_3"
                    android:src="@drawable/arrow_open_close" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout
                android:id="@+id/ll_content5"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="@dimen/dp_5"
                android:paddingRight="@dimen/dp_5"
                android:visibility="gone">
                <TextView
                    style="@style/style_form_title1"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_10"
                    android:text="本月安全情况：" />

                <EditText
                    android:id="@+id/et_byaqqk"
                    style="@style/et_remark"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_5"
                    android:hint="请输入本月安全情况" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_item6"
                style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="月报内容"
                    android:textSize="@dimen/sp_16"
                    android:textStyle="bold" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <ImageView
                    android:id="@+id/iv_item6"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:padding="@dimen/dp_3"
                    android:src="@drawable/arrow_open_close" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout
                android:id="@+id/ll_content6"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="@dimen/dp_5"
                android:paddingRight="@dimen/dp_5"
                android:visibility="gone">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_10"
                    android:text="月报内容：" />

                <EditText
                    android:id="@+id/et_content"
                    style="@style/et_remark"
                    android:layout_height="130dp"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_5"
                    android:hint="请输入月报内容"/>

            </LinearLayout>

            <TextView
                android:id="@+id/tv_sub"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:layout_marginBottom="@dimen/dp_30"
                android:layout_marginLeft="@dimen/dp_30"
                android:layout_marginRight="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_50"
                android:background="@drawable/button_fill_bg"
                android:gravity="center"
                android:text="提 交"
                android:textColor="@color/text_bg"
                android:textSize="@dimen/sp_14" />

        </LinearLayout>

    </ScrollView>


</LinearLayout>