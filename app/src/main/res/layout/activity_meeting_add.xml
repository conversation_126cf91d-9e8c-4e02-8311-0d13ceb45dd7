<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/total_bar" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout style="@style/style_form_ll1"
                android:orientation="vertical">

                <TextView
                    style="@style/style_form_title1"
                    android:text="项目名称：" />

                <TextView
                    android:id="@+id/tv_project_name"
                    style="@style/tv_form_content"
                    android:ellipsize="end"
                    android:minLines="2"
                    android:hint="项目名称"/>
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="会议主题：" />

                <EditText
                    android:id="@+id/ed_metting_theme"
                    style="@style/et_notice_bg1"
                    android:hint="请输入会议主题"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="会议日期：" />

                <TextView
                    android:id="@+id/tv_start_date"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1"
                    android:hint="请选择"
                     />

                <TextView
                    style="@style/style_form_title1"
                    android:text="至" />

                <TextView
                    android:id="@+id/tv_end_date"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:hint="请选择"
                    android:layout_weight="1"
                     />


            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_marginTop="@dimen/dp_0"
                >

                <TextView
                    style="@style/style_form_title1"
                    android:layout_width="wrap_content"
                    android:layout_height="24dp"
                    android:text="会议开始时间：" />
                <Spinner
                    android:id="@+id/sp_start_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    />
            </LinearLayout>

            <View style="@style/v_drvier_line_h"
                android:layout_marginTop="@dimen/dp_0"
                />
            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_marginTop="@dimen/dp_0"
                >
                <TextView
                    style="@style/style_form_title1"
                    android:text="会议结束时间：" />
                <Spinner
                    android:id="@+id/sp_end_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    />


            </LinearLayout>

            <View style="@style/v_drvier_line_h"
                android:layout_marginTop="@dimen/dp_0"
                />
            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="会议类型："
                    />

                <Spinner
                    android:id="@+id/sp_meet_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:entries="@array/array_meet_type"

                    />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="会议地点：" />

                <EditText
                    android:id="@+id/ed_address"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1"
                    android:hint="请输入会议地点"
                    />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="主持人：" />

                <EditText
                    android:id="@+id/ed_host"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:hint="请输入主持人"
                    android:layout_marginLeft="@dimen/dp_22"
                    android:layout_weight="1" />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <TextView
                style="@style/style_form_title1"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_10"
                android:text="参与人员：" />
                <EditText
                    android:id="@+id/ed_join"
                    style="@style/et_remark"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_5"
                    android:layout_marginRight="@dimen/dp_10"
                    android:hint="请输入参与人员"
                    android:layout_weight="1" />
            <View style="@style/v_drvier_line_h" />
            <TextView
                style="@style/style_form_title1"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_10"
                android:text="会议正文：" />

            <EditText
                android:id="@+id/et_content"
                style="@style/et_remark"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_5"
                android:layout_marginRight="@dimen/dp_10"
                android:hint="请输入会议正文" />

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"/>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="@dimen/padding"
                android:paddingRight="@dimen/padding">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="图片"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_picturepath"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/check_image_hei"
                    android:layout_marginBottom="@dimen/padding"
                    android:orientation="horizontal" />
            </LinearLayout>
            <LinearLayout
                android:id="@+id/ll_answer"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:layout_margin="@dimen/dp_27"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_save"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginRight="@dimen/padding"
                    android:layout_weight="1"
                    android:background="@drawable/button_fill_bg"
                    android:gravity="center"
                    android:visibility="gone"
                    android:text="保 存"
                    android:textColor="@color/text_bg"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_sub"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/padding"
                    android:layout_weight="1"
                    android:background="@drawable/button_fill_bg"
                    android:gravity="center"
                    android:text="提 交"
                    android:textColor="@color/text_bg"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>