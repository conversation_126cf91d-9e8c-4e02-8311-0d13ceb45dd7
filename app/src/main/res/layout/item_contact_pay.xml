<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/dp_10">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_time"
                style="@style/style_notice_item"
                android:text="2019-05-13"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/sp_14"
                android:textStyle="bold" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                style="@style/style_notice_item"
                android:text="最后更新：" />

            <TextView
                android:id="@+id/tv_create_name"
                style="@style/style_notice_item" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                style="@style/style_notice_item"
                android:text="变更编号：" />

            <TextView
                android:id="@+id/tv_no"
                style="@style/style_notice_item" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:orientation="horizontal">

            <TextView
                style="@style/style_notice_item"
                android:text="变更类型：" />

            <TextView
                android:id="@+id/tv_type"
                style="@style/style_notice_item"
                android:ellipsize="end"
                android:lines="1" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:orientation="horizontal">
            <TextView
                style="@style/style_notice_item"
                android:text="变更价款(元)：" />

            <TextView
                android:id="@+id/tv_amount"
                style="@style/style_notice_item" />

        </LinearLayout>
    </LinearLayout>

    <View style="@style/v_drvier_line_h" />
</LinearLayout>