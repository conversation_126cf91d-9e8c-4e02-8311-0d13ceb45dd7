<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/total_bar"/>


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:padding="@dimen/padding"
            android:layout_height="match_parent">

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_27">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="工程名称："
                    android:textSize="@dimen/sp_12"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_project_name"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="2"
                    android:textSize="@dimen/sp_12"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>



            </LinearLayout>
            <include layout="@layout/line_space"/>

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="回访客户："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_visit_unit"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="回访人员："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_visiter"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>

            <TextView
                android:id="@+id/tv_rg1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:gravity="center_vertical"
                android:textSize="@dimen/sp_14"
                android:background="@color/line_space"
                android:textColor="@color/text_main_black"/>

            <RadioGroup
                android:id="@+id/rg_1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/def_height"
                android:orientation="horizontal">


                <RadioButton
                    android:id="@+id/rb1_pleased_1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="不满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_1_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


                <RadioButton
                    android:id="@+id/rb1_pleased_2"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="基本满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_2_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


                <RadioButton
                    android:id="@+id/rb1_pleased_3"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="较满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_0_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


                <RadioButton
                    android:id="@+id/rb1_pleased_4"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="非常满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_3_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


            </RadioGroup>

            <TextView
                android:id="@+id/tv_rg2"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:gravity="center_vertical"
                android:textSize="@dimen/sp_14"
                android:background="@color/line_space"
                android:textColor="@color/text_main_black"/>

            <RadioGroup
                android:id="@+id/rg_2"
                android:layout_width="match_parent"
                android:layout_height="@dimen/def_height"
                android:orientation="horizontal">


                <RadioButton
                    android:id="@+id/rb2_pleased_1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="不满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_1_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


                <RadioButton
                    android:id="@+id/rb2_pleased_2"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="基本满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_2_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


                <RadioButton
                    android:id="@+id/rb2_pleased_3"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="较满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_0_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


                <RadioButton
                    android:id="@+id/rb2_pleased_4"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="非常满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_3_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


            </RadioGroup>


            <TextView
                android:id="@+id/tv_rg3"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:gravity="center_vertical"
                android:textSize="@dimen/sp_14"
                android:background="@color/line_space"
                android:textColor="@color/text_main_black"/>

            <RadioGroup
                android:id="@+id/rg_3"
                android:layout_width="match_parent"
                android:layout_height="@dimen/def_height"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/rb3_pleased_1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="不满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_1_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


                <RadioButton
                    android:id="@+id/rb3_pleased_2"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="基本满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_2_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


                <RadioButton
                    android:id="@+id/rb3_pleased_3"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="较满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_0_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


                <RadioButton
                    android:id="@+id/rb3_pleased_4"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="非常满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_3_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


            </RadioGroup>

            <TextView
                android:id="@+id/tv_rg4"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:gravity="center_vertical"
                android:textSize="@dimen/sp_14"
                android:background="@color/line_space"
                android:textColor="@color/text_main_black"/>

            <RadioGroup
                android:id="@+id/rg_4"
                android:layout_width="match_parent"
                android:layout_height="@dimen/def_height"
                android:orientation="horizontal">


                <RadioButton
                    android:id="@+id/rb4_pleased_1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="不满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_1_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


                <RadioButton
                    android:id="@+id/rb4_pleased_2"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="基本满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_2_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


                <RadioButton
                    android:id="@+id/rb4_pleased_3"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="较满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_0_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


                <RadioButton
                    android:id="@+id/rb4_pleased_4"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="非常满意"
                    android:textSize="@dimen/sp_12"
                    android:button="@drawable/button_pleased_3_bg"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:layout_weight="1"/>


            </RadioGroup>



            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:text="总体评价："
                android:gravity="center_vertical"
                android:textSize="@dimen/sp_14"
                android:background="@color/line_space"
                android:textColor="@color/text_main_black"/>

            <EditText
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="120dp"
                android:gravity="center_vertical"
                android:hint="在此填写客户意见总结"
                android:textColorHint="@color/edit_hit"
                android:background="@null"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/text_main_black"/>

            <TextView
                android:id="@+id/tv_sub"
                android:layout_width="match_parent"
                android:layout_margin="@dimen/dp_27"
                android:background="@drawable/button_fill_bg"
                android:layout_height="@dimen/title_bar_hei"
                android:text="提 交"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/title_text"
                android:gravity="center"/>


        </LinearLayout>

    </ScrollView>

</LinearLayout>