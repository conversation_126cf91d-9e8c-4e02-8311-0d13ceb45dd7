<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white"
    tools:context=".views.CourseDetailsViewController">

    <com.kisoft.yuejianli.ui.MyJzvd
        android:id="@+id/jz_video"
        android:layout_width="match_parent"
        android:layout_height="200dp" />

    <RadioGroup
        android:id="@+id/rg_sheet_type"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:gravity="center"
        android:orientation="horizontal">

        <RadioButton
            android:id="@+id/bt_type_1"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:button="@null"
            android:checked="true"
            android:gravity="center"
            android:padding="@dimen/dp_5"
            android:textColor="@color/colorAccent"
            android:textSize="@dimen/sp_15"
            android:text="介绍"/>

        <View
            android:layout_width="1dp"
            android:layout_height="@dimen/dp_25"
            android:background="@color/line_space" />

        <RadioButton
            android:id="@+id/bt_type_2"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:button="@null"
            android:gravity="center"
            android:padding="@dimen/dp_5"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_15"
            android:text="章节"/>

        <View
            android:layout_width="1dp"
            android:layout_height="@dimen/dp_25"
            android:background="@color/line_space" />

        <RadioButton
            android:id="@+id/bt_type_3"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:button="@null"
            android:gravity="center"
            android:padding="@dimen/dp_5"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_15"
            android:text="评论"/>

        <View
            android:layout_width="1dp"
            android:layout_height="@dimen/dp_25"
            android:background="@color/line_space" />

        <RadioButton
            android:id="@+id/bt_type_4"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:button="@null"
            android:gravity="center"
            android:padding="@dimen/dp_5"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_15"
            android:text="笔记"/>

    </RadioGroup>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <View
            android:id="@+id/view1"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_1"
            android:layout_margin="@dimen/dp_5"
            android:layout_weight="1"
            android:background="@color/colorAccent" />

        <View
            android:id="@+id/view2"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_1"
            android:layout_margin="@dimen/dp_5"
            android:layout_weight="1"
            android:background="@color/line_space" />

        <View
            android:id="@+id/view3"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_1"
            android:layout_margin="@dimen/dp_5"
            android:layout_weight="1"
            android:background="@color/line_space" />

        <View
            android:id="@+id/view4"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_1"
            android:layout_margin="@dimen/dp_5"
            android:layout_weight="1"
            android:background="@color/line_space" />
    </LinearLayout>

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/vp_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>