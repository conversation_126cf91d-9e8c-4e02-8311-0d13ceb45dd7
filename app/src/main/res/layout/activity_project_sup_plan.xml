<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_gradient_bg"
    android:orientation="vertical">

    <include layout="@layout/total_bar" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="@dimen/dp_10"
        android:background="@drawable/shape_bg"
        android:orientation="vertical"
        android:padding="10dp">

        <RadioGroup
            android:id="@+id/rg_tab"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:gravity="center"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/rb_tab1"
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                android:padding="@dimen/dp_5"
                android:text="详细信息"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/sp_15" />

            <View
                android:layout_width="1dp"
                android:layout_height="@dimen/dp_25"
                android:background="@color/line_space" />

            <RadioButton
                android:id="@+id/rb_tab2"
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:button="@null"
                android:gravity="center"
                android:padding="@dimen/dp_5"
                android:text="审批办理"
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_15" />
        </RadioGroup>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <View
                android:id="@+id/view1"
                android:layout_width="@dimen/dp_0"
                android:layout_height="@dimen/dp_1"
                android:layout_margin="@dimen/dp_5"
                android:layout_weight="1"
                android:background="@color/colorAccent" />

            <View
                android:id="@+id/view2"
                android:layout_width="@dimen/dp_0"
                android:layout_height="@dimen/dp_1"
                android:layout_margin="@dimen/dp_5"
                android:layout_weight="1"
                android:background="@color/line_space" />
        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ll_tab_content1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout style="@style/style_form_ll1">

                        <TextView
                            style="@style/style_form_title1"
                            android:text="主　　题：" />

                        <TextView
                            android:id="@+id/tv_subject"
                            style="@style/et_notice_bg1"
                            android:layout_width="@dimen/dp_0"
                            android:layout_weight="1" />
                    </LinearLayout>


                    <LinearLayout style="@style/style_form_ll1">

                        <TextView
                            style="@style/style_form_title1"
                            android:text="工程名称：" />

                        <TextView
                            android:id="@+id/tv_project_name"
                            style="@style/et_notice_bg1"
                            android:layout_width="@dimen/dp_0"
                            android:layout_weight="1"
                            android:lines="2" />
                    </LinearLayout>


                    <LinearLayout style="@style/style_form_ll1">

                        <TextView
                            style="@style/style_form_title1"
                            android:text="项目总监：" />

                        <TextView
                            android:id="@+id/tv_charger"
                            style="@style/et_notice_bg1"
                            android:layout_width="@dimen/dp_0"
                            android:layout_weight="1" />
                    </LinearLayout>


                    <LinearLayout
                        style="@style/style_form_ll1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            style="@style/style_form_title1"
                            android:layout_marginTop="@dimen/dp_10"
                            android:text="内　　容：" />

                        <TextView
                            android:id="@+id/tv_content"
                            style="@style/et_remark"
                            android:layout_marginTop="@dimen/dp_5" />
                    </LinearLayout>

                    <LinearLayout style="@style/style_form_ll1">

                        <TextView
                            style="@style/style_form_title1"
                            android:text="审核状态：" />

                        <TextView
                            android:id="@+id/tv_verifier_status"
                            style="@style/et_notice_bg1"
                            android:layout_width="@dimen/dp_0"
                            android:layout_weight="1" />
                    </LinearLayout>


                    <com.kisoft.yuejianli.ui.YFileListView
                        android:id="@+id/y_file_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                    <!--
                                    <TextView
                                        style="@style/style_form_title1"
                                        android:layout_marginLeft="@dimen/dp_10"
                                        android:layout_marginTop="@dimen/dp_10"
                                        android:text="附　　件：" />



                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/rv_list_fj"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content" />
                                        -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="10dp" />
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/ll_tab_content2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        style="@style/style_form_title1"
                        android:layout_margin="@dimen/dp_10"
                        android:text="办理过程：" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_10"
                        android:layout_marginRight="@dimen/dp_10" />

                </LinearLayout>
            </LinearLayout>

        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</LinearLayout>