<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_horizontal"
        android:background="@drawable/demo_bg_dialog_list">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:text="标题"
            android:textSize="14sp"
            android:minHeight="40dp"
            android:gravity="center"
            android:textColor="@color/gray_normal"/>

        <View
            android:id="@+id/view_divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@drawable/demo_default_divider_list"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_dialog_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:background="#f9f9f9"/>

    <Button
        android:id="@+id/btn_cancel"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        tools:text="取消"
        android:textSize="@dimen/em_size_big"
        android:textColor="@color/red"
        android:background="@drawable/demo_bg_dialog_list_button"/>

</LinearLayout>