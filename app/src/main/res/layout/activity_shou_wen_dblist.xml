<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".views.ShouWenDBListActivity">

    <include layout="@layout/total_bar" />

    <RadioGroup
        android:id="@+id/rg_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:gravity="center"
        android:orientation="horizontal">

        <RadioButton
            android:id="@+id/rb_task_detail"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:button="@null"
            android:checked="true"
            android:gravity="center"
            android:padding="@dimen/dp_5"
            android:textColor="@color/colorAccent"
            android:textSize="@dimen/sp_15"
            android:text="基本信息" />

        <View
            android:layout_width="1dp"
            android:layout_height="@dimen/dp_25"
            android:background="@color/line_space" />

        <RadioButton
            android:id="@+id/rb_task_record"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:button="@null"
            android:gravity="center"
            android:padding="@dimen/dp_5"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_15"
            android:text="审批办理" />
    </RadioGroup>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <View
            android:id="@+id/view_task_detail"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_1"
            android:layout_margin="@dimen/dp_5"
            android:layout_weight="1"
            android:background="@color/colorAccent" />

        <View
            android:id="@+id/view_task_record"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_1"
            android:layout_margin="@dimen/dp_5"
            android:layout_weight="1"
            android:background="@color/line_space" />
    </LinearLayout>

    <com.kisoft.yuejianli.ui.NoScrollViewPager
        android:id="@+id/vp_mainframe_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/ll_public_titleBar_fg_main" />
</LinearLayout>