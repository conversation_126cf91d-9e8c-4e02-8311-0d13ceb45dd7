<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/total_bar" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="@dimen/dp_12">

        <TextView
            android:id="@+id/tv_author"
            style="@style/tv_text_author_bg" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_10"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_sponsor"
                style="@style/style_notice_item"
                android:text="杨建军"
                android:textColor="@color/text_main_black" />

            <TextView
                android:id="@+id/tv_status"
                style="@style/style_notice_item"
                android:layout_marginTop="@dimen/dp_5"
                android:text="待审批"
                android:textColor="@color/text_main_black" />


        </LinearLayout>


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="@dimen/dp_12"
        android:paddingLeft="@dimen/dp_12">

        <TextView
            style="@style/style_notice_item"
            android:text="通知单编号："
            android:textColor="@color/text_main_black" />

        <TextView
            android:id="@+id/tv_pend_num"
            style="@style/style_notice_item"
            android:text="201812170096"
            android:textColor="@color/text_main_black" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="@dimen/dp_12"
        android:paddingLeft="@dimen/dp_12">

        <TextView
            style="@style/style_notice_item"
            android:text="通知单类型："
            android:textColor="@color/text_main_black" />

        <TextView
            android:id="@+id/tv_notice_type"
            style="@style/style_notice_item"
            android:text="安全隐患通知单"
            android:textColor="@color/text_main_black" />

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="@dimen/dp_12"
        android:paddingLeft="@dimen/dp_12">

        <TextView
            style="@style/style_notice_item"
            android:text="详        情："
            android:textColor="@color/text_main_black" />

        <TextView
            android:id="@+id/tv_content"
            style="@style/style_notice_item"
            android:text="详情"
            android:textColor="@color/text_main_black" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/line_space" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_12"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_create_author"
                style="@style/tv_text_author_bg" />

            <TextView
                android:id="@+id/tv_create"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_weight="0.4"
                android:text="杨建军"
                android:textColor="@color/text_main_black" />

            <TextView
                android:id="@+id/tv_create_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:gravity="center"
                android:text="发起的审批"
                android:textColor="@color/text_main_black" />

            <TextView
                android:id="@+id/tv_create_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp_30"
                android:layout_weight="1"
                android:gravity="right"
                android:text="2018-12-17 13:05"
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_12" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_12"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_approver_author"
                style="@style/tv_text_author_bg" />

            <TextView
                android:id="@+id/tv_approver"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_weight="0.4"
                android:text="邢主任"
                android:textColor="@color/text_main_black" />

            <TextView
                android:id="@+id/tv_approver_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:gravity="center"
                android:text="待审批"
                android:textColor="@color/text_main_black" />

            <TextView
                android:id="@+id/tv_approver_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp_30"
                android:layout_weight="1"
                android:gravity="right"
                android:text=""
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_12" />

        </LinearLayout>



    </LinearLayout>


    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/line_space" />

    <LinearLayout
        android:id="@+id/ll_btn"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_60"
        android:gravity="center"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_agree"
            style="@style/rb_notice_bg_checked"
            android:text="同意"
            android:textColor="@color/colorAccent" />

        <Button
            android:id="@+id/btn_refuse"
            style="@style/rb_notice_bg_checked"
            android:layout_marginLeft="@dimen/dp_36"
            android:text="拒绝"
            android:textColor="@color/colorAccent" />

    </LinearLayout>

</LinearLayout>