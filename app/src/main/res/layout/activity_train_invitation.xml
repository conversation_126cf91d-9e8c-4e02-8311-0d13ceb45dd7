<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent">
    <include layout="@layout/total_bar"/>


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:padding="@dimen/padding"
            android:layout_height="match_parent">


            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="标         题："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_train_invitation_title"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>


            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="截止时间："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_over_time"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>


            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="拟定部门："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_train_invitation_org"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>


            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="拟 定   人："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_train_invitation_person"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>


            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="征集时间："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>

                <TextView
                    android:id="@+id/tv_begin_time"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

                <TextView
                    android:layout_width="@dimen/dp_27"
                    android:layout_height="match_parent"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:text="到"
                    android:gravity="center"/>


                <TextView
                    android:id="@+id/tv_end_time"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"/>

            </LinearLayout>
            <include layout="@layout/line_space"/>


            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:text="详细内容："
                android:gravity="center_vertical"
                android:textSize="@dimen/sp_14"
                android:background="@color/line_space"
                android:textColor="@color/text_main_black"/>

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="120dp"
                android:gravity="center_vertical"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/ic_text_normal"/>

            <include layout="@layout/line_space"/>


            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:text="参与人："
                android:gravity="center_vertical"
                android:textSize="@dimen/sp_14"
                android:background="@color/line_space"
                android:textColor="@color/text_main_black"/>

            <TextView
                android:id="@+id/tv_add_names"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="120dp"
                android:gravity="center_vertical"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/ic_text_normal"/>

            <include layout="@layout/line_space"/>
            
            <LinearLayout
                android:id="@+id/ll_answer"
                android:visibility="gone"
                android:orientation="horizontal"
                android:layout_margin="@dimen/dp_27"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei" >

                <TextView
                    android:id="@+id/tv_ok"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/button_fill_bg"
                    android:text="赞成"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_bg"
                    android:layout_marginRight="@dimen/padding"
                    android:gravity="center"/>

                <TextView
                    android:id="@+id/tv_no"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/button_fill_bg"
                    android:text="不赞成"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_bg"
                    android:layout_marginLeft="@dimen/padding"
                    android:gravity="center"/>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>