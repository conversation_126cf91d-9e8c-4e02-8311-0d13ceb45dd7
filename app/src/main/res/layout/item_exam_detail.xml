<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/padding"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:focusableInTouchMode="true"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/text_main_black"
            android:lineSpacingExtra="@dimen/dp_3"
            android:textSize="@dimen/sp_14"
            />

        <ImageView
            android:id="@+id/ivPhoto"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_yuhua"
            android:visibility="gone"
            android:layout_marginTop="@dimen/dp_5"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycleView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <EditText
            android:id="@+id/etContent"
            style="@style/et_remark"
            android:visibility="gone"
            android:layout_marginTop="@dimen/dp_5"
            android:hint="请输入您的答案"/>
        <TextView
            android:id="@+id/tvConfirm"
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_bar_hei"
            android:background="@drawable/button_fill_bg"
            android:text="确定"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_bg"
            android:layout_marginTop="@dimen/dp_50"
            android:layout_marginBottom="@dimen/dp_30"
            android:layout_marginLeft="@dimen/dp_30"
            android:layout_marginRight="@dimen/dp_30"
            android:gravity="center"/>

        <LinearLayout
            android:id="@+id/llRightAnswer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="visible"
            android:orientation="vertical">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_15"
                android:layout_marginTop="@dimen/dp_30"
                android:text="试题解析"/>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_0_5"
                android:layout_marginTop="@dimen/padding"
                android:background="@color/line_space"/>
            <TextView
                android:id="@+id/tvRightAnswer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/text_main_black"
                android:layout_marginTop="@dimen/padding"
                android:lineSpacingExtra="@dimen/dp_3"
                android:text="答案："
                />


            <TextView
                android:id="@+id/tvAnalysis"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/padding"
                android:lineSpacingExtra="@dimen/dp_3"
                android:layout_marginTop="@dimen/padding"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/text_main_black"
                />

        </LinearLayout>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
