<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/total_bar"/>
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="@dimen/padding">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="问题名称："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <EditText
                    android:id="@+id/et_accident_title"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="请输入质量问题标题"
                    android:textColorHint="@color/edit_hit"
                    android:drawableLeft="@drawable/ic_edit_hit"
                    android:drawablePadding="@dimen/padding"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                style="@style/style_form_ll1"
                android:orientation="vertical"
                android:padding="@dimen/dp_0">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="项目名称："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_project"
                    style="@style/tv_form_content"
                    android:ellipsize="end"
                    android:minLines="2"
                    android:hint="项目名称"
                    android:layout_marginBottom="@dimen/dp_10"/>
            </LinearLayout>

            <include layout="@layout/line_space" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="施工负责人："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <EditText
                    android:id="@+id/et_accident_construction_charge"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:hint="请输入施工负责人"
                    android:background="@null"
                    android:textColorHint="@color/edit_hit"
                    android:drawablePadding="@dimen/padding"
                    android:drawableLeft="@drawable/ic_edit_hit"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <include layout="@layout/line_space" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="问题跟踪人："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_accident_supervision_charge"
                    android:layout_width="wrap_content"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

                <ImageView
                    android:id="@+id/iv_supervision_charge_more"
                    android:layout_width="@dimen/dp_40"
                    android:layout_height="match_parent"
                    android:scaleType="center"
                    android:src="@drawable/ic_down" />
            </LinearLayout>

            <include layout="@layout/line_space" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="限期完成："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />


                <TextView
                    android:id="@+id/tv_accident_complete_time"
                    android:layout_width="wrap_content"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

                <ImageView
                    android:id="@+id/iv_complete_time_more"
                    android:layout_width="@dimen/dp_40"
                    android:layout_height="match_parent"
                    android:scaleType="center"
                    android:src="@drawable/ic_down" />
            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="320dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:text="下发监理通知单"
                    android:drawablePadding="@dimen/padding"
                    android:drawableLeft="@drawable/ic_supervision"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <CheckBox
                    android:id="@+id/cb_send_supervision_notice"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:button="@null"
                    android:background="@drawable/check_box_bg"/>
            </LinearLayout>

            <include layout="@layout/line_space"/>

            <LinearLayout
                android:id="@+id/ll_picture"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="现场图片"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_check_picture"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/check_image_hei"
                    android:layout_marginBottom="@dimen/padding"
                    android:orientation="horizontal">

                </androidx.recyclerview.widget.RecyclerView>

                <include layout="@layout/line_space" />

            </LinearLayout>


            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:gravity="center_vertical"
                android:text="问题描述"
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_16" />

            <EditText
                android:id="@+id/et_accident_dis"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:background="@null"
                android:drawableLeft="@drawable/ic_edit_hit"
                android:drawablePadding="@dimen/padding"
                android:gravity="fill_vertical"
                android:hint="请输入问题描述"
                android:minHeight="@dimen/dp_72"
                android:textColor="@color/ic_text_normal"
                android:textColorHint="@color/edit_hit"
                android:textSize="@dimen/sp_14" />
            <include layout="@layout/line_space"/>
            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp_40">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:text="问题等级"
                    android:textSize="@dimen/sp_16"
                    android:textColor="@color/text_main_black"
                    android:gravity="center_vertical"/>
                <RadioGroup
                    android:id="@+id/rg_accident_level"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei">

                    <RadioButton
                        android:id="@+id/rb_accident_level3"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:text="预警"
                        android:textSize="@dimen/sp_14"
                        android:textColor="@color/ic_text_normal"/>

                    <RadioButton
                        android:id="@+id/rb_accident_level2"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:text="异常"
                        android:textSize="@dimen/sp_14"
                        android:textColor="@color/ic_text_normal"/>

                    <RadioButton
                        android:id="@+id/rb_accident_level1"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:text="正常"
                        android:textSize="@dimen/sp_14"
                        android:textColor="@color/ic_text_normal"/>

                </RadioGroup>



            </LinearLayout>

            <TextView
                android:id="@+id/tv_submit"
                android:layout_width="match_parent"
                android:layout_height="@dimen/bottom_tab_hei"
                android:textSize="@dimen/sp_16"
                android:text="提 交"
                android:background="@drawable/button_fill_bg"
                android:textColor="@color/text_bg"
                android:gravity="center"
                android:layout_margin="@dimen/line_space_wid"/>



        </LinearLayout>

    </ScrollView>

</LinearLayout>