<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="@dimen/padding">

    <TextView
        android:id="@+id/tvName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_main_black"
        android:textSize="@dimen/sp_14"
        android:textStyle="bold"
        android:text="--"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:gravity="center_vertical">
        <TextView
            android:id="@+id/tvScore"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textSize="@dimen/sp_12"
            android:textColor="@color/text_main_black"
            android:text="总分"/>
        <TextView
            android:id="@+id/tvElapsedTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_12"
            android:text="用时"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:gravity="center_vertical">
        <TextView
            android:id="@+id/tvExamType"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textSize="@dimen/sp_12"
            android:textColor="@color/text_main_black"
            android:text="试卷用途"/>
        <TextView
            android:id="@+id/tvGo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/button_fill_bg"
            android:text="开始考试"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/white"
            android:paddingStart="@dimen/padding"
            android:paddingEnd="@dimen/padding"
            android:paddingTop="@dimen/dp_5"
            android:paddingBottom="@dimen/dp_5"
            />
    </LinearLayout>

</LinearLayout>
