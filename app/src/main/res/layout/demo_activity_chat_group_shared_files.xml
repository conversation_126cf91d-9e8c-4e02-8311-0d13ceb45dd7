<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.hyphenate.easeui.widget.EaseTitleBar
        android:id="@+id/title_bar"
        android:layout_width="0dp"
        android:layout_height="@dimen/em_common_title_bar_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:titleBarTitle="@string/em_chat_group_detail_share_file"
        app:titleBarRightTitle="@string/em_chat_group_shared_files_upload"
        app:titleBarRightVisible="true"
        app:titleBarDisplayHomeAsUpEnabled="true"/>

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/srl_refresh"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_bar"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.hyphenate.easeui.widget.EaseRecyclerView
            android:id="@+id/rv_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>