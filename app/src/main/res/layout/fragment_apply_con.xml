<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/scrollView"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".views.ApplyConFragment">

   <LinearLayout
       android:layout_width="match_parent"
       android:layout_height="wrap_content"
       android:orientation="vertical">

       <LinearLayout
           style="@style/line_item">
           <TextView
               android:id="@+id/tvBaseTitle"
               android:layout_width="0dp"
               android:layout_weight="1"
               android:layout_height="match_parent"
               android:textColor="@color/text_main_black"
               android:textSize="@dimen/sp_15"
               android:textStyle="bold"
               android:gravity="center_vertical"
               android:text="基本信息"/>
           <ImageView
               android:id="@+id/ivBaseArrow"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:src="@drawable/arrow_down"
               android:paddingStart="@dimen/padding"
               android:paddingEnd="@dimen/padding"/>
       </LinearLayout>

       <include layout="@layout/line_space"/>

       <LinearLayout
           android:id="@+id/llBaseContent"
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           android:orientation="vertical">
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="实施方式："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvConClass"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivConClass"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/ic_down" />
           </LinearLayout>

           <include layout="@layout/line_space" />

           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="合同类别："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvConType"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivConType"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/ic_down" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="业务性质："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvBusiNature"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivBusiNature"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/ic_down" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="获取方式："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvAcquiWay"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivAcquiWay"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/ic_down" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="资金来源："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvSourceFonds"
                   android:layout_width="0dp"

                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivSourceFonds"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/ic_down" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="合同档号："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <EditText
                   android:id="@+id/etConNo"
                   android:layout_width="match_parent"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:background="@null"
                   android:hint="请输入合同档号"
                   android:textSize="@dimen/sp_14" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/style_form_ll1"
               android:orientation="vertical">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="合同名称："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <EditText
                   android:id="@+id/etConName"
                   style="@style/tv_form_content"
                   android:ellipsize="end"
                   android:minLines="2"
                   android:hint="请输入合同名称"
                   android:layout_marginBottom="@dimen/dp_10"/>
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="合同金额："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <EditText
                   android:id="@+id/etConAmount"
                   android:layout_width="match_parent"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:inputType="number"
                   android:background="@null"
                   android:hint="请输入合同金额"
                   android:textSize="@dimen/sp_14" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="合同工期："
                   android:inputType="number"
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <EditText
                   android:id="@+id/etConPeriod"
                   android:layout_width="match_parent"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:background="@null"
                   android:hint="请输入合同工期（天数）"
                   android:textSize="@dimen/sp_14" />
           </LinearLayout>

           <include layout="@layout/line_space" />

           <TextView
               style="@style/line_item_has_padding"
               android:textSize="@dimen/sp_14"
               android:textColor="@color/text_main_black"
               android:text="工期说明"
               />
           <EditText
               android:id="@+id/etPeriodRemark"
               style="@style/et_remark"
               android:layout_marginTop="@dimen/dp_5"
               android:layout_marginStart="@dimen/padding"
               android:layout_marginEnd="@dimen/padding"
               />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="合同质保金："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <EditText
                   android:id="@+id/etConBond"
                   android:layout_width="match_parent"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:inputType="number"
                   android:background="@null"
                   android:hint="请输入合同质保金"
                   android:textSize="@dimen/sp_14" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="质保金到期日期："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvBondWarrDate"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivBondWarrDate"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/ic_down" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="合同履约金："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <EditText
                   android:id="@+id/etPerfFee"
                   android:layout_width="match_parent"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:inputType="number"
                   android:background="@null"
                   android:hint="请输入合同履约金"
                   android:textSize="@dimen/sp_14" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="履约金形式："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvBondType"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivBondType"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/ic_down" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="履约金到期日期："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvPerFeeWarrDate"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivPerFeeWarrDate"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/ic_down" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="合同计划开始日期："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvPlanStartDate"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivPlanStartDate"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/ic_down" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="合同计划结束日期："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvPlanEndDate"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivPlanEndDate"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/ic_down" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="合同发出日期："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvIssueDate"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivIssueDate"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/ic_down" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="合同回流日期："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvRefulxDate"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivRefulxDate"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/ic_down" />
           </LinearLayout>

           <include layout="@layout/line_space" />
       </LinearLayout>
       <LinearLayout
           style="@style/line_item">
           <TextView
               android:id="@+id/tvProjectTitle"
               android:layout_width="0dp"
               android:layout_weight="1"
               android:layout_height="match_parent"
               android:textColor="@color/text_main_black"
               android:textSize="@dimen/sp_15"
               android:textStyle="bold"
               android:gravity="center_vertical"
               android:text="项目信息"/>
           <ImageView
               android:id="@+id/ivProjectArrow"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:src="@drawable/arrow_down"
               android:paddingStart="@dimen/padding"
               android:paddingEnd="@dimen/padding"/>
       </LinearLayout>

       <include layout="@layout/line_space"/>

       <LinearLayout
           android:id="@+id/llProjectContent"
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           android:orientation="vertical">
           <LinearLayout
               style="@style/style_form_ll1"
               android:orientation="vertical">

               <LinearLayout
                   android:layout_width="match_parent"
                   android:layout_height="wrap_content"
                   android:orientation="horizontal">
                   <TextView
                       android:layout_width="0dp"
                       android:layout_weight="1"
                       android:layout_height="match_parent"
                       android:gravity="center_vertical"
                       android:text="项目名称："
                       android:textColor="@color/text_main_black"
                       android:textSize="@dimen/sp_14" />
                   <ImageView
                       android:id="@+id/ivProjectName"
                       android:layout_width="wrap_content"
                       android:layout_height="match_parent"
                       android:scaleType="center"
                       android:src="@drawable/arrow_right" />
               </LinearLayout>
               <TextView
                   android:id="@+id/tvProjectName"
                   style="@style/tv_form_content"
                   android:ellipsize="end"
                   android:minLines="2"
                   android:hint="项目名称"
                   android:layout_marginBottom="@dimen/dp_10"/>

           </LinearLayout>

           <include layout="@layout/line_space" />

           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="建设单位："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <EditText
                   android:id="@+id/etOwnName"
                   android:layout_width="match_parent"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:background="@null"
                   android:hint="请输入建设单位"
                   android:textSize="@dimen/sp_14" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="总投金额："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <EditText
                   android:id="@+id/etPjTotalInv"
                   android:layout_width="match_parent"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:inputType="number"
                   android:textColor="@color/ic_text_normal"
                   android:background="@null"
                   android:hint="请输入总投金额"
                   android:textSize="@dimen/sp_14" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="工程面积："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <EditText
                   android:id="@+id/etProjectArea"
                   android:layout_width="match_parent"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:background="@null"
                   android:hint="请输入工程面积"
                   android:textSize="@dimen/sp_14" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="项目总监："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvChargeDirName"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivChargeDir"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/arrow_right" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="项目负责人："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvChargeEngName"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivChargeEng"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/arrow_right" />
           </LinearLayout>

           <include layout="@layout/line_space" />
       </LinearLayout>

       <LinearLayout
           style="@style/line_item">
           <TextView
               android:id="@+id/tvConSignTitle"
               android:layout_width="0dp"
               android:layout_weight="1"
               android:layout_height="match_parent"
               android:textColor="@color/text_main_black"
               android:textSize="@dimen/sp_15"
               android:textStyle="bold"
               android:gravity="center_vertical"
               android:text="签订信息"/>
           <ImageView
               android:id="@+id/ivConSignArrow"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:src="@drawable/arrow_down"
               android:paddingStart="@dimen/padding"
               android:paddingEnd="@dimen/padding"/>
       </LinearLayout>

       <include layout="@layout/line_space"/>

       <LinearLayout
           android:id="@+id/llConSignContent"
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           android:orientation="vertical">
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="签订日期："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvConDate"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivConDate"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/ic_down" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="签订地点："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <EditText
                   android:id="@+id/etConAddress"
                   android:layout_width="match_parent"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:background="@null"
                   android:hint="请输入签订地点"
                   android:textSize="@dimen/sp_14" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="联系电话："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <EditText
                   android:id="@+id/etLinkTel"
                   android:layout_width="match_parent"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:background="@null"
                   android:inputType="phone"
                   android:hint="请输入联系电话"
                   android:textSize="@dimen/sp_14" />
           </LinearLayout>

           <include layout="@layout/line_space" />
       </LinearLayout>

       <LinearLayout
           style="@style/line_item">
           <TextView
               android:id="@+id/tvOther"
               android:layout_width="0dp"
               android:layout_weight="1"
               android:layout_height="match_parent"
               android:textColor="@color/text_main_black"
               android:textSize="@dimen/sp_15"
               android:textStyle="bold"
               android:gravity="center_vertical"
               android:text="其他信息"/>
           <ImageView
               android:id="@+id/ivOther"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:src="@drawable/arrow_down"
               android:paddingStart="@dimen/padding"
               android:paddingEnd="@dimen/padding"/>
       </LinearLayout>

       <include layout="@layout/line_space"/>

       <LinearLayout
           android:id="@+id/llOtherContent"
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           android:orientation="vertical">
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="备案总监："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvBidDirName"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivBidDir"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/arrow_right" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="负责部门："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvChargeOrgName"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:hint="请选择负责部门"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivChargeOrg"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/arrow_right" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="实施部门："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvImplOrgName"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:hint="请选择实施部门"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivImplOrg"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/arrow_right" />
           </LinearLayout>

           <include layout="@layout/line_space" />

           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="信息录入人："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvCreateName"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="信息录入时间："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <TextView
                   android:id="@+id/tvCreateTime"
                   android:layout_width="0dp"
                   android:layout_weight="1"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:textSize="@dimen/sp_14" />
               <ImageView
                   android:id="@+id/ivCreateTime"
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:scaleType="center"
                   android:src="@drawable/ic_down" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <LinearLayout
               style="@style/line_item_has_padding">

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="match_parent"
                   android:gravity="center_vertical"
                   android:text="合同付款方式："
                   android:textColor="@color/text_main_black"
                   android:textSize="@dimen/sp_14" />

               <EditText
                   android:id="@+id/etPayWay"
                   android:layout_width="match_parent"
                   android:layout_height="match_parent"
                   android:layout_marginStart="@dimen/line_space_wid"
                   android:gravity="center_vertical"
                   android:textColor="@color/ic_text_normal"
                   android:background="@null"
                   android:hint="请输入合同付款方式"
                   android:textSize="@dimen/sp_14" />
           </LinearLayout>

           <include layout="@layout/line_space" />
           <TextView
               style="@style/line_item_has_padding"
               android:textSize="@dimen/sp_14"
               android:textColor="@color/text_main_black"
               android:text="备注"
               />
           <EditText
               android:id="@+id/etRemark"
               style="@style/et_remark"
               android:layout_marginTop="@dimen/dp_5"
               android:layout_marginStart="@dimen/padding"
               android:layout_marginEnd="@dimen/padding"
               />
       </LinearLayout>

       <LinearLayout
           style="@style/line_item"
           android:visibility="gone">
           <TextView
               android:id="@+id/tvAttachment"
               android:layout_width="0dp"
               android:layout_weight="1"
               android:layout_height="match_parent"
               android:textColor="@color/text_main_black"
               android:textSize="@dimen/sp_15"
               android:textStyle="bold"
               android:gravity="center_vertical"
               android:text="附件信息"/>
           <ImageView
               android:id="@+id/ivAttachment"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:src="@drawable/arrow_up"
               android:paddingStart="@dimen/padding"
               android:paddingEnd="@dimen/padding"/>
       </LinearLayout>

       <androidx.recyclerview.widget.RecyclerView
           android:id="@+id/recycleViewAttachment"
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           android:visibility="gone"/>

       <LinearLayout
           style="@style/line_item"
           android:background="@color/colorPrimary">

           <TextView
               android:layout_width="0dp"
               android:layout_weight="1"
               android:layout_height="wrap_content"
               android:textSize="@dimen/sp_14"
               android:textColor="@color/white"
               android:layout_marginStart="@dimen/padding"
               android:text="支付计划"/>

           <TextView
               android:id="@+id/tvPayPlanAdd"
               android:layout_width="wrap_content"
               android:layout_height="match_parent"
               android:textColor="@color/white"
               android:textSize="@dimen/sp_14"
               android:padding="@dimen/padding"
               android:text="添加"/>

       </LinearLayout>
       <LinearLayout style="@style/line_item">
           <TextView
               android:layout_width="0dp"
               android:layout_height="wrap_content"
               android:layout_weight="1"
               android:gravity="center"
               android:textSize="@dimen/sp_14"
               android:textColor="@color/colorPrimary"
               android:text="支付次数"/>
           <TextView
               android:layout_width="0dp"
               android:layout_height="wrap_content"
               android:layout_weight="1"
               android:gravity="center"
               android:textSize="@dimen/sp_14"
               android:textColor="@color/colorPrimary"
               android:text="支付时间"/>
           <TextView
               android:layout_width="0dp"
               android:layout_height="wrap_content"
               android:layout_weight="1"
               android:gravity="center"
               android:textSize="@dimen/sp_14"
               android:textColor="@color/colorPrimary"
               android:text="支付比例"/>
           <TextView
               android:layout_width="0dp"
               android:layout_height="wrap_content"
               android:layout_weight="1"
               android:gravity="center"
               android:textSize="@dimen/sp_14"
               android:textColor="@color/colorPrimary"
               android:text="支付金额"/>
           <TextView
               android:layout_width="0dp"
               android:layout_height="wrap_content"
               android:layout_weight="1"
               android:gravity="center"
               android:textSize="@dimen/sp_14"
               android:textColor="@color/colorPrimary"
               android:text="支付说明"/>
       </LinearLayout>
       <androidx.recyclerview.widget.RecyclerView
           android:id="@+id/recycleViewPayPlan"
           android:layout_width="match_parent"
           android:layout_height="wrap_content"/>
       <com.kisoft.yuejianli.ui.YFileListView
           android:id="@+id/y_file_list"
           android:layout_width="match_parent"
           android:layout_height="wrap_content"/>
       <TextView
           android:id="@+id/tv_sub"
           android:layout_width="match_parent"
           android:layout_height="@dimen/bottom_tab_hei"
           android:textSize="@dimen/sp_14"
           android:text="提 交"
           android:background="@drawable/button_fill_bg"
           android:textColor="@color/text_bg"
           android:gravity="center"
           android:layout_margin="@dimen/line_space_wid"/>

   </LinearLayout>
</androidx.core.widget.NestedScrollView>