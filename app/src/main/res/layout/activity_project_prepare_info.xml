<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/total_bar"/>

    <LinearLayout
        android:id="@+id/ll_sup_plan"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:padding="@dimen/padding"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:drawableLeft="@drawable/ic_sup_plan"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:drawablePadding="@dimen/padding"
            android:textSize="@dimen/sp_16"
            android:textColor="@color/text_main_black"
            android:text="监理规划"/>

        <TextView
            android:id="@+id/tv_sup_plan_status"
            android:layout_width="120dp"
            android:gravity="center"
            android:textColor="@color/ic_text_normal"
            android:layout_gravity="center_vertical"
            android:drawableRight="@drawable/ic_more"
            android:textSize="@dimen/sp_14"
            android:layout_height="@dimen/dp_40" />


    </LinearLayout>

    <include layout="@layout/line_space"/>

    <LinearLayout
        android:id="@+id/ll_sup_detail_blame"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:padding="@dimen/padding"
        android:layout_height="wrap_content">


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:drawableLeft="@drawable/ic_sup_plan"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:drawablePadding="@dimen/padding"
            android:textSize="@dimen/sp_16"
            android:textColor="@color/text_main_black"
            android:text="监理细则"/>

        <TextView
            android:id="@+id/tv_sup_detail_blame_status"
            android:layout_width="120dp"
            android:gravity="center"
            android:textColor="@color/ic_text_normal"
            android:layout_gravity="center_vertical"
            android:drawableRight="@drawable/ic_more"
            android:textSize="@dimen/sp_14"
            android:layout_height="@dimen/dp_40" />


    </LinearLayout>

    <include layout="@layout/line_space"/>


    <LinearLayout
        android:id="@+id/ll_safe_sup_plan"
        android:orientation="horizontal"
        android:padding="@dimen/padding"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:drawableLeft="@drawable/ic_sup_plan"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:drawablePadding="@dimen/padding"
            android:textSize="@dimen/sp_16"
            android:textColor="@color/text_main_black"
            android:text="安全监理规划"/>

        <TextView
            android:id="@+id/tv_safe_sup_plan_status"
            android:layout_width="120dp"
            android:gravity="center"
            android:textColor="@color/ic_text_normal"
            android:layout_gravity="center_vertical"
            android:drawableRight="@drawable/ic_more"
            android:textSize="@dimen/sp_14"
            android:layout_height="@dimen/dp_40" />


    </LinearLayout>

    <include layout="@layout/line_space"/>


    <LinearLayout
        android:id="@+id/ll_safe_sup_detail_blame"
        android:orientation="horizontal"
        android:padding="@dimen/padding"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:drawableLeft="@drawable/ic_sup_plan"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:drawablePadding="@dimen/padding"
            android:textSize="@dimen/sp_16"
            android:textColor="@color/text_main_black"
            android:text="安全监理细则"/>

        <TextView
            android:id="@+id/tv_safe_sup_detail_blame_status"
            android:layout_width="120dp"
            android:gravity="center"
            android:textColor="@color/ic_text_normal"
            android:layout_gravity="center_vertical"
            android:drawableRight="@drawable/ic_more"
            android:textSize="@dimen/sp_14"
            android:layout_height="@dimen/dp_40" />


    </LinearLayout>

    <include layout="@layout/line_space"/>


    <LinearLayout
        android:id="@+id/ll_onside_sup"
        android:orientation="horizontal"
        android:padding="@dimen/padding"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:drawableLeft="@drawable/ic_sup_plan"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:drawablePadding="@dimen/padding"
            android:textSize="@dimen/sp_16"
            android:textColor="@color/text_main_black"
            android:text="旁站监理方案"/>

        <TextView
            android:id="@+id/tv_onside_sup_status"
            android:layout_width="120dp"
            android:gravity="center"
            android:textColor="@color/ic_text_normal"
            android:layout_gravity="center_vertical"
            android:drawableRight="@drawable/ic_more"
            android:textSize="@dimen/sp_14"
            android:layout_height="@dimen/dp_40" />


    </LinearLayout>

    <include layout="@layout/line_space"/>



    <LinearLayout
        android:id="@+id/ll_take_sample"
        android:orientation="horizontal"
        android:padding="@dimen/padding"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:drawableLeft="@drawable/ic_sup_plan"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:drawablePadding="@dimen/padding"
            android:textSize="@dimen/sp_16"
            android:textColor="@color/text_main_black"
            android:text="见证取样方案"/>

        <TextView
            android:id="@+id/tv_take_sample_status"
            android:layout_width="120dp"
            android:gravity="center"
            android:textColor="@color/ic_text_normal"
            android:layout_gravity="center_vertical"
            android:drawableRight="@drawable/ic_more"
            android:textSize="@dimen/sp_14"
            android:layout_height="@dimen/dp_40" />


    </LinearLayout>

    <include layout="@layout/line_space"/>



    <LinearLayout
        android:id="@+id/ll_first_meeting"
        android:orientation="horizontal"
        android:padding="@dimen/padding"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:drawableLeft="@drawable/ic_sup_plan"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:drawablePadding="@dimen/padding"
            android:textSize="@dimen/sp_16"
            android:textColor="@color/text_main_black"
            android:text="第一次工地例会"/>

        <TextView
            android:id="@+id/tv_first_meeting_status"
            android:layout_width="120dp"
            android:gravity="center"
            android:textColor="@color/ic_text_normal"
            android:layout_gravity="center_vertical"
            android:drawableRight="@drawable/ic_more"
            android:textSize="@dimen/sp_14"
            android:layout_height="@dimen/dp_40" />


    </LinearLayout>

    <include layout="@layout/line_space"/>

</LinearLayout>