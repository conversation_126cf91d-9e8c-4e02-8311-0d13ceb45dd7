<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">


    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="@dimen/bottom_tab_hei">

        <TextView
            android:id="@+id/tv_order"
            android:layout_width="40dp"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:text="序号"
            android:gravity="center"/>

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="55dp"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:text="姓名"
            android:gravity="center"/>


        <TextView
            android:id="@+id/tv_begin_time"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:text="上午上班"
            android:layout_weight="1"
            android:gravity="center"/>
        <TextView
            android:id="@+id/tv_end_time"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:text="上午下班"
            android:layout_weight="1"
            android:gravity="center"/>

        <TextView
            android:id="@+id/tv_begin_time1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:text="下午上班"
            android:layout_weight="1"
            android:gravity="center"/>
        <TextView
            android:id="@+id/tv_end_time1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:text="下午下班"
            android:layout_weight="1"
            android:gravity="center"/>

        <TextView
            android:id="@+id/tv_status"
            android:layout_width="40dp"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:text="状态"
            android:gravity="center"/>

    </LinearLayout>

    <include layout="@layout/line_space"/>




</LinearLayout>