<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <TextView
        android:id="@+id/tv_btnsel_title"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/title_bar_hei"
        android:gravity="center_vertical"
        android:text="结果"
        android:textColor="@color/text_main_black"
        android:textSize="@dimen/sp_16" />

    <RadioGroup
        android:id="@+id/rg_btnsel_result"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/title_bar_hei"
        android:orientation="horizontal">

        <RadioButton
            android:id="@+id/rb_btnsel_yes"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="合格"
            android:textColor="@color/ic_text_normal"
            android:textSize="@dimen/sp_14" />

        <RadioButton
            android:id="@+id/rb_btnsel_no"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/line_space_wid"
            android:gravity="center_vertical"
            android:text="不合格"
            android:textColor="@color/ic_text_normal"
            android:textSize="@dimen/sp_14" />
    </RadioGroup>
    <include layout="@layout/line_space" />
</LinearLayout>