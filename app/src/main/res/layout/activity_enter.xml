<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/smartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/rl_tool"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/bottom_tab_hei">

                    <LinearLayout
                        android:id="@+id/canL"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/tv_month_day"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:paddingLeft="16dp"
                            android:text="1月23日"
                            android:textColor="@color/text_main_black"
                            android:textSize="22sp"
                            android:textStyle="bold"
                            tools:ignore="RtlSymmetry" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:visibility="gone"
                            android:layout_toRightOf="@+id/tv_month_day"
                            android:orientation="vertical"
                            android:paddingLeft="6dp"
                            android:paddingTop="@dimen/padding"
                            android:paddingRight="6dp"
                            android:paddingBottom="@dimen/padding"
                            tools:ignore="RelativeOverlap">

                            <TextView
                                android:id="@+id/tv_year"
                                android:layout_width="wrap_content"
                                android:layout_height="0dp"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:text="2018"
                                android:textColor="@color/text_main_black"
                                android:textSize="@dimen/sp_12" />

                            <TextView
                                android:id="@+id/tv_lunar"
                                android:layout_width="wrap_content"
                                android:layout_height="0dp"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:text="初五"
                                android:textColor="@color/text_main_black"
                                android:textSize="@dimen/sp_12"
                                tools:ignore="SmallSp" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/iv_logo"
                            android:layout_width="@dimen/title_bar_hei"
                            android:layout_height="@dimen/title_bar_hei"
                            android:scaleType="centerInside"
                            android:src="@drawable/ic_yuejianli" />

                        <TextView
                            android:id="@+id/tv_app_name"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/dp_4"
                            android:paddingRight="@dimen/padding"
                            android:text="凯悦软件"
                            android:textColor="@color/colorAccent"
                            android:textSize="@dimen/sp_14" />

                        <!--android:text="盛皓工程建设项目管理"-->
                        <!--android:textSize="@dimen/sp_12"-->

                        <!--android:text="河北裕华"-->
                        <!--android:textSize="@dimen/sp_14"-->

                    </LinearLayout>

                    <FrameLayout
                        android:id="@+id/fl_current"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="12dp"
                        android:visibility="gone">

                        <ImageView
                            android:id="@+id/ib_calendar"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:contentDescription="@string/app_name"
                            android:src="@mipmap/ic_calendar_bg" />

                        <TextView
                            android:id="@+id/tv_current_day"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="2dp"
                            android:gravity="center"
                            android:text="13"
                            android:textColor="@color/colorAccent"
                            android:textSize="12sp" />
                    </FrameLayout>
                </RelativeLayout>

                <include layout="@layout/line_space" />

                <com.haibin.calendarview.CalendarView
                    android:id="@+id/calendarView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#0fff"
                    app:calendar_height="40dp"
                    app:current_month_lunar_text_color="#CFCFCF"
                    app:current_month_text_color="#333333"
                    app:max_year="2030"
                    app:min_year="1991"
                    app:month_view="com.kisoft.yuejianli.ui.MzMonthView"
                    app:month_view_show_mode="mode_only_current"
                    app:other_month_lunar_text_color="#e1e1e1"
                    app:other_month_text_color="#e1e1e1"
                    app:scheme_text="假"
                    app:scheme_text_color="#333"
                    app:scheme_theme_color="#128c4b"
                    app:selected_lunar_text_color="#CFCFCF"
                    app:selected_text_color="#333"
                    app:selected_theme_color="#80cfcfcf"
                    app:week_background="#fff"
                    app:week_text_color="#111111"
                    app:week_view="com.kisoft.yuejianli.ui.MzWeekView"
                    app:year_view_day_text_color="#333333"
                    app:year_view_day_text_size="8sp"
                    app:year_view_month_text_color="#ff1111"
                    app:year_view_month_text_size="18sp"
                    app:year_view_scheme_color="#f17706" />

                <!-- 轮播图 -->
                <com.youth.banner.Banner
                    android:id="@+id/looper_banner"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:visibility="gone" />
                <!--
                            <com.youth.banner.indicator.BaseIndicator
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                -->


                <include layout="@layout/line_space" />


                <LinearLayout
                    android:id="@+id/ll_notes_view"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/title_bar_hei"
                        android:orientation="horizontal"
                        android:paddingLeft="@dimen/padding"
                        android:paddingRight="@dimen/padding">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:drawableLeft="@drawable/ic_news"
                            android:drawablePadding="@dimen/padding"
                            android:gravity="center_vertical"
                            android:text="公告通知"
                            android:textColor="@color/text_main_black"
                            android:textSize="@dimen/sp_14" />

                        <TextView
                            android:id="@+id/tv_notes_count"
                            android:layout_width="@dimen/dp_27"
                            android:layout_height="@dimen/dp_27"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="@dimen/padding"
                            android:background="@drawable/piont_bg"
                            android:gravity="center"
                            android:text="0"
                            android:textColor="@color/text_bg"
                            android:textSize="@dimen/sp_14"
                            android:textStyle="bold"
                            android:visibility="gone" />
                    </LinearLayout>
                    <include layout="@layout/line_space" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_wbs_view"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/title_bar_hei"
                        android:orientation="vertical"
                        android:paddingLeft="@dimen/padding"
                        android:paddingRight="@dimen/padding">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:drawableLeft="@drawable/ic_progress_time"
                            android:drawablePadding="@dimen/padding"
                            android:gravity="center_vertical"
                            android:text="当日待销项"
                            android:textColor="@color/text_main_black"
                            android:textSize="@dimen/sp_14" />
                    </LinearLayout>

                    <include layout="@layout/line_space" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_cer_view"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/title_bar_hei"
                        android:orientation="vertical"
                        android:paddingLeft="@dimen/padding"
                        android:paddingRight="@dimen/padding">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:drawableLeft="@drawable/zhengjian"
                            android:drawablePadding="@dimen/padding"
                            android:gravity="center_vertical"
                            android:text="证件公告"
                            android:textColor="@color/text_main_black"
                            android:textSize="@dimen/sp_14" />
                    </LinearLayout>

                    <include layout="@layout/line_space" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/homeTodoId"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="200dp"
                        android:layout_height="match_parent"
                        android:drawableLeft="@drawable/daiban1"
                        android:drawablePadding="@dimen/padding"
                        android:gravity="center_vertical"
                        android:paddingLeft="@dimen/padding"
                        android:text="待办事项" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingTop="@dimen/dp_10"
                        android:layout_marginLeft="6dp"
                        android:layout_marginRight="6dp"
                        android:orientation="horizontal">

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/badge_avatar1"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="80dp"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:Image="@drawable/daiban1"
                            app:title="待办文件" />
                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/badge_avatar3"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="80dp"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:Image="@drawable/daiban1"
                            app:title="待办收文" />
                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/badge_avatar2"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="80dp"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:Image="@drawable/gonggao"
                            app:title="转发查阅" />
                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/badge_avatar4"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="80dp"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:Image="@drawable/gonggao"
                            app:title="新闻待阅" />
                    </LinearLayout>
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/homePortalId"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="200dp"
                        android:layout_height="match_parent"
                        android:drawableLeft="@drawable/daiban1"
                        android:drawablePadding="@dimen/padding"
                        android:gravity="center_vertical"
                        android:paddingLeft="@dimen/padding"
                        android:text="首页门户" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingTop="@dimen/dp_10"
                        android:layout_marginLeft="6dp"
                        android:layout_marginRight="6dp"
                        android:orientation="horizontal">

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/portal1"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="80dp"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:Image="@drawable/gonggao"
                            app:title="项目清单" />
                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/portal2"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="80dp"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:Image="@drawable/gonggao"
                            app:title="工程门户" />
                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/portal3"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="80dp"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:Image="@drawable/gonggao"
                            app:title="经营门户" />
                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/portal4"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="80dp"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:title="" />
                    </LinearLayout>
                </LinearLayout>




                <LinearLayout
                    android:id="@+id/weeklyPortalId"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="200dp"
                        android:layout_height="match_parent"
                        android:drawableLeft="@drawable/daiban1"
                        android:drawablePadding="@dimen/padding"
                        android:gravity="center_vertical"
                        android:paddingLeft="@dimen/padding"
                        android:text="周报审批" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingTop="@dimen/dp_10"
                        android:layout_marginLeft="6dp"
                        android:layout_marginRight="6dp"
                        android:orientation="horizontal">

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/weekly1"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="80dp"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:Image="@drawable/weekly"
                            app:title="待审批" />
                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/weekly2"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="80dp"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:Image="@drawable/weekly"
                            app:title="今日新增" />
                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/weekly3"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="80dp"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:Image="@drawable/weekly"
                            app:title="待查阅" />
                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/weekly4"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="80dp"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:title="" />
                    </LinearLayout>
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/ll_funcation"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:drawableLeft="@drawable/ic_my_function"
                        android:drawablePadding="@dimen/padding"
                        android:gravity="center_vertical"
                        android:paddingLeft="@dimen/padding"
                        android:text="常用功能" />

                    <ImageView
                        android:layout_width="@dimen/title_bar_hei"
                        android:layout_height="match_parent"
                        android:scaleType="center"
                        android:src="@drawable/ic_more" />
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_funcation"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"></androidx.recyclerview.widget.RecyclerView>
            </LinearLayout>

        </ScrollView>
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>


    <include layout="@layout/line_space" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/bottom_tab_hei"
        android:background="@color/line_space"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_enter_1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginRight="1dp"
            android:layout_weight="1"
            android:background="@color/colorAccent"
            android:gravity="center"
            android:text="工程监理"
            android:textColor="@color/text_bg"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/tv_enter_2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="1dp"
            android:layout_marginRight="1dp"
            android:layout_weight="1"
            android:background="@color/colorAccent"
            android:gravity="center"
            android:text="人力资源"
            android:textColor="@color/text_bg"
            android:textSize="@dimen/sp_14"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_enter_3"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/colorAccent"
            android:gravity="center"
            android:text="经营管理"
            android:textColor="@color/text_bg"
            android:textSize="@dimen/sp_14"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_enter_4"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="1dp"
            android:layout_marginRight="1dp"
            android:layout_weight="1"
            android:background="@color/colorAccent"
            android:gravity="center"
            android:text="协同办公"
            android:textColor="@color/text_bg"
            android:textSize="@dimen/sp_14" />

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_enter_5"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/colorAccent"
                android:gravity="center"
                android:text="我的资源"
                android:textColor="@color/text_bg"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:id="@+id/tvRemindNum"
                android:layout_width="@dimen/dp_18"
                android:layout_height="@dimen/dp_18"
                android:layout_gravity="top|end"
                android:layout_marginTop="3dp"
                android:layout_marginEnd="@dimen/padding"
                android:background="@drawable/bg_circle_red"
                android:gravity="center"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:visibility="gone" />
        </FrameLayout>

    </LinearLayout>
</LinearLayout>