<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:fresco="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <include layout="@layout/total_bar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/padding">

            <LinearLayout
                style="@style/style_form_ll1"
                android:orientation="vertical"
                android:padding="@dimen/dp_0">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="项目名称："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_project"
                    style="@style/tv_form_content"
                    android:ellipsize="end"
                    android:minLines="2"
                    android:hint="项目名称"
                    android:layout_marginBottom="@dimen/dp_10"/>
            </LinearLayout>

            <include layout="@layout/line_space" />


            <LinearLayout
                android:id="@+id/ll_quality_accept"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="检查部位:"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_quality_accept_point"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="请选择验收工点"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />


                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="验收内容："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_quality_accept_content"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="施工单位："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_quality_accept_company"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <include layout="@layout/line_space" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="验收时间："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_quality_accept_time"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="选择验收时间"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />


                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="报验人："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_quality_accept_report_name"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="共同验收人："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_quality_accept_partner"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="请选择共同验收人"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />


                </LinearLayout>

                <include layout="@layout/line_space" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/title_bar_hei"
                        android:gravity="center_vertical"
                        android:text="验收结果"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />


                    <RadioGroup
                        android:id="@+id/rg_quality_accept_result"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/title_bar_hei"
                        android:clickable="false"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/rb_quality_accept_ok"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:text="合格"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                        <RadioButton
                            android:id="@+id/rb_quality_accept_no"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="@dimen/line_space_wid"
                            android:gravity="center_vertical"
                            android:text="不合格"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                    </RadioGroup>

                </LinearLayout>

                <include layout="@layout/line_space" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="验收情况"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_quality_accept_dis"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:gravity="fill_vertical"
                    android:minHeight="@dimen/dp_72"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_onside"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="施工单位："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_onside_company"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <include layout="@layout/line_space" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/title_bar_hei"
                        android:gravity="center_vertical"
                        android:text="旁站的关键部位、关键工序："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_onside_item"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="fill_vertical"
                        android:minHeight="@dimen/dp_72"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="旁站开始时间："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_onside_beginTime"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="选择开始时间"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />


                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="旁站结束时间："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_onside_endTime"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="选择结束时间"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <include layout="@layout/line_space" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="旁站关键部位、关键工序的施工情况："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_onside_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="fill_vertical"
                    android:minHeight="@dimen/dp_72"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />


                <include layout="@layout/line_space" />


                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="发现的问题及处理情况："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_onside_problem"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="fill_vertical"
                    android:minHeight="@dimen/dp_72"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_sheltered"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="检查工点:"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_sheltered_accept_point"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="请选择验收工点"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />


                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="隐蔽工程内容："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_sheltered_accept_content"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="施工单位："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_sheltered_company"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="施工负责人："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_sheltered_charge_person"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <include layout="@layout/line_space" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="隐蔽时间："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_sheltered_invisible_time"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="请选择隐蔽时间"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <include layout="@layout/line_space" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="检查时间："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_sheltered_accept_time"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="选择检查时间"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>


                <include layout="@layout/line_space" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/title_bar_hei"
                        android:gravity="center_vertical"
                        android:text="检验结果"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />


                    <RadioGroup
                        android:id="@+id/rg_sheltered"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/title_bar_hei"
                        android:clickable="false"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/rb_sheltered_ok"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:text="合格"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                        <RadioButton
                            android:id="@+id/rb_sheltered_no"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="@dimen/line_space_wid"
                            android:gravity="center_vertical"
                            android:text="不合格"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                    </RadioGroup>

                </LinearLayout>

                <include layout="@layout/line_space" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="隐蔽工程情况"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_sheltered_dis"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="fill_vertical"
                    android:minHeight="@dimen/dp_72"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />


            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_supervision_log"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_72"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/padding"
                    android:paddingRight="@dimen/padding">


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/title_bar_hei"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:text="监理员："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_14" />


                    <LinearLayout
                        android:layout_width="@dimen/quality_card_hei"
                        android:layout_height="@dimen/bottom_tab_hei"
                        android:layout_gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_supLog_supName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center_vertical" />

                        <include layout="@layout/line_space" />
                    </LinearLayout>

                    <View
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/title_bar_hei"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:text="日期："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_14" />


                    <LinearLayout
                        android:layout_width="@dimen/quality_card_hei"
                        android:layout_height="@dimen/bottom_tab_hei"
                        android:layout_gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_supLog_time"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center_vertical" />

                        <include layout="@layout/line_space" />
                    </LinearLayout>
                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/padding"
                    android:paddingRight="@dimen/padding">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="天气状况:"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:id="@+id/tv_supLog_weather"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/padding"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:singleLine="true"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/padding"
                    android:paddingRight="@dimen/padding">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="温度状况:"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:id="@+id/tv_supLog_temperature"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/padding"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:singleLine="true"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="@dimen/padding"
                    android:paddingRight="@dimen/padding">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/title_bar_hei"
                        android:gravity="center_vertical"
                        android:text="施工部位、施工内容"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_14" />


                    <TextView
                        android:id="@+id/tv_supLog_work_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:minHeight="@dimen/bottom_tab_hei"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="@dimen/padding"
                    android:paddingRight="@dimen/padding">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/title_bar_hei"
                        android:gravity="center_vertical"
                        android:text="施工质量检测、安全作业情况"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_14" />


                    <TextView
                        android:id="@+id/tv_supLog_detection_situation"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:minHeight="@dimen/bottom_tab_hei"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <include layout="@layout/line_space" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="@dimen/padding"
                    android:paddingRight="@dimen/padding">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/title_bar_hei"
                        android:gravity="center_vertical"
                        android:text="施工作业中存在的问题及处理情况"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_14" />


                    <TextView
                        android:id="@+id/tv_supLog_problem"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="请输入问题及处理情况"
                        android:minHeight="@dimen/bottom_tab_hei"
                        android:textColor="@color/ic_text_normal"
                        android:textColorHint="@color/edit_hit"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="@dimen/padding"
                    android:paddingRight="@dimen/padding">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/title_bar_hei"
                        android:gravity="center_vertical"
                        android:text="人员到位情况："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_14" />


                    <TextView
                        android:id="@+id/tv_supLog_personnelStatus"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:minHeight="@dimen/bottom_tab_hei"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="@dimen/padding"
                    android:paddingRight="@dimen/padding">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/title_bar_hei"
                        android:gravity="center_vertical"
                        android:text="设备情况"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_14" />


                    <TextView
                        android:id="@+id/tv_supLog_equipmentStatus"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:minHeight="@dimen/bottom_tab_hei"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="@dimen/padding"
                    android:paddingRight="@dimen/padding">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/title_bar_hei"
                        android:gravity="center_vertical"
                        android:text="其它"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_14" />


                    <TextView
                        android:id="@+id/tv_supLog_other"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:minHeight="@dimen/bottom_tab_hei"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_scene"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="检查部位:"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_scene_check_point"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="请选择检查部位"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />


                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="施工内容："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_scene_check_ontent"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="施工单位："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_scene_company"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <include layout="@layout/line_space" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/title_bar_hei"
                        android:gravity="center_vertical"
                        android:text="进度情况"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />


                    <RadioGroup
                        android:id="@+id/rg_scene_progress"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/title_bar_hei"
                        android:clickable="false"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/rb_scene_progress_complete"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:text="完成"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                        <RadioButton
                            android:id="@+id/rb_scene_progress_delay"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="@dimen/line_space_wid"
                            android:gravity="center_vertical"
                            android:text="延误"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                        <RadioButton
                            android:id="@+id/rb_scene_progress_ahead"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="@dimen/line_space_wid"
                            android:gravity="center_vertical"
                            android:text="提前完成"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                    </RadioGroup>

                </LinearLayout>


                <include layout="@layout/line_space" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/title_bar_hei"
                        android:gravity="center_vertical"
                        android:text="质量情况"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />


                    <RadioGroup
                        android:id="@+id/rg_scene_quality"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/title_bar_hei"
                        android:clickable="false"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/rb_scene_quality_ok"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:text="正常"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                        <RadioButton
                            android:id="@+id/rb_scene_quality_war"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="@dimen/line_space_wid"
                            android:gravity="center_vertical"
                            android:text="预警"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                        <RadioButton
                            android:id="@+id/rb_scene_quality_no"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="@dimen/line_space_wid"
                            android:gravity="center_vertical"
                            android:text="异常"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                    </RadioGroup>

                </LinearLayout>


                <include layout="@layout/line_space" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/title_bar_hei"
                        android:gravity="center_vertical"
                        android:text="安全情况"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />


                    <RadioGroup
                        android:id="@+id/rg_scene_safe"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/title_bar_hei"
                        android:clickable="false"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/rb_scene_safe_ok"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:text="正常"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                        <RadioButton
                            android:id="@+id/rb_scene_safe_war"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="@dimen/line_space_wid"
                            android:gravity="center_vertical"
                            android:text="预警"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                        <RadioButton
                            android:id="@+id/rb_scene_safe_no"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="@dimen/line_space_wid"
                            android:gravity="center_vertical"
                            android:text="异常"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                    </RadioGroup>

                </LinearLayout>

                <include layout="@layout/line_space" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="检查描述"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_scene_dis"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dp_40"
                    android:gravity="fill_vertical"
                    android:minHeight="@dimen/dp_72"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />


            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_material_enter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="检测材料："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_material_name"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="请选择材料"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />


                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="材料数量："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_material_count"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="材料数量"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <include layout="@layout/line_space" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="出场合格证"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <com.facebook.drawee.view.SimpleDraweeView
                    android:id="@+id/iv_quality_card"
                    android:layout_width="@dimen/quality_card_wid"
                    android:layout_height="150dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="@dimen/padding"
                    fresco:backgroundImage="@color/text_bg"
                    fresco:failureImage="@drawable/ic_add_picture"
                    fresco:failureImageScaleType="centerInside"
                    fresco:placeholderImage="@drawable/ic_add_picture"
                    fresco:placeholderImageScaleType="fitCenter" />

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="是否进场"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />


                    <RadioGroup
                        android:id="@+id/rg_material_enter"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/title_bar_hei"
                        android:clickable="false"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/rb_material_enter_yes"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:text="进场"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                        <RadioButton
                            android:id="@+id/rb_material_enter_no"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="@dimen/line_space_wid"
                            android:gravity="center_vertical"
                            android:text="不进场"
                            android:clickable="false"
                            android:textColor="@color/ic_text_normal"
                            android:textSize="@dimen/sp_14" />

                    </RadioGroup>

                </LinearLayout>

                <include layout="@layout/line_space" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="进场时间："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_material_enterTime"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="选择进场时间"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <include layout="@layout/line_space" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="检测描述"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_material_enter_dis"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="fill_vertical"
                    android:minHeight="@dimen/dp_72"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <include layout="@layout/line_space" />
            <LinearLayout
                android:id="@+id/ll_picture"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="现场图片"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_check_picture"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/check_image_hei"
                    android:layout_marginBottom="@dimen/padding"
                    android:orientation="horizontal">

                </androidx.recyclerview.widget.RecyclerView>

                <include layout="@layout/line_space" />

            </LinearLayout>



            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:gravity="center_vertical"
                android:text="审核意见"
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_16" />

            <EditText
                android:id="@+id/et_approve_dis"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:drawableLeft="@drawable/ic_edit_hit"
                android:drawablePadding="@dimen/padding"
                android:gravity="fill_vertical"
                android:hint="请输入意见"
                android:minHeight="@dimen/dp_72"
                android:textColor="@color/ic_text_normal"
                android:textColorHint="@color/edit_hit"
                android:textSize="@dimen/sp_14" />


            <LinearLayout
                android:id="@+id/ll_approve_handle"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/ll_accident"
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <include layout="@layout/line_space"/>

                    <LinearLayout
                        android:id="@+id/ll_go_quality_accident"
                        android:orientation="horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/title_bar_hei"
                            android:layout_weight="1"
                            android:text="生成质量事故报告"
                            android:textSize="@dimen/sp_16"
                            android:drawableLeft="@drawable/ic_go_qulity_accident"
                            android:drawablePadding="@dimen/padding"
                            android:textColor="@color/text_main_black"
                            android:gravity="center_vertical"/>
                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_go_orther"/>


                    </LinearLayout>

                    <include layout="@layout/line_space"/>

                    <LinearLayout
                        android:id="@+id/ll_go_safe_accident"
                        android:orientation="horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/title_bar_hei"
                            android:layout_weight="1"
                            android:text="生成安全事故报告"
                            android:textSize="@dimen/sp_16"
                            android:drawableLeft="@drawable/ic_go_safe_accident"
                            android:drawablePadding="@dimen/padding"
                            android:textColor="@color/text_main_black"
                            android:gravity="center_vertical"/>
                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_go_orther"/>


                    </LinearLayout>

                    <include layout="@layout/line_space"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_40"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_disAgree"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/bottom_tab_hei"
                        android:layout_margin="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:background="@drawable/button_fill_bg"
                        android:gravity="center"
                        android:text="拒绝"
                        android:textColor="@color/text_bg"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tv_agree"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/bottom_tab_hei"
                        android:layout_margin="@dimen/line_space_wid"
                        android:layout_weight="1"
                        android:background="@drawable/button_fill_bg"
                        android:gravity="center"
                        android:text="同意"
                        android:textColor="@color/text_bg"
                        android:textSize="@dimen/sp_16" />


                </LinearLayout>


            </LinearLayout>



        </LinearLayout>

    </ScrollView>

</LinearLayout>