<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_marginLeft="@dimen/padding"
    android:layout_marginRight="@dimen/padding"
    android:layout_height="wrap_content">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_bar_hei">
            <TextView
                android:id="@+id/tv_acceptance_id"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:text="建设单位：单位A"
                android:textSize="@dimen/sp_14"
                android:paddingLeft="@dimen/dp_4"
                android:textColor="@color/text_main_black"
                android:gravity="center_vertical"
                android:layout_weight="1"/>

        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_accept_name"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:paddingLeft="@dimen/dp_4"
            android:layout_height="@dimen/dp_27">


            <TextView
                android:id="@+id/tv_checkup_time"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:text="验收日期：2019-10-12"
                android:textColor="@color/ic_text_normal"
                android:textSize="@dimen/sp_12" />

            <TextView
                android:id="@+id/tv_pwi_name"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginRight="@dimen/dp_4"
                android:text="验收人：张三"
                android:textColor="@color/ic_text_normal"
                android:textSize="@dimen/sp_12" />

        </LinearLayout>
<!---->
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_27">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:id="@+id/tv_super_visor"
                android:text="合格状态：合格"
                android:layout_weight="1"
                android:textSize="@dimen/sp_12"
                android:paddingLeft="@dimen/dp_4"
                android:textColor="@color/ic_text_normal" />
<!--    android:textColor="@color/text_main_black"    黑色-->
        </LinearLayout>

    </LinearLayout>

    <include layout="@layout/line_space"/>

</LinearLayout>