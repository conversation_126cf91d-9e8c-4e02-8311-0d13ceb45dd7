<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp_10"
    android:background="@drawable/shape_bg"
    android:clipChildren="true"
    android:clipToPadding="true"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_content"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40">

        <TextView
            android:id="@+id/tv_view_title"
            style="@style/style_form_title1"
            android:layout_centerVertical="true"
            android:paddingLeft="@dimen/dp_10"
            android:text="标题：" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dp_10"
            android:src="@drawable/arrow_right" />
    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
    <!--        <com.kisoft.yuejianli.ui.scgl.YLabelCell1-->
    <!--            android:id="@+id/label1"-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="0dp"-->
    <!--            android:layout_weight="1"/>-->
    <!--        <com.kisoft.yuejianli.ui.scgl.YLabelCell1-->
    <!--            android:id="@+id/label2"-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="0dp"-->
    <!--            android:layout_weight="1"/>-->
    <!--        <com.kisoft.yuejianli.ui.scgl.YLabelCell1-->
    <!--            android:id="@+id/label3"-->
    <!--            android:visibility="gone"-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="0dp"-->
    <!--            android:layout_weight="1"/>-->


</LinearLayout>