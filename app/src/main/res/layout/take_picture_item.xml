<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        >
    <ImageView
        android:id="@+id/iv_sfenclosure"
        android:layout_width="@dimen/dp_110"
        android:layout_height="@dimen/dp_110"
        android:src="@drawable/ic_yuejianli"
        />
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_3"
                android:text="随手拍标题"
                android:singleLine="true"
                android:textSize="@dimen/sp_14"
                android:paddingLeft="@dimen/dp_5"
                android:textColor="@color/text_main_black"
                android:gravity="center_vertical"
                />
        <!--实现多行   android:singleLine="false"
         android:ellipsize="end"  省略号显示在结尾
         -->
            <TextView
                android:id="@+id/tv_createname"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="发布人：小明"
                android:singleLine="true"
                android:layout_marginTop="@dimen/dp_5"
                android:layout_marginLeft="@dimen/dp_5"
                android:textColor="@color/ic_text_normal"
                android:textSize="@dimen/sp_12" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/tv_position"
                android:text="位置：西安市"
                android:singleLine="true"
                android:layout_marginTop="@dimen/dp_4"
                android:layout_marginLeft="@dimen/dp_0"
                android:textSize="@dimen/sp_12"
                android:paddingLeft="@dimen/dp_4"
                android:textColor="@color/ic_text_normal" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/tv_createtime"
                android:singleLine="true"
                android:text="时间：2000-01-01 00:00"
                android:layout_marginTop="@dimen/dp_4"
                android:layout_marginLeft="@dimen/dp_0"
                android:textSize="@dimen/sp_12"
                android:paddingLeft="@dimen/dp_4"
                android:textColor="@color/ic_text_normal" />
            <!--    android:textColor="@color/text_main_black"    黑色-->

        <TextView
            android:id="@+id/tv_shootpublic"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:singleLine="true"
            android:text="谁可以看：公开"
            android:layout_marginTop="@dimen/dp_4"
            android:layout_marginLeft="@dimen/dp_5"
            android:textSize="@dimen/sp_12"
            android:layout_marginRight="@dimen/dp_4"
            android:textColor="@color/ic_text_normal"
            />
    </LinearLayout>
    </LinearLayout>
    <include layout="@layout/line_space"/>
</LinearLayout>