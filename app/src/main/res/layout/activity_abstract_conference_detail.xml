<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/total_bar" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout style="@style/style_form_ll1"
                android:orientation="vertical">

                <TextView
                    style="@style/style_form_title1"
                    android:text="项目名称：" />

                <TextView
                    android:id="@+id/tv_project_name"
                    style="@style/tv_form_content"
                    android:ellipsize="end"
                    android:minLines="2"
                    android:hint="项目名称"/>
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="会议主题：" />

                <TextView
                    android:id="@+id/tv_metting_theme"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="开始时间：" />

                <TextView
                    android:id="@+id/tv_start_time"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="结束时间：" />

                <TextView
                    android:id="@+id/tv_end_time"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="会议类型：" />

                <TextView
                    android:id="@+id/tv_type"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="会议地点：" />

                <TextView
                    android:id="@+id/tv_address"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="主持人：" />

                <TextView
                    android:id="@+id/tv_host"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="参与人员：" />

                <TextView
                    android:id="@+id/tv_join"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />
            </LinearLayout>
            <View style="@style/v_drvier_line_h" />
            <TextView
                style="@style/style_form_title1"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_10"
                android:text="会议正文：" />
            <com.itheima.view.BridgeWebView
                android:id="@+id/wb_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginRight="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_5"
                android:background="@drawable/edit_unfill_bg1"
                android:padding="5dp" />
            <View style="@style/v_drvier_line_h" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="@dimen/padding"
                android:paddingRight="@dimen/padding">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="图片"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_meetpicturepath"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/check_image_hei"
                    android:layout_marginBottom="@dimen/padding"
                    android:orientation="horizontal" />
            </LinearLayout>
            <TextView
                style="@style/style_form_title1"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_10"
                android:text="附　　件：" />

            <View style="@style/v_drvier_line_h" />

            <com.kisoft.yuejianli.ui.YFileListView
                android:id="@+id/y_file_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
<!--            <androidx.recyclerview.widget.RecyclerView-->
<!--                android:id="@+id/rv_list"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content" />-->
<!--            <View-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="10dp"/>-->

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>