<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/bg_ll"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="@dimen/dp_10"
    android:paddingRight="@dimen/dp_10"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_file_name"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14" />

    </LinearLayout>

    <include layout="@layout/line_space" />

</LinearLayout>