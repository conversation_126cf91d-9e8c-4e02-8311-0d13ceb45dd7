<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingLeft="@dimen/common_margin"
    android:paddingRight="@dimen/common_margin">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_bar_hei"
            android:gravity="center_vertical"
            android:lines="1"
            android:maxLines="1"
            android:ellipsize="end"
            android:paddingLeft="@dimen/padding"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/tv_describe"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:lines="1"
            android:maxLines="1"
            android:ellipsize="end"
            android:paddingLeft="@dimen/padding"
            android:textColor="@color/ic_text_normal"
            android:textSize="@dimen/sp_12" />

    </LinearLayout>

    <include layout="@layout/line_space" />

</LinearLayout>