<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        style="@style/style_notice_item"
        android:layout_marginTop="@dimen/dp_10"
        android:ellipsize="end"
        android:maxLines="2"
        android:paddingLeft="@dimen/dp_10"
        android:paddingRight="@dimen/dp_10"
        android:text="sdflkjklfs"
        android:textColor="@color/text_main_black"
        android:textSize="@dimen/sp_16" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/dp_10"
        android:paddingRight="@dimen/dp_10">

        <TextView
            android:id="@+id/tv_time1"
            style="@style/style_notice_item"
            android:textSize="@dimen/sp_12"  />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tv_time2"
            style="@style/style_notice_item"
            android:textSize="@dimen/sp_12" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/dp_10"
        android:paddingRight="@dimen/dp_10">

        <TextView
            android:id="@+id/tv_time3"
            style="@style/style_notice_item"
            android:textSize="@dimen/sp_12"  />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tv_time4"
            style="@style/style_notice_item"
            android:textSize="@dimen/sp_12" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/dp_10"
        android:paddingRight="@dimen/dp_10">

        <TextView
            android:id="@+id/tv_percentage"
            style="@style/style_notice_item"
            android:textSize="@dimen/sp_12"  />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tv_state"
            style="@style/style_notice_item"
            android:textSize="@dimen/sp_12" />
    </LinearLayout>


    <View style="@style/v_drvier_line_h" />

</LinearLayout>