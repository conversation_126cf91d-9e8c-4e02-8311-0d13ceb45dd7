<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/nomal_bg_f5"
    android:orientation="vertical">

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/smartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/rl_tool"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/bottom_tab_hei"
                    android:background="@color/bga_pp_eighteen_maskTextColor">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="@dimen/dp_5"
                        android:orientation="horizontal">
                        <ImageView
                            android:id="@+id/iv_logo"
                            android:layout_width="@dimen/title_bar_hei"
                            android:layout_height="@dimen/title_bar_hei"
                            android:layout_marginTop="@dimen/dp_10"
                            android:layout_marginBottom="@dimen/dp_8"
                            android:layout_marginLeft="10dp"
                            android:layout_marginRight="@dimen/dp_2"
                            android:scaleType="fitCenter"
                            android:src="@drawable/logo" />
                        <TextView
                            android:id="@+id/tv_app_name"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/dp_4"
                            android:text="四川公路智慧监理平台"
                            android:textColor="@color/colorAccent"
                            android:textSize="18sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/canL"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="@dimen/dp_10"
                        android:orientation="horizontal">
                        <TextView
                            android:id="@+id/tv_month_day"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:paddingLeft="14dp"
                            android:text="2025-1-23"
                            android:textColor="@color/text_main_black"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            tools:ignore="RtlSymmetry" />
                    </LinearLayout>
                </RelativeLayout>


                <!-- 轮播图 -->

                <com.youth.banner.Banner
                    android:id="@+id/looper_banner"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginBottom="@dimen/dp_0"
                    android:background="@drawable/shape_bg" />

                <LinearLayout
                    android:id="@+id/ll_notes_view"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_100"
                    android:orientation="horizontal">

                    <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                        android:id="@+id/badge_news"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="10dp"
                        android:layout_weight="1"
                        android:background="@drawable/shape_bg"
                        android:paddingTop="@dimen/dp_10"
                        app:Image="@drawable/tongzhi"
                        app:badge_offsetX="-6dp"
                        app:badge_offsetY="0dp"
                        app:textStyle="bold"
                        app:title="公告通知" />

                    <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                        android:id="@+id/badge_cers"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="10dp"
                        android:layout_weight="1"
                        android:background="@drawable/shape_bg"
                        android:paddingTop="@dimen/dp_10"
                        app:Image="@drawable/gonggao"
                        app:badge_offsetX="-6dp"
                        app:badge_offsetY="0dp"
                        app:textStyle="bold"
                        app:title="证件公告" />
                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="@dimen/padding"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/dp_10"
                    android:text="待办事项"
                    android:textStyle="bold"
                    android:textSize="18sp"
                    android:textColor="@color/text_main_black"/>

                <LinearLayout
                    android:id="@+id/homeTodoId"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/dp_10"
                    android:background="@drawable/shape_bg"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp_10"
                    android:visibility="visible">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/badge_avatar1"
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_weight="1"
                            app:Image="@drawable/daiban1"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:title="待办文件" />

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/badge_avatar3"
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_weight="1"
                            app:Image="@drawable/daiban2"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:title="待办收文" />

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/badge_avatar2"
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_weight="1"
                            app:Image="@drawable/chayue"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:title="转发查阅" />

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/badge_avatar4"
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_weight="1"
                            app:Image="@drawable/daiyue"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:title="新闻待阅" />
                    </LinearLayout>
                </LinearLayout>


                <TextView
                    android:layout_width="200dp"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/dp_10"
                    android:text="首页门户"
                    android:textStyle="bold"
                    android:textSize="18sp"
                    android:textColor="@color/text_main_black"/>

                <LinearLayout
                    android:id="@+id/homePortalId"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:background="@drawable/shape_bg"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp_10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/portal1"
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_weight="1"
                            app:Image="@drawable/qindan"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:title="项目清单" />

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/portal2"
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_weight="1"
                            app:Image="@drawable/menhu1"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:title="工程门户" />

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/portal3"
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_weight="1"
                            app:Image="@drawable/menhu2"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:title="经营门户" />

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/portal4"
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_weight="1"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:title="" />
                    </LinearLayout>
                </LinearLayout>

                <TextView
                    android:layout_width="200dp"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/dp_10"
                    android:text="周报审批"
                    android:textStyle="bold"
                    android:textSize="18sp"
                    android:textColor="@color/text_main_black"/>

                <LinearLayout
                    android:id="@+id/weeklyPortalId"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:background="@drawable/shape_bg"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp_10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/weekly1"
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_weight="1"
                            app:Image="@drawable/zhoubaoshenpi1"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:title="待审批" />

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/weekly2"
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_weight="1"
                            app:Image="@drawable/zhoubaoshenpi2"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:title="今日新增" />

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/weekly3"
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_weight="1"
                            app:Image="@drawable/zhoubaoshenpi3"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:title="待查阅" />

                        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                            android:id="@+id/weekly4"
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_weight="1"
                            app:badge_offsetX="-16dp"
                            app:badge_offsetY="0dp"
                            app:title="" />
                    </LinearLayout>
                </LinearLayout>

<!--                <TextView-->
<!--                    android:layout_width="200dp"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:gravity="center_vertical"-->
<!--                    android:paddingLeft="@dimen/dp_10"-->
<!--                    android:text="常用功能"-->
<!--                    android:textStyle="bold"-->
<!--                    android:textSize="18sp"-->
<!--                    android:textColor="@color/text_main_black"/>-->

                <LinearLayout
                    android:id="@+id/ll_funcation"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:background="@drawable/shape_bg"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/dp_10">
                </LinearLayout>
            </LinearLayout>
        </ScrollView>
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>


    <include layout="@layout/line_space" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/bottom_tab_hei"
        android:paddingTop="@dimen/dp_6"
        android:background="@color/line_space"
        android:orientation="horizontal">

        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
            android:id="@+id/tv_enter_1"
            android:layout_width="0dp"
            android:layout_height="@dimen/bottom_tab_hei"
            android:layout_weight="1"
            app:Image="@drawable/tabbar1"
            app:badge_offsetX="-16dp"
            app:badge_offsetY="0dp"
            app:title="工程监理" />

        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
            android:id="@+id/tv_enter_3"
            android:layout_width="0dp"
            android:layout_height="@dimen/bottom_tab_hei"
            android:layout_weight="1"
            app:Image="@drawable/tabbar2"
            app:badge_offsetX="-16dp"
            app:badge_offsetY="0dp"
            app:title="经营管理" />

        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
            android:id="@+id/tv_enter_4"
            android:layout_width="0dp"
            android:layout_height="@dimen/bottom_tab_hei"
            android:layout_weight="1"
            app:Image="@drawable/tabbar3"
            app:badge_offsetX="-16dp"
            app:badge_offsetY="0dp"
            app:title="协同办公" />

        <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
            android:id="@+id/tv_enter_5"
            android:layout_width="0dp"
            android:layout_height="@dimen/bottom_tab_hei"
            android:layout_weight="1"
            app:Image="@drawable/tabbar4"
            app:badge_offsetX="-16dp"
            app:badge_offsetY="0dp"
            app:title="我的资源" />

    </LinearLayout>
</LinearLayout>