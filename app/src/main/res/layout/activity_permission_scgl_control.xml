<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".views.PermissionControlActivity">

    <include layout="@layout/total_bar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_gradient_bg"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp_8"
                android:background="@drawable/shape_bg"
                android:orientation="vertical">

                <com.kisoft.yuejianli.ui.scgl.YLabelCell2
                    android:id="@+id/project_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:title="项目名称" />

                <com.kisoft.yuejianli.ui.scgl.YLabelCell1
                    android:id="@+id/construction_unit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:title="建设单位" />

                <com.kisoft.yuejianli.ui.scgl.YLabelCell1
                    android:id="@+id/project_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:title="项目地址" />

                <com.kisoft.yuejianli.ui.scgl.YLabelCell1
                    android:id="@+id/contract_duration"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:title="合同工期" />
            </LinearLayout>


            <TextView
                android:id="@+id/tv_today_situation"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_8"
                android:text="到岗情况"
                android:textSize="@dimen/sp_16"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/ll_num"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_100"
                android:layout_margin="@dimen/dp_8"
                android:background="@drawable/shape_bg"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="考勤人数"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:id="@+id/tv_checkwork_num"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="20"
                        android:textSize="@dimen/sp_36"
                        android:textStyle="bold"/>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/rl_cometo"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_cometo_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/transparent_bg"
                        android:gravity="center"
                        android:text="到岗人数"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:id="@+id/tv_cometo_num"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/transparent_bg"
                        android:gravity="center"
                        android:text="20"
                        android:textColor="#1485ee"
                        android:textSize="@dimen/sp_36"
                        android:textStyle="bold"/>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/rl_leave"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_leave_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/transparent_bg"
                        android:gravity="center"
                        android:text="请假人数"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:id="@+id/tv_leave_num"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/transparent_bg"
                        android:gravity="center"
                        android:text="1"
                        android:textColor="#69e4f8"
                        android:textSize="@dimen/sp_36"
                        android:textStyle="bold"/>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/rl_err"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_err_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/transparent_bg"
                        android:gravity="center"
                        android:text="异常人数"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:id="@+id/tv_err_num"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/transparent_bg"
                        android:gravity="center"
                        android:text="1"
                        android:textColor="#fd5340"
                        android:textSize="@dimen/sp_36"
                        android:textStyle="bold"/>
                </LinearLayout>


            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_8"
                android:text="公司管理"
                android:textSize="@dimen/sp_16"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_100"
                android:layout_margin="@dimen/dp_8"
                android:background="@drawable/shape_bg"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                    android:id="@+id/attendance"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_weight="1"
                    app:Image="@drawable/dakai"
                    app:badge_offsetX="-16dp"
                    app:badge_offsetY="0dp"
                    app:title="考勤打卡" />

                <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                    android:id="@+id/wbs"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_weight="1"
                    app:Image="@drawable/wbs_img"
                    app:badge_offsetX="-16dp"
                    app:badge_offsetY="0dp"
                    app:title="WBS任务" />

                <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView
                    android:id="@+id/fangan"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_weight="1"
                    app:Image="@drawable/fangan"
                    app:badge_offsetX="-16dp"
                    app:badge_offsetY="0dp"
                    app:title="方案审批" />

<!--                <com.kisoft.yuejianli.ui.badgeview.YBadgeImageView-->
<!--                    android:id="@+id/weekly4"-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="80dp"-->
<!--                    android:layout_weight="1"-->
<!--                    app:badge_offsetX="-16dp"-->
<!--                    app:badge_offsetY="0dp"-->
<!--                    app:title="" />-->
            </LinearLayout>

            <LinearLayout
                android:id="@+id/workPanel"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:paddingLeft="@dimen/dp_10"
                        android:textSize="@dimen/sp_16"
                        android:textStyle="bold"
                        android:text="工作履职" />

                    <ImageView
                        android:id="@+id/iv_task_status"
                        android:layout_width="@dimen/dp_40"
                        android:layout_height="@dimen/dp_40"
                        android:scaleType="center"
                        android:src="@drawable/arrow_right" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/dp_8"
                    android:background="@drawable/shape_bg"
                    android:orientation="vertical">
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/mWorkRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/projectPanel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/dp_10"
                    android:textSize="@dimen/sp_16"
                    android:textStyle="bold"
                    android:text="项目管理" />
                <ImageView
                    android:layout_width="@dimen/dp_40"
                    android:layout_height="@dimen/dp_40"
                    android:scaleType="center"
                    android:src="@drawable/arrow_right" />
            </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dp_8"
            android:background="@drawable/shape_bg"
            android:layout_marginBottom="@dimen/dp_20"
            android:orientation="vertical">
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>

        </LinearLayout>
    </ScrollView>
</LinearLayout>
