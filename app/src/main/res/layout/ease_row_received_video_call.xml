<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingTop="13dp">

    <TextView
        android:id="@+id/timestamp"
        style="@style/chat_text_date_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_chat_activity" >

        <com.hyphenate.easeui.widget.EaseImageView
            android:id="@+id/iv_userhead"
            style="@style/ease_row_receive_iv_userhead_style"/>

        <RelativeLayout
            android:id="@+id/bubble"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/margin_chat_activity"
            android:layout_toRightOf="@id/iv_userhead"
            android:layout_below="@+id/tv_userid"
            android:minHeight="30dp"
            android:padding="8dp"
            android:layout_marginTop="2dp"
            android:background="@drawable/ease_chat_bubble_receive_bg" >

            <TextView
                android:id="@+id/tv_chatcontent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/ease_chat_text_min_height"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                android:textSize="15sp"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:textColor="#ff000000"
                tools:text="通话时长"/>

            <ImageView
                android:layout_marginRight="4dp"
                android:id="@+id/iv_call_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@id/tv_chatcontent"
                android:layout_centerVertical="true"
                android:src="@drawable/d_chat_video_call_receive" />

        </RelativeLayout>

         <TextView
            android:id="@+id/tv_userid"
            style="@style/chat_text_name_style"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/chat_nick_margin_left"
            android:textSize="@dimen/chat_nick_text_size"
            android:layout_toRightOf="@id/iv_userhead"
            android:visibility="gone" />
    </RelativeLayout>

</LinearLayout>