<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingLeft="@dimen/common_margin"
    android:paddingRight="@dimen/common_margin">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_gravity="center_vertical"
            android:scaleType="fitXY"
            android:src="@mipmap/icon_file" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:lines="1"
            android:paddingLeft="@dimen/padding"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14" />

    </LinearLayout>

    <include layout="@layout/line_space" />

</LinearLayout>