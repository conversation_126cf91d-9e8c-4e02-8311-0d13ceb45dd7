<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:paddingLeft="@dimen/dp_72"
        android:paddingRight="@dimen/dp_72">

        <ImageView
            android:id="@+id/iv_left"
            android:layout_width="@dimen/title_bar_hei"
            android:layout_height="@dimen/title_bar_hei"
            android:padding="@dimen/padding"
            android:src="@drawable/ic_left" />

        <TextView
            android:id="@+id/tv_month"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="20xx-xx"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14" />

        <ImageView
            android:id="@+id/iv_right"
            android:layout_width="@dimen/title_bar_hei"
            android:layout_height="@dimen/title_bar_hei"
            android:padding="@dimen/padding"
            android:src="@drawable/ic_right" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:background="@color/nomal_bg" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/ll_container"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="horizontal">

                <HorizontalScrollView
                    android:id="@+id/scroll_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:scrollbars="none"
                    app:layout_behavior="@string/appbar_scrolling_view_behavior">

                    <LinearLayout
                        android:id="@+id/ll_content_container"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:descendantFocusability="blocksDescendants"
                        android:orientation="horizontal">

                    </LinearLayout>
                </HorizontalScrollView>

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:layout_marginBottom="40dp"
                android:gravity="center"
                android:orientation="horizontal">

                <View
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:background="@android:color/holo_green_light" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:text="计划进度"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/dp_12" />

                <View
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_marginLeft="20dp"
                    android:background="@android:color/holo_red_dark" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:text="实际进度"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/dp_12" />

                <View
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_marginLeft="20dp"
                    android:background="@color/chat_color_1" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:text="里程碑"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/dp_12" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>