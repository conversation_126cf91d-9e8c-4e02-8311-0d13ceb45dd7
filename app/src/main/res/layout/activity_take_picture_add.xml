<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".views.TakePictureAddActivity">

    <include layout="@layout/total_bar"/>
    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/padding"
                android:paddingRight="@dimen/padding">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal"
                android:paddingRight="@dimen/padding">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/dp_0"
                    android:text="标题:"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <EditText
                    android:id="@+id/et_title"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/dp_65"
                    android:layout_weight="1"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="请输标题"
                    android:singleLine="true"
                    android:textColor="@color/text_main_black"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>
            <View
                style="@style/v_drvier_line_h"
                android:layout_marginTop="@dimen/dp_0" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="项目名称:"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_projectname"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text=""
                    android:layout_marginLeft="@dimen/dp_40"
                    android:layout_weight="1"
                    android:lines="2"
                    android:textColor="@color/text_main_black"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>
            <View
                style="@style/v_drvier_line_h"
                android:layout_marginTop="@dimen/dp_0" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/padding"
                android:paddingRight="@dimen/padding">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="发布人:"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/et_createname"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/dp_50"
                    android:layout_weight="1"
                    android:hint="发布人"
                    android:gravity="center_vertical"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>
            <View
                style="@style/v_drvier_line_h"
                android:layout_marginTop="@dimen/dp_0" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/padding"
                android:paddingRight="@dimen/padding">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="发布时间:"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/et_createtime"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/dp_35"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:hint="发布时间"
                    android:textColor="@color/text_main_black"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
            <View
                style="@style/v_drvier_line_h"
                android:layout_marginTop="@dimen/dp_0" />

            <TextView
                style="@style/style_form_title1"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_10"
                android:text="内容：" />

            <EditText
                android:id="@+id/et_acceptidea"
                style="@style/et_remark"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginRight="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_5"
                android:hint="请输入内容" />

            <View
                style="@style/v_drvier_line_h"
                android:layout_marginTop="@dimen/dp_0" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="@dimen/padding"
                android:paddingRight="@dimen/padding">
<!--
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="图片"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_sfenclosure"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/check_image_hei"
                    android:layout_marginBottom="@dimen/padding"
                    android:orientation="horizontal" />
-->
                <com.kisoft.yuejianli.ui.YImageViewCell
                    android:id="@+id/imageViewCell"
                    android:layout_marginBottom="5dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

            </LinearLayout>
            <View
                style="@style/v_drvier_line_h"
                android:layout_marginTop="@dimen/dp_0" />

            <LinearLayout
                android:id="@+id/ll_take_picture"
                style="@style/style_form_ll1"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_0"
                >

                <TextView
                    style="@style/style_form_title1"

                    android:text="谁可以看："
                    android:layout_marginRight="@dimen/dp_0"
                    />

                <Spinner
                    android:id="@+id/sp_shootpublic"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:visibility="gone"
                    android:entries="@array/array_shootpublic_state"
                    />
                <TextView
                    android:id="@+id/tv_shootpublic"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_weight="1"
                    android:text="公开"
                    android:scrollbars="vertical"
                    android:textColor="@color/text_main_black"
                    />
                <ImageView
                    android:id="@+id/iv_unit_more"
                    android:layout_width="@dimen/dp_40"
                    android:layout_height="@dimen/dp_80"
                    android:scaleType="center"
                    android:src="@drawable/ic_down" />
            </LinearLayout>
            <View
                style="@style/v_drvier_line_h"
                android:layout_marginTop="@dimen/dp_0" />
            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/padding"
                android:paddingRight="@dimen/padding">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="所在位置:"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />
                <TextView
                    android:id="@+id/tv_addresss"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_40"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:text=""
                    android:hint="请选择"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <ImageView
                    android:id="@+id/iv_today_quality11"
                    android:layout_width="@dimen/title_bar_hei"
                    android:layout_height="@dimen/dp_80"
                    android:scaleType="center"
                    android:src="@drawable/ic_more" />

            </LinearLayout>
            <View
                style="@style/v_drvier_line_h"
                android:layout_marginTop="@dimen/dp_0" />
            <LinearLayout
                style="@style/style_form_ll1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/padding"
                android:paddingRight="@dimen/padding">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="地址备注:"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />
                <EditText
                    android:id="@+id/et_addressDetail"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_40"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:text=""
                    android:lines="1"
                    android:ellipsize="end"
                    style="@style/et_notice_bg1"
                    android:hint="请输入备注地址（例如楼层）"
                    />

            </LinearLayout>
            <View
                style="@style/v_drvier_line_h"
                android:layout_marginTop="@dimen/dp_0" />
            <LinearLayout
                android:id="@+id/ll_answer"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:layout_margin="@dimen/dp_27"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_save"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginRight="@dimen/padding"
                    android:layout_weight="1"
                    android:background="@drawable/button_fill_bg"
                    android:gravity="center"
                    android:visibility="gone"
                    android:text="保 存"
                    android:textColor="@color/text_bg"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_sub"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/padding"
                    android:layout_weight="1"
                    android:background="@drawable/button_fill_bg"
                    android:gravity="center"
                    android:text="提 交"
                    android:textColor="@color/text_bg"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

        </LinearLayout>


    </ScrollView>

</LinearLayout>