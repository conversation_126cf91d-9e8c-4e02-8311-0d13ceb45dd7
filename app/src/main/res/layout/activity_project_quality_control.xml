<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:layout_height="match_parent">

    <include layout="@layout/total_bar"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:paddingLeft="@dimen/dp_72"
        android:paddingRight="@dimen/dp_72">

        <ImageView
            android:id="@+id/iv_left"
            android:layout_width="@dimen/title_bar_hei"
            android:layout_height="@dimen/title_bar_hei"
            android:padding="@dimen/padding"
            android:scaleType="center"
            android:src="@drawable/ic_left"/>

        <TextView
            android:id="@+id/tv_month"
            android:layout_width="134dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="2018-03"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14" />

        <ImageView
            android:id="@+id/iv_right"
            android:layout_width="@dimen/title_bar_hei"
            android:layout_height="@dimen/title_bar_hei"
            android:padding="@dimen/padding"
            android:scaleType="center"
            android:src="@drawable/ic_right"/>
    </LinearLayout>

    <com.github.mikephil.charting.charts.BarChart
        android:id="@+id/bar_chart"
        android:layout_width="match_parent"
        android:layout_centerInParent="true"
        android:layout_margin="@dimen/dp_27"
        android:layout_height="0dp"
        android:layout_weight="1">

    </com.github.mikephil.charting.charts.BarChart>


    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_marginLeft="@dimen/dp_40"
        android:layout_marginRight="@dimen/dp_40"
        android:layout_height="@dimen/dp_27">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:text="本月统计："
            android:textSize="@dimen/sp_14"
            android:gravity="center_vertical"
            android:textColor="@color/text_main_black"/>

        <TextView
            android:id="@+id/tv_count_month"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:paddingLeft="@dimen/padding"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:gravity="center_vertical"
            android:textColor="@color/text_main_black"/>


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:text="本日统计："
            android:textSize="@dimen/sp_14"
            android:gravity="center_vertical"
            android:textColor="@color/text_main_black"/>

        <TextView
            android:id="@+id/tv_count_day"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:paddingLeft="@dimen/padding"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:gravity="center_vertical"
            android:textColor="@color/text_main_black"/>

    </LinearLayout>

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_marginLeft="@dimen/dp_40"
        android:layout_marginRight="@dimen/dp_40"
        android:layout_height="@dimen/dp_27">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:text="正常记录："
            android:textSize="@dimen/sp_14"
            android:gravity="center_vertical"
            android:textColor="@color/text_main_black"/>

        <TextView
            android:id="@+id/tv_count_ok"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:paddingLeft="@dimen/padding"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:gravity="center_vertical"
            android:textColor="@color/text_main_black"/>


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:text="预警记录："
            android:textSize="@dimen/sp_14"
            android:gravity="center_vertical"
            android:textColor="@color/text_main_black"/>

        <TextView
            android:id="@+id/tv_count_waring"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:paddingLeft="@dimen/padding"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:gravity="center_vertical"
            android:textColor="@color/text_main_black"/>

    </LinearLayout>

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_marginLeft="@dimen/dp_40"
        android:layout_marginRight="@dimen/dp_40"
        android:layout_height="@dimen/dp_27">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:text="异常记录："
            android:textSize="@dimen/sp_14"
            android:gravity="center_vertical"
            android:textColor="@color/text_main_black"/>


        <TextView
            android:id="@+id/tv_count_no"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:paddingLeft="@dimen/padding"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:gravity="center_vertical"
            android:textColor="@color/text_main_black"/>

    </LinearLayout>

    <TextView
        android:id="@+id/tv_detail"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:text="查看详情"
        android:textSize="@dimen/sp_14"
        android:textColor="@color/title_text"
        android:gravity="center"
        android:background="@drawable/button_fill_bg"
        android:layout_margin="@dimen/dp_27"/>




</LinearLayout>