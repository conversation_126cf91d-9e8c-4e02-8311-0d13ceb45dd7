<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:fresco="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/welcom1"
    android:orientation="vertical">


    <com.facebook.drawee.view.SimpleDraweeView
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_100"
        android:layout_marginBottom="@dimen/dp_50"
        fresco:placeholderImage="@mipmap/ic_app_sc"
        fresco:roundBottomLeft="true"
        fresco:roundBottomRight="true"
        fresco:roundTopLeft="true"
        fresco:roundTopRight="true"
        fresco:roundedCornerRadius="@dimen/dp_4" />
    <!--    <ImageView-->
    <!--        android:layout_width="@dimen/dp_72"-->
    <!--        android:layout_height="@dimen/dp_72"-->
    <!--        android:src="@mipmap/ic_app_log"-->
    <!--/>-->
    <TextView
        android:id="@+id/tv_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_hi"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="30dp"
        android:gravity="center"
        android:text="欢迎登陆四川公路智慧监理平台"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/input_view_hei"
            android:layout_marginLeft="@dimen/dp_40"
            android:layout_marginRight="@dimen/dp_40"
            android:background="@drawable/login_bg"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/dp_5"
            android:paddingRight="@dimen/padding">

            <EditText
                android:id="@+id/et_company_code"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/padding"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="请输入企业码"
                android:paddingLeft="@dimen/padding"
                android:textColorHint="@color/edit_hit"
                android:textSize="@dimen/main_text_size" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/input_view_hei"
            android:layout_margin="@dimen/dp_40"
            android:background="@drawable/login_bg"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/padding"
            android:paddingRight="@dimen/padding">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:src="@drawable/user_login" />

            <EditText
                android:id="@+id/et_user"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/padding"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="请输入账号"
                android:inputType="text"
                android:paddingLeft="@dimen/padding"
                android:textColorHint="@color/edit_hit"
                android:textSize="@dimen/main_text_size" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/input_view_hei"
            android:layout_marginLeft="@dimen/dp_40"
            android:layout_marginRight="@dimen/dp_40"
            android:background="@drawable/login_bg"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/padding"
            android:paddingRight="@dimen/padding">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:src="@drawable/password_login" />

            <EditText
                android:id="@+id/et_password"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/padding"
                android:layout_weight="1"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="请输入密码"
                android:inputType="textPassword"
                android:paddingLeft="@dimen/padding"
                android:textColorHint="@color/edit_hit"
                android:textSize="@dimen/main_text_size" />

            <ImageView
                android:id="@+id/iv_visible_change"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_gravity="center_vertical"
                android:scaleType="center"
                android:src="@drawable/ic_visible" />

        </LinearLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/tv_login"
        android:layout_width="match_parent"
        android:layout_height="@dimen/input_view_hei"
        android:layout_marginLeft="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_40"
        android:layout_marginRight="@dimen/dp_40"
        android:layout_marginBottom="20dp"
        android:background="@drawable/button_fill_bg"
        android:gravity="center"
        android:text="登录"
        android:textColor="@color/white"
        android:textSize="@dimen/title_bar_text"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvLoginByFace"
        android:layout_width="match_parent"
        android:layout_height="@dimen/input_view_hei"
        android:layout_marginLeft="@dimen/dp_40"
        android:layout_marginRight="@dimen/dp_40"
        android:layout_marginBottom="20dp"
        android:background="@drawable/login_bg"
        android:gravity="center"
        android:text="人脸登录"
        android:textColor="@color/ic_text_normal"
        android:textSize="@dimen/main_text_size"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_current_url"
        android:layout_width="@dimen/dp_72"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_40"
        android:gravity="center"
        android:text="切换服务器"
        android:textColor="@color/colorAccent"
        android:textSize="@dimen/sp_14"
        android:visibility="gone" />
</LinearLayout>
