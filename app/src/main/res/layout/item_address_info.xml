<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/linearLayout2"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/selector_item_grey">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:textColor="@color/title1"
        android:textSize="16sp"
        app:layout_constraintEnd_toStartOf="@+id/checkBox"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="广东省博物馆"/>

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:layout_marginStart="8dp"
        android:textColor="@color/title2"
        android:textSize="14sp"
        app:layout_constraintEnd_toStartOf="@+id/checkBox"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:text="广东省广州市天河区珠江东路2号"/>

    <CheckBox
        android:id="@+id/checkBox"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="8dp"
        android:button="@drawable/selector_check_box_green"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/view"
        android:layout_width="0dp"
        android:layout_height="1px"
        android:layout_marginTop="8dp"
        android:background="@color/line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_message"/>
</androidx.constraintlayout.widget.ConstraintLayout>
