<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingRight="@dimen/padding"
        android:paddingLeft="@dimen/padding">

        <ImageView
            android:id="@+id/iv_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:src="@drawable/ic_material_type" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/padding"
            android:layout_marginRight="@dimen/padding"
            android:layout_weight="1"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_27"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_material_name"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="材料规格名称："
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="bottom"
                    android:layout_marginRight="@dimen/padding"
                    android:lines="1" />

                <TextView
                    android:id="@+id/tv_material_rank"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="xxxx"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_main_black"
                    android:gravity="bottom|center_horizontal"
                    android:lines="1"
                    android:layout_weight="2" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_27"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_person_key"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="数量："
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_12" />

                <TextView
                    android:id="@+id/tv_inspector"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:text="xxxx"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_12" />

                <TextView
                    android:id="@+id/tv_time_key"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="进场时间："
                    android:textSize="@dimen/sp_12"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical" />

                <TextView
                    android:id="@+id/tv_inspector_time"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="2018-6-6"
                    android:textSize="@dimen/sp_12"
                    android:textColor="@color/ic_text_normal"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:layout_weight="1" />



            </LinearLayout>


        </LinearLayout>

        <CheckBox
            android:id="@+id/cb_select"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:button="@drawable/check_box_bg" />


    </LinearLayout>

    <include layout="@layout/line_space" />

</LinearLayout>