<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/total_bar" />

    <LinearLayout
        android:id="@+id/ll_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="@dimen/dp_8"
            android:paddingBottom="@dimen/dp_8"
            android:paddingLeft="@dimen/dp_20"
            android:paddingRight="@dimen/dp_20">

            <TextView
                android:id="@+id/tv_start_date"
                android:layout_width="0dp"
                android:visibility="gone"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="left"
                android:text=""
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:id="@+id/tv_con_period"
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text=""
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:id="@+id/tv_end_date"
                android:visibility="gone"
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="right"
                android:text=""
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_2"
            android:layout_marginBottom="@dimen/dp_5"
            android:background="@color/line_space" />

        <RadioGroup
            android:id="@+id/rg_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp_10"
            android:gravity="center"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/rb_status1"
                style="@style/et_notice_stati_bg"
                android:checked="true" />

            <RadioButton
                android:id="@+id/rb_status2"
                style="@style/et_notice_stati_bg"
                android:layout_marginLeft="@dimen/dp_70"
                android:layout_marginRight="@dimen/dp_70" />

            <RadioButton
                android:id="@+id/rb_status3"
                style="@style/et_notice_stati_bg" />
        </RadioGroup>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_10"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_status1"
                android:layout_width="@dimen/dp_70"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="待审批"
                android:textColor="@android:color/holo_red_light"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:id="@+id/tv_status2"
                android:layout_width="@dimen/dp_70"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_50"
                android:layout_marginRight="@dimen/dp_50"
                android:gravity="center"
                android:text="待回复"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:id="@+id/tv_status3"
                android:layout_width="@dimen/dp_70"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="已完成"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_problem_track"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_110"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible">

        <RadioGroup
            android:id="@+id/rg_notice_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:gravity="center"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/rb_notice_all"
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                android:padding="@dimen/dp_5"
                android:text="全部"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/sp_15" />

            <View
                android:layout_width="1dp"
                android:layout_height="@dimen/dp_25"
                android:background="@color/line_space" />

            <RadioButton
                android:id="@+id/rb_notice_mine"
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:button="@null"
                android:gravity="center"
                android:padding="@dimen/dp_5"
                android:text="我下发的"
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_15" />
        </RadioGroup>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <View
                android:id="@+id/view_notice_all"
                android:layout_width="@dimen/dp_0"
                android:layout_height="@dimen/dp_1"
                android:layout_margin="@dimen/dp_5"
                android:layout_weight="1"
                android:background="@color/colorAccent" />

            <View
                android:id="@+id/view_notice_mine"
                android:layout_width="@dimen/dp_0"
                android:layout_height="@dimen/dp_1"
                android:layout_margin="@dimen/dp_5"
                android:layout_weight="1"
                android:background="@color/line_space" />
        </LinearLayout>

        <Spinner
            android:id="@+id/spinner_notice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:entries="@array/array_notice"
            android:padding="@dimen/dp_10" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/line_space" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:layout_weight="1">

        <com.jwenfeng.library.pulltorefresh.PullToRefreshLayout
            android:id="@+id/ptrl_notice_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.core.widget.NestedScrollView
                app:layout_behavior="@string/appbar_scrolling_view_behavior"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:descendantFocusability="beforeDescendants"
                    android:orientation="vertical">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/lv_notice"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"/>

                </LinearLayout>

            </androidx.core.widget.NestedScrollView>

        </com.jwenfeng.library.pulltorefresh.PullToRefreshLayout>

        <Button
            android:id="@+id/btn_add_notice"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_gravity="bottom|right"
            android:layout_marginRight="@dimen/dp_10"
            android:layout_marginBottom="@dimen/dp_50"
            android:visibility="gone"
            android:background="@drawable/shap_intelligent_bg"
            android:text="+"
            android:textColor="@color/colorAccent"
            android:textSize="@dimen/sp_20" />

    </FrameLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/line_space" />

    <RadioGroup
        android:id="@+id/rg_problem_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_10"
        android:gravity="center"
        android:orientation="horizontal">

        <RadioButton
            android:id="@+id/rb_problem"
            style="@style/rb_notice_bg_checked"
            android:checked="true"
            android:text="问题跟踪"
            android:textColor="@android:color/white" />

        <RadioButton
            android:id="@+id/rb_count"
            style="@style/rb_notice_bg_checked"
            android:layout_marginLeft="@dimen/dp_36"
            android:text="统计"
            android:textColor="@color/colorAccent" />
    </RadioGroup>

</LinearLayout>