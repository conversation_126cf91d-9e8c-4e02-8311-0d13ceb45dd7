<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:background="@drawable/welcom1"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/rl_na"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_centerInParent="true">

        <ImageView
            android:id="@+id/ivInvalid"
            android:layout_width="@dimen/dp_125"
            android:layout_height="@dimen/dp_50"
            android:src="@drawable/logo"
            android:layout_marginEnd="@dimen/dp_20"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/dp_20"
            />
        <TextView
            android:id="@+id/tv_hi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="HI, KAIYUE"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="26sp"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/dp_20"
            android:layout_below="@id/ivInvalid"/>
        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="欢迎登陆四川公路智慧监理平台"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="150dp"
            android:layout_below="@id/tv_hi"/>

        <TextView
            android:id="@+id/tv_move"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:text="悦监理，您专属的项目助手"
            android:textColor="@android:color/transparent"
            android:textSize="23sp" />

    </RelativeLayout>


    <TextView
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:layout_alignParentBottom="true"
        android:text="工序化管理 让管理回归简单"
        android:textColor="@color/text_main_black"
        android:gravity="center"
        android:visibility="gone"
        android:textSize="@dimen/sp_16"/>

</RelativeLayout>