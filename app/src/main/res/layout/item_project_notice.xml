<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_20">

        <TextView
            android:id="@+id/tv_notice_title"
            style="@style/style_notice_item"
            android:text="通知单"
            android:textColor="@color/text_main_black" />

        <TextView
            android:id="@+id/tv_notice_date"
            style="@style/style_notice_item"
            android:layout_alignParentRight="true"
            android:layout_alignTop="@id/tv_notice_title"
            android:textColor="@color/colorAccent" />

        <TextView
            android:id="@+id/tv_type"
            style="@style/style_notice_item"
            android:layout_below="@id/tv_notice_title"
            android:layout_marginTop="@dimen/dp_8"
            android:text="通知单类型："
            android:textColor="@color/text_main_black" />

        <TextView
            android:id="@+id/tv_notice_type"
            style="@style/style_notice_item"
            android:layout_alignTop="@id/tv_type"
            android:layout_toRightOf="@id/tv_type"
            android:textColor="@color/text_main_black" />

        <TextView
            android:id="@+id/tv_create_person"
            style="@style/style_notice_item"
            android:layout_below="@id/tv_type"
            android:layout_marginTop="@dimen/dp_8"
            android:text="创建人："
            android:textColor="@color/text_main_black" />

        <TextView
            android:id="@+id/tv_notice_create_person"
            style="@style/style_notice_item"
            android:layout_alignTop="@id/tv_create_person"
            android:layout_toRightOf="@id/tv_create_person"
            android:textColor="@color/text_main_black" />

        <TextView
            android:id="@+id/tv_review_person"
            style="@style/style_notice_item"
            android:layout_below="@id/tv_create_person"
            android:layout_marginTop="@dimen/dp_8"
            android:text="审核人："
            android:textColor="@color/text_main_black" />

        <TextView
            android:id="@+id/tv_notice_review_person"
            style="@style/style_notice_item"
            android:layout_alignTop="@id/tv_review_person"
            android:layout_toRightOf="@id/tv_review_person"
            android:textColor="@color/text_main_black" />

        <TextView
            android:id="@+id/tv_notice_time"
            style="@style/style_notice_item"
            android:layout_alignParentRight="true"
            android:layout_alignTop="@id/tv_review_person"
            android:textColor="@color/colorAccent" />

        <TextView
            android:id="@+id/tv_time"
            style="@style/style_notice_item"
            android:layout_alignTop="@id/tv_notice_time"
            android:layout_toLeftOf="@id/tv_notice_time"
            android:text="审批时间："
            android:textColor="@color/text_main_black" />

        <TextView
            android:id="@+id/tv_review_opinion"
            style="@style/style_notice_item"
            android:layout_below="@id/tv_review_person"
            android:layout_marginTop="@dimen/dp_8"
            android:text="审批意见："
            android:textColor="@color/text_main_black" />

        <TextView
            android:id="@+id/tv_notice_opinion"
            style="@style/style_notice_item"
            android:layout_alignTop="@id/tv_review_opinion"
            android:layout_toRightOf="@id/tv_review_opinion"
            android:text="同意"
            android:textColor="@color/text_main_black" />

        <TextView
            android:id="@+id/tv_notice_status"
            style="@style/style_notice_item"
            android:layout_alignParentRight="true"
            android:layout_alignTop="@id/tv_review_opinion"
            android:padding="@dimen/dp_5"
            android:text="已完成"
            android:textColor="@color/colorAccent" />

        <View
            android:id="@+id/view_notice"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:layout_below="@id/tv_review_opinion"
            android:visibility="gone" />
    </RelativeLayout>

    <View
        style="@style/v_drvier_line_h"/>
</LinearLayout>