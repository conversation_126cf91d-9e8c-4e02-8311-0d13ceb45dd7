<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/total_bar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingLeft="@dimen/dp_4"
            android:paddingRight="@dimen/dp_4">


            <LinearLayout
                style="@style/style_form_ll1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="项目名称："
                    android:lines="1"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_project"
                    style="@style/tv_form_content"
                    android:ellipsize="end"
                    android:minLines="2"
                    android:hint="项目名称"
                    android:layout_marginBottom="@dimen/dp_10"/>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_table_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/table_bg"
                android:orientation="vertical"
                android:padding="@dimen/padding">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_27"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="致："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_14" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_construction"
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:lines="1"
                            android:layout_weight="1"/>

                        <include layout="@layout/line_bottom" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="(施工单位)"
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_14" />


                </LinearLayout>

                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_marginTop="@dimen/dp_27"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_27"
                        android:gravity="center_vertical"
                        android:text="事由："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_14" />


                    <EditText
                        android:id="@+id/et_reason"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minHeight="@dimen/dp_72"
                        android:textSize="@dimen/sp_14"
                        android:textColor="@color/text_main_black"
                        android:textColorHint="@color/edit_hit"
                        android:hint="请输入"
                        android:drawablePadding="@dimen/padding"
                        android:drawableLeft="@drawable/ic_edit_hit"
                        android:background="@null"/>

                </LinearLayout>

                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_marginTop="@dimen/dp_27"
                    android:layout_marginBottom="@dimen/dp_27"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_27"
                        android:gravity="center_vertical"
                        android:text="内容："
                        android:textColor="@color/text_main_black"
                        android:textSize="@dimen/sp_14" />


                    <EditText
                        android:id="@+id/et_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minHeight="200dp"
                        android:textSize="@dimen/sp_14"
                        android:textColor="@color/text_main_black"
                        android:textColorHint="@color/edit_hit"
                        android:hint="请输入"
                        android:drawablePadding="@dimen/padding"
                        android:drawableLeft="@drawable/ic_edit_hit"
                        android:background="@null"/>

                </LinearLayout>


                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:gravity="end"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_marginLeft="@dimen/dp_72"
                        android:layout_height="@dimen/dp_27"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:text="项目监理机构："
                            android:textColor="@color/text_main_black"
                            android:textSize="@dimen/sp_14" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_sup_org"
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:textColor="@color/text_main_black"
                                android:textSize="@dimen/sp_14"
                                android:gravity="center_vertical"
                                android:layout_weight="1"/>

                            <include layout="@layout/line_bottom" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_27"
                        android:layout_marginLeft="@dimen/dp_72"
                        android:layout_marginTop="@dimen/padding"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:text="总监理工程师："
                            android:textColor="@color/text_main_black"
                            android:textSize="@dimen/sp_14" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <EditText
                                android:id="@+id/tv_sup"
                                android:layout_width="match_parent"
                                android:textColor="@color/text_main_black"
                                android:textSize="@dimen/sp_14"
                                android:gravity="center_vertical"
                                android:layout_height="0dp"
                                android:background="@null"
                                android:layout_weight="1"/>

                            <include layout="@layout/line_bottom" />

                        </LinearLayout>

                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_27"
                        android:layout_marginTop="@dimen/padding"
                        android:layout_marginBottom="@dimen/dp_27"
                        android:layout_marginRight="@dimen/dp_27"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:text="日期："
                            android:textColor="@color/text_main_black"
                            android:textSize="@dimen/sp_14" />
                        <LinearLayout
                            android:orientation="horizontal"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent">

                            <TextView
                                android:id="@+id/tv_year"
                                android:layout_width="@dimen/dp_72"
                                android:gravity="center"
                                android:layout_height="match_parent"
                                android:textColor="@color/text_main_black"
                                android:textSize="@dimen/sp_14" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical"
                                android:text="年"
                                android:textColor="@color/text_main_black"
                                android:textSize="@dimen/sp_14" />

                            <TextView
                                android:id="@+id/tv_month"
                                android:layout_width="@dimen/dp_27"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:textColor="@color/text_main_black"
                                android:textSize="@dimen/sp_14" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical"
                                android:text="月"
                                android:textColor="@color/text_main_black"
                                android:textSize="@dimen/sp_14" />

                            <TextView
                                android:id="@+id/tv_day"
                                android:layout_width="@dimen/dp_27"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:textColor="@color/text_main_black"
                                android:textSize="@dimen/sp_14" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical"
                                android:text="日"
                                android:textColor="@color/text_main_black"
                                android:textSize="@dimen/sp_14" />

                        </LinearLayout>

                    </LinearLayout>




                </LinearLayout>


            </LinearLayout>

        </LinearLayout>


    </ScrollView>

</LinearLayout>