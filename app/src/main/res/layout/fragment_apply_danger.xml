<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".views.ApplyDangerFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

            <com.kisoft.yuejianli.ui.YLabelCell
                android:id="@+id/tv_projectname"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="项目名称"/>

            <com.kisoft.yuejianli.ui.YSelectDateCell
                android:id="@+id/tv_acceptanceTime"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_50"
                app:title="验收日期："/>
            <!--
                        <com.kisoft.yuejianli.ui.YSelectTextViewCell
                            android:id="@+id/tv_unitProjectName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:title="单位工程："/>

                        <com.kisoft.yuejianli.ui.YSelectTextViewCell
                            android:id="@+id/tv_branchName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:title="分部工程："/>

                        <com.kisoft.yuejianli.ui.YSelectTextViewCell
                            android:id="@+id/tv_subItemProject"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:title="分项工程："/>
            -->
            <com.kisoft.yuejianli.ui.TipsQuestionView
                android:id="@+id/tips_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/et_acceptanceSite"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="验收部位:" />

            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/et_acceptanceContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="验收内容:" />

            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/et_request"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="方案审批程序(人员资格、签字等)是否符合规定要求:" />

            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/et_range"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="各项控制指标是否在方案所明确的允许偏差范围内:" />

            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/et_conclusion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="检查验收结论:" />
            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/et_acceptanceStaff"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="验收人员:" />
            <com.kisoft.yuejianli.ui.YSpinnerCell
                android:id="@+id/et_outcome"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="验收结果:" />
            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/et_chiefSuperintendent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="总监理工程师:" />
            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/et_technicalLeader"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="项目技术负责人:" />
            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/et_remark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="备注:" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="@dimen/padding"
                android:paddingRight="@dimen/padding">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/title_bar_hei"
                    android:gravity="center_vertical"
                    android:text="图片"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_ptenclosure"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/check_image_hei"
                    android:layout_marginBottom="@dimen/padding"
                    android:orientation="horizontal" />
            </LinearLayout>
            <com.kisoft.yuejianli.ui.YSubmitCell
                android:id="@+id/submit_cell"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</FrameLayout>