<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

            <com.kisoft.yuejianli.ui.YLabelCell
                android:id="@+id/create_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="申请人:" />

            <com.kisoft.yuejianli.ui.YLabelCell
                android:id="@+id/create_dept"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="部门:" />

            <TextView
                style="@style/style_form_title1"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_10"
                android:text="税种：" />

            <com.zhy.view.flowlayout.TagFlowLayout
                android:id="@+id/tfl_seal_type_container"
                app:max_select="-1"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_5"
                android:paddingLeft="@dimen/dp_10"
                android:paddingRight="@dimen/dp_10" />
            <View style="@style/v_drvier_line_h" />

            <com.kisoft.yuejianli.ui.YTextFieldCell
                android:id="@+id/totalFees"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="金额小写:" />

            <com.kisoft.yuejianli.ui.YLabelCell
                android:id="@+id/totalFeesCapital"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="金额大写:" />
            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/why_reason"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="申请原因:" />

            <com.kisoft.yuejianli.ui.YFileListView
                android:id="@+id/y_file_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <com.kisoft.yuejianli.ui.YSubmitCell
                android:id="@+id/submit_cell"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</LinearLayout>