<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:paddingLeft="@dimen/padding"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/bottom_tab_hei">

        <TextView
            android:id="@+id/tv_type_name"
            android:layout_width="wrap_content"
            android:layout_weight="1"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:gravity="center_vertical"
            android:drawablePadding="@dimen/padding"
            android:drawableLeft="@drawable/ic_material_type"
            android:layout_height="match_parent" />

            <ImageView
                android:layout_width="@dimen/bottom_tab_hei"
                android:layout_height="match_parent"
                android:src="@drawable/ic_more"
                android:scaleType="center"/>

    </LinearLayout>

    <include layout="@layout/line_space"/>

</LinearLayout>