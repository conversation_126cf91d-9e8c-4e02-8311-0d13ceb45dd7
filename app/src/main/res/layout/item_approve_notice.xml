<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:paddingLeft="@dimen/padding"
        android:paddingRight="@dimen/padding"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:scaleType="center"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="@dimen/padding"
            android:src="@drawable/ic_approve"/>

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_approve_type"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp_27"
                    android:text="审批类型"
                    android:gravity="center_vertical"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14"
                    android:layout_weight="2"/>

                <TextView
                    android:id="@+id/tv_approve_status"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp_27"
                    android:layout_marginLeft="@dimen/padding"
                    android:text="审批状态"
                    android:gravity="center_vertical"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14"
                    android:layout_weight="1"/>

            </LinearLayout>

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_approve_person"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp_27"
                    android:text="审批人"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textSize="@dimen/sp_12"
                    android:layout_weight="1"/>

                <TextView
                    android:id="@+id/tv_approve_time"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_27"
                    android:text="2018-4-23"
                    android:layout_marginLeft="@dimen/padding"
                    android:gravity="center_vertical"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_12" />

            </LinearLayout>





        </LinearLayout>


    </LinearLayout>

    <include layout="@layout/line_space"/>

</LinearLayout>