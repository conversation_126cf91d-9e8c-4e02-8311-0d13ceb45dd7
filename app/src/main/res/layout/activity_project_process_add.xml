<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <include layout="@layout/total_bar" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="@dimen/padding">

            <LinearLayout
                android:id="@+id/ll_zx"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="专项名称："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_process_name"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="请选择专项"
                    android:textColor="@color/text_main_black"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />

                <ImageView
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="@dimen/dp_14"
                    android:padding="@dimen/dp_5"
                    android:src="@drawable/arrow_right" />

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="所属项目："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_project"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="在建数量："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <EditText
                    android:id="@+id/et_process_num"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:background="@null"
                    android:digits="0123456789"
                    android:gravity="center_vertical"
                    android:hint="请输入数量"
                    android:inputType="phone"
                    android:textColor="@color/text_main_black"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="完工数量："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <EditText
                    android:id="@+id/et_complete_num"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/line_space_wid"
                    android:layout_weight="1"
                    android:background="@null"
                    android:digits="0123456789"
                    android:gravity="center_vertical"
                    android:hint="请输入数量"
                    android:inputType="phone"
                    android:textColor="@color/text_main_black"
                    android:textColorHint="@color/edit_hit"
                    android:textSize="@dimen/sp_14" />

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="建设开始日期："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_begin_date"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1"
                    android:hint="请选择日期"/>

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="建设完成日期："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <TextView
                    android:id="@+id/tv_end_date"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:textSize="@dimen/sp_16"
                    android:layout_weight="1"
                    android:hint="请选择日期"/>

            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="进度状态："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_16" />

                <Spinner
                    android:id="@+id/sp_type"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_5"
                    android:layout_weight="1"
                    android:entries="@array/array_progress_type" />

                <TextView
                    android:id="@+id/tv_type"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1"
                    android:visibility="gone" />

            </LinearLayout>

            <include layout="@layout/line_space" />

            <TextView
                android:id="@+id/tv_reason"
                style="@style/style_form_title1"
                android:layout_marginTop="@dimen/dp_10"
                android:visibility="gone"
                android:textSize="@dimen/sp_16"
                android:text="滞后原因：" />

            <EditText
                android:id="@+id/et_reason"
                style="@style/et_remark"
                android:visibility="gone"
                android:layout_marginLeft="@dimen/dp_0"
                android:layout_marginTop="@dimen/dp_10"
                android:hint="请输入滞后原因"
                android:textSize="@dimen/sp_16" />

            <TextView
                style="@style/style_form_title1"
                android:layout_marginTop="@dimen/dp_10"
                android:textSize="@dimen/sp_16"
                android:text="备　　注：" />

            <EditText
                android:id="@+id/et_remark"
                style="@style/et_remark"
                android:layout_marginLeft="@dimen/dp_0"
                android:layout_marginTop="@dimen/dp_10"
                android:textSize="@dimen/sp_16"
                android:hint="请输入备注"/>

            <TextView
                android:id="@+id/tv_sub"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:background="@drawable/button_fill_bg"
                android:text="提 交"
                android:textSize="@dimen/sp_16"
                android:textColor="@color/text_bg"
                android:layout_marginTop="@dimen/dp_50"
                android:layout_marginBottom="@dimen/dp_30"
                android:layout_marginLeft="@dimen/dp_30"
                android:layout_marginRight="@dimen/dp_30"
                android:gravity="center"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</LinearLayout>