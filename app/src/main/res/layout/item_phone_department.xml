<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/phone_item_hei"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/padding"
        android:paddingRight="@dimen/padding">

        <ImageView
            android:id="@+id/iv_show"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:scaleType="fitXY"
            android:src="@drawable/ic_down" />

        <TextView
            android:id="@+id/work_name"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:paddingLeft="@dimen/padding"
            android:text="部门信息"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_16" />

        <TextView
            android:id="@+id/worker_size"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingLeft="@dimen/padding"
            android:text="人数"
            android:textColor="@color/ic_text_normal"
            android:textSize="@dimen/sp_14" />


    </LinearLayout>

    <include
        android:id="@+id/line"
        layout="@layout/line_space" />

</LinearLayout>