<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="15dp"
        android:textColor="@color/ease_conversation_color_item_time"
        android:textSize="13sp"
        tools:text="09:51"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/time"
        android:layout_marginTop="18dp"
        android:minHeight="122dp"
        android:background="@drawable/demo_bg_system_message"
        android:layout_marginLeft="@dimen/em_margin_15"
        android:layout_marginStart="@dimen/em_margin_15"
        android:layout_marginRight="@dimen/em_margin_15"
        android:layout_marginEnd="@dimen/em_margin_15">

        <com.hyphenate.easeui.widget.EaseImageView
            android:id="@+id/avatar"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="36dp"
            android:layout_marginBottom="33dp"
            android:padding="5dp"
            android:src="@drawable/ease_default_avatar"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:textColor="#ff333333"
            android:textSize="16sp"
            tools:text="name"
            app:layout_constraintTop_toTopOf="@id/avatar"
            app:layout_constraintBottom_toTopOf="@id/message"
            app:layout_constraintLeft_toRightOf="@id/avatar" />

        <TextView
            android:id="@+id/message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="#ffa3a3a3"
            android:textSize="@dimen/em_size_normal"
            tools:text="content"
            android:layout_marginRight="@dimen/em_margin_20"
            app:layout_constraintLeft_toLeftOf="@id/name"
            app:layout_constraintTop_toBottomOf="@id/name"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="@id/avatar"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>