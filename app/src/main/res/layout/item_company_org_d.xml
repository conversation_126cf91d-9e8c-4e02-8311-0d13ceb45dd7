<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:paddingLeft="20dp"
    android:paddingRight="@dimen/padding"
    android:layout_height="wrap_content">

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:text="部门："
            android:textSize="@dimen/sp_14"
            android:gravity="center_vertical"
            android:textColor="@color/text_main_black"/>

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="match_parent"
            android:text="********"
            android:textSize="@dimen/sp_14"
            android:gravity="center_vertical"
            android:textColor="@color/text_main_black"/>


    </LinearLayout>

    <include layout="@layout/line_space"/>

</LinearLayout>