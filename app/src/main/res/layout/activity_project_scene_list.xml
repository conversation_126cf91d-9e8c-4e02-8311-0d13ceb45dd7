<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_gradient_bg"
    android:orientation="vertical"
    tools:context=".views.ProjectSceneListActivity">

    <include layout="@layout/total_bar" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/dp_10">

        <com.jwenfeng.library.pulltorefresh.PullToRefreshLayout
            android:id="@+id/prLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"></androidx.recyclerview.widget.RecyclerView>
        </com.jwenfeng.library.pulltorefresh.PullToRefreshLayout>
    </LinearLayout>
</LinearLayout>