<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">
            <TextView
                android:id="@+id/tv_material_num"
                android:layout_width="wrap_content"
                android:layout_height="42dp"
                android:background="@drawable/ic_circle_blue"
                android:gravity="center"
                android:text="34"
                android:textColor="@color/colorAccent" />
            />
            <TextView
                android:id="@+id/tv_material_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="设备进退场" />
            />
        </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_material_num1"
            android:layout_width="wrap_content"
            android:layout_height="42dp"
            android:background="@drawable/ic_circle_blue"
            android:gravity="center"
            android:text="34"
            android:textColor="@color/colorAccent" />
        />
        <TextView
            android:id="@+id/tv_material_name1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="设备进退场" />
        />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_material_num2"
            android:layout_width="wrap_content"
            android:layout_height="42dp"
            android:background="@drawable/ic_circle_blue"
            android:gravity="center"
            android:text="34"
            android:textColor="@color/colorAccent" />
        />
        <TextView
            android:id="@+id/tv_material_name2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="设备进退场" />
        />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_material_num3"
            android:layout_width="wrap_content"
            android:layout_height="42dp"
            android:background="@drawable/ic_circle_blue"
            android:gravity="center"
            android:text="34"
            android:textColor="@color/colorAccent" />
        />
        <TextView
            android:id="@+id/tv_material_name3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="设备进退场" />
        />
    </LinearLayout>
</LinearLayout>