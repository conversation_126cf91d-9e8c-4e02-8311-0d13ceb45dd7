<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:tools="http://schemas.android.com/tools"
tools:context=".views.OutdataTransferActivity"

android:layout_width="match_parent"
android:layout_height="match_parent"

android:orientation="vertical">

<include layout="@layout/total_bar" />

<RadioGroup
    android:id="@+id/rg_tab"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dp_5"
    android:gravity="center"
    android:orientation="horizontal">

    <RadioButton
        android:id="@+id/rb_tab1"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:button="@null"
        android:checked="true"
        android:gravity="center"
        android:padding="@dimen/dp_5"
        android:text="我发起的"
        android:textColor="@color/colorAccent"
        android:textSize="@dimen/sp_15" />

    <View
        android:layout_width="1dp"
        android:layout_height="@dimen/dp_25"
        android:background="@color/line_space" />

    <RadioButton
        android:id="@+id/rb_tab2"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:button="@null"
        android:gravity="center"
        android:padding="@dimen/dp_5"
        android:text="待我审批"
        android:textColor="@color/text_main_black"
        android:textSize="@dimen/sp_15" />

    <View
        android:layout_width="1dp"
        android:layout_height="@dimen/dp_25"
        android:background="@color/line_space" />

    <RadioButton
        android:id="@+id/rb_tab3"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:button="@null"
        android:gravity="center"
        android:padding="@dimen/dp_5"
        android:text="我已审批"
        android:textColor="@color/text_main_black"
        android:textSize="@dimen/sp_15" />
</RadioGroup>

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <View
        android:id="@+id/view1"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_1"
        android:layout_margin="@dimen/dp_5"
        android:layout_weight="1"
        android:background="@color/colorAccent" />

    <View
        android:id="@+id/view2"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_1"
        android:layout_margin="@dimen/dp_5"
        android:layout_weight="1"
        android:background="@color/line_space" />

    <View
        android:id="@+id/view3"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_1"
        android:layout_margin="@dimen/dp_5"
        android:layout_weight="1"
        android:background="@color/line_space" />
</LinearLayout>
<RelativeLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    </androidx.recyclerview.widget.RecyclerView>
</RelativeLayout>

</LinearLayout>