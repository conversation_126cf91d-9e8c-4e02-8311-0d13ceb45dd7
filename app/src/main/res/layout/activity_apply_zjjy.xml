<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/nomal_bg"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/ll_plan"
                style="@style/style_form_ll1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center">
                    <TextView
                        android:layout_width="@dimen/dp_0"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/sp_14"
                        android:textColor="@color/text_main_black"
                        android:layout_weight="1"
                        android:text="投标计划：" />
                    <ImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:padding="@dimen/dp_5"
                        android:src="@drawable/arrow_right"/>
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_plan"
                    style="@style/tv_form_content"
                    android:ellipsize="end"
                    android:minLines="2"
                    android:hint="请选择项目计划" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="投标负责人：" />

                <TextView
                    android:id="@+id/tv_tbfzr"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="证件负责人：" />

                <EditText
                    android:id="@+id/et_zjfzr"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1"
                    android:hint="请输入证件负责人" />

            </LinearLayout>


            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="借用时间：" />

                <TextView
                    android:id="@+id/tv_time"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="证件配置完成时间：" />

                <TextView
                    android:id="@+id/tv_time1"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="归还日期：" />

                <TextView
                    android:id="@+id/tv_time2"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:layout_height="25dp"
                    android:text="移交日期：" />

                <TextView
                    android:id="@+id/tv_time3"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llAddStateItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <View style="@style/v_drvier_line_h" />

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:layout_height="25dp"
                        android:text="公司证件：" />

                    <TextView
                        android:id="@+id/tvFilingCertificate"
                        style="@style/et_notice_bg1"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:drawablePadding="@dimen/dp_5"
                        android:drawableEnd="@drawable/ic_more"
                        android:textColor="@color/ic_text_normal"
                        android:text="已选0"/>

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycleViewFC"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

                <View style="@style/v_drvier_line_h" />

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:layout_height="25dp"
                        android:text="个人证件：" />

                    <TextView
                        android:id="@+id/tvEmpProfess"
                        style="@style/et_notice_bg1"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:drawablePadding="@dimen/dp_5"
                        android:drawableEnd="@drawable/ic_more"
                        android:textColor="@color/ic_text_normal"
                        android:text="已选0"/>

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycleViewEmpProfess"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

                <View style="@style/v_drvier_line_h" />

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:layout_height="25dp"
                        android:text="劳动合同：" />

                    <TextView
                        android:id="@+id/tvEmpCon"
                        style="@style/et_notice_bg1"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:drawablePadding="@dimen/dp_5"
                        android:drawableEnd="@drawable/ic_more"
                        android:textColor="@color/ic_text_normal"
                        android:text="已选0"/>

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycleViewEmpCon"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

                <View style="@style/v_drvier_line_h" />

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:layout_height="25dp"
                        android:text="业绩证明材料：" />

                    <TextView
                        android:id="@+id/tvConInfo"
                        style="@style/et_notice_bg1"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:drawablePadding="@dimen/dp_5"
                        android:drawableEnd="@drawable/ic_more"
                        android:textColor="@color/ic_text_normal"
                        android:text="已选0"/>

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycleViewConInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

            </LinearLayout>
            <View
                android:id="@+id/divHasCommit"
                style="@style/v_drvier_line_h"
                android:visibility="gone"/>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycleViewHasCommit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="联系方式：" />

                <EditText
                    android:id="@+id/et_mobile"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1"
                    android:maxLength="11"
                    android:digits="0123456789"
                    android:inputType="phone"
                    android:hint="请输入联系方式" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="使用类型：" />

                <Spinner
                    android:id="@+id/sp2"
                    android:layout_height="wrap_content"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1"
                    android:layout_marginLeft="@dimen/dp_5"
                    android:entries="@array/array_certificates_type2"/>

                <TextView
                    android:id="@+id/tv2"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:visibility="gone"
                    android:layout_weight="1" />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="是否打印：" />

                <Spinner
                    android:id="@+id/sp3"
                    android:layout_height="wrap_content"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1"
                    android:layout_marginLeft="@dimen/dp_5"
                    android:entries="@array/array_certificates_type3"/>

                <TextView
                    android:id="@+id/tv3"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:visibility="gone"
                    android:layout_weight="1" />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />
            <com.kisoft.yuejianli.ui.YFileListView
                android:id="@+id/y_file_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/tv_sub"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:background="@drawable/button_fill_bg"
                android:text="提 交"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/text_bg"
                android:layout_marginTop="@dimen/dp_50"
                android:layout_marginBottom="@dimen/dp_30"
                android:layout_marginLeft="@dimen/dp_30"
                android:layout_marginRight="@dimen/dp_30"
                android:gravity="center"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>