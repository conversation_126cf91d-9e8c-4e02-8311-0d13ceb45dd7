<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/dp_8">

    <ImageView
        android:id="@+id/head_icon"
        android:layout_width="@dimen/dp_25"
        android:layout_height="@dimen/dp_25"
        android:background="@drawable/ic_circle_first"
        android:scaleType="fitXY"/>

    <TextView
        android:id="@+id/name_tv"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_25"
        android:layout_toRightOf="@+id/head_icon"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/dp_5"
        android:text="张老师"
        android:textColor="@color/text_main_black"
        android:textSize="@dimen/dp_10" />

    <TextView
        android:id="@+id/comment_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/head_icon"
        android:layout_marginTop="@dimen/dp_5"
        android:text="一口气五集！真棒的课程：内容全面，架构有序！加上老师口语表达速度平稳清楚，收获很多。"
        android:textColor="@color/text_main_black"
        android:textSize="@dimen/dp_12" />

</RelativeLayout>