<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".views.ApplyMHPersonRequireFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusableInTouchMode="true"
            android:orientation="vertical"
            android:padding="@dimen/padding">

            <com.kisoft.yuejianli.ui.YLabelCell
                android:id="@+id/project_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="项目名称:" />

            <com.kisoft.yuejianli.ui.YSelectDateCell
                android:id="@+id/create_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="填表时间:" />

            <com.kisoft.yuejianli.ui.YTextFieldCell
                android:id="@+id/ctrl_rmb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="监理费:" />

            <com.kisoft.yuejianli.ui.YSelectDateCell
                android:id="@+id/work_start_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="工期(起始日期):" />

            <com.kisoft.yuejianli.ui.YTextFieldCell
                android:id="@+id/people_num_sum"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="总人数:" />

            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/major_work"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="各专业分工:" />

            <com.kisoft.yuejianli.ui.YTextViewCell
                android:id="@+id/why_reason"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="申请原因:" />


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/padding">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="人员需求"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <LinearLayout
                    android:id="@+id/llOpDetail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_gravity="end"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/tvAddDetail"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/padding"
                        android:background="@drawable/bg_round_main_empty_10"
                        android:paddingStart="@dimen/padding"
                        android:paddingTop="@dimen/dp_3"
                        android:paddingEnd="@dimen/padding"
                        android:paddingBottom="@dimen/dp_3"
                        android:text="添加"
                        android:textColor="@color/colorPrimary"
                        android:textSize="@dimen/sp_13" />

                    <TextView
                        android:id="@+id/tvDeleteDetail"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/padding"
                        android:background="@drawable/bg_round_grey_empty_10"
                        android:paddingStart="@dimen/padding"
                        android:paddingTop="@dimen/dp_3"
                        android:paddingEnd="@dimen/padding"
                        android:paddingBottom="@dimen/dp_3"
                        android:text="删除"
                        android:textColor="@color/ic_text_normal"
                        android:textSize="@dimen/sp_13" />

                </LinearLayout>
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/line_space"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/table_div"
                    android:layout_marginTop="@dimen/table_div"
                    android:layout_marginEnd="@dimen/table_div"
                    android:layout_marginBottom="@dimen/table_div"
                    android:layout_weight="1"
                    android:background="@color/main_bg"
                    android:gravity="center"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:text="岗位"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/table_text" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/table_div"
                    android:layout_marginEnd="@dimen/table_div"
                    android:layout_marginBottom="@dimen/table_div"
                    android:layout_weight="1"
                    android:background="@color/main_bg"
                    android:gravity="center"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:text="专业"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/table_text" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/table_div"
                    android:layout_marginEnd="@dimen/table_div"
                    android:layout_marginBottom="@dimen/table_div"
                    android:layout_weight="1"
                    android:background="@color/main_bg"
                    android:gravity="center"
                    android:paddingTop="@dimen/dp_3"
                    android:paddingBottom="@dimen/dp_3"
                    android:text="人数"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/table_text" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycleViewDetail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.kisoft.yuejianli.ui.YFileListView
                android:id="@+id/y_file_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>


            <com.kisoft.yuejianli.ui.YSubmitCell
                android:id="@+id/submit_cell"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>