<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="形成年度：" />

                <EditText
                    android:id="@+id/et_reply_time"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1"
                    android:enabled="false" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="承办部门：" />

                <TextView
                    android:id="@+id/tv_document_dep"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="文件日期：" />

                <TextView
                    android:id="@+id/tv_document_basic_date"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="责&#8194;任&#8194;者：" />

                <EditText
                    android:id="@+id/et_document_person"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="文件字号：" />

                <EditText
                    android:id="@+id/et_document_basic_font_num"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="标　　题：" />

                <EditText
                    android:id="@+id/et_document_basic_num"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="保&#8194;密&#8194;级：" />

                <Spinner
                    android:id="@+id/spinner_document_basic_level"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_5"
                    android:layout_weight="1"
                    android:entries="@array/array_notice" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="保密期限：" />

                <Spinner
                    android:id="@+id/spinner_document_basic_term"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_5"
                    android:layout_weight="1"
                    android:entries="@array/array_document_basic_term" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="紧急程度：" />

                <Spinner
                    android:id="@+id/spinner_document_basic_urgent"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_5"
                    android:layout_weight="1"
                    android:entries="@array/array_notice" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="主　　送：" />

                <EditText
                    android:id="@+id/et_document_basic_main"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="抄　　送：" />

                <EditText
                    android:id="@+id/et_document_basic_cc"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

          <com.kisoft.yuejianli.ui.YTextViewCell
              android:id="@+id/arcBody"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              app:title="内容："/>

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="承&#8194;办&#8194;人：" />

                <TextView
                    android:id="@+id/tv_document_basic_user"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1"
                    android:text="测试02" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="承办日期：" />

                <TextView
                    android:id="@+id/tv_document_basic_take_date"
                    style="@style/et_notice_bg1"
                    android:layout_width="@dimen/dp_0"
                    android:layout_weight="1"
                    android:text="2018-11-27" />

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="拟&#8194;稿&#8194;人：" />


                <LinearLayout
                    android:id="@+id/ll_document_basic_draft"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_document_basic_draft"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1" />

                    <ImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:layout_marginRight="@dimen/dp_14"
                        android:padding="@dimen/dp_5"
                        android:src="@drawable/arrow_right" />
                </LinearLayout>
            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="核&#8194;稿&#8194;人：" />

                <LinearLayout
                    android:id="@+id/ll_document_basic_review"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_document_basic_review"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1" />

                    <ImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:layout_marginRight="@dimen/dp_14"
                        android:padding="@dimen/dp_5"
                        android:src="@drawable/arrow_right" />
                </LinearLayout>

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="审&#8194;稿&#8194;人：" />

                <LinearLayout
                    android:id="@+id/ll_document_basic_verify"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_document_basic_verify"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1" />

                    <ImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:layout_marginRight="@dimen/dp_14"
                        android:padding="@dimen/dp_5"
                        android:src="@drawable/arrow_right" />
                </LinearLayout>

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="校&#8194;对&#8194;人：" />

                <LinearLayout
                    android:id="@+id/ll_document_basic_proof"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_document_basic_proof"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1" />

                    <ImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:layout_marginRight="@dimen/dp_14"
                        android:padding="@dimen/dp_5"
                        android:src="@drawable/arrow_right" />
                </LinearLayout>

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="会&#8194;签&#8194;人：" />

                <LinearLayout
                    android:id="@+id/ll_document_basic_sign"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_document_basic_sign"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1" />

                    <ImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:layout_marginRight="@dimen/dp_14"
                        android:padding="@dimen/dp_5"
                        android:src="@drawable/arrow_right" />
                </LinearLayout>

            </LinearLayout>

            <View style="@style/v_drvier_line_h" />

            <LinearLayout style="@style/style_form_ll1">

                <TextView
                    style="@style/style_form_title1"
                    android:text="签&#8194;发&#8194;人：" />

                <LinearLayout
                    android:id="@+id/ll_document_basic_lssue"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_document_basic_lssue"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1" />

                    <ImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:layout_marginRight="@dimen/dp_14"
                        android:padding="@dimen/dp_5"
                        android:src="@drawable/arrow_right" />
                </LinearLayout>

            </LinearLayout>

            <!--<View style="@style/v_drvier_line_h" />-->

            <!--<LinearLayout style="@style/style_form_ll1">-->

                <!--<TextView-->
                    <!--style="@style/style_form_title1"-->
                    <!--android:text="附　　件：" />-->

                <!--<LinearLayout-->
                    <!--android:layout_width="@dimen/dp_0"-->
                    <!--android:layout_height="wrap_content"-->
                    <!--android:layout_weight="3"-->
                    <!--android:orientation="horizontal">-->

                    <!--<TextView-->
                        <!--android:id="@+id/tv_document_basic_factfile"-->
                        <!--style="@style/et_notice_bg1"-->
                        <!--android:layout_width="@dimen/dp_0"-->
                        <!--android:layout_weight="1" />-->

                    <!--<Button-->
                        <!--android:id="@+id/btn_document_basic_factfile"-->
                        <!--android:layout_width="wrap_content"-->
                        <!--android:layout_height="@dimen/dp_30"-->
                        <!--android:layout_marginLeft="@dimen/dp_5"-->
                        <!--android:background="@drawable/button_fill_bg"-->
                        <!--android:gravity="center"-->
                        <!--android:text="选择文件"-->
                        <!--android:textColor="@color/text_bg" />-->
                <!--</LinearLayout>-->

            <!--</LinearLayout>-->

            <View style="@style/v_drvier_line_h" />
            <com.kisoft.yuejianli.ui.YFileListView
                android:id="@+id/y_doc_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:title="文档列表"/>

            <com.kisoft.yuejianli.ui.YFileListView
                android:id="@+id/y_file_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/tv_sub"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_bar_hei"
                android:layout_marginBottom="@dimen/dp_30"
                android:layout_marginLeft="@dimen/dp_30"
                android:layout_marginRight="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_50"
                android:background="@drawable/button_fill_bg"
                android:gravity="center"
                android:text="提 交"
                android:textColor="@color/text_bg"
                android:textSize="@dimen/sp_14" />

        </LinearLayout>
    </ScrollView>
</LinearLayout>