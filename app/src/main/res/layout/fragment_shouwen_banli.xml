<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                style="@style/style_form_title1"
                android:layout_margin="@dimen/dp_10"
                android:text="办理过程：" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginRight="@dimen/dp_10"
                android:orientation="vertical"
                android:padding="@dimen/dp_1">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/style_task_table_title1"
                        android:layout_weight="1"
                        android:textColor="@color/colorPrimary"
                        android:text="序号" />

                    <TextView
                        style="@style/style_task_table_title1"
                        android:layout_weight="1.5"
                        android:textColor="@color/colorPrimary"
                        android:text="办理人" />

                    <TextView
                        style="@style/style_task_table_title1"
                        android:layout_weight="1.5"
                        android:textColor="@color/colorPrimary"
                        android:text="办理结果" />

                    <TextView
                        style="@style/style_task_table_title1"
                        android:layout_weight="2"
                        android:textColor="@color/colorPrimary"
                        android:text="办理意见" />

                    <TextView
                        style="@style/style_task_table_title1"
                        android:layout_weight="2"
                        android:textColor="@color/colorPrimary"
                        android:text="接收日期" />

                    <TextView
                        style="@style/style_task_table_title1"
                        android:layout_weight="2"
                        android:textColor="@color/colorPrimary"
                        android:text="结束日期" />
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_document_approval_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_footer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dp_5"
                android:orientation="vertical">

                <LinearLayout style="@style/style_form_ll1">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="当前办理人：" />

                    <TextView
                        android:id="@+id/tv_document_approval_name"
                        style="@style/et_notice_bg1"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1" />

                </LinearLayout>



                <!--<View style="@style/v_drvier_line_h" />-->

                <!--<LinearLayout style="@style/style_form_ll1">-->

                <!--<TextView-->
                <!--style="@style/style_form_title1"-->
                <!--android:text="下一审批节点：" />-->

                <!--<Spinner-->
                <!--android:id="@+id/spinner_document_approval"-->
                <!--android:layout_width="@dimen/dp_0"-->
                <!--android:layout_height="wrap_content"-->
                <!--android:layout_weight="2.5"-->
                <!--android:entries="@array/array_task" />-->

                <!--</LinearLayout>-->

                <View style="@style/v_drvier_line_h" />

                <TextView
                    style="@style/style_form_title1"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_10"
                    android:text="办理意见：" />

                <EditText
                    android:id="@+id/tv_document_approval_content"
                    style="@style/et_remark"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_5"
                    android:text="已阅"
                    android:hint="请输入批示"/>

                <com.kisoft.yuejianli.ui.YSelectTextViewCell
                    android:id="@+id/songyueView"
                    android:layout_width="match_parent"
                    android:layout_height="150dp"
                    app:title="送阅人"/>

<!--
                <LinearLayout
                    android:id="@+id/ll_jd"
                    style="@style/style_form_ll1"
                    android:visibility="gone">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="选择节点：" />

                    <Spinner
                        android:id="@+id/sp_jd"
                        android:layout_height="wrap_content"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1"
                        android:layout_marginLeft="@dimen/dp_5"/>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_jd1"
                    style="@style/style_form_ll1"
                    android:visibility="gone">

                    <TextView
                        style="@style/style_form_title1"
                        android:text="选择节点：" />

                    <Spinner
                        android:id="@+id/sp_jd1"
                        android:layout_height="wrap_content"
                        android:layout_width="@dimen/dp_0"
                        android:layout_weight="1"
                        android:layout_marginLeft="@dimen/dp_5"/>

                </LinearLayout>
                -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/tv_submit"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/title_bar_hei"
                        android:layout_weight="1"
                        android:layout_margin="5dp"
                        android:background="@drawable/button_fill_bg"
                        android:gravity="center"
                        android:text="已阅"
                        android:textColor="@color/text_bg"
                        android:textSize="@dimen/sp_14" />
                    <TextView
                        android:id="@+id/tv_tuihui"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/title_bar_hei"
                        android:layout_weight="1"
                        android:layout_margin="5dp"
                        android:background="@drawable/button_fill_bg"
                        android:gravity="center"
                        android:text="退回"
                        android:textColor="@color/text_bg"
                        android:textSize="@dimen/sp_14" />
                    <TextView
                        android:id="@+id/tv_zhuanfa"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/title_bar_hei"
                        android:layout_weight="1"
                        android:layout_margin="5dp"
                        android:background="@drawable/button_fill_bg"
                        android:gravity="center"
                        android:text="转发"
                        android:textColor="@color/text_bg"
                        android:textSize="@dimen/sp_14" />
                    <TextView
                        android:id="@+id/tv_jieshu"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/title_bar_hei"
                        android:layout_weight="1"
                        android:layout_margin="5dp"
                        android:background="@drawable/button_fill_bg"
                        android:gravity="center"
                        android:text="结束"
                        android:textColor="@color/text_bg"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>