<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".views.ProjectCheckReviewActivity">
    <include layout="@layout/total_bar" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusableInTouchMode="true"
            android:orientation="vertical">
            <LinearLayout
                style="@style/line_item_has_padding">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="表单编号："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <EditText
                    android:id="@+id/etPcbNum"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:background="@null"
                    android:hint="请输入编号"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
            <include layout="@layout/line_space" />
            <LinearLayout
                style="@style/line_item_has_padding">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="所属项目："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvProjectName"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>
            <include layout="@layout/line_space" />
            <LinearLayout
                style="@style/line_item_has_padding">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="整改时间："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvRectDate"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:drawableEnd="@drawable/ic_down"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <include layout="@layout/line_space" />
            <LinearLayout
                style="@style/line_item_has_padding">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="项目监理部："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvSupOrgName"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:hint="请选择部门"
                    android:drawableEnd="@drawable/arrow_right"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <include layout="@layout/line_space" />

            <LinearLayout
                style="@style/line_item_has_padding">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="复查人员："
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvReviewUser"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/line_space_wid"
                    android:gravity="center_vertical"
                    android:hint=""
                    android:drawableEnd="@drawable/arrow_right"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <include layout="@layout/line_space" />
            <TextView
                style="@style/line_item_has_padding"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/text_main_black"
                android:text="问题状态"
                />

            <LinearLayout
                style="@style/line_item_has_padding">
                <RadioGroup
                    android:id="@+id/rgState"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <RadioButton
                        android:id="@+id/rb1"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:text="整改中" />

                    <RadioButton
                        android:id="@+id/rb2"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:text="已整改" />

                    <RadioButton
                        android:id="@+id/rb3"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:text="已复查" />

                </RadioGroup>
            </LinearLayout>
            <include layout="@layout/line_space" />
            <TextView
                style="@style/line_item_has_padding"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/text_main_black"
                android:text="复查结果"
                />

            <LinearLayout
                style="@style/line_item_has_padding">
                <RadioGroup
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <RadioButton
                        android:id="@+id/rbOk"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:text="合格" />

                    <RadioButton
                        android:id="@+id/rbNo"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:text="不合格" />

                    <Space
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"/>

                </RadioGroup>
            </LinearLayout>
            <include layout="@layout/line_space" />
            <TextView
                style="@style/line_item_has_padding"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/text_main_black"
                android:text="整改详细内容"
                />
            <EditText
                android:id="@+id/etRectContent"
                style="@style/et_remark"
                android:layout_marginTop="@dimen/dp_5"
                android:layout_marginStart="@dimen/padding"
                android:layout_marginEnd="@dimen/padding"
                />
            <TextView
                style="@style/line_item_has_padding"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/text_main_black"
                android:text="复查意见"
                />
            <EditText
                android:id="@+id/etReviewOpinion"
                style="@style/et_remark"
                android:layout_marginTop="@dimen/dp_5"
                android:layout_marginStart="@dimen/padding"
                android:layout_marginEnd="@dimen/padding"
                />

            <LinearLayout
                style="@style/line_item">

                <TextView
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="添加问题："
                    android:textStyle="bold"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:scaleType="center"
                    android:layout_marginEnd="@dimen/padding"
                    android:src="@drawable/arrow_down" />
            </LinearLayout>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycleViewQuestion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/tv_sub"
                android:layout_width="match_parent"
                android:layout_height="@dimen/bottom_tab_hei"
                android:textSize="@dimen/sp_14"
                android:text="提 交"
                android:background="@drawable/button_fill_bg"
                android:textColor="@color/text_bg"
                android:gravity="center"
                android:layout_margin="@dimen/line_space_wid"/>
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</LinearLayout>