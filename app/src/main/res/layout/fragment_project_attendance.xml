<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:paddingLeft="@dimen/dp_72"
        android:paddingRight="@dimen/dp_72">

        <ImageView
            android:id="@+id/iv_left"
            android:layout_width="@dimen/title_bar_hei"
            android:layout_height="@dimen/title_bar_hei"
            android:padding="@dimen/padding"
            android:src="@drawable/ic_left"/>

        <TextView
            android:id="@+id/tv_month"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="2018-03-25"
            android:textColor="@color/text_main_black"
            android:gravity="center"
            android:textSize="@dimen/sp_14"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/iv_right"
            android:layout_width="@dimen/title_bar_hei"
            android:layout_height="@dimen/title_bar_hei"
            android:padding="@dimen/padding"
            android:src="@drawable/ic_right"/>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_project_attendance"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    </androidx.recyclerview.widget.RecyclerView>

</LinearLayout>