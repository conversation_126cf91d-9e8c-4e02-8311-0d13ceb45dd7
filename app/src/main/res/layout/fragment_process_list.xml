<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <LinearLayout
        android:visibility="gone"
        android:id="@+id/query_container"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei">

        <EditText
            android:id="@+id/et_query_content"
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/padding"
            android:layout_marginRight="@dimen/padding"
            android:layout_weight="1"
            android:background="@drawable/edit_unfill_bg"
            android:gravity="center_vertical"
            android:hint="请输入名称"
            android:paddingLeft="@dimen/padding"
            android:textColorHint="@color/edit_hit"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/tv_query"
            android:layout_width="@dimen/def_height"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="@dimen/padding"
            android:background="@drawable/edit_unfill_bg"
            android:gravity="center"
            android:text="查询"
            android:textColor="@color/colorAccent" />

    </LinearLayout>
    <com.jwenfeng.library.pulltorefresh.PullToRefreshLayout
        android:id="@+id/ptrl_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
    </com.jwenfeng.library.pulltorefresh.PullToRefreshLayout>
</LinearLayout>