<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingTop="13dp">

    <TextView
        android:id="@+id/timestamp"
        style="@style/chat_text_date_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_chat_activity" >

        <com.hyphenate.easeui.widget.EaseImageView
            android:id="@+id/iv_userhead"
            style="@style/ease_row_sent_iv_userhead_style"/>

        <RelativeLayout
            android:id="@+id/bubble"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/margin_chat_activity"
            android:layout_toLeftOf="@id/iv_userhead"
            android:layout_below="@id/tv_userid"
            android:padding="8dp"
            android:minHeight="30dp"
            android:layout_marginTop="2dp"
            android:background="@drawable/ease_chat_bubble_send_bg">

            <TextView
                android:id="@+id/tv_chatcontent"
                style="@style/chat_content_date_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:background="@null"
                android:minHeight="@dimen/ease_chat_text_min_height"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                tools:text="已取消" />

            <ImageView
                android:layout_marginRight="4dp"
                android:id="@+id/iv_call_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@id/tv_chatcontent"
                android:layout_centerVertical="true"
                android:src="@drawable/d_chat_voice_call" />

        </RelativeLayout>

        <ImageView
            android:id="@+id/msg_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/bubble"
            android:layout_marginRight="@dimen/ease_chat_ack_margin_bubble"
            android:clickable="true"
            android:src="@drawable/ease_msg_state_failed_resend"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_ack"
            style="@style/chat_text_name_style"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/bubble"
            android:layout_marginRight="@dimen/ease_chat_ack_margin_bubble"
            android:text="@string/text_ack_msg"
            android:textSize="12sp"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/tv_delivered"
            style="@style/chat_text_name_style"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/bubble"
            android:layout_marginRight="@dimen/ease_chat_ack_margin_bubble"
            android:text="@string/text_delivered_msg"
            android:textSize="12sp"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/tv_userid"
            style="@style/chat_text_name_style"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/chat_nick_margin_left"
            android:textSize="@dimen/chat_nick_text_size"
            android:layout_toLeftOf="@id/iv_userhead"
            android:visibility="gone" />

    </RelativeLayout>

</LinearLayout>