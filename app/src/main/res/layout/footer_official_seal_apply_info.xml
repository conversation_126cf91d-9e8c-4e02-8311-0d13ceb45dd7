<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <TextView
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:background="@color/colorAccent"
        android:text="请选择审批意见："
        android:paddingLeft="@dimen/padding"
        android:layout_marginTop="@dimen/dp_27"
        android:textSize="@dimen/sp_14"
        android:textColor="@color/title_text"
        android:gravity="center_vertical"/>

<RadioGroup
    android:id="@+id/rg_apply_result"
    android:layout_width="match_parent"
    android:orientation="horizontal"
    android:paddingLeft="@dimen/dp_27"
    android:paddingRight="@dimen/dp_27"
    android:layout_height="@dimen/def_height">

    <RadioButton
        android:id="@+id/rv_apply_ok"
        android:layout_width="wrap_content"
        android:layout_weight="1"
        android:layout_height="match_parent"
        android:text="同意"
        android:button="@drawable/check_box_bg"
        android:textSize="@dimen/sp_14"
        android:textColor="@color/chat_color_5"
        android:gravity="center_vertical"/>

    <RadioButton
        android:id="@+id/rv_apply_back"
        android:layout_width="wrap_content"
        android:layout_weight="1"
        android:layout_height="match_parent"
        android:text="退回调整"
        android:button="@drawable/check_box_bg"
        android:textSize="@dimen/sp_14"
        android:textColor="@color/colorAccent"
        android:gravity="center_vertical"/>


    <RadioButton
        android:id="@+id/rv_apply_no"
        android:layout_width="wrap_content"
        android:layout_weight="1"
        android:layout_height="match_parent"
        android:text="不同意"
        android:button="@drawable/check_box_bg"
        android:textSize="@dimen/sp_14"
        android:textColor="@color/sign_not"
        android:gravity="center_vertical" />

</RadioGroup>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_27"
        android:text="审批说明："
        android:textColor="@color/text_main_black"
        android:textSize="@dimen/sp_14"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/padding"/>

    <EditText
        android:id="@+id/et_apply_remark"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dp_72"
        android:layout_marginRight="@dimen/padding"
        android:background="@null"
        android:hint="请输入审批备注  （非必填）"
        android:textColorHint="@color/edit_hit"
        android:textSize="@dimen/sp_14"
        android:textColor="@color/ic_text_normal"
        android:layout_marginLeft="@dimen/padding"/>

    <TextView
        android:id="@+id/tv_submit"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:layout_margin="@dimen/title_bar_hei"
        android:textColor="@color/title_text"
        android:gravity="center"
        android:text="提 交"
        android:textSize="@dimen/sp_14"
        android:background="@drawable/button_fill_bg"/>

</LinearLayout>