<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <com.facebook.drawee.view.SimpleDraweeView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:fresco="http://schemas.android.com/apk/res-auto"
        android:id="@+id/iv_pic"
        android:layout_width="@dimen/check_image_hei"
        android:layout_height="@dimen/check_image_hei"
        android:layout_marginLeft="@dimen/padding"
        android:layout_marginRight="@dimen/padding"
        fresco:backgroundImage="@color/text_bg"
        fresco:failureImage="@drawable/ic_add_picture"
        fresco:failureImageScaleType="centerInside"
        fresco:placeholderImage="@drawable/ic_add_picture"
        fresco:placeholderImageScaleType="fitCenter"/>
    <ImageView
        android:id="@+id/clear_btn"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:layout_marginLeft="@dimen/padding"
        android:layout_marginRight="@dimen/padding"
        android:scaleType="center"
        android:src="@drawable/ib_public_delete" />
</FrameLayout>