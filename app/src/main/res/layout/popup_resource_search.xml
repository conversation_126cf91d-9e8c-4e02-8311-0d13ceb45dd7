<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/white"
    android:focusableInTouchMode="true"
    android:padding="@dimen/padding">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tvInputTitle1"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="项目名称："
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14" />

        <EditText
            android:id="@+id/etInputTitle1"
            style="@style/et_notice_bg1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:hint="请输入"
            />

    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tvInputTitle2"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="项目名称："
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14" />

        <EditText
            android:id="@+id/etInputTitle2"
            style="@style/et_notice_bg1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:hint="请输入"
            />

    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tvInputTitle3"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="项目名称："
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14" />

        <EditText
            android:id="@+id/etInputTitle3"
            style="@style/et_notice_bg1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:hint="请输入"
            />

    </LinearLayout>
    <LinearLayout
        android:id="@+id/llInput4"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tvInputTitle4"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="项目名称："
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14" />

        <EditText
            android:id="@+id/etInputTitle4"
            style="@style/et_notice_bg1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:hint="请输入"
            />

    </LinearLayout>
    <LinearLayout
        android:id="@+id/llStartTime"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="开始时间："
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/tvStartTime"
            style="@style/et_notice_bg1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:drawableEnd="@drawable/ic_down"
            android:hint=""
            />

    </LinearLayout>
    <LinearLayout
        android:id="@+id/llEndTime"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="结束时间："
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/tvEndTime"
            style="@style/et_notice_bg1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:drawableEnd="@drawable/ic_down"
            android:hint=""
            />
    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/bottom_tab_hei">

        <TextView
            android:id="@+id/tvReset"
            android:layout_width="@dimen/dp_0"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:textSize="@dimen/sp_15"
            android:textColor="@color/text_bg"
            android:gravity="center"
            android:text="重置"
            android:background="@drawable/button_fill_bg"/>
        <TextView
            android:id="@+id/tvConfirm"
            android:layout_width="@dimen/dp_0"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:textSize="@dimen/sp_15"
            android:textColor="@color/text_bg"
            android:gravity="center"
            android:text="确认"
            android:background="@drawable/button_fill_bg"/>
    </LinearLayout>
</LinearLayout>
