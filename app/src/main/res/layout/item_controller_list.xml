<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/padding"
        android:paddingRight="@dimen/padding">

        <ImageView
            android:id="@+id/iv_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:src="@drawable/ic_sup_log" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/padding"
            android:layout_marginRight="@dimen/padding"
            android:layout_weight="1"
            android:orientation="vertical">


            <TextView
                android:id="@+id/tv_material_name"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_27"
                android:layout_marginRight="@dimen/padding"
                android:gravity="bottom"
                android:lines="1"
                android:text="现场巡视记录"
                android:textColor="@color/text_main_black"
                android:textSize="@dimen/sp_14" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_27"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_state"
                    android:layout_width="wrap_content"
                    android:layout_marginTop="5dp"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="质量情况:"
                    android:textSize="@dimen/sp_12" />
                <View
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="1dp"/>

                <TextView
                    android:id="@+id/tv_status"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:layout_marginTop="5dp"
                    android:text="已上报"
                    android:textSize="@dimen/sp_12" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_27"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_person_key"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="巡视人:"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_12" />

                <TextView
                    android:id="@+id/tv_inspector"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:text="xxxx"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_12" />

                <TextView
                    android:id="@+id/tv_time_key"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="巡视时间:"
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_12" />

                <TextView
                    android:id="@+id/tv_inspector_time"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:text=""
                    android:textColor="@color/ic_text_normal"
                    android:textSize="@dimen/sp_12" />


            </LinearLayout>

        </LinearLayout>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:src="@drawable/ic_more" />


    </LinearLayout>

    <include layout="@layout/line_space" />

</LinearLayout>