<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
  -->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_idcard"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingLeft="@dimen/activity_horizontal_margin"
    android:paddingRight="@dimen/activity_horizontal_margin"
    android:orientation="vertical"
    android:paddingTop="@dimen/activity_vertical_margin"
    android:paddingBottom="@dimen/activity_vertical_margin"
    tools:context=".views.IDCardActivity">

    <Button
        android:text="相册选择 正面"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/gallery_button_front"
        android:visibility="gone"
        tools:ignore="HardcodedText"/>

    <Button
        android:text="相册选择 反面"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/gallery_button_back"
        android:visibility="gone"
        tools:ignore="HardcodedText"/>

    <Button
        android:text="身份证正面"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/id_card_front_button"
        tools:ignore="HardcodedText"/>

    <Button
        android:text="身份证正面(本地质量控制)"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/id_card_front_button_native"
        tools:ignore="HardcodedText"/>

    <Button
        android:text="身份证反面"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/id_card_back_button"
        tools:ignore="HardcodedText"/>

    <Button
        android:text="身份证反面(本地质量控制)"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/id_card_back_button_native"
        tools:ignore="HardcodedText"/>

    <TextView
        android:id="@+id/info_text_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

</LinearLayout>
