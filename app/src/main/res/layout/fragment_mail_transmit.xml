<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingStart="@dimen/padding"
    android:paddingEnd="@dimen/padding"
    tools:context=".views.MailTransmitFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="收件人："
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/tvReceiverName"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/line_space_wid"
            android:gravity="center_vertical"
            android:hint="请选择收件人"
            android:drawableEnd="@drawable/arrow_right"
            android:textColor="@color/ic_text_normal"
            android:textSize="@dimen/sp_14" />
    </LinearLayout>

    <include layout="@layout/line_space" />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="抄送人："
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/tvCcmanName"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/line_space_wid"
            android:gravity="center_vertical"
            android:hint="请选择抄送人"
            android:drawableEnd="@drawable/arrow_right"
            android:textColor="@color/ic_text_normal"
            android:textSize="@dimen/sp_14" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvCommit"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:layout_margin="@dimen/dp_27"
        android:layout_marginStart="@dimen/padding"
        android:background="@drawable/button_fill_bg"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:text="确定转发"
        android:textColor="@color/text_bg"
        android:textSize="@dimen/sp_14" />
</LinearLayout>