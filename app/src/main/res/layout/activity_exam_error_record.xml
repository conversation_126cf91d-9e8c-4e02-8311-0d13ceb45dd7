<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".views.ExamErrorRecordActivity">

    <include layout="@layout/total_bar"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/def_height"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/padding"
        android:paddingEnd="@dimen/padding">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:text="全部错题"/>

        <TextView
            android:id="@+id/tvAll"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/ic_text_normal"
            android:gravity="end|center_vertical"
            android:drawablePadding="@dimen/padding"
            android:text="--"
            android:drawableEnd="@drawable/arrow_right"/>

    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/line_space"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/def_height"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/padding"
        android:paddingEnd="@dimen/padding">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:text="练习错题"/>

        <TextView
            android:id="@+id/tvTest"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/ic_text_normal"
            android:gravity="end|center_vertical"
            android:drawablePadding="@dimen/padding"
            android:text="--"
            android:drawableEnd="@drawable/arrow_right"/>

    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/line_space"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/def_height"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/padding"
        android:paddingEnd="@dimen/padding">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:text="在线考试错题"/>

        <TextView
            android:id="@+id/tvPractice"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/ic_text_normal"
            android:gravity="end|center_vertical"
            android:drawablePadding="@dimen/padding"
            android:text="--"
            android:drawableEnd="@drawable/arrow_right"/>

    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/line_space"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/def_height"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/padding"
        android:paddingEnd="@dimen/padding">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/text_main_black"
            android:text="在线考试错题"/>

        <TextView
            android:id="@+id/tvOnline"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/ic_text_normal"
            android:gravity="end|center_vertical"
            android:drawablePadding="@dimen/padding"
            android:text="--"
            android:drawableEnd="@drawable/arrow_right"/>

    </LinearLayout>

</LinearLayout>