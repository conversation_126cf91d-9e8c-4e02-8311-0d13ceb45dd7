<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        >

        <TextView
            android:id="@+id/tv_view_title"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="标题"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_14" />

        <LinearLayout
            android:id="@+id/llOpDetail"
            android:layout_width="@dimen/dp_110"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/white"
            android:layout_gravity="end"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tvAddDetail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/padding"
                android:paddingStart="@dimen/padding"
                android:paddingTop="@dimen/dp_3"
                android:paddingEnd="@dimen/padding"
                android:paddingBottom="@dimen/dp_3"
                android:text="添加"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/sp_13" />

            <TextView
                android:id="@+id/tvDeleteDetail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/padding"
                android:background="@drawable/bg_round_grey_empty_10"
                android:paddingStart="@dimen/padding"
                android:paddingTop="@dimen/dp_3"
                android:paddingEnd="@dimen/padding"
                android:paddingBottom="@dimen/dp_3"
                android:text="删除"
                android:textColor="@color/ic_text_normal"
                android:textSize="@dimen/sp_13" />
        </LinearLayout>
    </RelativeLayout>
    <LinearLayout
        android:id="@+id/title_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:orientation="horizontal">
    </LinearLayout>
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycleViewDetail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>