<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:fresco="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/total_bar" />


    <include layout="@layout/line_space" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_hei"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/padding"
        android:paddingRight="@dimen/padding">

        <!--android:layout_width="@dimen/bottom_tab_hei"-->
        <!--android:layout_height="@dimen/bottom_tab_hei"-->
        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/user_avatar"
            android:layout_width="@dimen/title_bar_hei"
            android:layout_height="@dimen/title_bar_hei"
            android:layout_gravity="center_vertical"
            fresco:backgroundImage="@color/text_bg"
            fresco:failureImage="@drawable/ic_user_avatar"
            fresco:failureImageScaleType="centerInside"
            fresco:placeholderImageScaleType="fitCenter"
            fresco:roundAsCircle="true" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/padding"
            android:layout_marginRight="@dimen/padding"
            android:layout_weight="1"
            android:text="用户名"
            android:textColor="@color/text_main_black"
            android:textSize="@dimen/sp_16" />

        <TextView
            android:id="@+id/tv_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:drawableLeft="@drawable/ic_timer"
            android:drawablePadding="@dimen/button_radius"
            android:textColor="@color/colorAccent"
            android:textSize="@dimen/sp_14" />
    </LinearLayout>

    <!--<com.amap.api.maps.MapView

        android:id="@+id/map"

        android:layout_width="match_parent"

        android:layout_height="200dp"/>

    <include layout="@layout/line_space_blod"/>-->


    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:padding="@dimen/padding">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical">


                <ImageView
                    android:id="@+id/iv_point1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_point_unfill" />

                <View
                    android:id="@+id/v_line1"
                    android:layout_width="0.5dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1"
                    android:background="@color/edit_hit" />

                <ImageView
                    android:id="@+id/iv_point2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_point_unfill" />

                <View
                    android:id="@+id/v_line2"
                    android:layout_width="0.5dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1"
                    android:background="@color/edit_hit"/>

                <ImageView
                    android:id="@+id/iv_point3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_point_unfill" />

                <View
                    android:id="@+id/v_line3"
                    android:layout_width="0.5dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1"
                    android:background="@color/edit_hit"/>

                <ImageView
                    android:id="@+id/iv_point4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_point_fill" />

                <View
                    android:id="@+id/v_line4"
                    android:layout_width="0.5dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingLeft="@dimen/padding">

                <TextView
                    android:id="@+id/tv_text1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="上午上班时间"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_begin_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_weight="1"
                    android:text="8:30" />

                <LinearLayout
                    android:id="@+id/ll_up"
                    android:layout_width="@dimen/quality_card_hei"
                    android:layout_height="@dimen/quality_card_hei"
                    android:layout_gravity="center"
                    android:layout_marginBottom="@dimen/dp_27"
                    android:background="@drawable/take_card_bg_normal"
                    android:orientation="vertical"
                    android:visibility="invisible">

                    <TextView
                        android:id="@+id/tv_up_do"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="bottom|center"
                        android:paddingBottom="@dimen/padding"
                        android:text="上午上班打卡"
                        android:textColor="@color/text_bg"
                        android:textSize="@dimen/sp_14" />


                    <TextView
                        android:id="@+id/tv_up_time"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="top|center"
                        android:paddingTop="@dimen/padding"
                        android:text=""
                        android:textColor="@color/text_bg"
                        android:textSize="@dimen/sp_14" />


                </LinearLayout>

                <TextView
                    android:id="@+id/tv_text2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="上午下班时间"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_begin_time1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_weight="1"
                    android:text="12:10" />
                <LinearLayout
                    android:id="@+id/ll_up1"
                    android:layout_width="@dimen/quality_card_hei"
                    android:layout_height="@dimen/quality_card_hei"
                    android:layout_gravity="center"
                    android:layout_marginBottom="@dimen/dp_27"
                    android:background="@drawable/take_card_bg_normal"
                    android:orientation="vertical"
                    android:visibility="invisible">

                    <TextView
                        android:id="@+id/tv_up_do1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="bottom|center"
                        android:paddingBottom="@dimen/padding"
                        android:text="上午下班打卡"
                        android:textColor="@color/text_bg"
                        android:textSize="@dimen/sp_14" />


                    <TextView
                        android:id="@+id/tv_up_time1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="top|center"
                        android:paddingTop="@dimen/padding"
                        android:text=""
                        android:textColor="@color/text_bg"
                        android:textSize="@dimen/sp_14" />


                </LinearLayout>

                <TextView
                    android:id="@+id/tv_text3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="下午上班时间"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_end_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_weight="1"
                    android:text="14:00" />

                <LinearLayout
                    android:id="@+id/ll_down"
                    android:layout_width="@dimen/quality_card_hei"
                    android:layout_height="@dimen/quality_card_hei"
                    android:layout_gravity="center"
                    android:layout_marginBottom="@dimen/dp_27"
                    android:background="@drawable/take_card_bg_normal"
                    android:orientation="vertical"
                    android:visibility="invisible">

                    <TextView
                        android:id="@+id/tv_down_do"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="bottom|center"
                        android:paddingBottom="@dimen/padding"
                        android:text="下午上班打卡"
                        android:textColor="@color/text_bg"
                        android:textSize="@dimen/sp_14" />


                    <TextView
                        android:id="@+id/tv_down_time"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="top|center_horizontal"

                        android:paddingTop="@dimen/padding"
                        android:text=""
                        android:textColor="@color/text_bg"
                        android:textSize="@dimen/sp_14" />


                </LinearLayout>

                <TextView
                    android:id="@+id/tv_text4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="下午下班时间"
                    android:textColor="@color/text_main_black"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tv_end_time1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_weight="1"
                    android:text="18:10" />

                <LinearLayout
                    android:id="@+id/ll_down1"
                    android:layout_width="@dimen/quality_card_hei"
                    android:layout_height="@dimen/quality_card_hei"
                    android:layout_gravity="center"
                    android:layout_marginBottom="@dimen/dp_27"
                    android:background="@drawable/take_card_bg_normal"
                    android:orientation="vertical"
                    android:visibility="invisible">

                    <TextView
                        android:id="@+id/tv_down_do1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="bottom|center"
                        android:paddingBottom="@dimen/padding"
                        android:text="下午下班打卡"
                        android:textColor="@color/text_bg"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:id="@+id/tv_down_time1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="top|center_horizontal"
                        android:paddingTop="@dimen/padding"
                        android:text=""
                        android:textColor="@color/text_bg"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>