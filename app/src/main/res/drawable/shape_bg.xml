<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android">
<!--    <corners android:topRightRadius="@dimen/padding"-->
<!--        android:topLeftRadius="@dimen/padding"-->
<!--        android:bottomRightRadius="@dimen/padding"-->
<!--        android:bottomLeftRadius="@dimen/padding"/>-->

    <!-- 可选：添加边框 -->
    <!-- 圆角半径 -->
    <corners android:radius="10dp" />
    <corners
        android:topLeftRadius="10dp"
        android:topRightRadius="10dp"
        android:bottomLeftRadius="10dp"
        android:bottomRightRadius="10dp" />
    <solid android:color="#fff"/>

</shape>