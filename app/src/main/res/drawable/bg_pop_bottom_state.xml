<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

<!--    <item android:state_pressed="true" >-->
<!--        <shape>-->
<!--            <stroke android:color="@color/colorPrimary" android:width="@dimen/dp_0_5"/>                           &lt;!&ndash;填充颜色&ndash;&gt;-->
<!--            <solid android:color="#20000000"/>-->
<!--            <corners android:radius="3dp"/>-->
<!--        </shape>-->
<!--    </item>-->

<!--    <item android:state_pressed="false" >-->
<!--        <shape>-->
<!--            <stroke android:color="@color/colorPrimary" android:width="@dimen/dp_0_5"/>                           &lt;!&ndash;填充颜色&ndash;&gt;-->
<!--            <corners android:radius="3dp"/>-->
<!--        </shape>-->
<!--    </item>-->

    <item android:state_enabled="false" >
        <shape>
<!--            <stroke android:color="@color/colorPrimary" android:width="@dimen/dp_0_5"/>                           &lt;!&ndash;填充颜色&ndash;&gt;-->
            <solid android:color="#20000000"/>
            <corners android:radius="3dp"/>
        </shape>
    </item>

    <item android:state_enabled="true" >
        <shape>
            <stroke android:color="@color/colorPrimary" android:width="@dimen/dp_0_5"/>                           <!--填充颜色-->
            <corners android:radius="3dp"/>
        </shape>
    </item>
</selector>