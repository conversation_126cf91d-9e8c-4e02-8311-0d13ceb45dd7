<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="AppTheme.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="AppTheme.NoActionBar" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@android:color/transparent</item>
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="NoAnimTheme" parent="AppTheme">
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowAnimationStyle">@style/noAnimation</item>
    </style>

    <style name="noAnimation">
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>

    </style>


    <!--showDialog弹出动画-->
    <style name="DialogAnim" mce_bogus="1" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/zoom_in</item>
        <item name="android:windowExitAnimation">@anim/zoom_out</item>
    </style>

    <style name="dialog_up_down">
        <item name="android:windowEnterAnimation"> @anim/dialog_enter_top</item>
        <item name="android:windowExitAnimation"> @anim/dialog_out_top</item>
    </style>

    <declare-styleable name="LoadingView">
        <!--声明我们的属性，名称为default_size,取值类型为尺寸类型（dp,px等）-->
        <attr name="default_size" format="dimension" />

        <attr name="pathColor" format="color"/>
        <attr name="segmentLength" format="dimension"/>
        <attr name="segmentWidth" format="dimension"/>
    </declare-styleable>

    <!-- 网络进度加载框-->
    <style name="loadingDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <!-- 通知单item样式-->
    <style name="style_notice_item">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/sp_13</item>
    </style>

    <!-- 通知单radiobutton-->
    <style name="rb_notice_bg_checked">
        <item name="android:layout_width">@dimen/dp_100</item>
        <item name="android:layout_height">@dimen/dp_40</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:button">@null</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/rb_notice_bg</item>
    </style>


    <!-- 通知单radiobutton-->
    <style name="et_notice_bg">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/shape_notice_detail_bg</item>
        <item name="android:padding">@dimen/dp_3</item>
        <item name="android:layout_marginLeft">@dimen/dp_5</item>
        <item name="android:textColor">@color/text_main_black</item>
        <item name="android:maxLines">1</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:lines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="et_notice_bg1">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@null</item>
        <item name="android:padding">@dimen/dp_3</item>
        <item name="android:layout_marginLeft">@dimen/dp_5</item>
        <item name="android:textColor">@color/text_main_black</item>
        <item name="android:textColorHint">@color/edit_hit</item>
        <item name="android:maxLines">1</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:lines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="et_remark">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
<!--        <item name="android:background">@drawable/edit_unfill_bg1</item>-->
        <item name="android:padding">5dp</item>
        <item name="android:gravity">top</item>
        <item name="android:singleLine">false</item>
        <item name="android:inputType">textMultiLine</item>
        <item name="android:minLines">5</item>
        <item name="android:maxLength">500</item>
        <item name="android:textColor">@color/text_main_black</item>
        <item name="android:textColorHint">@color/edit_hit</item>
        <item name="android:textSize">@dimen/sp_14</item>
    </style>

<!-- 表单标题 -->
    <style name="tv_form_title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:lines">1</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/text_main_black</item>
        <item name="android:layout_marginRight">@dimen/dp_10</item>
    </style>
    //
    <style name="tv_form_content">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/edit_unfill_bg1</item>
        <item name="android:gravity">top</item>
        <item name="android:ellipsize">end</item>
        <item name="android:hint">请输入</item>
        <item name="android:minLines">1</item>
        <item name="android:layout_weight">1</item>
        <item name="android:maxLength">500</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:textColor">@color/text_main_black</item>
    </style>
    <style name="tv_form_content_mult">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/edit_unfill_bg1</item>
        <item name="android:gravity">top</item>
        <item name="android:ellipsize">end</item>
        <item name="android:hint">请输入</item>
        <item name="android:minLines">4</item>
        <item name="android:maxLength">500</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:textColor">@color/text_main_black</item>
    </style>


    <!-- 通知单统计radiobutton-->
    <style name="et_notice_stati_bg">
        <item name="android:layout_width">@dimen/dp_50</item>
        <item name="android:layout_height">@dimen/dp_50</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:button">@null</item>
        <item name="android:text">0</item>
        <item name="android:background">@drawable/rb_notice_stati</item>
    </style>


    <!-- 通讯录 文字头像 -->
    <style name="tv_text_author_bg">
        <item name="android:layout_width">@dimen/dp_40</item>
        <item name="android:layout_height">@dimen/dp_40</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/shape_circle_calendar_today_bg</item>
    </style>


    <!-- PopupWindow弹出/隐藏动画 -->
    <style name="MyPopupWindow_anim_style">
        <item name="android:windowEnterAnimation">@anim/anim_popwindow_show</item>
        <item name="android:windowExitAnimation">@anim/anim_popwindow_hidden</item>
    </style>



    <!-- 通知单item样式-->
    <style name="style_form_title">
        <item name="android:layout_width">@dimen/dp_0</item>
        <item name="android:layout_weight">1.5</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:textColor">@color/text_main_black</item>
        <item name="android:gravity">right</item>
        <item name="android:layout_marginRight">@dimen/dp_10</item>
    </style>
    <style name="style_form_title1">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:lines">1</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/text_main_black</item>
        <item name="android:layout_marginRight">@dimen/dp_10</item>
    </style>

    <!-- 通知单item样式-->
    <style name="style_form_ll">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">@dimen/dp_10</item>
        <item name="android:paddingRight">@dimen/dp_30</item>
        <item name="android:gravity">top</item>
    </style>

    <style name="style_form_ll1">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">@dimen/dp_10</item>
        <item name="android:paddingLeft">@dimen/dp_10</item>
        <item name="android:paddingRight">@dimen/dp_10</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:minHeight">@dimen/dp_44</item>
        <item name="android:background">@drawable/shape_bg_cell</item>
    </style>

    <!-- 通知单item样式-->
    <style name="style_form_content">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/shape_notice_detail_bg</item>
        <item name="android:padding">@dimen/dp_3</item>
        <item name="android:layout_marginLeft">@dimen/dp_5</item>
        <item name="android:textColor">@color/text_main_black</item>
        <item name="android:maxLines">1</item>
        <item name="android:textSize">@dimen/sp_14</item>
    </style>


    <!-- 任务表格样式title-->
    <style name="style_task_table_title">
        <item name="android:layout_width">@dimen/dp_0</item>
        <item name="android:layout_height">@dimen/dp_30</item>
        <item name="android:textSize">@dimen/sp_12</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/shape_notice_detail_bg</item>
    </style>

    <style name="style_task_table_title1">
        <item name="android:layout_width">@dimen/dp_0</item>
        <item name="android:layout_height">@dimen/dp_30</item>
        <item name="android:textSize">@dimen/sp_12</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/shape_notice_detail_bg1</item>
    </style>


    <!-- 任务表格样式-->
    <style name="style_task_table">
        <item name="android:layout_width">@dimen/dp_0</item>
        <item name="android:layout_height">@dimen/dp_40</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/shape_notice_detail_bg</item>
    </style>

    <!--下划线-->
    <style name="v_drvier_line_h">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dp_1</item>
        <item name="android:layout_marginTop">@dimen/dp_10</item>
        <item name="android:layout_marginLeft">@dimen/dp_10</item>
        <item name="android:layout_marginRight">@dimen/dp_10</item>
        <item name="android:background">@color/line_space</item>
    </style>
    <style name="v_drvier_line_v">
        <item name="android:layout_width">@dimen/dp_1</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_marginTop">@dimen/dp_5</item>
        <item name="android:layout_marginBottom">@dimen/dp_5</item>
        <item name="android:background">@color/line_space</item>
    </style>


    <style name="popwindow_anim_style">
        <item name="android:windowEnterAnimation">@anim/pop_in_anim</item>
        <item name="android:windowExitAnimation">@anim/pop_out_anim</item>
    </style>


    <!--switch按钮属性设置   是否提醒按钮-->
    <style name="s_true" parent="@android:style/TextAppearance.Small">
        <item name="android:textColor">#33da33</item>
    </style>

    <style name="s_false" parent="@android:style/TextAppearance.Small">
        <item name="android:textColor">#9b9b9b</item>
    </style>

    <style name="result_pop_scroll">
        <item name="android:background">@color/white</item>
    </style>
    <style name="result_pop_scrollinner">
        <item name="android:padding">@dimen/result_pop_padding</item>
        <item name="android:rowHeight">12dp</item>
    </style>

    <style name="line_item_has_padding">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/title_bar_hei</item>
        <item name="android:paddingStart">@dimen/padding</item>
        <item name="android:paddingEnd">@dimen/padding</item>
        <item name="android:gravity">center_vertical</item>
    </style>
    <style name="line_item">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/title_bar_hei</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="DraggableDialog" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item><!--背景透明-->
    </style>

</resources>
