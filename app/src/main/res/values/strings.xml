<resources>
<!--        <string name="app_name">首盛云</string>-->
    <string name="app_name">智慧监理</string>
<!--        <string name="app_name">华东监理</string>-->
<!--        <string name="app_name">悦监理</string>-->
<!--    <string name="app_name">悦监云</string>-->
    <string name="title_activity_main">MainActivity</string>
    <string name="request_error">网络请求错误</string>

    <string name="detect_no_face">未检测到人脸</string>
    <string name="detect_face_in">把脸移入框内</string>
    <string name="detect_zoom_in">手机拿近一点</string>
    <string name="detect_zoom_out">手机拿远一点</string>
    <string name="detect_head_up">建议略微抬头</string>
    <string name="detect_head_down">建议略微低头</string>
    <string name="detect_head_left">建议略微向左转头</string>
    <string name="detect_head_right">建议略微向右转头</string>
    <string name="detect_occ_face">脸部有遮挡</string>
    <string name="detect_low_light">光线再亮些</string>
    <string name="detect_keep">请保持不动</string>
    <string name="detect_standard">请正对手机</string>
    <string name="detect_timeout">检测超时</string>
    <string name="liveness_eye">眨眨眼</string>
    <string name="liveness_eye_left">请眨眨左边眼睛</string>
    <string name="liveness_eye_right">请眨眨右边眼睛</string>
    <string name="liveness_mouth">张张嘴</string>
    <string name="liveness_head_left">向左缓慢转头</string>
    <string name="liveness_head_right">向右缓慢转头</string>
    <string name="liveness_head_left_right">摇摇头</string>
    <string name="liveness_head_up">缓慢抬头</string>
    <string name="liveness_head_down">缓慢低头</string>
    <string name="liveness_good">非常好</string>
    <string name="camera_permission_required">本功能需要相机权限！</string>

    <!-- TODO: Remove or change this placeholder text -->
    <string name="hello_blank_fragment">Hello blank fragment</string>

    <string name="protocol_confirm_remind">请您务必审慎阅读、充分理解“隐私政策和用户协议”各条款，包括但不限于：为了工作打卡、图片上传、语音识别、面部识别等操作，我们需要获取定位、存储、相机等权限。你可以在“账号管理”中查看个人信息并管理您的授权。\n
    你可以阅读</string>
    <string name="protocol_confirm_remind_end">了解详细信息。如你同意，请点击“同意”开始接受我们的服务</string>

    <item name="anylayer_float_position" type="id" />


    <string name="gravity_left">left</string>
    <string name="gravity_center">center</string>
    <string name="gravity_right">right</string>
    <string name="title_activity_read_record_list">
        ReadRecordListActivity
    </string>
    <!-- Strings used for fragments for navigation -->
    <string name="first_fragment_label">First Fragment
    </string>
    <string name="second_fragment_label">Second Fragment
    </string>
    <string name="next">Next</string>
    <string name="previous">Previous</string>

    <string name="hello_first_fragment">Hello first
        fragment
    </string>
    <string name="hello_second_fragment">Hello second
        fragment. Arg: %1$s
    </string>
</resources>
