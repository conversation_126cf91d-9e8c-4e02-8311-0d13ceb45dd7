<?xml version="1.0" encoding="utf-8"?>

<resources>
    <attr name="title" format="string" />
    <declare-styleable name="YLabelCell">
        <attr name="title" />
    </declare-styleable>
    <declare-styleable name="YLabelCell1">
        <attr name="title" />
    </declare-styleable>
    <declare-styleable name="YLabelCell2">
        <attr name="title" />
    </declare-styleable>
    <declare-styleable name="YLabelCellMult">
        <attr name="title" />
    </declare-styleable>

    <declare-styleable name="YTextFieldCell">
        <attr name="title" />
    </declare-styleable>

    <declare-styleable name="YTextViewCell">
        <attr name="title" />
    </declare-styleable>

    <declare-styleable name="YSelectTextViewCell">
        <attr name="title" />
    </declare-styleable>

    <declare-styleable name="YSelectDateCell">
        <attr name="title" />
    </declare-styleable>

    <declare-styleable name="YAudioCell">
        <attr name="title" />
    </declare-styleable>

    <declare-styleable name="YBtnSelCell">
        <attr name="title" />
    </declare-styleable>

    <declare-styleable name="TagFlowLayout">
        <attr name="max_select" format="integer"></attr>
        <attr name="tag_gravity">
            <enum name="left" value="-1" />
            <enum name="center" value="0" />
            <enum name="right" value="1" />
        </attr>
    </declare-styleable>

    <declare-styleable name="YFileListView">
        <attr name="title" />
    </declare-styleable>
    <declare-styleable name="YFileListItemView">
        <attr name="title" />
    </declare-styleable>

    <declare-styleable name="YBadgeImageView">
        <attr name="title" />
        <attr name="Image" format="reference"/>
        <attr name="textStyle">
            <flag name="normal" value="0" />
            <flag name="bold" value="1" />
            <flag name="italic" value="2" />
        </attr>
    </declare-styleable>




    <!-- 角标相对于View的对齐方式 -->
    <attr name="badge_gravity">
        <flag name="left" value="1" />
        <flag name="top" value="2" />
        <flag name="right" value="4" />
        <flag name="bottom" value="8" />
        <flag name="center" value="16" />
    </attr>
    <!--角标背景-->
    <attr name="badge_background" format="reference|color" />

    <!--角标边距-->
    <attr name="badge_padding" format="reference|dimension" />
    <!--角标左边距-->
    <attr name="badge_paddingLeft" format="reference|dimension" />
    <!--角标上边距-->
    <attr name="badge_paddingTop" format="reference|dimension" />
    <!--角标右边距-->
    <attr name="badge_paddingRight" format="reference|dimension" />
    <!--角标下边距-->
    <attr name="badge_paddingBottom" format="reference|dimension" />

    <!--角标横向偏移量-->
    <attr name="badge_offsetX" format="reference|dimension" />
    <!--角标纵向偏移量-->
    <attr name="badge_offsetY" format="reference|dimension" />

    <!--角标字体大小-->
    <attr name="badge_textSize" format="reference|dimension" />
    <!--角标字体颜色-->
    <attr name="badge_textColor" format="reference|color" />
    <!--文本加粗-->
    <attr name="badge_boldText" format="boolean" />

    <!--小圆点半径-->
    <attr name="badge_dotRadius" format="reference|dimension" />

    <declare-styleable name="Badge">

        <!--角标对齐方式-->
        <attr name="badge_gravity" />
        <!--角标背景-->
        <attr name="badge_background" />

        <!--角标边距-->
        <attr name="badge_padding" />
        <!--角标左边距-->
        <attr name="badge_paddingLeft" />
        <!--角标上边距-->
        <attr name="badge_paddingTop" />
        <!--角标右边距-->
        <attr name="badge_paddingRight" />
        <!--角标下边距-->
        <attr name="badge_paddingBottom" />

        <!--角标横向偏移量-->
        <attr name="badge_offsetX" />
        <!--角标纵向偏移量-->
        <attr name="badge_offsetY" />

        <!--字体大小-->
        <attr name="badge_textSize" />
        <!--字体颜色-->
        <attr name="badge_textColor" />
        <!--文本加粗-->
        <attr name="badge_boldText" />

        <!--小圆点半径-->
        <attr name="badge_dotRadius" />
    </declare-styleable>
</resources>