<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipToPadding="true"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/actionbar"
        layout="@layout/sign_actionbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sign_actionbar_height" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:layout_weight="1"
            android:background="@drawable/sign_bg_shape"
            android:padding="10dp">


            <android.king.signature.view.HVScrollView
                android:id="@+id/sv_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fadingEdge="none">

                <android.king.signature.view.HandWriteEditView
                    android:id="@+id/et_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@null" />
            </android.king.signature.view.HVScrollView>

        </RelativeLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_margin="10dp"
            android:background="@drawable/sign_bg_shape"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="10dp">


            <LinearLayout
                android:layout_width="@dimen/sign_grid_toolbar_height"
                android:layout_height="match_parent"
                android:layout_marginRight="10dp"
                android:gravity="center"
                android:orientation="vertical">


                <android.king.signature.view.CircleImageView
                    android:id="@+id/space"
                    android:layout_width="@dimen/sign_tool_icon_size"
                    android:layout_height="@dimen/sign_tool_icon_size"
                    android:layout_weight="1"
                    android:src="@drawable/sign_ic_space" />

                <android.king.signature.view.CircleImageView
                    android:id="@+id/enter"
                    android:layout_width="@dimen/sign_tool_icon_size"
                    android:layout_height="@dimen/sign_tool_icon_size"
                    android:layout_weight="1"
                    android:src="@drawable/sign_ic_enter" />

                <android.king.signature.view.CircleImageView
                    android:id="@+id/delete"
                    android:layout_width="@dimen/sign_tool_icon_size"
                    android:layout_height="@dimen/sign_tool_icon_size"
                    android:layout_weight="1"
                    android:src="@drawable/sign_ic_delete" />

                <android.king.signature.view.CircleImageView
                    android:id="@+id/clear"
                    android:layout_width="@dimen/sign_tool_icon_size"
                    android:layout_height="@dimen/sign_tool_icon_size"
                    android:layout_weight="1"
                    android:src="@drawable/sign_ic_clear" />

                <RelativeLayout
                    android:id="@+id/circle_container"
                    android:layout_width="@dimen/sign_tool_icon_size"
                    android:layout_height="@dimen/sign_tool_icon_size"
                    android:layout_weight="1">

                    <android.king.signature.view.CircleView
                        android:id="@+id/pen_color"
                        android:layout_width="@dimen/sign_tool_icon_size"
                        android:layout_height="@dimen/sign_tool_icon_size"
                        android:layout_centerInParent="true"
                        app:showOutBorder="true"
                        app:sizeLevel="2" />
                </RelativeLayout>

            </LinearLayout>

            <android.king.signature.view.GridPaintView
                android:id="@+id/paint_view"
                android:layout_width="@dimen/sign_grid_size"
                android:layout_height="@dimen/sign_grid_size" />

        </LinearLayout>
    </LinearLayout>

</LinearLayout>
