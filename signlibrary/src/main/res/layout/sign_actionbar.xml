<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/sign_toolbar_bg">

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="10dp"
        android:padding="5dp"
        android:text="取消"
        android:textColor="@android:color/white"
        android:textSize="@dimen/sign_actionbar_text_size" />

    <TextView
        android:id="@+id/tv_ok"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="10dp"
        android:padding="5dp"
        android:text="保存"
        android:textColor="@android:color/white"
        android:textSize="@dimen/sign_actionbar_text_size" />
</RelativeLayout>