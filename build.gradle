// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {

    repositories {
        mavenCentral()
        google()

        maven {url 'https://developer.huawei.com/repo/'}
        maven {url 'https://maven.aliyun.com/repository/public/'}
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }

//        // 阿里云云效仓库：https://maven.aliyun.com/mvn/guide
//        maven { url 'https://maven.aliyun.com/repository/public' }
//        maven { url 'https://maven.aliyun.com/repository/google' }
//        // 华为开源镜像：https://mirrors.huaweicloud.com
//        maven { url 'https://repo.huaweicloud.com/repository/maven' }
        // JitPack 远程仓库：https://jitpack.io
        maven { url 'https://jitpack.io' }
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.2'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files

        classpath 'org.greenrobot:greendao-gradle-plugin:3.3.0'
        // // hms，若不集成华为厂商通道，可直接跳过
        classpath 'com.huawei.agconnect:agcp:1.6.0.300'
    }
}

ext {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}

allprojects {
    repositories {
        mavenCentral()
        google()



        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
//        maven { url 'https://maven.aliyun.com/repository/google' }
//        maven { url 'https://maven.aliyun.com/repository/jcenter' }
//        maven { url 'https://repo.huaweicloud.com/repository/maven' }
        maven { url 'https://jitpack.io' }

        maven {url 'https://developer.huawei.com/repo/'} //hms，集成华为厂商通道

        jcenter()

//      maven {url 'https://dl.bintray.com/jetbrains/anko'}



    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
tasks.withType(JavaCompile) {
    options.encoding = "UTF-8"
}

